"use client"

import { <PERSON><PERSON><PERSON> } from "lucide-react"
import Link from "next/link"
import { useEffect, useState } from "react"
import { cn } from "@/lib/utils"

interface QuickAccessButtonProps {
  workoutId?: string
  className?: string
}

export function QuickAccessButton({ workoutId, className }: QuickAccessButtonProps) {
  const [lastWorkout, setLastWorkout] = useState<string | null>(null)
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)

  // Get the last workout from localStorage or use the provided workoutId
  useEffect(() => {
    const storedWorkout = localStorage.getItem('lastWorkoutId')
    setLastWorkout(workoutId || storedWorkout)

    // Handle scroll events to hide/show the button
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      
      // Hide button when scrolling down, show when scrolling up
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false)
      } else {
        setIsVisible(true)
      }
      
      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [workoutId, lastScrollY])

  // If no workout is available, don't render the button
  if (!lastWorkout && !workoutId) {
    return null
  }

  const targetWorkoutId = workoutId || lastWorkout

  return (
    <Link
      href={`/dashboard/workout-session/${targetWorkoutId}`}
      className={cn(
        "fixed bottom-6 right-6 z-50 flex items-center justify-center w-14 h-14 rounded-full bg-primary text-primary-foreground shadow-lg transition-all duration-300 hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
        !isVisible && "translate-y-20 opacity-0",
        isVisible && "translate-y-0 opacity-100",
        className
      )}
      onClick={() => {
        // Store this workout ID for future quick access
        localStorage.setItem('lastWorkoutId', targetWorkoutId || '')
      }}
      aria-label="Quick access to current workout"
    >
      <Dumbbell className="h-6 w-6" />
    </Link>
  )
}
