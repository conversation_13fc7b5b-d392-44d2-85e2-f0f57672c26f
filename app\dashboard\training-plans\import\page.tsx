"use client"

import { FileText, Download, Upload, Co<PERSON>, RotateCcw } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { generateTrainingPlanTemplate, parseAITrainingPlan } from "@/lib/utils"
import { Separator } from "@/components/ui/separator"

export default function ImportTrainingPlan() {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("export")
  const [exportTemplate, setExportTemplate] = useState(generateTrainingPlanTemplate())
  const [importText, setImportText] = useState("")
  const [isImporting, setIsImporting] = useState(false)
  const [importProgress, setImportProgress] = useState(0)
  const textAreaRef = useRef<HTMLTextAreaElement>(null)
  
  // Functions for exporting templates
  const handleCopyTemplate = () => {
    if (textAreaRef.current) {
      textAreaRef.current.select()
      document.execCommand('copy')
      
      toast({
        title: "Template copied",
        description: "Template has been copied to clipboard. Paste it into your AI tool of choice."
      })
    }
  }
  
  const handleDownloadTemplate = () => {
    const blob = new Blob([exportTemplate], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'training-plan-template.txt'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast({
      title: "Template downloaded",
      description: "Template has been downloaded as a text file."
    })
  }
  
  // Functions for importing plans
  const simulateImportProgress = () => {
    setImportProgress(0)
    const interval = setInterval(() => {
      setImportProgress(prev => {
        const newProgress = prev + 5
        if (newProgress >= 100) {
          clearInterval(interval)
          return 100
        }
        return newProgress
      })
    }, 50)
    
    return () => clearInterval(interval)
  }
  
  const handleImport = () => {
    if (!importText.trim()) {
      toast({
        title: "Error",
        description: "Please paste the AI-generated training plan first.",
        variant: "destructive"
      })
      return
    }
    
    setIsImporting(true)
    const cleanup = simulateImportProgress()
    
    setTimeout(() => {
      try {
        // Parse the imported text into a training plan object
        const plan = parseAITrainingPlan(importText)
        
        // Save to localStorage
        const existingPlans = JSON.parse(localStorage.getItem("trainer_plans") || "[]")
        localStorage.setItem("trainer_plans", JSON.stringify([...existingPlans, plan]))
        
        toast({
          title: "Success",
          description: "Training plan imported successfully!"
        })
        
        // Navigate back to training plans page
        setTimeout(() => {
          router.push("/dashboard/training-plans")
        }, 500)
      } catch (error) {
        console.error("Import error:", error)
        toast({
          title: "Import failed",
          description: "There was an error parsing the training plan. Please check the format and try again.",
          variant: "destructive"
        })
        setIsImporting(false)
        cleanup()
      }
    }, 1500)
  }
  
  return (
    <div className="container max-w-5xl py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Training Plan</h1>
          <p className="text-muted-foreground mt-1">
            Export templates and import AI-generated training plans
          </p>
        </div>
        <Button variant="outline" onClick={() => router.push('/dashboard/training-plans')}>
          Back to Plans
        </Button>
      </div>
      
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>AI Training Plan Generator</CardTitle>
          <CardDescription>
            Use AI tools like ChatGPT to generate structured training plans that you can import directly into the system.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="export" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="export">
                <Download className="h-4 w-4 mr-2" />
                Export Template
              </TabsTrigger>
              <TabsTrigger value="import">
                <Upload className="h-4 w-4 mr-2" />
                Import AI Plan
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="export" className="space-y-4">
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <p className="text-sm font-medium">How to use:</p>
                </div>
                <ol className="list-decimal list-inside text-sm text-muted-foreground space-y-1 ml-5">
                  <li>Copy or download the template below</li>
                  <li>Paste it into ChatGPT or your preferred AI tool</li>
                  <li>Ask the AI to fill in the template with a comprehensive training plan</li>
                  <li>Copy the AI's response and switch to the Import tab</li>
                </ol>
              </div>
              
              <div className="relative">
                <Textarea 
                  ref={textAreaRef}
                  value={exportTemplate}
                  onChange={(e) => setExportTemplate(e.target.value)}
                  className="font-mono text-sm min-h-[400px]"
                />
                <div className="absolute top-2 right-2 flex gap-2">
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    onClick={handleCopyTemplate}
                    className="h-8 px-2"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    onClick={handleDownloadTemplate}
                    className="h-8 px-2"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    onClick={() => setExportTemplate(generateTrainingPlanTemplate())}
                    className="h-8 px-2"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="flex justify-end">
                <Button onClick={() => setActiveTab("import")}>
                  Continue to Import
                  <Upload className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="import" className="space-y-4">
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <p className="text-sm font-medium">Import Instructions:</p>
                </div>
                <ol className="list-decimal list-inside text-sm text-muted-foreground space-y-1 ml-5">
                  <li>Paste the AI-generated training plan below</li>
                  <li>Make sure it follows the template format</li>
                  <li>Click Import to create your new training plan</li>
                </ol>
              </div>
              
              {isImporting ? (
                <div className="space-y-4 py-8">
                  <p className="text-center text-muted-foreground">
                    {importProgress < 100 ? "Processing your training plan..." : "Import complete!"}
                  </p>
                  <Progress value={importProgress} className="w-full" />
                </div>
              ) : (
                <>
                  <Textarea 
                    value={importText}
                    onChange={(e) => setImportText(e.target.value)}
                    placeholder="Paste your AI-generated training plan here..."
                    className="min-h-[400px]"
                  />
                  
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setActiveTab("export")}>
                      Back to Template
                    </Button>
                    <Button onClick={handleImport}>
                      Import Plan
                    </Button>
                  </div>
                </>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Separator className="my-6" />

      <div className="text-center">
        <p className="text-muted-foreground mb-4">
          Can&apos;t find the perfect template? Create your own custom plan.
        </p>
        <Button variant="secondary" onClick={() => router.push('/dashboard/training-plans/create')}>
          Create Custom Plan
        </Button>
      </div>
    </div>
  )
} 