"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, User } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'

export default function DevLoginPage() {
  const router = useRouter()
  const { data: session } = useSession()
  const userRole = session?.user?.role
  const isDev = process.env.NODE_ENV === 'development'

  // Only allow access in development mode
  // If logged in, only allow admin/trainer roles
  if (!isDev || (session?.user && userRole !== 'admin' && userRole !== 'trainer')) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <h1 className="text-2xl font-bold mb-4">Development Login Only</h1>
        <p className="text-muted-foreground">This page is only available in development mode.</p>
        {session?.user && (
          <p className="text-red-500 mt-2">Only administrators and trainers can access this page when logged in.</p>
        )}
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <div className="mx-auto max-w-md space-y-6 px-4">
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold">Development Login</h1>
          <p className="text-muted-foreground">
            Select a role to login automatically without credentials
          </p>
        </div>

        <div className="space-y-4">
          <Button
            variant="outline"
            size="lg"
            className="w-full justify-start gap-2 py-6"
            onClick={() => router.push('/api/auth/dev-login?role=admin')}
          >
            <Shield className="h-5 w-5 text-blue-600" />
            <div className="flex-1 text-left">
              <div className="font-medium">Admin</div>
              <div className="text-xs text-muted-foreground">
                Full access to all features
              </div>
            </div>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="w-full justify-start gap-2 py-6"
            onClick={() => router.push('/api/auth/dev-login?role=trainer')}
          >
            <Dumbbell className="h-5 w-5 text-green-600" />
            <div className="flex-1 text-left">
              <div className="font-medium">Trainer</div>
              <div className="text-xs text-muted-foreground">
                Create workouts, manage clients, etc.
              </div>
            </div>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="w-full justify-start gap-2 py-6"
            onClick={() => router.push('/api/auth/dev-login?role=client')}
          >
            <User className="h-5 w-5 text-purple-600" />
            <div className="flex-1 text-left">
              <div className="font-medium">Client</div>
              <div className="text-xs text-muted-foreground">
                View workouts, track progress, message trainers
              </div>
            </div>
          </Button>
        </div>

        <div className="text-center text-sm">
          <p className="text-muted-foreground mt-6">
            For development purposes only.
            <br />
            This bypasses the normal authentication flow.
          </p>
          <div className="mt-4">
            <p className="text-sm font-medium mb-2">Direct Login URLs:</p>
            <div className="flex flex-col space-y-2">
              <a
                href="/api/auth/dev-login?role=client"
                className="text-primary hover:underline"
              >
                Client Direct Login
              </a>
              <a
                href="/api/auth/dev-login?role=trainer"
                className="text-primary hover:underline"
              >
                Trainer Direct Login
              </a>
              <a
                href="/api/auth/dev-login?role=admin"
                className="text-primary hover:underline"
              >
                Admin Direct Login
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}