"use client"

import { Plus } from "lucide-react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"

interface AddExerciseButtonProps {
  workoutId: string
}

export function AddExerciseButton({ workoutId }: AddExerciseButtonProps) {
  const router = useRouter()

  return (
    <Button
      onClick={() => router.push(`/dashboard/training-plans/workouts/${workoutId}/add-exercises`)}
      variant="outline"
      size="sm"
      className="mt-4"
    >
      <Plus className="h-4 w-4 mr-2" />
      Add Exercise
    </Button>
  )
} 