"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import {
  TrainerTheme, 
  SubscriptionTier, 
  DigitalProduct, 
  CoachingService, 
  Trainer,
  SelectedItem
} from "@/types/trainer"

// Import enhanced components
import { EnhancedTrainerHeader } from "./enhanced-trainer-header"
import { FeaturedContentSection } from "./featured-content-section"
import { SocialProofSection } from "./social-proof-section"
import { PersonalizedFooter } from "./personalized-footer"

// Import existing components
import { CoachingSection } from "./coaching-section"
import { ContactSection } from "./contact-section"
import { ProgramsSection } from "./programs-section"
import { SubscriptionSection } from "./subscription-section"

// Import UI components
import { Dialog, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

interface EnhancedTrainerLandingPageProps {
  trainer: Trainer
  theme: TrainerTheme
  subscriptionTiers: SubscriptionTier[]
  digitalProducts: DigitalProduct[]
  coachingServices: CoachingService[]
  // Enhanced props
  featuredProducts?: DigitalProduct[]
  transformations?: Array<{
    id: string
    title: string
    description: string
    beforeImage: string
    afterImage: string
    duration: string
  }>
  achievements?: Array<{
    icon: string
    title: string
    description: string
  }>
  testimonials?: Array<{
    id: string
    name: string
    avatar?: string
    text: string
    rating: number
    program?: string
    date?: string
    verified?: boolean
  }>
  statistics?: {
    clients?: number
    experience?: number
    programs?: number
    certifications?: number
  }
  instagramPosts?: Array<{
    id: string
    imageUrl: string
    caption: string
    likes: number
    url: string
  }>
}

export function EnhancedTrainerLandingPage({ 
  trainer, 
  theme, 
  subscriptionTiers,
  digitalProducts,
  coachingServices = [],
  featuredProducts = [],
  transformations = [],
  achievements = [],
  testimonials = [],
  statistics = {},
  instagramPosts = []
}: EnhancedTrainerLandingPageProps) {
  const router = useRouter()
  const { toast } = useToast()
  const { data: session, status: sessionStatus } = useSession()
  
  const [activeTab, setActiveTab] = useState<string>("subscriptions")
  
  // --- Auth State & Modal --- 
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login')
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [name, setName] = useState("")
  const [processingAuth, setProcessingAuth] = useState(false)
  
  // --- Purchase State & Modal ---
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [selectedItem, setSelectedItem] = useState<SelectedItem | null>(null)
  const [processingPurchase, setProcessingPurchase] = useState(false)
  const [paymentInfo, setPaymentInfo] = useState({
    cardNumber: "",
    expiry: "",
    cvc: ""
  })
  
  // --- Tab Navigation ---
  const switchToContactTab = () => {
    setActiveTab("contact")
  }
  
  // --- Item Selection Handlers ---
  const handleSelectSubscription = (subscription: SubscriptionTier) => {
    setSelectedItem({
      id: subscription.id,
      type: 'subscription',
      name: subscription.name,
      price: subscription.price
    })
    
    if (!session?.user) {
      setShowAuthModal(true)
    } else {
      setShowPurchaseModal(true)
    }
  }
  
  const handleSelectProduct = (product: DigitalProduct) => {
    setSelectedItem({
      id: product.id,
      type: 'product',
      name: product.title,
      price: product.price
    })
    
    if (!session?.user) {
      setShowAuthModal(true)
    } else {
      setShowPurchaseModal(true)
    }
  }
  
  // Handle auth (login/register) (simulated)
  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    setProcessingAuth(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      // TODO: Replace simulation with actual NextAuth calls (signIn/signUp)
      toast({ title: authMode === 'login' ? "Login Successful" : "Registration Successful" })
      setShowAuthModal(false)
      setEmail("")
      setPassword("")
      setName("")
      // After successful auth, reopen purchase modal to continue
      if (selectedItem) {
        setShowPurchaseModal(true)
      }
    } catch (error) {
      toast({ title: "Authentication Error", variant: "destructive" })
    } finally {
      setProcessingAuth(false)
    }
  }
  
  // Handle purchase (simulated)
  const handlePurchase = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedItem) return
    
    setProcessingPurchase(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      toast({ 
        title: "Purchase Successful!", 
        description: `You've purchased ${selectedItem.name}` 
      })
      
      setShowPurchaseModal(false)
      setSelectedItem(null)
      setPaymentInfo({
        cardNumber: "",
        expiry: "",
        cvc: ""
      })
      
      // Redirect to dashboard or show success message
      // router.push('/dashboard')
    } catch (error) {
      toast({ 
        title: "Purchase Failed", 
        description: "There was an error processing your payment",
        variant: "destructive" 
      })
    } finally {
      setProcessingPurchase(false)
    }
  }
  
  // --- Scroll to featured content ---
  const scrollToFeatured = () => {
    const featuredSection = document.getElementById('featured-content')
    if (featuredSection) {
      featuredSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  // --- Styles --- 
  const customStyles = {
    "--theme-primary": theme.primaryColor,
    "--theme-secondary": theme.secondaryColor,
    "--theme-font": theme.fontFamily,
  } as React.CSSProperties
  
  // --- Render --- 
  return (
    <div className="flex flex-col min-h-screen" style={customStyles}>
      {/* Custom CSS */}
      <style jsx global>{`
        :root {
          --primary: ${theme.primaryColor};
          --primary-foreground: white;
          --secondary: ${theme.secondaryColor};
        }
        body { font-family: ${theme.fontFamily}, system-ui, sans-serif; }
        .custom-gradient { background: linear-gradient(135deg, ${theme.primaryColor}, ${theme.secondaryColor}); }
        .custom-button { background-color: var(--primary); color: white; transition: all 0.3s ease; }
        .custom-button:hover { background-color: var(--secondary); transform: translateY(-3px); box-shadow: 0 10px 20px rgba(0,0,0,0.1); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-5px); box-shadow: 0 15px 30px rgba(0,0,0,0.1); }
        .feature-icon { transition: all 0.2s ease; }
        .feature-item:hover .feature-icon { transform: scale(1.2); color: var(--primary); }
        
        /* Animation classes */
        .animate-fade-in {
          animation: fadeIn 1s ease-in-out;
        }
        .animate-fade-in-down {
          animation: fadeInDown 1s ease-in-out;
        }
        .animate-bounce-slow {
          animation: bounceSlow 3s infinite;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        @keyframes fadeInDown {
          from { opacity: 0; transform: translateY(-20px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes bounceSlow {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-10px); }
        }
      `}</style>
      
      {/* Enhanced Header */}
      <EnhancedTrainerHeader 
        trainer={trainer} 
        theme={theme} 
        onActionClick={scrollToFeatured}
      />
      
      {/* Featured Content Section */}
      <section id="featured-content">
        <FeaturedContentSection
          theme={theme}
          featuredProducts={featuredProducts.length > 0 ? featuredProducts : digitalProducts.slice(0, 3)}
          transformations={transformations}
          achievements={achievements}
          onSelectProduct={handleSelectProduct}
        />
      </section>
      
      {/* Social Proof Section */}
      <SocialProofSection
        theme={theme}
        statistics={statistics}
        testimonials={testimonials}
        instagramPosts={instagramPosts}
      />
      
      {/* Main Content Tabs */}
      <main className="flex-grow bg-background">
        <div className="container mx-auto px-4 py-12">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full max-w-md mx-auto grid-cols-4 mb-8">
              <TabsTrigger value="subscriptions">Memberships</TabsTrigger>
              <TabsTrigger value="coaching">1:1 Coaching</TabsTrigger>
              <TabsTrigger value="programs">Programs</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
            </TabsList>
            
            <SubscriptionSection 
              subscriptionTiers={subscriptionTiers} 
              onSelectSubscription={handleSelectSubscription} 
            />
            <CoachingSection 
              coachingServices={coachingServices} 
              onSwitchToContact={switchToContactTab} 
            />
            <ProgramsSection 
              digitalProducts={digitalProducts} 
              onSelectProduct={handleSelectProduct} 
            />
            <ContactSection 
              trainer={trainer}
            />
          </Tabs>
        </div>
      </main>
      
      {/* Personalized Footer */}
      <PersonalizedFooter trainer={trainer} theme={theme} />
      
      {/* Auth Modal */}
      <Dialog open={showAuthModal} onOpenChange={setShowAuthModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {authMode === 'login' ? 'Log in to your account' : 'Create an account'}
            </DialogTitle>
            <DialogDescription>
              {authMode === 'login' 
                ? 'Enter your credentials to continue with your purchase.' 
                : 'Sign up to purchase programs and subscriptions.'}
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleAuth} className="space-y-4">
            {authMode === 'register' && (
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input 
                  id="name" 
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter your name" 
                  required={authMode === 'register'} 
                />
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input 
                id="email" 
                type="email" 
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email" 
                required 
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input 
                id="password" 
                type="password" 
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password" 
                required 
              />
            </div>
            
            <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setAuthMode(authMode === 'login' ? 'register' : 'login')}
                className="w-full sm:w-auto"
              >
                {authMode === 'login' ? 'Create account' : 'Log in instead'}
              </Button>
              
              <Button 
                type="submit" 
                className="w-full sm:w-auto"
                disabled={processingAuth}
              >
                {processingAuth 
                  ? 'Processing...' 
                  : authMode === 'login' ? 'Log in' : 'Sign up'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Purchase Modal */}
      <Dialog open={showPurchaseModal} onOpenChange={setShowPurchaseModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Complete your purchase</DialogTitle>
            <DialogDescription>
              Enter your payment details to purchase {selectedItem?.name}.
            </DialogDescription>
          </DialogHeader>
          
          {selectedItem && (
            <form onSubmit={handlePurchase} className="space-y-4">
              <Card className="bg-muted/50">
                <CardContent className="pt-6">
                  <div className="flex justify-between mb-2">
                    <span>Item:</span>
                    <span className="font-medium">{selectedItem.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Price:</span>
                    <span className="font-bold">${selectedItem.price.toFixed(2)}</span>
                  </div>
                </CardContent>
              </Card>
              
              <div className="space-y-2">
                <Label htmlFor="cardNumber">Card Number</Label>
                <Input 
                  id="cardNumber" 
                  value={paymentInfo.cardNumber}
                  onChange={(e) => setPaymentInfo({...paymentInfo, cardNumber: e.target.value})}
                  placeholder="1234 5678 9012 3456" 
                  required 
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expiry">Expiry Date</Label>
                  <Input 
                    id="expiry" 
                    value={paymentInfo.expiry}
                    onChange={(e) => setPaymentInfo({...paymentInfo, expiry: e.target.value})}
                    placeholder="MM/YY" 
                    required 
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="cvc">CVC</Label>
                  <Input 
                    id="cvc" 
                    value={paymentInfo.cvc}
                    onChange={(e) => setPaymentInfo({...paymentInfo, cvc: e.target.value})}
                    placeholder="123" 
                    required 
                  />
                </div>
              </div>
              
              <DialogFooter>
                <Button 
                  type="submit" 
                  className="w-full"
                  disabled={processingPurchase}
                >
                  {processingPurchase 
                    ? 'Processing...' 
                    : `Pay $${selectedItem.price.toFixed(2)}`}
                </Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
