/**
 * Transaction utility for Prisma
 * Provides a clean way to use transactions for multiple database operations
 */

import { PrismaClient } from '@prisma/client';
import { prisma } from './prisma';

/**
 * Execute multiple database operations in a transaction
 * @param fn Function that receives a transaction client and performs operations
 * @returns The result of the transaction function
 */
export async function withTransaction<T>(
  fn: (tx: PrismaClient) => Promise<T>
): Promise<T> {
  return prisma.$transaction(async (tx) => {
    return fn(tx as unknown as PrismaClient);
  });
}

/**
 * Execute multiple database operations in a transaction with timeout and isolation level
 * @param fn Function that receives a transaction client and performs operations
 * @param options Transaction options
 * @returns The result of the transaction function
 */
export async function withExtendedTransaction<T>(
  fn: (tx: PrismaClient) => Promise<T>,
  options: {
    timeout?: number;
    isolationLevel?: 'ReadUncommitted' | 'ReadCommitted' | 'RepeatableRead' | 'Serializable';
  } = {}
): Promise<T> {
  const { timeout = 5000, isolationLevel = 'ReadCommitted' } = options;
  
  return prisma.$transaction(
    async (tx) => {
      return fn(tx as unknown as PrismaClient);
    },
    {
      timeout,
      isolationLevel,
    }
  );
}

/**
 * Example usage:
 * 
 * // Simple transaction
 * const result = await withTransaction(async (tx) => {
 *   const user = await tx.user.create({ data: { ... } });
 *   const profile = await tx.profile.create({ data: { userId: user.id, ... } });
 *   return { user, profile };
 * });
 * 
 * // Extended transaction with options
 * const result = await withExtendedTransaction(
 *   async (tx) => {
 *     // Complex operations that need more time or specific isolation
 *     const user = await tx.user.findUnique({ where: { id } });
 *     await tx.user.update({ where: { id }, data: { ... } });
 *     return user;
 *   },
 *   { timeout: 10000, isolationLevel: 'Serializable' }
 * );
 */
