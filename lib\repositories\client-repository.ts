import { ClientProfile, Prisma } from "@prisma/client"
import { prisma } from "@/lib/prisma"

export interface IClientRepository {
  getClientCounts(trainerId: string): Promise<{ totalClients: number; activeClients: number }>
  findById(id: string): Promise<ClientProfile | null>
  findByIdWithDetails(id: string): Promise<any | null>
  create(data: Prisma.ClientProfileCreateInput): Promise<ClientProfile>
  update(id: string, data: Partial<ClientProfile>): Promise<ClientProfile>
  delete(id: string): Promise<ClientProfile>
  findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.ClientProfileWhereInput
    orderBy?: Prisma.ClientProfileOrderByWithRelationInput
    include?: Prisma.ClientProfileInclude
  }): Promise<ClientProfile[]>
  findByTrainerId(trainerId: string): Promise<any[]>
  count(where?: Prisma.ClientProfileWhereInput): Promise<number>
  assignTrainingPlan(clientId: string, trainingPlanId: string, trainerId: string, customName?: string, planData?: any): Promise<any>
}

export class ClientRepository implements IClientRepository {
  async getClientCounts(trainerId: string): Promise<{ totalClients: number; activeClients: number }> {
    // Count all coaching relationships for this trainer
    const totalClients = await prisma.coachingRelationship.count({
      where: {
        trainerId: trainerId
      }
    })

    // Count active coaching relationships
    const activeClients = await prisma.coachingRelationship.count({
      where: {
        trainerId: trainerId,
        status: "active"
      }
    })

    return {
      totalClients,
      activeClients
    }
  }

  async findById(id: string): Promise<ClientProfile | null> {
    return prisma.clientProfile.findUnique({
      where: { id }
    })
  }

  async findByIdWithDetails(id: string): Promise<any | null> {
    try {
      console.log(`Finding client with ID: ${id}`);
      if (!id) {
        console.error('Invalid client ID: empty or undefined');
        return null;
      }

      // First, check if this is a user ID
      const user = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          email: true,
          avatarUrl: true,
          role: true,
          clientProfile: {
            include: {
              assignedTrainer: {
                include: {
                  user: {
                    select: {
                      id: true,
                      name: true,
                      email: true,
                      avatarUrl: true
                    }
                  }
                }
              }
            }
          },
          clientCoachingRelationships: {
            where: { status: 'active' },
            include: {
              trainer: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatarUrl: true
                }
              }
            }
          }
        }
      })

      if (user) {
        console.log(`Found user with ID ${id}:`, user.name)

        try {
          // Get training plans for this user
          const trainingPlans = await prisma.trainingPlanTemplate.findMany({
            where: {
              clientId: id,
              type: 'personalized'
            },
            orderBy: {
              createdAt: 'desc' // Get the most recently created plan first
            }
            // Note: weeks is a JSON field, not a relation, so we don't need to include it
          })

          console.log(`Found ${trainingPlans.length} training plans for user ${id}`)
          if (trainingPlans.length > 0) {
            console.log(`Most recent plan: ${trainingPlans[0].id} - ${trainingPlans[0].title}`)
          }

          // Get the client profile to find the assigned trainer
          const clientProfile = await prisma.clientProfile.findUnique({
            where: { userId: user.id },
            include: {
              assignedTrainer: {
                include: {
                  user: {
                    select: {
                      id: true,
                      name: true,
                      email: true,
                      avatarUrl: true
                    }
                  }
                }
              }
            }
          });

          console.log('Found client profile:', clientProfile ? 'yes' : 'no',
                      'with trainer:', clientProfile?.assignedTrainer ? 'yes' : 'no');

          // Return user data with training plans
          return {
            id: user.id,
            name: user.name,
            email: user.email,
            avatarUrl: user.avatarUrl,
            role: user.role,
            subscriptions: user.clientCoachingRelationships || [],
            // Use the trainer from the client profile if available
            assignedTrainer: clientProfile?.assignedTrainer?.user ||
                            (user.clientCoachingRelationships && user.clientCoachingRelationships.length > 0 ?
                              user.clientCoachingRelationships[0]?.trainer : null),
            // Include the raw client profile for debugging
            clientProfile: clientProfile,
            trainingPlans: trainingPlans.length > 0 ? trainingPlans : null
          }
        } catch (error) {
          console.error(`Error fetching additional data for user ${id}:`, error);
          // Return basic user data even if we couldn't fetch additional data
          return {
            id: user.id,
            name: user.name,
            email: user.email,
            avatarUrl: user.avatarUrl,
            role: user.role,
            subscriptions: [],
            assignedTrainer: null,
            clientProfile: null,
            trainingPlans: null
          };
        }
      }

      // Fallback to the old client profile approach
      try {
        const client = await prisma.clientProfile.findUnique({
          where: { id },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatarUrl: true
              }
            },
            assignedTrainer: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    avatarUrl: true
                  }
                }
              }
            }
          }
        })

        if (!client) {
          console.log(`No client profile found with ID ${id}`);
          return null;
        }

        if (!client.user) {
          console.error(`Client profile ${id} has no associated user`);
          return {
            ...client,
            trainingPlans: null
          };
        }

        // Get training plans separately
        const trainingPlans = await prisma.trainingPlanTemplate.findMany({
          where: {
            clientId: client.user.id,
            type: 'personalized'
          }
          // Note: weeks is a JSON field, not a relation, so we don't need to include it
        })

        // Return the combined data
        return {
          ...client,
          trainingPlans: trainingPlans.length > 0 ? trainingPlans[0] : null
        }
      } catch (error) {
        console.error(`Error in client profile fallback for ID ${id}:`, error);
        return null;
      }
    } catch (error) {
      console.error(`Unexpected error in findByIdWithDetails for ID ${id}:`, error);
      return null;
    }
  }

  async create(data: Prisma.ClientProfileCreateInput): Promise<ClientProfile> {
    return prisma.clientProfile.create({
      data
    })
  }

  async update(id: string, data: Partial<ClientProfile>): Promise<ClientProfile> {
    return prisma.clientProfile.update({
      where: { id },
      data
    })
  }

  async delete(id: string): Promise<ClientProfile> {
    return prisma.clientProfile.delete({
      where: { id }
    })
  }

  async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.ClientProfileWhereInput
    orderBy?: Prisma.ClientProfileOrderByWithRelationInput
    include?: Prisma.ClientProfileInclude
  }): Promise<ClientProfile[]> {
    return prisma.clientProfile.findMany(params)
  }

  async findByTrainerId(trainerId: string): Promise<any[]> {
    // Get basic client profiles
    const clients = await prisma.clientProfile.findMany({
      where: {
        assignedTrainerId: trainerId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            avatarUrl: true,
            lastLoginAt: true
          }
        }
      }
    })

    // Return the clients
    return clients
  }

  async count(where?: Prisma.ClientProfileWhereInput): Promise<number> {
    return prisma.clientProfile.count({ where })
  }

  async assignTrainingPlan(clientId: string, trainingPlanId: string, trainerId: string, customName?: string, planData?: any): Promise<any> {
    console.log(`Checking client ${clientId} is assigned to trainer ${trainerId}`);

    // First check if the client exists
    const client = await prisma.clientProfile.findUnique({
      where: { id: clientId },
      include: { assignedTrainer: true }
    });

    if (!client) {
      throw new Error(`Client profile with ID ${clientId} not found`);
    }

    console.log('Found client profile:', {
      id: client.id,
      userId: client.userId,
      assignedTrainerId: client.assignedTrainerId
    });

    // Then check if the client is assigned to this trainer
    if (client.assignedTrainerId !== trainerId) {
      // Get the trainer profile to check if the user ID matches
      const trainerProfile = await prisma.trainerProfile.findUnique({
        where: { id: client.assignedTrainerId || '' }
      });

      console.log('Trainer profile check:', {
        trainerProfileId: trainerProfile?.id,
        trainerUserId: trainerProfile?.userId,
        expectedTrainerId: trainerId
      });

      // If the trainer user ID matches, allow it (this handles the case where trainerId is the user ID)
      if (trainerProfile && trainerProfile.userId === trainerId) {
        console.log('Trainer user ID matches, allowing assignment');
      } else {
        throw new Error(`Client is not assigned to trainer with ID ${trainerId}`);
      }
    }

    console.log(`Checking training plan ${trainingPlanId} belongs to trainer ${trainerId}`);

    // First check if the training plan exists
    const trainingPlan = await prisma.trainingPlanTemplate.findUnique({
      where: { id: trainingPlanId }
    });

    if (!trainingPlan) {
      throw new Error(`Training plan with ID ${trainingPlanId} not found`);
    }

    console.log('Found training plan:', {
      id: trainingPlan.id,
      title: trainingPlan.title,
      trainerId: trainingPlan.trainerId
    });

    // Then check if the training plan belongs to this trainer
    if (trainingPlan.trainerId !== trainerId) {
      // Get the trainer profile to check if the user ID matches
      const trainerProfile = await prisma.trainerProfile.findFirst({
        where: { userId: trainerId }
      });

      console.log('Trainer profile check for plan:', {
        trainerProfileId: trainerProfile?.id,
        trainerUserId: trainerProfile?.userId,
        planTrainerId: trainingPlan.trainerId
      });

      // If the trainer profile ID matches the plan's trainer ID, allow it
      if (trainerProfile && trainerProfile.id === trainingPlan.trainerId) {
        console.log('Trainer profile ID matches plan trainer ID, allowing assignment');
      } else {
        throw new Error(`Training plan does not belong to trainer with ID ${trainerId}`);
      }
    }

    // Get the user ID for the client profile
    const clientUser = await prisma.user.findFirst({
      where: {
        clientProfile: {
          id: clientId
        }
      }
    });

    if (!clientUser) {
      throw new Error(`User not found for client profile ${clientId}`);
    }

    console.log(`Found client user:`, {
      userId: clientUser.id,
      name: clientUser.name,
      email: clientUser.email
    });

    // If planData is provided, use it; otherwise, use the original plan's data
    const createData = planData ? {
      title: customName || planData.title,
      description: planData.description,
      difficulty: planData.difficulty,
      type: 'personalized',
      clientId: clientUser.id, // Use the User ID, not the ClientProfile ID
      trainerId: trainerId,
      weeks: planData.weeks
    } : {
      title: customName || `${trainingPlan.title} (Assigned)`,
      description: trainingPlan.description,
      difficulty: trainingPlan.difficulty,
      type: 'personalized',
      clientId: clientUser.id, // Use the User ID, not the ClientProfile ID
      trainerId: trainerId,
      weeks: trainingPlan.weeks || []
    };

    console.log('Creating personalized plan with data:', {
      title: createData.title,
      description: createData.description,
      difficulty: createData.difficulty,
      clientId: createData.clientId,
      trainerId: createData.trainerId,
      weeksType: typeof createData.weeks
    });

    // Create a personalized training plan for the client
    return prisma.trainingPlanTemplate.create({
      data: createData
    })
  }
}
