{"name": "clear-coach-app", "version": "0.1.0", "private": true, "repository": {"type": "git", "url": "git+https://github.com/<PERSON>-<PERSON>/Clear-Coach-app_v0.git"}, "author": "<PERSON>", "scripts": {"dev": "next dev", "dev:socket": "node start-server.js", "dev:all": "node start-app.js", "build": "next build", "start": "next start", "lint": "next lint", "db:drop": "dropdb Clear_Coach_app", "db:create": "createdb Clear_Coach_app", "db:migrate": "psql -d Clear_Coach_app -f db/migrations/001_initial_schema.sql", "db:seed": "npx prisma db seed", "db:reset": "npm run db:drop && npm run db:create && npm run db:migrate && npm run db:seed", "db:test": "ts-node scripts/test-db.ts", "db:create-admin": "ts-node scripts/create-admin.ts", "db:create-client": "ts-node scripts/create-client.ts", "db:check-user": "ts-node scripts/check-user.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:workflows": "vitest run tests/workflows/basic.test.ts tests/workflows/user-management.test.ts tests/workflows/client-management.test.ts --config vitest.config.mjs", "test:client-workflows": "vitest run tests/workflows/client-workflows.test.ts --config vitest.config.mjs", "test:premium-client-workflows": "vitest run tests/workflows/premium-client-workflows.test.ts --config vitest.config.mjs", "test:trainer-workflows": "vitest run tests/workflows/trainer-workflows.test.ts --config vitest.config.mjs", "test:e2e": "playwright test", "test:e2e:client": "playwright test tests/workflows/client-journey.spec.ts", "test:e2e:premium": "playwright test tests/workflows/premium-client-journey.spec.ts", "test:e2e:trainer": "playwright test tests/workflows/trainer-journey.spec.ts", "test:e2e:upgrade": "playwright test tests/workflows/client-upgrade-journey.spec.ts", "test:e2e:coaching": "playwright test tests/workflows/coaching-journey.spec.ts", "test:trainer-complete": "vitest run tests/workflows/trainer-complete-workflow.test.ts --config vitest.config.mjs", "test:trainer-service": "vitest run tests/workflows/trainer-service-workflow.test.ts --config vitest.config.mjs", "test:trainer-corrected": "vitest run tests/workflows/trainer-corrected-workflow.test.ts --config vitest.config.mjs", "test:all-workflows": "vitest run tests/workflows/basic.test.ts tests/workflows/client-management.test.ts tests/workflows/client-workflows.test.ts tests/workflows/trainer-corrected-workflow.test.ts tests/workflows/trainer-service-workflow.test.ts tests/workflows/user-management.test.ts --config vitest.config.mjs", "test:basic": "vitest run tests/workflows/basic.test.ts --config vitest.config.mjs", "test:pre-merge": "npm run lint && npm run test && npm run test:all-workflows", "test:pre-merge-e2e": "npm run lint && npm run test && npm run test:e2e:client && npm run test:e2e:premium && npm run test:e2e:trainer && npm run test:e2e:upgrade && npm run test:e2e:coaching", "seed-trainers": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' scripts/seed-trainers.ts", "security:audit": "ts-node scripts/security-audit.ts", "security:generate-secrets": "ts-node scripts/generate-env-secrets.ts", "security:check-deps": "npm audit --production", "security:fix-deps": "npm audit fix --production", "security:check-hooks": "ts-node scripts/check-secure-hooks.ts", "security:all": "npm run security:audit && npm run security:check-deps && npm run security:check-hooks"}, "dependencies": {"@auth/prisma-adapter": "^2.9.0", "@aws-sdk/client-s3": "^3.797.0", "@clerk/nextjs": "^6.12.8", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.3.4", "@nextui-org/react": "^2.2.9", "@prisma/client": "6.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-table": "^8.11.7", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.6", "ai": "^4.3.9", "aws-sdk": "^2.1547.0", "axios": "^1.6.7", "bcryptjs": "^3.0.2", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.0", "cloudinary": "^2.6.1", "clsx": "^2.1.0", "cmdk": "^0.2.0", "cookies-next": "^4.1.1", "crypto": "^1.0.1", "csv-parse": "^5.6.0", "date-fns": "^3.3.1", "dnd-core": "^16.0.1", "embla-carousel-react": "^8.0.0-rc22", "emoji-picker-react": "^4.12.2", "file-upload-with-preview": "^6.1.2", "framer-motion": "^11.0.3", "geist": "^1.2.1", "ioredis": "^5.3.2", "jotai": "^2.6.4", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "leaflet-gesture-handling": "^1.2.2", "lucide-react": "^0.323.0", "multer": "^1.4.5-lts.1", "next": "^14.1.0", "next-auth": "^4.24.5", "next-client-cookies": "^1.1.0", "next-themes": "^0.2.1", "nodemailer": "^6.9.9", "plyr": "^3.7.8", "plyr-react": "^5.3.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-calendly": "^4.4.0", "react-chartjs-2": "^5.3.0", "react-chat-elements": "^12.0.18", "react-color": "^2.19.3", "react-day-picker": "^8.10.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.50.1", "react-icons": "^5.0.1", "react-leaflet": "^4.2.1", "react-player": "^2.14.1", "react-youtube": "^10.1.0", "recharts": "^2.11.0", "resend": "^2.0.0", "sharp": "^0.33.2", "showdown": "^2.1.0", "sonner": "^2.0.3", "stripe": "^14.14.0", "styled-components": "^6.1.8", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "vaul": "^0.8.0", "vercel": "^41.6.1", "video-react": "^0.16.0", "ws": "^8.16.0", "zod": "^3.22.4", "zustand": "^4.5.0"}, "devDependencies": {"@playwright/test": "^1.51.1", "@swc/cli": "^0.7.3", "@swc/core": "^1.4.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/cookie": "^0.6.0", "@types/jest": "^29.5.12", "@types/js-cookie": "^3.0.6", "@types/node": "^20.11.16", "@types/nodemailer": "^6.4.17", "@types/react": "^18.3.19", "@types/react-color": "^3.0.13", "@types/react-dom": "^18.3.5", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.27.0", "autoprefixer": "^10.0.1", "dotenv": "^16.4.7", "eslint": "^8.57.1", "eslint-config-next": "^14.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-unused-imports": "^4.1.4", "glob": "^11.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-mocks-http": "^1.16.2", "pino-pretty": "^13.0.0", "postcss": "^8", "prisma": "^6.6.0", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.8.2", "vitest": "^3.1.1"}, "prisma": {"seed": "node prisma/seed.js"}}