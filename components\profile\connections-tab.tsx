'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Calendar, CheckCircle, AlertCircle, ExternalLink, Save, Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface CalendlySettings {
  calendlyUserId: string | null;
  calendlyUrl: string;
}

export function ConnectionsTab() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [calendlySettings, setCalendlySettings] = useState<CalendlySettings>({
    calendlyUserId: null,
    calendlyUrl: '',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    const fetchCalendlySettings = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/calendly?action=my-settings');
        
        if (!response.ok) {
          throw new Error('Failed to fetch Calendly settings');
        }
        
        const data = await response.json();
        
        if (data.user?.calendlyUserId) {
          setCalendlySettings({
            calendlyUserId: data.user.calendlyUserId,
            calendlyUrl: `https://calendly.com/${data.user.calendlyUserId}`,
          });
        } else {
          setCalendlySettings({
            calendlyUserId: null,
            calendlyUrl: '',
          });
        }
      } catch (error) {
        console.error('Error fetching Calendly settings:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load Calendly settings. Please try again later.',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCalendlySettings();
  }, [toast]);

  const handleCalendlyUrlChange = (value: string) => {
    setCalendlySettings(prev => ({
      ...prev,
      calendlyUrl: value,
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);

      // Validate URL format
      if (calendlySettings.calendlyUrl && !calendlySettings.calendlyUrl.includes('calendly.com')) {
        toast({
          variant: 'destructive',
          title: 'Invalid URL',
          description: 'Please enter a valid Calendly URL (e.g., https://calendly.com/yourusername)',
        });
        return;
      }

      const response = await fetch('/api/calendly?action=update-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          calendlyUrl: calendlySettings.calendlyUrl,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save Calendly settings');
      }
      
      const responseData = await response.json();
      
      setCalendlySettings({
        calendlyUserId: responseData.user.calendlyUserId,
        calendlyUrl: calendlySettings.calendlyUrl,
      });
      
      setHasChanges(false);
      
      toast({
        title: 'Settings saved',
        description: 'Your Calendly integration has been updated successfully.',
      });
    } catch (error) {
      console.error('Error saving Calendly settings:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save Calendly settings. Please try again.',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Connections</CardTitle>
            <CardDescription>Loading your connection settings...</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Calendly Integration
          </CardTitle>
          <CardDescription>
            Connect your Calendly account to allow clients to schedule 1:1 sessions with you
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="calendly-url">Calendly URL</Label>
              <Input
                id="calendly-url"
                type="url"
                placeholder="https://calendly.com/yourusername"
                value={calendlySettings.calendlyUrl}
                onChange={(e) => handleCalendlyUrlChange(e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                Enter your Calendly scheduling page URL. This will be used to allow clients to book sessions with you.
              </p>
            </div>

            {calendlySettings.calendlyUrl && (
              <div className="flex items-center gap-2 text-sm">
                <ExternalLink className="h-4 w-4" />
                <a 
                  href={calendlySettings.calendlyUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary hover:underline"
                >
                  Preview your Calendly page
                </a>
              </div>
            )}

            <div className="flex gap-2">
              <Button 
                onClick={handleSave} 
                disabled={!hasChanges || isSaving}
                className="flex items-center gap-2"
              >
                {isSaving ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                {isSaving ? 'Saving...' : 'Save Settings'}
              </Button>
              
              {hasChanges && (
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setCalendlySettings(prev => ({
                      ...prev,
                      calendlyUrl: prev.calendlyUserId ? `https://calendly.com/${prev.calendlyUserId}` : '',
                    }));
                    setHasChanges(false);
                  }}
                >
                  Cancel
                </Button>
              )}
            </div>
          </div>

          <Separator />

          {calendlySettings.calendlyUserId ? (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Calendly integration is active!</strong> Clients can now schedule 1:1 sessions with you 
                through the Follow-up tab in their client detail pages.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Calendly integration not configured.</strong> Add your Calendly URL above to enable 
                client scheduling functionality.
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-3">
            <h4 className="font-medium">How it works:</h4>
            <ul className="text-sm text-muted-foreground space-y-1 ml-4">
              <li>• Clients will see a "Schedule Meeting" button in their Follow-up tab</li>
              <li>• Clicking the button opens your Calendly scheduling widget</li>
              <li>• Client information is automatically pre-filled in the form</li>
              <li>• Sessions are managed entirely through your Calendly account</li>
              <li>• No session data is stored in this application</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Future Integrations</CardTitle>
          <CardDescription>More connection options coming soon</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <p>Additional integrations like Zoom, Google Calendar, and other scheduling platforms will be available in future updates.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
