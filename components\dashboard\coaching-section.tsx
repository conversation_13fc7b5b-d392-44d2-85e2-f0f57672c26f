'use client';

import Link from "next/link";
import { MessageSquare, Calendar, Info, ArrowRight, Target } from "lucide-react";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface CoachingSectionProps {
  hasCoaching: boolean;
  coachName?: string;
  coachAvatar?: string;
  nextCheckInDate?: Date;
  coachingPrice?: string;
}

export function CoachingSection({
  hasCoaching,
  coachName,
  coachAvatar,
  nextCheckInDate,
  coachingPrice = "$XX/month"
}: CoachingSectionProps) {
  // Format the check-in date if available
  const formattedCheckInDate = nextCheckInDate
    ? new Date(nextCheckInDate).toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit'
      })
    : null;

  if (hasCoaching) {
    return (
      <Card className="shadow-md hover:shadow-lg transition-all overflow-hidden group">
        <div className="bg-gradient-to-r from-primary/10 to-primary/5 px-4 py-3 border-b border-primary/10 group-hover:from-primary/15 group-hover:to-primary/10 transition-all">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <MessageSquare className="h-4 w-4 text-primary" />
            1:1 Coaching
          </CardTitle>
        </div>

        <CardContent className="p-5 space-y-5">
          <div className="flex items-center gap-4">
            <Avatar className="h-14 w-14 border-2 border-primary/20 shadow-sm">
              <AvatarImage src={coachAvatar} alt={coachName} />
              <AvatarFallback className="bg-primary/10 text-primary font-medium">{coachName?.charAt(0) || 'C'}</AvatarFallback>
            </Avatar>

            <div>
              <h3 className="font-semibold text-lg">{coachName}</h3>
              <div className="flex items-center mt-1">
                <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20 text-xs font-normal">
                  Personal Coach
                </Badge>
              </div>
            </div>
          </div>

          {formattedCheckInDate && (
            <div className="bg-muted/10 p-4 rounded-xl border border-border/20 flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-blue-500/10 flex items-center justify-center">
                <Calendar className="h-5 w-5 text-blue-500" />
              </div>
              <div>
                <p className="text-xs text-muted-foreground">Next Check-in</p>
                <p className="font-medium">{formattedCheckInDate}</p>
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-3">
            <Button asChild variant="default" className="w-full bg-primary hover:bg-primary/90 h-11">
              <Link href="/dashboard/premium?tab=coaching-chat">
                <MessageSquare className="mr-2 h-4 w-4" />
                Message Coach
              </Link>
            </Button>

            <Button asChild variant="outline" className="w-full hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors h-11">
              <Link href="/dashboard/coaching/schedule">
                <Calendar className="mr-2 h-4 w-4" />
                Schedule Call
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // CTA for users without coaching
  return (
    <Card className="shadow-md hover:shadow-lg transition-all overflow-hidden group">
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 opacity-50"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <CardContent className="p-6 space-y-5 relative z-10">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-foreground/90">Ready to go further?</h3>
            <div className="h-10 w-10 rounded-full bg-background/80 flex items-center justify-center shadow-sm">
              <MessageSquare className="h-5 w-5 text-primary" />
            </div>
          </div>

          <p className="text-muted-foreground">
            Get personalized plans, direct chat with your coach, and weekly check-ins to accelerate your progress.
          </p>

          <div className="bg-muted/10 p-4 rounded-xl border border-border/20 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-green-500/10 flex items-center justify-center">
                <Target className="h-5 w-5 text-green-500" />
              </div>
              <div>
                <p className="text-sm font-medium">Personalized Coaching</p>
                <p className="text-xs text-muted-foreground">Starting at {coachingPrice}</p>
              </div>
            </div>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center text-sm text-primary hover:text-primary/80 cursor-pointer">
                    <Info className="h-4 w-4 mr-1" />
                    Details
                  </div>
                </TooltipTrigger>
                <TooltipContent className="p-3 max-w-xs">
                  <p className="text-sm">1:1 Coaching includes personalized workout plans, nutrition guidance, and weekly check-ins with your dedicated coach.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <Button asChild className="w-full bg-primary hover:bg-primary/90 h-11 text-base">
            <Link href="/dashboard/coaching/request">
              Request 1:1 Coaching
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </CardContent>
      </div>
    </Card>
  );
}
