import { DefaultSession, DefaultUser } from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      role: "admin" | "trainer" | "client"
    } & DefaultSession["user"]
  }

  interface User extends DefaultUser {
    role: "admin" | "trainer" | "client"
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string
    role: "admin" | "trainer" | "client"
  }
} 