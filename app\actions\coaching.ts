'use server'

import { prisma } from "@/lib/prisma"
import { getServerAuthSession } from "@/lib/auth"
import { Prisma, CoachingRelationship } from "@prisma/client"

interface Client {
  id: string
  name: string
  email: string
  status: string
  joinedDate: string
  nextSession: string | null
  unreadMessages: number
  plan: string
  relationshipId: string
  monthlyFee: number
  expirationDate?: string
}

interface CoachingRelationshipWithClient {
  id: string
  status: string
  startDate: Date
  endDate: Date | null
  plan: string | null
  monthlyFee: Prisma.Decimal
  client: {
    id: string
    name: string
    email: string
  } | null
}

export async function getTrainerClients(): Promise<Client[]> {
  const session = await getServerAuthSession()
  
  if (!session?.user) {
    throw new Error("Not authenticated")
  }

  const trainerId = session.user.id
  console.log("[getTrainerClients] Fetching clients for trainer:", trainerId)

  try {
    // Get all coaching relationships for the trainer
    const relationships = await prisma.coachingRelationship.findMany({
      where: {
        trainerId: trainerId,
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        startDate: 'desc',
      },
    }) as (CoachingRelationship & {
      client: {
        id: string
        name: string | null
        email: string
      }
    })[]

    // Map the relationships to the client interface
    const mappedClients = relationships.map(relationship => ({
      id: relationship.client.id,
      name: relationship.client.name || 'No name',
      email: relationship.client.email,
      status: relationship.status,
      joinedDate: relationship.startDate.toISOString(),
      nextSession: null, // This would need to be fetched from sessions table
      unreadMessages: 0, // This would need to be fetched from messages
      plan: relationship.plan || "Premium Coaching",
      relationshipId: relationship.id,
      monthlyFee: Number(relationship.monthlyFee),
      expirationDate: relationship.expirationDate?.toISOString()
    }))

    console.log("[getTrainerClients] Found clients:", mappedClients.length)
    return mappedClients
  } catch (error) {
    console.error("[getTrainerClients] Error:", error)
    throw error
  }
}

interface CreateClientData {
  firstName: string
  lastName: string
  email: string
  password: string
  monthlyFee: number
  subscriptionDuration: number
}

export async function createClient(data: CreateClientData) {
  const session = await getServerAuthSession()
  
  if (!session?.user) {
    throw new Error("Not authenticated")
  }

  const trainerId = session.user.id

  try {
    // Create the user with client role
    const client = await prisma.user.create({
      data: {
        name: `${data.firstName} ${data.lastName}`,
        email: data.email,
        password: data.password, // Note: In production, this should be hashed
        role: "client",
      },
    })

    // Calculate expiration date
    const expirationDate = new Date()
    expirationDate.setMonth(expirationDate.getMonth() + data.subscriptionDuration)

    // Create the coaching relationship
    const relationship = await prisma.coachingRelationship.create({
      data: {
        trainerId: trainerId,
        clientId: client.id,
        status: "active",
        monthlyFee: new Prisma.Decimal(data.monthlyFee),
        startDate: new Date(),
        expirationDate: expirationDate,
        plan: "Premium Coaching",
      },
      select: {
        id: true,
        status: true,
        startDate: true,
        expirationDate: true,
        plan: true,
        monthlyFee: true,
        client: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return { client, relationship }
  } catch (error) {
    console.error("[createClient] Error:", error)
    throw new Error("Failed to create client")
  }
}

export async function renewCoachingRelationship(relationshipId: string, monthlyFee?: number, subscriptionDuration: number = 1): Promise<CoachingRelationshipWithClient> {
  const session = await getServerAuthSession()
  
  if (!session?.user) {
    throw new Error("Not authenticated")
  }

  console.log("[renewCoachingRelationship] Renewing relationship:", relationshipId, "with fee:", monthlyFee, "duration:", subscriptionDuration)

  try {
    // Get the current relationship
    const currentRelationship = await prisma.coachingRelationship.findUnique({
      where: { id: relationshipId },
    })

    if (!currentRelationship) {
      throw new Error("Coaching relationship not found")
    }

    // Calculate new expiration date
    const newExpirationDate = new Date()
    newExpirationDate.setMonth(newExpirationDate.getMonth() + subscriptionDuration)

    // Check if the relationship was cancelled in the current month
    const now = new Date()
    const endDate = currentRelationship.endDate
    const isCurrentMonth = endDate && 
      endDate.getMonth() === now.getMonth() && 
      endDate.getFullYear() === now.getFullYear()

    if (isCurrentMonth) {
      // If cancelled this month, just reactivate the existing relationship
      const reactivatedRelationship = await prisma.coachingRelationship.update({
        where: { id: relationshipId },
        data: {
          status: "active",
          endDate: null,
          monthlyFee: monthlyFee !== undefined ? new Prisma.Decimal(monthlyFee) : currentRelationship.monthlyFee,
          expirationDate: newExpirationDate,
        },
      })

      const client = await prisma.user.findUnique({
        where: { id: reactivatedRelationship.clientId },
        select: {
          id: true,
          name: true,
          email: true,
        },
      })

      console.log("[renewCoachingRelationship] Reactivated existing relationship:", reactivatedRelationship.id)
      return {
        ...reactivatedRelationship,
        client,
      }
    } else {
      // If cancelled in a previous month, create a new relationship
      const newRelationship = await prisma.coachingRelationship.create({
        data: {
          trainerId: currentRelationship.trainerId,
          clientId: currentRelationship.clientId,
          status: "active",
          plan: currentRelationship.plan || "Premium Coaching",
          monthlyFee: monthlyFee !== undefined ? new Prisma.Decimal(monthlyFee) : currentRelationship.monthlyFee,
          startDate: new Date(),
          expirationDate: newExpirationDate,
        },
      })

      const client = await prisma.user.findUnique({
        where: { id: newRelationship.clientId },
        select: {
          id: true,
          name: true,
          email: true,
        },
      })

      console.log("[renewCoachingRelationship] Created new relationship:", newRelationship.id)
      return {
        ...newRelationship,
        client,
      }
    }
  } catch (error) {
    console.error("[renewCoachingRelationship] Error:", error)
    throw error
  }
}

export async function cancelCoachingRelationship(relationshipId: string) {
  const session = await getServerAuthSession()
  if (!session?.user?.id) throw new Error("Not authenticated")

  const relationship = await prisma.coachingRelationship.findUnique({
    where: { id: relationshipId },
  })

  if (!relationship) {
    throw new Error("Relationship not found")
  }

  if (relationship.trainerId !== session.user.id) {
    throw new Error("Not authorized")
  }

  try {
    // Update the current relationship to inactive
    const updatedRelationship = await prisma.coachingRelationship.update({
      where: { id: relationshipId },
      data: {
        status: "inactive",
        endDate: new Date(),
      },
    })

    return updatedRelationship
  } catch (error) {
    console.error("[cancelCoachingRelationship] Error:", error)
    throw new Error("Failed to cancel coaching relationship")
  }
}

export async function getClientHistory(clientId: string) {
  const session = await getServerAuthSession()
  if (!session?.user?.id) throw new Error("Not authenticated")

  try {
    // Get all relationships for this client with this trainer
    const relationships = await prisma.coachingRelationship.findMany({
      where: {
        trainerId: session.user.id,
        clientId: clientId,
      },
      select: {
        id: true,
        status: true,
        startDate: true,
        endDate: true,
        plan: true,
        monthlyFee: true,
      },
      orderBy: {
        startDate: 'desc',
      },
    })

    return relationships
  } catch (error) {
    console.error("[getClientHistory] Error:", error)
    throw new Error("Failed to fetch client history")
  }
}

export async function getMonthlyRevenue(month: number, year: number) {
  const session = await getServerAuthSession()
  if (!session?.user?.id) throw new Error("Not authenticated")

  try {
    const startDate = new Date(year, month - 1, 1)
    const endDate = new Date(year, month, 0)

    // Get all relationships that were active during the specified month
    const relationships = await prisma.coachingRelationship.findMany({
      where: {
        trainerId: session.user.id,
        OR: [
          {
            // Active relationships that started before or during the month
            startDate: {
              lte: endDate,
            },
            AND: [
              {
                OR: [
                  { endDate: null }, // Still active
                  { endDate: { gte: startDate } }, // Ended during or after the month
                ],
              },
            ],
          },
        ],
      },
      select: {
        monthlyFee: true,
        startDate: true,
        endDate: true,
        status: true,
      },
    })

    // Calculate total revenue for the month
    const revenue = relationships.reduce((total, rel) => {
      // For relationships that started during the month, prorate the fee
      let fee = Number(rel.monthlyFee)
      
      // If the relationship started during this month, prorate the fee
      if (rel.startDate > startDate) {
        const daysInMonth = endDate.getDate()
        const startDay = rel.startDate.getDate()
        fee = (fee / daysInMonth) * (daysInMonth - startDay + 1)
      }

      // If the relationship ended during this month, prorate the fee
      if (rel.endDate && rel.endDate < endDate) {
        const daysInMonth = endDate.getDate()
        const endDay = rel.endDate.getDate()
        fee = (fee / daysInMonth) * endDay
      }

      return total + fee
    }, 0)

    return {
      month,
      year,
      revenue: Math.round(revenue * 100) / 100, // Round to 2 decimal places
      relationshipCount: relationships.length,
    }
  } catch (error) {
    console.error("[getMonthlyRevenue] Error:", error)
    throw new Error("Failed to calculate monthly revenue")
  }
}

export async function checkAndUpdateExpiredRelationships() {
  console.log("[checkAndUpdateExpiredRelationships] Starting check...")

  try {
    // Find all active relationships that have passed their expiration date
    const expiredRelationships = await prisma.coachingRelationship.findMany({
      where: {
        status: "active",
        expirationDate: {
          lt: new Date() // Less than current date
        }
      }
    })

    console.log(`[checkAndUpdateExpiredRelationships] Found ${expiredRelationships.length} expired relationships`)

    // Update all expired relationships to inactive
    const updatePromises = expiredRelationships.map(relationship =>
      prisma.coachingRelationship.update({
        where: { id: relationship.id },
        data: {
          status: "inactive",
          endDate: new Date()
        }
      })
    )

    const results = await Promise.all(updatePromises)
    console.log(`[checkAndUpdateExpiredRelationships] Updated ${results.length} relationships to inactive`)

    return results
  } catch (error) {
    console.error("[checkAndUpdateExpiredRelationships] Error:", error)
    throw error
  }
} 