import { PrismaClient } from "@prisma/client"
import { hash, compare, genSalt } from "bcryptjs"

const prisma = new PrismaClient()

async function main() {
  const email = "<EMAIL>"
  const password = "password123"
  console.log("Using credentials:", { email, password })
  
  // Use a different salt round
  const salt = await genSalt(12)
  const hashedPassword = await hash(password, salt)
  console.log("Hashed password:", hashedPassword)

  // Delete existing user if exists
  await prisma.user.deleteMany({
    where: {
      email: email
    }
  })

  const client = await prisma.user.create({
    data: {
      email: email,
      password: hashedPassword,
      fullName: "Test User 2",
      role: "client",
    },
  })

  // Verify the password
  const isPasswordValid = await compare(password, hashedPassword)
  console.log("Password verification test:", isPasswordValid)

  // Double check the stored password
  const storedUser = await prisma.user.findUnique({
    where: { email }
  })

  if (storedUser) {
    const storedPasswordValid = await compare(password, storedUser.password)
    console.log("Stored password verification test:", storedPasswordValid)
  }

  console.log("Created client user:", client)
}

main()
  .catch((error: unknown) => {
    if (error instanceof Error) {
      console.error("Error creating client:", error.message)
    } else {
      console.error("Unknown error creating client")
    }
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 