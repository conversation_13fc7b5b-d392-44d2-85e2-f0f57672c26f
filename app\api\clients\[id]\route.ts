// API route handler for client by ID
import { ClientByIdHandler } from "@/lib/api/client-handler";

const handler = new ClientByIdHandler();

export async function GET(req: Request, { params }: { params: { id: string } }) {
  return handler.handleGet(req, params);
}

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  return handler.handlePut(req, params);
}

export async function DELETE(req: Request, { params }: { params: { id: string } }) {
  return handler.handleDelete(req, params);
}
