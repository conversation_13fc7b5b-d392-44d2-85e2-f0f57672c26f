"use client"

import { useEffect, useState, Suspense } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, CheckCircle, XCircle } from "lucide-react"

function VerifyEmailContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const token = searchParams.get("token")
  const [status, setStatus] = useState<"loading" | "success" | "error">("loading")
  const [message, setMessage] = useState("Verifying your email address...")

  useEffect(() => {
    if (!token) {
      setStatus("error")
      setMessage("Verification token is missing or invalid. Please use the link from your verification email.")
      return
    }

    const verifyToken = async () => {
      try {
        const response = await fetch(`/api/auth/verify-email?token=${token}`)
        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.message || "Verification failed.")
        }

        setStatus("success")
        setMessage("Your email has been verified successfully! You can now log in to your Clear-Coach account.")

        // Redirect to login after a delay
        setTimeout(() => {
          router.push("/login?verified=true")
        }, 3000)

      } catch (error) {
        setStatus("error")
        setMessage(error instanceof Error ? error.message : "An unexpected error occurred during verification.")
      }
    }

    verifyToken()
  }, [token, router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center bg-white p-8 rounded-lg shadow-md">
        {/* Clear-Coach Logo */}
        <div className="flex justify-center">
          <Link href="/">
            <Image
              src="/logo.png"
              alt="Clear-Coach Logo"
              width={180}
              height={50}
              priority
            />
          </Link>
        </div>

        <div>
          {status === "loading" && (
            <Loader2 className="mx-auto h-16 w-16 animate-spin text-indigo-600" />
          )}
          {status === "success" && (
            <CheckCircle className="mx-auto h-16 w-16 text-green-600" />
          )}
          {status === "error" && (
            <XCircle className="mx-auto h-16 w-16 text-red-600" />
          )}
        </div>
        <div>
          <h2 className="mt-6 text-center text-2xl font-bold text-gray-900">
            {status === "loading" && "Verifying Your Email..."}
            {status === "success" && "Email Verification Successful!"}
            {status === "error" && "Email Verification Failed"}
          </h2>
          <p className="mt-4 text-center text-md text-gray-600">
            {message}
          </p>
        </div>
        {(status === "success" || status === "error") && (
          <div className="mt-6">
            <Button asChild className="w-full">
              <Link href="/login">
                {status === "success" ? "Go to Login" : "Try Again"}
              </Link>
            </Button>
            {status === "error" && (
              <p className="mt-4 text-sm text-gray-500">
                If you continue to experience issues, please contact
                <a href="mailto:<EMAIL>" className="text-indigo-600 ml-1"><EMAIL></a>
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading verification page...</div>}>
      <VerifyEmailContent />
    </Suspense>
  )
}