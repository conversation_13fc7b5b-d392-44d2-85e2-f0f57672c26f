import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// This endpoint is used as a webhook for new messages
// It can be called by the server when a new message is created
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { messageId } = await request.json();

    if (!messageId) {
      return new NextResponse("Message ID is required", { status: 400 });
    }

    // Get the message
    const message = await prisma.message.findUnique({
      where: {
        id: messageId,
      },
      include: {
        conversation: true,
      },
    });

    if (!message) {
      return new NextResponse("Message not found", { status: 404 });
    }

    // Check if the user is part of the conversation
    const conversation = message.conversation;
    if (conversation.user1Id !== session.user.id && conversation.user2Id !== session.user.id) {
      return new NextResponse("Unauthorized", { status: 403 });
    }

    // Update the message as read if the current user is the receiver
    if (message.receiverId === session.user.id) {
      await prisma.message.update({
        where: {
          id: messageId,
        },
        data: {
          read: true,
        },
      });

      // Also mark the corresponding notification as read
      await prisma.notification.updateMany({
        where: {
          sourceId: messageId,
          sourceType: "message",
          userId: session.user.id,
        },
        data: {
          read: true,
        },
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error in message webhook:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
