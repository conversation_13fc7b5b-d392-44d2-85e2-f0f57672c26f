import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET() {
  const session = await getServerSession(authOptions)

  if (!session?.user?.email) {
    return new NextResponse("Unauthorized", { status: 401 })
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return new NextResponse("User not found", { status: 404 })
    }

    const meals = await prisma.meal.findMany({
      where: { dietPlanId: user.id },
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json(meals)
  } catch (error) {
    console.error("[MEALS_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.email) {
    return new NextResponse("Unauthorized", { status: 401 })
  }

  try {
    const json = await request.json()
    const { name, description, calories, protein, carbs, fats, date } = json

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return new NextResponse("User not found", { status: 404 })
    }

    const meal = await prisma.meal.create({
      data: {
        name,
        description,
        calories,
        protein,
        carbs,
        fats,
        dietPlanId: user.id,
      },
    })

    return NextResponse.json(meal)
  } catch (error) {
    console.error("[MEALS_POST]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 