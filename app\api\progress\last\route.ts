import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Get the user's most recent measurement
    const lastMeasurement = await prisma.measurement.findFirst({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        date: "desc",
      },
    })

    if (!lastMeasurement) {
      return NextResponse.json(null)
    }

    return NextResponse.json(lastMeasurement)
  } catch (error) {
    console.error("[LAST_MEASUREMENT_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
