# Database Query Optimization Guide

This guide provides best practices for optimizing database queries in the Clear Coach application, especially in Docker environments.

## Table of Contents

1. [Connection Pooling](#connection-pooling)
2. [Query Caching](#query-caching)
3. [Optimized Queries](#optimized-queries)
4. [Database Indexes](#database-indexes)
5. [Query Batching](#query-batching)
6. [Transactions](#transactions)
7. [N+1 Query Problem](#n1-query-problem)
8. [API Route Optimization](#api-route-optimization)

## Connection Pooling

Connection pooling is now configured in `lib/prisma.ts`. The pool maintains a set of database connections that can be reused, reducing the overhead of creating new connections for each request.

```typescript
// Connection pooling configuration
const connectionPoolConfig = {
  max: 10,           // Maximum number of connections
  idle: 30000,       // Idle timeout (ms)
  connect_timeout: 10000, // Connection timeout (ms)
}
```

## Query Caching

Use the query cache for frequently accessed data that doesn't change often:

```typescript
import { queryCache } from '@/lib/cache';

// Get data with caching
const users = await queryCache.getOrSet(
  'users:list',
  () => prisma.user.findMany({ where: { role: 'client' } }),
  60 * 1000 // Cache for 1 minute
);
```

## Optimized Queries

### Use Select to Limit Fields

Only request the fields you need:

```typescript
// Bad: Fetches all fields
const user = await prisma.user.findUnique({
  where: { id: userId }
});

// Good: Only fetches needed fields
const user = await prisma.user.findUnique({
  where: { id: userId },
  select: {
    id: true,
    name: true,
    email: true,
    role: true
  }
});
```

### Use Pagination

Always paginate large result sets:

```typescript
const users = await prisma.user.findMany({
  where: { role: 'client' },
  skip: (page - 1) * pageSize,
  take: pageSize,
  orderBy: { createdAt: 'desc' }
});
```

### Optimize Includes

Only include related data when necessary:

```typescript
// Bad: Includes everything
const workout = await prisma.workout.findUnique({
  where: { id: workoutId },
  include: {
    exercises: true,
    trainingPlan: true,
    client: true,
    trainer: true
  }
});

// Good: Only includes what's needed with nested selection
const workout = await prisma.workout.findUnique({
  where: { id: workoutId },
  include: {
    exercises: {
      orderBy: { order: 'asc' },
      select: { id: true, name: true, sets: true, reps: true }
    },
    client: {
      select: { id: true, name: true }
    }
  }
});
```

## Database Indexes

We've added indexes to frequently queried fields in the schema. When adding new queries, consider whether you need to add indexes for the fields you're filtering or sorting by.

Key indexes added:
- User: role, status, name, createdAt
- Conversation: user1Id, user2Id, lastMessageAt
- Message: conversationId, createdAt, read

## Query Batching

Use `findMany` with `where: { id: { in: ids } }` instead of multiple `findUnique` calls:

```typescript
// Bad: Multiple separate queries
const user1 = await prisma.user.findUnique({ where: { id: id1 } });
const user2 = await prisma.user.findUnique({ where: { id: id2 } });
const user3 = await prisma.user.findUnique({ where: { id: id3 } });

// Good: Single batched query
const users = await prisma.user.findMany({
  where: {
    id: {
      in: [id1, id2, id3]
    }
  }
});
```

## Transactions

Use transactions for operations that modify multiple records:

```typescript
import { withTransaction } from '@/lib/transaction';

// Execute multiple operations in a transaction
const result = await withTransaction(async (tx) => {
  const user = await tx.user.create({ data: { /* ... */ } });
  const profile = await tx.profile.create({
    data: { userId: user.id, /* ... */ }
  });
  return { user, profile };
});
```

## N+1 Query Problem

Avoid the N+1 query problem by using proper includes:

```typescript
// Bad: N+1 problem
const workouts = await prisma.workout.findMany();
for (const workout of workouts) {
  // This causes N additional queries
  workout.exercises = await prisma.exercise.findMany({
    where: { workoutId: workout.id }
  });
}

// Good: Single query with includes
const workouts = await prisma.workout.findMany({
  include: {
    exercises: true
  }
});
```

## API Route Optimization

Use the optimized API handler for consistent performance:

```typescript
import { optimizedApiHandler } from '@/lib/api/optimized-api';

export const GET = optimizedApiHandler(
  async (req, { params }, session) => {
    // Your handler logic here
    return NextResponse.json({ data: 'example' });
  },
  { requireAuth: true, cacheResponse: true, cacheTTL: 60000 }
);
```

## Performance Testing

Regularly test the performance of your queries:

1. Use the Prisma Client's `$queryRaw` to execute `EXPLAIN ANALYZE` queries
2. Monitor query execution time in development with Prisma's logging
3. Use database monitoring tools to identify slow queries

## Additional Resources

- [Prisma Performance Best Practices](https://www.prisma.io/docs/guides/performance-and-optimization)
- [PostgreSQL Indexing Strategies](https://www.postgresql.org/docs/current/indexes-strategies.html)
- [Next.js API Routes Optimization](https://nextjs.org/docs/api-routes/response-helpers)
