import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function PUT(
  request: Request,
  context: any
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Safely access params
    const logId = context?.params?.id;
    if (!logId) {
        return new NextResponse("Nutrition log ID missing in URL", { status: 400 });
    }

    const body = await request.json()
    const { date, mealType, name, calories, protein, carbs, fat, notes } = body

    // Check if the nutrition log exists and belongs to the user
    const log = await prisma.nutritionLog.findUnique({
      where: {
        id: logId,
      },
      select: { clientId: true }
    })

    if (!log) {
      return new NextResponse("Nutrition log not found", { status: 404 })
    }

    // Check ownership
    if (log.clientId !== session.user.id) {
      return new NextResponse("Forbidden: You do not own this nutrition log", { status: 403 })
    }

    // Update the nutrition log
    const updatedLog = await prisma.nutritionLog.update({
      where: {
        id: logId,
      },
      data: {
        date: date ? new Date(date) : undefined,
        mealType: mealType || undefined,
        name: name || undefined,
        calories: calories !== undefined ? calories : undefined,
        protein: protein !== undefined ? protein : undefined,
        carbs: carbs !== undefined ? carbs : undefined,
        fat: fat !== undefined ? fat : undefined,
        notes: notes !== undefined ? notes : undefined,
      },
    })

    return NextResponse.json(updatedLog)
  } catch (error) {
    console.error("[NUTRITION_LOG_UPDATE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function DELETE(
  request: Request,
  context: any
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Safely access params
    const logId = context?.params?.id;
    if (!logId) {
        return new NextResponse("Nutrition log ID missing in URL", { status: 400 });
    }

    // Check if the nutrition log exists and belongs to the user
    const log = await prisma.nutritionLog.findUnique({
      where: {
        id: logId,
      },
      select: { clientId: true }
    })

    if (!log) {
      // Idempotent: Already deleted or never existed
      return new NextResponse(null, { status: 204 });
    }

    // Check ownership
    if (log.clientId !== session.user.id) {
      return new NextResponse("Forbidden: You do not own this nutrition log", { status: 403 })
    }

    // Delete the nutrition log
    await prisma.nutritionLog.delete({
      where: {
        id: logId,
      },
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[NUTRITION_LOG_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
