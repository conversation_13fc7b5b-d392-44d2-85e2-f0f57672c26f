"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Pencil, Plus, Trash2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface EditGoalsDialogProps {
  clientId: string
  initialGoals: string[]
  onGoalsUpdated: (goals: string[]) => void
  buttonProps?: React.ComponentProps<typeof Button>
}

export function EditGoalsDialog({
  clientId,
  initialGoals,
  onGoalsUpdated,
  buttonProps
}: EditGoalsDialogProps) {
  const [open, setOpen] = useState(false)
  const [goals, setGoals] = useState<string[]>(initialGoals || [])
  const [newGoal, setNewGoal] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleAddGoal = () => {
    if (newGoal.trim()) {
      setGoals([...goals, newGoal.trim()])
      setNewGoal("")
    }
  }

  const handleRemoveGoal = (index: number) => {
    setGoals(goals.filter((_, i) => i !== index))
  }

  const handleUpdateGoal = (index: number, value: string) => {
    const updatedGoals = [...goals]
    updatedGoals[index] = value
    setGoals(updatedGoals)
  }

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true)

      // Filter out any empty goals
      const filteredGoals = goals.filter(goal => goal.trim() !== "")

      // Make API call to update client goals
      const response = await fetch(`/api/users/${clientId}/goals`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ goals: filteredGoals }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update goals")
      }

      // Call the callback with updated goals
      onGoalsUpdated(filteredGoals)

      // Show success toast
      toast({
        title: "Goals Updated",
        description: "Client goals have been successfully updated.",
      })

      // Close the dialog
      setOpen(false)
    } catch (error) {
      console.error("Error updating goals:", error)
      toast({
        title: "Error",
        description: `Failed to update goals: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="mt-4" {...buttonProps}>
          <Pencil className="mr-2 h-3 w-3" />
          Edit Goals
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Client Goals</DialogTitle>
          <DialogDescription>
            Add, edit, or remove fitness goals for this client.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4 max-h-[60vh] overflow-y-auto">
          <div className="flex items-end gap-2">
            <div className="flex-1">
              <Label htmlFor="new-goal">Add New Goal</Label>
              <Input
                id="new-goal"
                value={newGoal}
                onChange={(e) => setNewGoal(e.target.value)}
                placeholder="Enter a new goal"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault()
                    handleAddGoal()
                  }
                }}
              />
            </div>
            <Button
              type="button"
              onClick={handleAddGoal}
              disabled={!newGoal.trim()}
              size="sm"
              className="mb-0.5"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add
            </Button>
          </div>

          <div className="space-y-2">
            <Label>Current Goals</Label>
            {goals.length === 0 ? (
              <p className="text-sm text-muted-foreground">No goals set. Add some goals above.</p>
            ) : (
              <div className="space-y-2">
                {goals.map((goal, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="rounded-full h-5 w-5 bg-primary/10 text-primary flex items-center justify-center shrink-0">
                      {index + 1}
                    </div>
                    <Input
                      value={goal}
                      onChange={(e) => handleUpdateGoal(index, e.target.value)}
                      className="flex-1"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveGoal(index)}
                      className="h-8 w-8 text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save Goals"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
