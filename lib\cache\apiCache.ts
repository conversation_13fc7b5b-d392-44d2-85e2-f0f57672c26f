/**
 * API Response Caching Utility
 *
 * This utility provides functions for caching API responses to improve performance.
 * It implements a simple LRU (Least Recently Used) cache with automatic expiration.
 */

// Cache entry interface
interface CacheEntry<T> {
  value: T;
  expiry: number;
  lastAccessed: number;
}

// Configuration for the cache
interface CacheConfig {
  maxSize: number;
  defaultTTL: number; // Time to live in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
}

/**
 * LRU Cache implementation with automatic expiration
 */
export class APICache<T = any> {
  private cache = new Map<string, CacheEntry<T>>();
  private config: CacheConfig;
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: config.maxSize || 100,
      defaultTTL: config.defaultTTL || 5 * 60 * 1000, // 5 minutes
      cleanupInterval: config.cleanupInterval || 60 * 1000, // 1 minute
    };

    this.startCleanupTimer();
  }

  /**
   * Get a value from the cache
   * @param key Cache key
   * @returns Cached value or undefined if not found or expired
   */
  get(key: string): T | undefined {
    const entry = this.cache.get(key);

    if (!entry) {
      return undefined;
    }

    // Check if entry has expired
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return undefined;
    }

    // Update last accessed time for LRU tracking
    entry.lastAccessed = Date.now();
    // Update the entry in the cache with the new lastAccessed time
    this.cache.set(key, entry);
    return entry.value;
  }

  /**
   * Set a value in the cache
   * @param key Cache key
   * @param value Value to cache
   * @param ttl Optional TTL in milliseconds (defaults to config value)
   */
  set(key: string, value: T, ttl?: number): void {
    // If cache is at max size, remove least recently used entry
    if (this.cache.size >= this.config.maxSize) {
      this.removeLeastRecentlyUsed();
    }

    const entryTTL = ttl || this.config.defaultTTL;

    this.cache.set(key, {
      value,
      expiry: Date.now() + entryTTL,
      lastAccessed: Date.now(),
    });
  }

  /**
   * Check if a key exists in the cache and is not expired
   * @param key Cache key
   * @returns true if the key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);

    if (!entry) {
      return false;
    }

    // Check if entry has expired
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Delete a key from the cache
   * @param key Cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all entries from the cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get the size of the cache
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Remove all expired entries from the cache
   */
  removeExpired(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Start the cleanup timer
   */
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.removeExpired();
    }, this.config.cleanupInterval);
  }

  /**
   * Remove the least recently used entry
   */
  private removeLeastRecentlyUsed(): void {
    let oldest: { key: string; time: number } | null = null;

    for (const [key, entry] of this.cache.entries()) {
      if (!oldest || entry.lastAccessed < oldest.time) {
        oldest = { key, time: entry.lastAccessed };
      }
    }

    if (oldest) {
      this.cache.delete(oldest.key);
    }
  }

  /**
   * Stop the cleanup timer
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }
}

// Create a singleton instance for global use
export const globalAPICache = new APICache();

/**
 * Decorator for caching async function results
 * @param cache Cache instance
 * @param keyPrefix Prefix for cache keys
 * @param ttl TTL in milliseconds
 */
export function withCache<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  cache: APICache = globalAPICache,
  keyPrefix: string = '',
  ttl?: number
): T {
  return (async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    // Generate cache key based on function args
    const key = `${keyPrefix}:${JSON.stringify(args)}`;

    // Try to get from cache first
    const cachedResult = cache.get(key);
    if (cachedResult !== undefined) {
      return cachedResult as ReturnType<T>;
    }

    // Call original function
    const result = await fn(...args);

    // Cache the result
    cache.set(key, result, ttl);

    return result;
  }) as T;
}

/**
 * Create a cache key from a URL and optional parameters
 */
export function createCacheKey(url: string, params?: Record<string, any>): string {
  if (!params) {
    return url;
  }

  // Sort keys for consistent order
  const sortedParams = Object.keys(params).sort().reduce(
    (obj, key) => {
      obj[key] = params[key];
      return obj;
    },
    {} as Record<string, any>
  );

  return `${url}:${JSON.stringify(sortedParams)}`;
}