import { NextResponse } from "next/server"
import { getAuthSession } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: Request,
  { params }: { params: { messageId: string } }
) {
  try {
    const session = await getAuthSession()
    
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }
    
    const userId = session.user.id
    const messageId = params.messageId
    
    if (!messageId) {
      return new NextResponse("Message ID is required", { status: 400 })
    }
    
    // Get the message
    const message = await prisma.message.findUnique({
      where: {
        id: messageId,
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
          },
        },
        receiver: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
          },
        },
        conversation: true,
      },
    })
    
    if (!message) {
      return new NextResponse("Message not found", { status: 404 })
    }
    
    // Check if the user is part of the conversation
    if (message.senderId !== userId && message.receiverId !== userId) {
      return new NextResponse("Unauthorized", { status: 403 })
    }
    
    return NextResponse.json(message)
  } catch (error) {
    console.error("[MESSAGE_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
