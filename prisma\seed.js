const { PrismaClient, Prisma } = require('@prisma/client')
const { hash } = require('bcryptjs')

const prisma = new PrismaClient()


const exercises = [
  // Upper Body
  {
    name: "Push-ups",
    description: "Classic bodyweight exercise targeting chest, shoulders, and triceps",
    sets: 3,
    reps: 12,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=IODxDxX7oi4",
    type: "strength",
    difficulty: "beginner",
    muscleGroup: "chest",
    isTemplate: true,
    equipment: "bodyweight"
  },
  {
    name: "Pull-ups",
    description: "Upper body pulling exercise targeting back and biceps",
    sets: 3,
    reps: 8,
    restTime: 90,
    videoUrl: "https://www.youtube.com/watch?v=eGo4IYlbE5g",
    type: "strength",
    difficulty: "intermediate",
    muscleGroup: "back",
    isTemplate: true,
    equipment: "pull-up bar"
  },
  {
    name: "Dumbbell Shoulder Press",
    description: "Overhead pressing exercise for shoulder development",
    sets: 3,
    reps: 10,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=qEwKCR5JCog",
    type: "strength",
    difficulty: "beginner",
    muscleGroup: "shoulders",
    isTemplate: true
  },
  {
    name: "Bicep Curls",
    description: "Isolation exercise for bicep development",
    sets: 3,
    reps: 12,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=ykJmrZ5v0Oo",
    type: "strength",
    difficulty: "beginner",
    muscleGroup: "arms",
    isTemplate: true
  },
  {
    name: "Tricep Dips",
    description: "Bodyweight exercise for tricep development",
    sets: 3,
    reps: 10,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=6kALZikXxLc",
    type: "strength",
    difficulty: "beginner",
    muscleGroup: "arms",
    isTemplate: true
  },

  // Lower Body
  {
    name: "Squats",
    description: "Fundamental lower body exercise targeting quads and glutes",
    sets: 4,
    reps: 12,
    restTime: 90,
    videoUrl: "https://www.youtube.com/watch?v=SW_C1A-rejs",
    type: "strength",
    difficulty: "beginner",
    muscleGroup: "legs",
    isTemplate: true
  },
  {
    name: "Deadlifts",
    description: "Compound exercise targeting posterior chain",
    sets: 3,
    reps: 8,
    restTime: 120,
    videoUrl: "https://www.youtube.com/watch?v=op9kVnSso6Q",
    type: "strength",
    difficulty: "intermediate",
    muscleGroup: "legs",
    isTemplate: true
  },
  {
    name: "Lunges",
    description: "Unilateral leg exercise for balance and strength",
    sets: 3,
    reps: 12,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=3XDriUn0udo",
    type: "strength",
    difficulty: "beginner",
    muscleGroup: "legs",
    isTemplate: true
  },
  {
    name: "Calf Raises",
    description: "Isolation exercise for calf development",
    sets: 3,
    reps: 15,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=gwLzYI0vKPo",
    type: "strength",
    difficulty: "beginner",
    muscleGroup: "legs",
    isTemplate: true
  },

  // Core
  {
    name: "Plank",
    description: "Core stability exercise",
    duration: 60,
    restTime: 30,
    videoUrl: "https://www.youtube.com/watch?v=pSHjTRCQxIw",
    type: "strength",
    difficulty: "beginner",
    muscleGroup: "core",
    isTemplate: true
  },
  {
    name: "Russian Twists",
    description: "Rotational core exercise",
    sets: 3,
    reps: 20,
    restTime: 45,
    videoUrl: "https://www.youtube.com/watch?v=wkD8rjkodUI",
    type: "strength",
    difficulty: "beginner",
    muscleGroup: "core",
    isTemplate: true
  },
  {
    name: "Leg Raises",
    description: "Lower abdominal exercise",
    sets: 3,
    reps: 12,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=l4kQd9eWclE",
    type: "strength",
    difficulty: "intermediate",
    muscleGroup: "core",
    isTemplate: true
  },

  // Cardio
  {
    name: "Running",
    description: "Cardio exercise for endurance",
    duration: 30,
    videoUrl: "https://www.youtube.com/watch?v=5OMTijvTy_s",
    type: "cardio",
    difficulty: "beginner",
    muscleGroup: "full body",
    isTemplate: true
  },
  {
    name: "Jump Rope",
    description: "High-intensity cardio exercise",
    duration: 10,
    restTime: 30,
    videoUrl: "https://www.youtube.com/watch?v=1BZM2Vre5oc",
    type: "cardio",
    difficulty: "beginner",
    muscleGroup: "full body",
    isTemplate: true
  },
  {
    name: "Burpees",
    description: "Full body conditioning exercise",
    sets: 3,
    reps: 10,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=TU8QYVW0gDU",
    type: "cardio",
    difficulty: "intermediate",
    muscleGroup: "full body",
    isTemplate: true
  },

  // Additional Strength Exercises
  {
    name: "Bench Press",
    description: "Classic chest exercise",
    sets: 4,
    reps: 8,
    restTime: 90,
    videoUrl: "https://www.youtube.com/watch?v=rT7DgCr-3pg",
    type: "strength",
    difficulty: "intermediate",
    muscleGroup: "chest",
    isTemplate: true
  },
  {
    name: "Barbell Rows",
    description: "Compound back exercise",
    sets: 3,
    reps: 10,
    restTime: 90,
    videoUrl: "https://www.youtube.com/watch?v=FWJR5Ve8bnQ",
    type: "strength",
    difficulty: "intermediate",
    muscleGroup: "back",
    isTemplate: true
  },
  {
    name: "Lateral Raises",
    description: "Shoulder isolation exercise",
    sets: 3,
    reps: 12,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=3VcKaXpzqRo",
    type: "strength",
    difficulty: "beginner",
    muscleGroup: "shoulders",
    isTemplate: true
  },
  {
    name: "Hammer Curls",
    description: "Bicep exercise with neutral grip",
    sets: 3,
    reps: 12,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=TwD-YGVP4Bk",
    type: "strength",
    difficulty: "beginner",
    muscleGroup: "arms",
    isTemplate: true
  },
  {
    name: "Skull Crushers",
    description: "Tricep isolation exercise",
    sets: 3,
    reps: 12,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=d_KZxkY_0cM",
    type: "strength",
    difficulty: "intermediate",
    muscleGroup: "arms",
    isTemplate: true
  },
  {
    name: "Romanian Deadlifts",
    description: "Hamstring and glute focused deadlift variation",
    sets: 3,
    reps: 10,
    restTime: 90,
    videoUrl: "https://www.youtube.com/watch?v=GYhlG5sQw8E",
    type: "strength",
    difficulty: "intermediate",
    muscleGroup: "legs",
    isTemplate: true
  },
  {
    name: "Bulgarian Split Squats",
    description: "Unilateral leg exercise with elevated rear foot",
    sets: 3,
    reps: 10,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=2cP1Yw6bqjQ",
    type: "strength",
    difficulty: "intermediate",
    muscleGroup: "legs",
    isTemplate: true
  },
  {
    name: "Hanging Leg Raises",
    description: "Advanced core exercise",
    sets: 3,
    reps: 10,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=l4kQd9eWclE",
    type: "strength",
    difficulty: "advanced",
    muscleGroup: "core",
    isTemplate: true
  },
  {
    name: "Mountain Climbers",
    description: "Dynamic core and cardio exercise",
    duration: 45,
    restTime: 30,
    videoUrl: "https://www.youtube.com/watch?v=nmwgirgXLYM",
    type: "cardio",
    difficulty: "intermediate",
    muscleGroup: "core",
    isTemplate: true
  }
]

async function main() {
  try {
    // Create development user
    console.log('Creating development user...')
    const hashedPassword = await hash('devpassword', 10)

    // Check if tables exist before attempting to create users
    try {
      // This will throw an error if the User table doesn't exist
      await prisma.$queryRaw`SELECT 1 FROM "User" LIMIT 1`;
      console.log('Database tables exist, proceeding with seed');
    } catch (error) {
      console.error('Tables may not exist yet. Run `npx prisma db push` first.');
      console.error('Error:', error.message);
      return;
    }

    // Create trainer user
    const trainerUser = await prisma.user.upsert({
      where: { id: 'dev-user-id' },
      update: {},
      create: {
        id: 'dev-user-id',
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Development Trainer',
        role: 'trainer',
      },
    })
    console.log('Successfully created development trainer')

    // Create trainer profile
    const trainerProfile = await prisma.trainerProfile.upsert({
      where: { userId: trainerUser.id },
      update: {},
      create: {
        userId: trainerUser.id,
      },
    })
    console.log('Successfully created trainer profile')

    // Create client users
    console.log('Creating client users...')
    const client1Password = await hash('client1password', 10)
    const client2Password = await hash('client2password', 10)
    const client3Password = await hash('client3password', 10)
    const client4Password = await hash('client4password', 10)
    const client5Password = await hash('client5password', 10)

    const client1 = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: client1Password,
        name: 'John Smith',
        role: 'client',
      },
    })

    const client2 = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: client2Password,
        name: 'Sarah Johnson',
        role: 'client',
      },
    })

    const client3 = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: client3Password,
        name: 'Mike Wilson',
        role: 'client',
      },
    })

    // Create future test client
    const client4 = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: client4Password,
        name: 'Future Test Client',
        role: 'client',
      },
    })

    // Create expired subscription test client
    const client5 = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: client5Password,
        name: 'Expired Test Client',
        role: 'client',
      },
    })

    // Create client profiles
    const client1Profile = await prisma.clientProfile.upsert({
      where: { userId: client1.id },
      update: {},
      create: {
        userId: client1.id,
        assignedTrainerId: trainerProfile.id,
      },
    })

    const client2Profile = await prisma.clientProfile.upsert({
      where: { userId: client2.id },
      update: {},
      create: {
        userId: client2.id,
        assignedTrainerId: trainerProfile.id,
      },
    })

    const client3Profile = await prisma.clientProfile.upsert({
      where: { userId: client3.id },
      update: {},
      create: {
        userId: client3.id,
        assignedTrainerId: trainerProfile.id,
      },
    })

    // Create profile for future test client
    const client4Profile = await prisma.clientProfile.upsert({
      where: { userId: client4.id },
      update: {},
      create: {
        userId: client4.id,
        assignedTrainerId: trainerProfile.id,
      },
    })

    // Create profile for expired test client
    const client5Profile = await prisma.clientProfile.upsert({
      where: { userId: client5.id },
      update: {},
      create: {
        userId: client5.id,
        assignedTrainerId: trainerProfile.id,
      },
    })
    
    console.log('Successfully created client profiles')

    // Create coaching relationships
    console.log('Creating coaching relationships...')
    
    // First, check if relationships exist
    const existingRelationship1 = await prisma.coachingRelationship.findFirst({
      where: {
        trainerId: trainerUser.id,
        clientId: client1.id,
        status: "active",
      },
    })

    if (!existingRelationship1) {
      // Calculate expiration date (3 months from start)
      const startDate = new Date()
      const expirationDate = new Date(startDate)
      expirationDate.setMonth(expirationDate.getMonth() + 3)

      await prisma.coachingRelationship.create({
        data: {
          trainerId: trainerUser.id,
          clientId: client1.id,
          status: "active",
          startDate: startDate,
          expirationDate: expirationDate,
          plan: "Premium Coaching",
          notes: "Focus on strength training and nutrition",
          monthlyFee: new Prisma.Decimal(20),
        }
      })
    }

    const existingRelationship2 = await prisma.coachingRelationship.findFirst({
      where: {
        trainerId: trainerUser.id,
        clientId: client2.id,
        status: "active",
      },
    })

    if (!existingRelationship2) {
      // Calculate expiration date (6 months from start)
      const startDate = new Date()
      const expirationDate = new Date(startDate)
      expirationDate.setMonth(expirationDate.getMonth() + 6)

      await prisma.coachingRelationship.create({
        data: {
          trainerId: trainerUser.id,
          clientId: client2.id,
          status: "active",
          startDate: startDate,
          expirationDate: expirationDate,
          plan: "Premium Coaching",
          notes: "Focus on weight loss and cardio",
          monthlyFee: new Prisma.Decimal(20),
        }
      })
    }

    // Create an inactive relationship from May for client3
    const mayStartDate = new Date(2025, 5, 1) // June 1st, 2025 (month is 0-based)
    const mayEndDate = new Date(2025, 5, 31) // June 31st, 2025
    const mayExpirationDate = new Date(2025, 8, 1) // September 1st, 2025 (3 months from start)

    const existingRelationship3 = await prisma.coachingRelationship.findFirst({
      where: {
        trainerId: trainerUser.id,
        clientId: client3.id,
      },
    })

    if (!existingRelationship3) {
      await prisma.coachingRelationship.create({
        data: {
          trainerId: trainerUser.id,
          clientId: client3.id,
          status: "inactive",
          startDate: mayStartDate,
          endDate: mayEndDate,
          expirationDate: mayExpirationDate,
          plan: "Premium Coaching",
          notes: "Focus on muscle gain and strength",
          monthlyFee: new Prisma.Decimal(20),
        }
      })
    }

    // Create future test client relationship
    const futureStartDate = new Date(2025, 3, 1) // April 1st, 2025 (month is 0-based)
    const futureExpirationDate = new Date(2025, 11, 1) // December 1st, 2025 (8 months later)

    const existingRelationship4 = await prisma.coachingRelationship.findFirst({
      where: {
        trainerId: trainerUser.id,
        clientId: client4.id,
      },
    })

    if (!existingRelationship4) {
      await prisma.coachingRelationship.create({
        data: {
          trainerId: trainerUser.id,
          clientId: client4.id,
          status: "active",
          startDate: futureStartDate,
          expirationDate: futureExpirationDate,
          plan: "Premium Coaching",
          notes: "Future test client with 8-month subscription",
          monthlyFee: new Prisma.Decimal(99.99),
        }
      })
    }

    // Create expired test client relationship
    const expiredStartDate = new Date(2025, 4, 1) // May 1st, 2025 (month is 0-based)
    const expiredExpirationDate = new Date(2025, 5, 1) // June 1st, 2025 (1 month later)

    const existingRelationship5 = await prisma.coachingRelationship.findFirst({
      where: {
        trainerId: trainerUser.id,
        clientId: client5.id,
      },
    })

    if (!existingRelationship5) {
      await prisma.coachingRelationship.create({
        data: {
          trainerId: trainerUser.id,
          clientId: client5.id,
          status: "inactive",
          startDate: expiredStartDate,
          endDate: expiredExpirationDate,
          expirationDate: expiredExpirationDate,
          plan: "Premium Coaching",
          notes: "Expired test client with 1-month subscription",
          monthlyFee: new Prisma.Decimal(49.99),
        }
      })
    }

    console.log('Successfully created coaching relationships')

    // Create training plan template
    console.log('Creating training plan template...')
    const trainingPlanTemplate = await prisma.trainingPlanTemplate.upsert({
      where: { id: 'dev-plan-id' },
      update: {},
      create: {
        id: 'dev-plan-id',
        title: 'Premium Strength & Conditioning',
        description: 'A comprehensive 4-week program designed to build strength and improve conditioning',
        difficulty: 'intermediate',
        type: 'template',
        trainerId: trainerUser.id,
        featured: true,
        weeks: {
          week1: {
            title: 'Week 1: Foundation',
            workouts: [
              {
                id: 'workout-1',
                title: 'Upper Body Strength',
                description: 'Focus on chest, shoulders, and triceps',
                duration: 45,
                exercises: [
                  { id: 'ex1', name: 'Bench Press', sets: 4, reps: '10', weight: '135 lbs', rest: 90 },
                  { id: 'ex2', name: 'Shoulder Press', sets: 3, reps: '12', weight: '95 lbs', rest: 60 },
                  { id: 'ex3', name: 'Tricep Pushdowns', sets: 3, reps: '15', weight: '50 lbs', rest: 45 },
                ],
              },
            ],
          },
        },
      },
    })
    console.log('Successfully created training plan template')

    // Create template exercises
    console.log('Creating template exercises...')
    for (const exercise of exercises) {
      const existingExercise = await prisma.exercise.findFirst({
        where: {
          name: exercise.name,
          isTemplate: true,
        },
      })

      if (!existingExercise) {
        await prisma.exercise.create({
          data: exercise,
        })
        console.log(`Created exercise: ${exercise.name}`)
      } else {
        console.log(`Exercise ${exercise.name} already exists, skipping`)
      }
    }

    console.log('Successfully completed seeding process')
  } catch (error) {
    console.error('Error seeding database:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })