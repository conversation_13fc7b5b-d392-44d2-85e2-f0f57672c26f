"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { SketchPicker } from "react-color"

interface ThemeSettings {
  primaryColor: string
  secondaryColor: string
  logoUrl: string | null
  bannerUrl: string | null
  fontFamily: string
}

interface TrainerCustomizationOptionsProps {
  theme: ThemeSettings
  onThemeChange: (theme: ThemeSettings) => void
  onSave: () => void
  isSaving: boolean
}

export function TrainerCustomizationOptions({ 
  theme, 
  onThemeChange, 
  onSave,
  isSaving
}: TrainerCustomizationOptionsProps) {
  const { toast } = useToast()
  const [colorPickerOpen, setColorPickerOpen] = useState<string | null>(null)

  const handleColorChange = (color: { hex: string }, type: 'primary' | 'secondary') => {
    onThemeChange({
      ...theme,
      [type === 'primary' ? 'primaryColor' : 'secondaryColor']: color.hex
    })
  }

  const handleImageUrlChange = (type: 'logo' | 'banner', url: string) => {
    onThemeChange({
      ...theme,
      [type === 'logo' ? 'logoUrl' : 'bannerUrl']: url || null
    })
  }

  const handleFontChange = (font: string) => {
    onThemeChange({
      ...theme,
      fontFamily: font
    })
  }

  return (
    <div className="grid gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Theme Colors</CardTitle>
          <CardDescription>
            Choose colors that represent your brand
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="primary-color">Primary Color</Label>
            <div className="flex gap-2">
              <div 
                className="h-10 w-10 rounded-md cursor-pointer border"
                style={{ backgroundColor: theme.primaryColor }}
                onClick={() => setColorPickerOpen(colorPickerOpen === 'primary' ? null : 'primary')}
              />
              <Input 
                id="primary-color"
                value={theme.primaryColor}
                onChange={(e) => handleColorChange({ hex: e.target.value }, 'primary')}
              />
            </div>
            {colorPickerOpen === 'primary' && (
              <div className="absolute z-10 mt-2">
                <SketchPicker 
                  color={theme.primaryColor}
                  onChange={(color) => handleColorChange(color, 'primary')}
                />
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="secondary-color">Secondary Color</Label>
            <div className="flex gap-2">
              <div 
                className="h-10 w-10 rounded-md cursor-pointer border"
                style={{ backgroundColor: theme.secondaryColor }}
                onClick={() => setColorPickerOpen(colorPickerOpen === 'secondary' ? null : 'secondary')}
              />
              <Input 
                id="secondary-color"
                value={theme.secondaryColor}
                onChange={(e) => handleColorChange({ hex: e.target.value }, 'secondary')}
              />
            </div>
            {colorPickerOpen === 'secondary' && (
              <div className="absolute z-10 mt-2">
                <SketchPicker 
                  color={theme.secondaryColor}
                  onChange={(color) => handleColorChange(color, 'secondary')}
                />
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={onSave} disabled={isSaving}>
            {isSaving ? "Saving..." : "Save Colors"}
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Images & Fonts</CardTitle>
          <CardDescription>
            Add your logo and banner images
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="logo-url">Logo URL</Label>
            <Input 
              id="logo-url"
              placeholder="https://example.com/logo.png"
              value={theme.logoUrl || ""}
              onChange={(e) => handleImageUrlChange('logo', e.target.value)}
            />
            <p className="text-sm text-muted-foreground">
              Square image recommended (400x400px)
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="banner-url">Banner URL</Label>
            <Input 
              id="banner-url"
              placeholder="https://example.com/banner.jpg"
              value={theme.bannerUrl || ""}
              onChange={(e) => handleImageUrlChange('banner', e.target.value)}
            />
            <p className="text-sm text-muted-foreground">
              Widescreen image recommended (1920x500px)
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="font-family">Font Family</Label>
            <select
              id="font-family"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={theme.fontFamily}
              onChange={(e) => handleFontChange(e.target.value)}
            >
              <option value="Inter, sans-serif">Inter (Default)</option>
              <option value="'Poppins', sans-serif">Poppins</option>
              <option value="'Montserrat', sans-serif">Montserrat</option>
              <option value="'Roboto', sans-serif">Roboto</option>
              <option value="'Open Sans', sans-serif">Open Sans</option>
              <option value="'Playfair Display', serif">Playfair Display</option>
            </select>
            <p className="text-sm text-muted-foreground">
              Choose a font that matches your brand style
            </p>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={onSave} disabled={isSaving}>
            {isSaving ? "Saving..." : "Save Images & Font"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
