import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"

interface TransactionHistoryProps {
  transactions: any[]
}

export function TransactionHistory({ transactions }: TransactionHistoryProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Transaction History</CardTitle>
        <CardDescription>Your recent transactions</CardDescription>
      </CardHeader>
      <CardContent>
        {transactions.length > 0 ? (
          <div className="space-y-4">
            {transactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0"
              >
                <div>
                  <p className="font-medium">{transaction.order_items?.[0]?.products?.title || "Product purchase"}</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(transaction.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div className="font-medium">+${transaction.total_amount.toFixed(2)}</div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-muted-foreground py-6">No transactions yet.</div>
        )}
      </CardContent>
    </Card>
  )
}

