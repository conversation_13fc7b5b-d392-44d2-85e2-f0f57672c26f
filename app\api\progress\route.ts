import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Check if the request is multipart/form-data
    const contentType = request.headers.get('content-type') || ''
    let weight, bodyFat, measurements, date, notes, sleepHours, stressLevel, coffeeCount
    let photoUrls: { front?: string, side?: string, back?: string } = {}

    if (contentType.includes('multipart/form-data')) {
      const formData = await request.formData()
      const jsonData = formData.get('data')

      if (jsonData) {
        const parsedData = JSON.parse(jsonData.toString())
        weight = parsedData.weight
        bodyFat = parsedData.bodyFat
        measurements = parsedData.measurements
        date = parsedData.date
        notes = parsedData.notes
      }

      // Handle photo uploads
      const frontPhoto = formData.get('frontPhoto') as File | null
      const sidePhoto = formData.get('sidePhoto') as File | null
      const backPhoto = formData.get('backPhoto') as File | null

      // In a real implementation, you would upload these to a storage service
      // and store the URLs in the database
      if (frontPhoto) {
        // Example: Upload to storage service and get URL
        // const frontPhotoUrl = await uploadToStorage(frontPhoto)
        // photoUrls.front = frontPhotoUrl
        photoUrls.front = URL.createObjectURL(frontPhoto) // This is just for demo purposes
      }

      if (sidePhoto) {
        // const sidePhotoUrl = await uploadToStorage(sidePhoto)
        // photoUrls.side = sidePhotoUrl
        photoUrls.side = URL.createObjectURL(sidePhoto) // This is just for demo purposes
      }

      if (backPhoto) {
        // const backPhotoUrl = await uploadToStorage(backPhoto)
        // photoUrls.back = backPhotoUrl
        photoUrls.back = URL.createObjectURL(backPhoto) // This is just for demo purposes
      }
    } else {
      // Handle regular JSON request
      const body = await request.json()
      weight = body.weight
      bodyFat = body.bodyFat
      measurements = body.measurements
      date = body.date
      notes = body.notes
      sleepHours = body.sleepHours
      stressLevel = body.stressLevel
      coffeeCount = body.coffeeCount
    }

    if (!weight) {
      return new NextResponse("Weight is required", { status: 400 })
    }

    const progress = await prisma.measurement.create({
      data: {
        userId: session.user.id,
        date: date ? new Date(date) : new Date(),
        weight: parseFloat(weight),
        bodyFat: bodyFat ? parseFloat(bodyFat) : null,
        waist: measurements?.waist ? parseFloat(measurements.waist) : null,
        chest: measurements?.chest ? parseFloat(measurements.chest) : null,
        arms: measurements?.arms ? parseFloat(measurements.arms) : null,
        thighs: measurements?.thighs ? parseFloat(measurements.thighs) : null,
        // Health metrics
        sleepHours: sleepHours ? parseFloat(sleepHours) : null,
        stressLevel: stressLevel ? parseInt(stressLevel) : null,
        coffeeCount: coffeeCount ? parseInt(coffeeCount) : null,
        notes: notes || null,
        frontPhotoUrl: photoUrls.front || null,
        sidePhotoUrl: photoUrls.side || null,
        backPhotoUrl: photoUrls.back || null,
      },
    })

    // Update streaks
    try {
      // Update measurement streak
      await fetch('/api/streaks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'measurement' }),
      })

      // Update specific metric streaks if they were logged
      if (sleepHours) {
        await fetch('/api/streaks', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'sleep' }),
        })
      }

      if (stressLevel !== undefined) {
        await fetch('/api/streaks', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'stress' }),
        })
      }

      if (coffeeCount !== undefined) {
        await fetch('/api/streaks', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'coffee' }),
        })
      }
    } catch (streakError) {
      console.error("[STREAK_UPDATE_ERROR]", streakError)
      // Don't fail the main request if streak update fails
    }

    return NextResponse.json(progress)
  } catch (error) {
    console.error("[PROGRESS_CREATE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // Build the where clause
    const where: any = {
      userId: session.user.id,
    }

    // Add date filtering if provided
    if (startDate || endDate) {
      where.date = {}

      if (startDate) {
        where.date.gte = new Date(startDate)
      }

      if (endDate) {
        where.date.lte = new Date(endDate)
      }
    }

    const progress = await prisma.measurement.findMany({
      where,
      orderBy: {
        date: "desc",
      },
    })

    return NextResponse.json(progress)
  } catch (error) {
    console.error("[PROGRESS_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}