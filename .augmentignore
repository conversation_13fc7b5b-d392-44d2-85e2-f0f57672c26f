node_modules/*
*.png


.git
.gitignore
.cursorignore
.cursorrules
.cursorconfig
.cursorrules
.cursorconfig


# Dependencies
node_modules/
.next/
.swc/
.cursor/

# Build outputs
.vercel/
out/
build/
dist/

# Coverage reports
coverage/

# Large generated files
package-lock.json
*.lock
yarn.lock
pnpm-lock.yaml

# Environment files
.env
.env.*
!.env.example

# Large data files
*.csv
*.json.gz
*.sql
*.dump

# Cache
.cache/
.eslintcache
.stylelintcache
.parcel-cache/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# System Files
.DS_Store
Thumbs.db

# Database
*.sqlite
*.db