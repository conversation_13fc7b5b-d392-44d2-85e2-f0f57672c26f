'use client';

import { useState, useRef, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, <PERSON>Footer, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Send, ArrowLeft, RefreshCw } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

// Mock data
const MOCK_TRAINER = {
  id: 'trainer-1',
  name: '<PERSON>',
  avatarUrl: null,
};

const MOCK_CLIENT = {
  id: 'client-1',
  name: '<PERSON> Client',
  avatarUrl: null,
};

// Initial messages if no saved messages exist
const DEFAULT_MESSAGES = [
  {
    id: 'msg-1',
    content: 'Hello! Welcome to your coaching chat. How can I help you today?',
    senderId: MOCK_TRAINER.id,
    createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
  },
];

// Message type
interface Message {
  id: string;
  content: string;
  senderId: string;
  createdAt: string;
}

export default function StandaloneChatPage() {
  const router = useRouter();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Load messages from localStorage on component mount
  useEffect(() => {
    const savedMessages = localStorage.getItem('standalone-chat-messages');
    if (savedMessages) {
      setMessages(JSON.parse(savedMessages));
    } else {
      setMessages(DEFAULT_MESSAGES);
    }
  }, []);

  // Save messages to localStorage whenever they change
  useEffect(() => {
    if (messages.length > 0) {
      localStorage.setItem('standalone-chat-messages', JSON.stringify(messages));
    }
  }, [messages]);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newMessage.trim()) return;
    
    // Add client message
    const clientMessage = {
      id: `msg-${Date.now()}-client`,
      content: newMessage,
      senderId: MOCK_CLIENT.id,
      createdAt: new Date().toISOString(),
    };
    
    setMessages((prev) => [...prev, clientMessage]);
    setNewMessage('');
    
    // Simulate trainer response after a delay
    setIsLoading(true);
    setTimeout(() => {
      const trainerResponse = {
        id: `msg-${Date.now()}-trainer`,
        content: getTrainerResponse(newMessage),
        senderId: MOCK_TRAINER.id,
        createdAt: new Date().toISOString(),
      };
      
      setMessages((prev) => [...prev, trainerResponse]);
      setIsLoading(false);
    }, 1000);
  };

  const getTrainerResponse = (message: string): string => {
    // Simple response logic
    if (message.toLowerCase().includes('hello') || message.toLowerCase().includes('hi')) {
      return 'Hi there! How are you doing today?';
    }
    
    if (message.toLowerCase().includes('workout') || message.toLowerCase().includes('exercise')) {
      return 'Great question about your workout! I recommend focusing on proper form and gradually increasing intensity. How has your progress been so far?';
    }
    
    if (message.toLowerCase().includes('diet') || message.toLowerCase().includes('nutrition') || message.toLowerCase().includes('food')) {
      return 'Nutrition is key to your success! Remember to focus on whole foods, adequate protein, and staying hydrated. Have you been tracking your meals?';
    }
    
    if (message.toLowerCase().includes('progress') || message.toLowerCase().includes('results')) {
      return 'Your progress has been impressive! Remember that consistency is key, and results take time. Keep up the great work!';
    }
    
    if (message.toLowerCase().includes('schedule') || message.toLowerCase().includes('appointment') || message.toLowerCase().includes('meeting')) {
      return 'I have availability next week on Tuesday and Thursday afternoons. Would either of those work for you? We can set up a 30-minute session to discuss your goals in detail.';
    }
    
    if (message.toLowerCase().includes('goal') || message.toLowerCase().includes('target')) {
      return 'Your goals are important to me. Let\'s break them down into smaller, achievable milestones. What specific aspect would you like to focus on first?';
    }
    
    // Default response
    return "That's a great point! Let's discuss this further in our next session. Is there anything specific you'd like to focus on?";
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const clearChat = () => {
    if (confirm('Are you sure you want to clear the chat history? This cannot be undone.')) {
      localStorage.removeItem('standalone-chat-messages');
      setMessages(DEFAULT_MESSAGES);
    }
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={clearChat}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Clear Chat
          </Button>
          
          <Link href="/mock-chat">
            <Button variant="outline">
              Switch to Mock Chat
            </Button>
          </Link>
        </div>
      </div>
      
      <h1 className="text-2xl font-bold">Standalone Coaching Chat</h1>
      <p className="text-muted-foreground">
        This is a standalone chat interface that works without a database connection. Messages are saved in your browser's localStorage.
      </p>
      
      <Card className="flex flex-col h-[600px] border-primary/20">
        <CardHeader className="border-b pb-3">
          <div className="flex items-center gap-3">
            <Avatar>
              <AvatarImage src={MOCK_TRAINER.avatarUrl || undefined} alt={MOCK_TRAINER.name} />
              <AvatarFallback>{getInitials(MOCK_TRAINER.name)}</AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">{MOCK_TRAINER.name}</CardTitle>
              <p className="text-sm text-muted-foreground">1:1 Coaching Chat (Standalone)</p>
            </div>
          </div>
        </CardHeader>
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {messages.map((message) => {
              const isTrainer = message.senderId === MOCK_TRAINER.id;
              const messageUser = isTrainer ? MOCK_TRAINER : MOCK_CLIENT;
              
              return (
                <div key={message.id} className={`flex ${isTrainer ? 'justify-start' : 'justify-end'}`}>
                  <div className="flex items-start gap-2 max-w-[80%]">
                    {isTrainer && (
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={messageUser.avatarUrl || undefined} alt={messageUser.name} />
                        <AvatarFallback>{getInitials(messageUser.name)}</AvatarFallback>
                      </Avatar>
                    )}
                    <div>
                      <div className={`p-3 rounded-lg ${isTrainer ? 'bg-muted' : 'bg-primary text-primary-foreground'}`}>
                        <p className="text-sm">{message.content}</p>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">{formatTime(message.createdAt)}</p>
                    </div>
                    {!isTrainer && (
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={messageUser.avatarUrl || undefined} alt={messageUser.name} />
                        <AvatarFallback>{getInitials(messageUser.name)}</AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                </div>
              );
            })}
            {isLoading && (
              <div className="flex justify-start">
                <div className="flex items-start gap-2 max-w-[80%]">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={MOCK_TRAINER.avatarUrl || undefined} alt={MOCK_TRAINER.name} />
                    <AvatarFallback>{getInitials(MOCK_TRAINER.name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="p-3 rounded-lg bg-muted">
                      <div className="flex items-center space-x-2">
                        <div className="h-2 w-2 bg-primary/50 rounded-full animate-bounce"></div>
                        <div className="h-2 w-2 bg-primary/50 rounded-full animate-bounce delay-75"></div>
                        <div className="h-2 w-2 bg-primary/50 rounded-full animate-bounce delay-150"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
        <CardFooter className="border-t p-3">
          <form onSubmit={handleSendMessage} className="flex w-full gap-2">
            <Input
              ref={inputRef}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type your message..."
              className="flex-1"
              disabled={isLoading}
            />
            <Button type="submit" disabled={!newMessage.trim() || isLoading}>
              <Send className="h-4 w-4" />
            </Button>
          </form>
        </CardFooter>
      </Card>
      
      <div className="bg-muted p-4 rounded-lg">
        <h2 className="font-medium mb-2">Test Prompts</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          <Button 
            variant="outline" 
            onClick={() => {
              setNewMessage("Hi, how are you today?");
              inputRef.current?.focus();
            }}
            disabled={isLoading}
          >
            Greeting
          </Button>
          <Button 
            variant="outline" 
            onClick={() => {
              setNewMessage("I need help with my workout routine");
              inputRef.current?.focus();
            }}
            disabled={isLoading}
          >
            Workout Help
          </Button>
          <Button 
            variant="outline" 
            onClick={() => {
              setNewMessage("What should I eat before my workout?");
              inputRef.current?.focus();
            }}
            disabled={isLoading}
          >
            Nutrition Question
          </Button>
          <Button 
            variant="outline" 
            onClick={() => {
              setNewMessage("I'm not seeing much progress yet");
              inputRef.current?.focus();
            }}
            disabled={isLoading}
          >
            Progress Concern
          </Button>
          <Button 
            variant="outline" 
            onClick={() => {
              setNewMessage("Can we schedule a 1:1 session next week?");
              inputRef.current?.focus();
            }}
            disabled={isLoading}
          >
            Schedule Request
          </Button>
          <Button 
            variant="outline" 
            onClick={() => {
              setNewMessage("I want to set a new goal for this month");
              inputRef.current?.focus();
            }}
            disabled={isLoading}
          >
            Goal Setting
          </Button>
        </div>
      </div>
    </div>
  );
}
