"use client"

import { Plus, Utensils, Coffee, Pizza, Apple, Beef, Trash2, MoreVertical } from "lucide-react"
import { useState, useEffect } from "react"
import { format, subDays, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { QuickMealForm } from "@/components/tracking/quick-meal-form"
import { useToast } from "@/components/ui/use-toast"

interface NutritionLog {
  id: string
  date: string
  mealType: string
  name: string
  calories?: number
  protein?: number
  carbs?: number
  fat?: number
  notes?: string
}

interface NutritionSummary {
  calories: {
    consumed: number
    goal: number
  }
  protein: {
    consumed: number
    goal: number
  }
  carbs: {
    consumed: number
    goal: number
  }
  fat: {
    consumed: number
    goal: number
  }
}

export function NutritionTracker() {
  const [activeTab, setActiveTab] = useState("today")
  const [nutritionLogs, setNutritionLogs] = useState<NutritionLog[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [logToDelete, setLogToDelete] = useState<string | null>(null)
  const [nutritionSummary, setNutritionSummary] = useState<NutritionSummary>({
    calories: { consumed: 0, goal: 2200 },
    protein: { consumed: 0, goal: 150 },
    carbs: { consumed: 0, goal: 220 },
    fat: { consumed: 0, goal: 70 },
  })
  const { toast } = useToast()

  const fetchNutritionLogs = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/nutrition-logs')

      if (!response.ok) {
        throw new Error('Failed to fetch nutrition logs')
      }

      const data = await response.json()
      setNutritionLogs(data)
      calculateNutritionSummary(data)
    } catch (error) {
      console.error('Error fetching nutrition logs:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load nutrition logs",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchNutritionLogs()
  }, [])

  const calculateNutritionSummary = (logs: NutritionLog[]) => {
    let filteredLogs: NutritionLog[] = []
    const now = new Date()

    // Filter logs based on active tab
    if (activeTab === "today") {
      const today = startOfDay(now)
      const todayEnd = endOfDay(now)
      filteredLogs = logs.filter(log => {
        const logDate = new Date(log.date)
        return logDate >= today && logDate <= todayEnd
      })
    } else if (activeTab === "week") {
      const weekStart = startOfWeek(now, { weekStartsOn: 1 }) // Start on Monday
      const weekEnd = endOfWeek(now, { weekStartsOn: 1 })
      filteredLogs = logs.filter(log => {
        const logDate = new Date(log.date)
        return logDate >= weekStart && logDate <= weekEnd
      })
    } else if (activeTab === "month") {
      const monthStart = startOfMonth(now)
      const monthEnd = endOfMonth(now)
      filteredLogs = logs.filter(log => {
        const logDate = new Date(log.date)
        return logDate >= monthStart && logDate <= monthEnd
      })
    }

    // Calculate totals
    const summary = {
      calories: { consumed: 0, goal: 2200 },
      protein: { consumed: 0, goal: 150 },
      carbs: { consumed: 0, goal: 220 },
      fat: { consumed: 0, goal: 70 },
    }

    filteredLogs.forEach(log => {
      if (log.calories) summary.calories.consumed += log.calories
      if (log.protein) summary.protein.consumed += log.protein
      if (log.carbs) summary.carbs.consumed += log.carbs
      if (log.fat) summary.fat.consumed += log.fat
    })

    setNutritionSummary(summary)
  }

  useEffect(() => {
    if (nutritionLogs.length > 0) {
      calculateNutritionSummary(nutritionLogs)
    }
  }, [activeTab, nutritionLogs])

  const handleLogSuccess = () => {
    setDialogOpen(false)
    fetchNutritionLogs()
    toast({
      title: "Success",
      description: "Meal logged successfully",
    })
  }

  const handleDeleteLog = async () => {
    if (!logToDelete) return

    try {
      const response = await fetch(`/api/nutrition-logs/${logToDelete}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete log')
      }

      // Remove the deleted log from state
      setNutritionLogs(prev => prev.filter(log => log.id !== logToDelete))

      toast({
        title: "Success",
        description: "Meal log deleted successfully",
      })
    } catch (error) {
      console.error('Error deleting log:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete meal log",
      })
    } finally {
      setLogToDelete(null)
      setDeleteDialogOpen(false)
    }
  }

  const confirmDelete = (id: string) => {
    setLogToDelete(id)
    setDeleteDialogOpen(true)
  }

  const getFilteredLogs = () => {
    const now = new Date()

    if (activeTab === "today") {
      const today = startOfDay(now)
      const todayEnd = endOfDay(now)
      return nutritionLogs.filter(log => {
        const logDate = new Date(log.date)
        return logDate >= today && logDate <= todayEnd
      })
    } else if (activeTab === "week") {
      const weekStart = startOfWeek(now, { weekStartsOn: 1 })
      const weekEnd = endOfWeek(now, { weekStartsOn: 1 })
      return nutritionLogs.filter(log => {
        const logDate = new Date(log.date)
        return logDate >= weekStart && logDate <= weekEnd
      })
    } else if (activeTab === "month") {
      const monthStart = startOfMonth(now)
      const monthEnd = endOfMonth(now)
      return nutritionLogs.filter(log => {
        const logDate = new Date(log.date)
        return logDate >= monthStart && logDate <= monthEnd
      })
    }

    return []
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Nutrition Tracker</h3>

        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" className="flex items-center gap-1">
              <Plus className="h-4 w-4" />
              <span>Log Nutrition</span>
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Log Nutrition</DialogTitle>
              <DialogDescription>
                Track your nutrition by logging meals or daily totals.
              </DialogDescription>
            </DialogHeader>
            <QuickMealForm onSuccess={handleLogSuccess} />
          </DialogContent>
        </Dialog>
      </div>

      <Card className="border-2 border-muted">
        <CardHeader className="pb-2">
          <Tabs defaultValue="today" className="w-full" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="today">Today</TabsTrigger>
              <TabsTrigger value="week">This Week</TabsTrigger>
              <TabsTrigger value="month">This Month</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-primary"></div>
                  <span className="font-medium">Calories</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="font-medium">{nutritionSummary.calories.consumed}</span>
                  <span className="text-sm text-muted-foreground">/ {nutritionSummary.calories.goal} kcal</span>
                </div>
              </div>
              <Progress
                value={(nutritionSummary.calories.consumed / nutritionSummary.calories.goal) * 100}
                className="h-2.5 rounded-full"
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <div className="flex justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                    <span className="font-medium">Protein</span>
                  </div>
                  <span className="text-sm">{Math.round((nutritionSummary.protein.consumed / nutritionSummary.protein.goal) * 100)}%</span>
                </div>
                <div className="flex justify-between items-center gap-2">
                  <Progress
                    value={(nutritionSummary.protein.consumed / nutritionSummary.protein.goal) * 100}
                    className="h-2.5 rounded-full bg-blue-100"
                    indicatorClassName="bg-blue-500"
                  />
                  <span className="text-xs whitespace-nowrap">
                    {nutritionSummary.protein.consumed}g
                  </span>
                </div>
              </div>

              <div>
                <div className="flex justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                    <span className="font-medium">Carbs</span>
                  </div>
                  <span className="text-sm">{Math.round((nutritionSummary.carbs.consumed / nutritionSummary.carbs.goal) * 100)}%</span>
                </div>
                <div className="flex justify-between items-center gap-2">
                  <Progress
                    value={(nutritionSummary.carbs.consumed / nutritionSummary.carbs.goal) * 100}
                    className="h-2.5 rounded-full bg-amber-100"
                    indicatorClassName="bg-amber-500"
                  />
                  <span className="text-xs whitespace-nowrap">
                    {nutritionSummary.carbs.consumed}g
                  </span>
                </div>
              </div>

              <div>
                <div className="flex justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-rose-500"></div>
                    <span className="font-medium">Fat</span>
                  </div>
                  <span className="text-sm">{Math.round((nutritionSummary.fat.consumed / nutritionSummary.fat.goal) * 100)}%</span>
                </div>
                <div className="flex justify-between items-center gap-2">
                  <Progress
                    value={(nutritionSummary.fat.consumed / nutritionSummary.fat.goal) * 100}
                    className="h-2.5 rounded-full bg-rose-100"
                    indicatorClassName="bg-rose-500"
                  />
                  <span className="text-xs whitespace-nowrap">
                    {nutritionSummary.fat.consumed}g
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Meals</CardTitle>
          <CardDescription>Your logged meals for {activeTab}</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : getFilteredLogs().length > 0 ? (
            <div className="space-y-4">
              {getFilteredLogs().map((log) => {
                // Determine icon based on meal type
                let MealIcon = Utensils;
                if (log.mealType.includes('Breakfast')) MealIcon = Coffee;
                if (log.mealType.includes('Lunch')) MealIcon = Pizza;
                if (log.mealType.includes('Snack')) MealIcon = Apple;
                if (log.mealType.includes('Workout')) MealIcon = Beef;

                return (
                  <div key={log.id} className="border rounded-md p-4 hover:border-primary transition-colors">
                    <div className="flex justify-between items-start">
                      <div className="flex items-start gap-3">
                        <div className="bg-muted rounded-full p-2 mt-1">
                          <MealIcon className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <h4 className="font-medium">{log.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {log.mealType} - {format(new Date(log.date), 'PPP')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="text-right">
                          {log.calories && (
                            <p className="text-sm font-medium">
                              {log.calories} <span className="text-xs text-muted-foreground">kcal</span>
                            </p>
                          )}
                          <div className="flex gap-2 mt-1">
                            {log.protein && (
                              <Badge variant="outline" className="text-xs font-normal">
                                P: {log.protein}g
                              </Badge>
                            )}
                            {log.carbs && (
                              <Badge variant="outline" className="text-xs font-normal">
                                C: {log.carbs}g
                              </Badge>
                            )}
                            {log.fat && (
                              <Badge variant="outline" className="text-xs font-normal">
                                F: {log.fat}g
                              </Badge>
                            )}
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreVertical className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive"
                              onClick={() => confirmDelete(log.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    {log.notes && <p className="text-sm mt-3 text-muted-foreground">{log.notes}</p>}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <Utensils className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-20" />
              <p className="text-muted-foreground">No meals logged for {activeTab}.</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => setDialogOpen(true)}
              >
                <Plus className="h-4 w-4 mr-2" /> Log your first meal
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the meal log from your account.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteLog} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
