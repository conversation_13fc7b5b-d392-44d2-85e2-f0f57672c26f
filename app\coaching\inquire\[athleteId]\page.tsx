import { Metadata } from "next"
import { CoachingInquiryForm } from "@/components/coaching/inquiry-form"

// Remove explicit interface if causing issues
// interface CoachingInquiryPageProps {
//   params: {
//     athleteId: string
//   }
// }

export const metadata: Metadata = {
  title: "Apply for 1:1 Coaching",
  description: "Submit your application for personalized 1:1 coaching.",
}

// Use 'any' as a targeted workaround for props typing issue
export default function CoachingInquiryPage(props: any) {
  // Safely access params
  const params = props.params ?? {};
  const athleteId = params.athleteId;

  // Add a check if athleteId is missing
  if (!athleteId) {
      // Handle missing ID, maybe redirect or show error
      console.error("Athlete ID is missing from params");
      // For now, returning a simple message, consider redirecting
      return <div>Error: Athlete ID not provided in URL.</div>;
  }

  return (
    <div className="container max-w-2xl py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Apply for 1:1 Coaching</h1>
        <p className="text-muted-foreground">
          Please fill out the form below to apply for our premium 1:1 coaching program.
          Our team will review your application and contact you to discuss the next steps.
        </p>
      </div>

      <div className="space-y-6">
        <div className="bg-muted p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">What to Expect</h2>
          <ul className="space-y-2">
            <li>• Personal coaching sessions tailored to your goals</li>
            <li>• Custom training and nutrition plans</li>
            <li>• Regular progress tracking and adjustments</li>
            <li>• Direct access to your coach via chat</li>
            <li>• Weekly check-ins and accountability</li>
          </ul>
        </div>

        <div className="border rounded-lg p-6">
          {/* Pass the extracted athleteId */}
          <CoachingInquiryForm athleteId={athleteId} />
        </div>
      </div>
    </div>
  )
} 