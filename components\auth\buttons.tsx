"use client"

import { useRouter } from "next/navigation"
import React from "react"
import { toast } from "sonner"

interface SignOutButtonProps {
  children: React.ReactNode
}

export function SignOutButton({ children }: SignOutButtonProps) {
  const router = useRouter()
  
  const handleSignOut = () => {
    // In a real app, this would call the API to sign out
    // Here we just simulate it
    
    toast.success("Successfully signed out")
    
    // Redirect to login page
    setTimeout(() => {
      router.push("/login")
    }, 1000)
  }
  
  return (
    <div onClick={handleSignOut}>
      {children}
    </div>
  )
} 