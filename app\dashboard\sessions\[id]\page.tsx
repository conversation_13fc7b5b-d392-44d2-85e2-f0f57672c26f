'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, Calendar, Clock, Video, MapPin, FileText, User } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ChatButton } from '@/components/coaching/chat-button';

interface CoachingSession {
  id: string;
  title: string;
  description: string | null;
  scheduledDate: string;
  duration: number;
  status: string;
  type: string;
  location: string | null;
  notes: string | null;
  videoConferenceUrl: string | null;
  coachingRelationship: {
    id: string;
    trainer: {
      id: string;
      name: string;
      email: string;
      avatarUrl: string | null;
    };
    client: {
      id: string;
      name: string;
      email: string;
      avatarUrl: string | null;
    };
  };
}

export default function SessionDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { data: session } = useSession();
  const { toast } = useToast();
  const [coachingSession, setCoachingSession] = useState<CoachingSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSessionDetails = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/coaching/sessions?action=session&sessionId=${params.id}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch session details');
        }
        
        const data = await response.json();
        setCoachingSession(data.session);
      } catch (error) {
        console.error('Error fetching session details:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load session details. Please try again later.',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchSessionDetails();
    }
  }, [params.id, toast]);

  const handleJoinSession = () => {
    if (coachingSession?.videoConferenceUrl) {
      window.open(coachingSession.videoConferenceUrl, '_blank');
    } else {
      toast({
        title: 'No video link available',
        description: 'The video conference link for this session is not available yet.',
      });
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'outline';
      case 'completed':
        return 'secondary';
      case 'cancelled':
        return 'destructive';
      case 'rescheduled':
        return 'default';
      default:
        return 'outline';
    }
  };

  if (!session?.user) {
    return (
      <div className="container py-6">
        <Card>
          <CardHeader>
            <CardTitle>Not Authenticated</CardTitle>
            <CardDescription>Please sign in to view session details.</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-6 space-y-6">
      <Button variant="outline" onClick={() => router.push('/dashboard/sessions')}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Sessions
      </Button>

      {isLoading ? (
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div>
                  <Skeleton className="h-5 w-32 mb-1" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </div>
              <Separator />
              <div className="grid gap-4 md:grid-cols-2">
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>
      ) : !coachingSession ? (
        <Card>
          <CardHeader>
            <CardTitle>Session Not Found</CardTitle>
            <CardDescription>
              The coaching session you're looking for doesn't exist or you don't have access to it.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => router.push('/dashboard/sessions')}>
              View All Sessions
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-3">
          <Card className="md:col-span-2 premium-card group">
            <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
            <CardHeader className="relative z-10 border-b border-primary/10 pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl premium-gradient-text">{coachingSession.title}</CardTitle>
                  <CardDescription>
                    {coachingSession.description || 'No description provided'}
                  </CardDescription>
                </div>
                <Badge variant={getStatusBadgeVariant(coachingSession.status)}>
                  {coachingSession.status.charAt(0).toUpperCase() + coachingSession.status.slice(1)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="relative z-10 pt-6">
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage 
                      src={
                        session.user.role === 'client'
                          ? coachingSession.coachingRelationship.trainer.avatarUrl || undefined
                          : coachingSession.coachingRelationship.client.avatarUrl || undefined
                      } 
                      alt={
                        session.user.role === 'client'
                          ? coachingSession.coachingRelationship.trainer.name
                          : coachingSession.coachingRelationship.client.name
                      } 
                    />
                    <AvatarFallback>
                      {getInitials(
                        session.user.role === 'client'
                          ? coachingSession.coachingRelationship.trainer.name
                          : coachingSession.coachingRelationship.client.name
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">
                      {session.user.role === 'client'
                        ? coachingSession.coachingRelationship.trainer.name
                        : coachingSession.coachingRelationship.client.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {session.user.role === 'client' ? 'Your Coach' : 'Your Client'}
                    </p>
                  </div>
                </div>

                <Separator className="bg-primary/10" />

                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <Calendar className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">Date</p>
                        <p className="text-sm text-muted-foreground">
                          {format(new Date(coachingSession.scheduledDate), 'EEEE, MMMM d, yyyy')}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <Clock className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">Time</p>
                        <p className="text-sm text-muted-foreground">
                          {format(new Date(coachingSession.scheduledDate), 'h:mm a')} ({coachingSession.duration} minutes)
                        </p>
                      </div>
                    </div>

                    {coachingSession.type && (
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                          {coachingSession.type === 'video' ? (
                            <Video className="h-5 w-5 text-primary" />
                          ) : (
                            <MapPin className="h-5 w-5 text-primary" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">Session Type</p>
                          <p className="text-sm text-muted-foreground">
                            {coachingSession.type.charAt(0).toUpperCase() + coachingSession.type.slice(1)}
                            {coachingSession.location && ` - ${coachingSession.location}`}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {coachingSession.notes && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <FileText className="h-5 w-5 text-primary" />
                        <p className="font-medium">Session Notes</p>
                      </div>
                      <div className="p-4 bg-muted rounded-lg">
                        <p className="text-sm whitespace-pre-wrap">{coachingSession.notes}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="relative z-10 border-t border-primary/10 pt-4 flex justify-between">
              {coachingSession.status === 'scheduled' && (
                <>
                  {coachingSession.type === 'video' && coachingSession.videoConferenceUrl && (
                    <Button onClick={handleJoinSession}>
                      <Video className="mr-2 h-4 w-4" />
                      Join Video Call
                    </Button>
                  )}
                  <Button variant="outline" onClick={() => router.push(`/dashboard/sessions/schedule/${coachingSession.coachingRelationship.trainer.id}`)}>
                    <Calendar className="mr-2 h-4 w-4" />
                    Reschedule
                  </Button>
                </>
              )}
              {coachingSession.status === 'cancelled' && (
                <p className="text-sm text-muted-foreground">This session has been cancelled.</p>
              )}
            </CardFooter>
          </Card>

          <div className="space-y-6">
            <Card className="premium-card">
              <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
              <CardHeader className="relative z-10 border-b border-primary/10 pb-3">
                <CardTitle className="flex items-center gap-2">
                  <User className="h-4 w-4 text-primary" />
                  {session.user.role === 'client' ? 'Your Coach' : 'Your Client'}
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10 pt-4">
                <div className="flex flex-col items-center text-center gap-3">
                  <Avatar className="h-20 w-20">
                    <AvatarImage 
                      src={
                        session.user.role === 'client'
                          ? coachingSession.coachingRelationship.trainer.avatarUrl || undefined
                          : coachingSession.coachingRelationship.client.avatarUrl || undefined
                      } 
                      alt={
                        session.user.role === 'client'
                          ? coachingSession.coachingRelationship.trainer.name
                          : coachingSession.coachingRelationship.client.name
                      } 
                    />
                    <AvatarFallback className="text-lg">
                      {getInitials(
                        session.user.role === 'client'
                          ? coachingSession.coachingRelationship.trainer.name
                          : coachingSession.coachingRelationship.client.name
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-lg">
                      {session.user.role === 'client'
                        ? coachingSession.coachingRelationship.trainer.name
                        : coachingSession.coachingRelationship.client.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {session.user.role === 'client' ? 'Professional Fitness Coach' : 'Coaching Client'}
                    </p>
                  </div>
                  <ChatButton
                    trainerId={coachingSession.coachingRelationship.trainer.id}
                    clientId={coachingSession.coachingRelationship.client.id}
                    className="w-full mt-2"
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="premium-card">
              <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
              <CardHeader className="relative z-10 border-b border-primary/10 pb-3">
                <CardTitle>What to Expect</CardTitle>
              </CardHeader>
              <CardContent className="relative z-10 pt-4">
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5">
                      <span className="text-xs text-primary">1</span>
                    </div>
                    <span>Discuss your fitness goals and progress</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5">
                      <span className="text-xs text-primary">2</span>
                    </div>
                    <span>Get personalized advice and feedback</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5">
                      <span className="text-xs text-primary">3</span>
                    </div>
                    <span>Ask questions about your training plan</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5">
                      <span className="text-xs text-primary">4</span>
                    </div>
                    <span>Address any challenges you're facing</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center mt-0.5">
                      <span className="text-xs text-primary">5</span>
                    </div>
                    <span>Adjust your program as needed</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}
