import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const url = new URL(req.url)
    const conversationId = url.searchParams.get("conversationId")
    const lastMessageTime = url.searchParams.get("lastMessageTime")

    if (!conversationId) {
      return NextResponse.json({ error: "Conversation ID is required" }, { status: 400 })
    }

    // Find the conversation to verify access
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        user1: true,
        user2: true,
      },
    })

    if (!conversation) {
      return NextResponse.json({ error: "Conversation not found" }, { status: 404 })
    }

    // Check if user is part of the conversation
    const isUser1 = conversation.user1Id === session.user.id
    const isUser2 = conversation.user2Id === session.user.id

    // If user is not part of the conversation, they are not authorized
    if (!isUser1 && !isUser2) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    // Check if there are new messages since lastMessageTime
    const query: any = {
      where: {
        conversationId,
      },
    }

    // If lastMessageTime is provided, only check for messages after that time
    if (lastMessageTime) {
      query.where.createdAt = {
        gt: new Date(lastMessageTime),
      }
    }

    // Count new messages
    const newMessagesCount = await prisma.message.count(query)

    return NextResponse.json({
      hasNewMessages: newMessagesCount > 0,
      count: newMessagesCount
    })
  } catch (error) {
    console.error("Error checking for new messages:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
