import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

interface Meal {
  name: string
  calories: number
  protein: number
  carbs: number
  fats: number
}

interface DietPlanFormData {
  title: string
  description: string
  meals: Meal[]
}

interface DietPlanFormProps {
  initialData?: DietPlanFormData
  onSubmit?: (data: DietPlanFormData) => void
}

export function DietPlanForm({ initialData, onSubmit }: DietPlanFormProps) {
  const [formData, setFormData] = useState<DietPlanFormData>(
    initialData || {
      title: '',
      description: '',
      meals: [
        {
          name: '',
          calories: 0,
          protein: 0,
          carbs: 0,
          fats: 0,
        },
      ],
    }
  )

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target
    if (name.startsWith('meals.')) {
      const [_, index, field] = name.split('.')
      const updatedMeals = [...formData.meals]
      updatedMeals[Number(index)] = {
        ...updatedMeals[Number(index)],
        [field]: field === 'name' ? value : Number(value),
      }
      setFormData({ ...formData, meals: updatedMeals })
    } else {
      setFormData({ ...formData, [name]: value })
    }
  }

  const handleAddMeal = () => {
    setFormData({
      ...formData,
      meals: [
        ...formData.meals,
        {
          name: '',
          calories: 0,
          protein: 0,
          carbs: 0,
          fats: 0,
        },
      ],
    })
  }

  const handleRemoveMeal = (index: number) => {
    const updatedMeals = formData.meals.filter((_, i) => i !== index)
    setFormData({ ...formData, meals: updatedMeals })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit?.(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="title">Title</Label>
        <Input
          id="title"
          name="title"
          value={formData.title}
          onChange={handleInputChange}
          placeholder="Enter plan title"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleInputChange}
          placeholder="Enter plan description"
        />
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>Meals</Label>
          <Button type="button" variant="outline" onClick={handleAddMeal}>
            Add Meal
          </Button>
        </div>

        {formData.meals.map((meal, index) => (
          <div key={index} className="space-y-4 p-4 border rounded-lg">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">Meal {index + 1}</h3>
              <Button
                type="button"
                variant="ghost"
                onClick={() => handleRemoveMeal(index)}
              >
                Remove
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor={`meal-${index}-name`}>Name</Label>
                <Input
                  id={`meal-${index}-name`}
                  name={`meals.${index}.name`}
                  value={meal.name}
                  onChange={handleInputChange}
                  placeholder="Enter meal name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor={`meal-${index}-calories`}>Calories</Label>
                <Input
                  id={`meal-${index}-calories`}
                  name={`meals.${index}.calories`}
                  type="number"
                  value={meal.calories}
                  onChange={handleInputChange}
                  placeholder="Enter calories"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor={`meal-${index}-protein`}>Protein (g)</Label>
                <Input
                  id={`meal-${index}-protein`}
                  name={`meals.${index}.protein`}
                  type="number"
                  value={meal.protein}
                  onChange={handleInputChange}
                  placeholder="Enter protein"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor={`meal-${index}-carbs`}>Carbs (g)</Label>
                <Input
                  id={`meal-${index}-carbs`}
                  name={`meals.${index}.carbs`}
                  type="number"
                  value={meal.carbs}
                  onChange={handleInputChange}
                  placeholder="Enter carbs"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor={`meal-${index}-fats`}>Fats (g)</Label>
                <Input
                  id={`meal-${index}-fats`}
                  name={`meals.${index}.fats`}
                  type="number"
                  value={meal.fats}
                  onChange={handleInputChange}
                  placeholder="Enter fats"
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      <Button type="submit">Save Plan</Button>
    </form>
  )
} 