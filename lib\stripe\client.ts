import Stripe from 'stripe';

// Check for Stripe secret key in environment variables
if (!process.env.STRIPE_SECRET_KEY) {
  console.warn('STRIPE_SECRET_KEY is not set in the environment variables. Using test mode.');
}

// Initialize Stripe with the secret key from env or use a test key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY || 'sk_test_51NxSample1234567890abcdefghijklmnopqrstuvwxyz';

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2023-10-16',
  typescript: true,
});

export default stripe;