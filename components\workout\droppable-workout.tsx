"use client"

import { useDroppable } from "@dnd-kit/core"
import { useState, useEffect } from "react"
import { Info } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { DraggableExercise } from "./draggable-exercise"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface Exercise {
  id: string
  name: string
  sets?: number | null
  reps?: number | null
  description?: string | null
  duration?: number | null
  restTime?: number | null
  videoUrl?: string | null
  muscleGroup?: string | null
  order: number
  type?: string | null
  thumbnailUrl?: string | null
  isTemplate?: boolean
  difficulty?: string | null
  equipment?: string | null
  calories?: number | null
  parentExerciseId?: string | null
  isAlternative?: boolean
}

interface Workout {
  id: string
  title: string
  type: string | null
  exercises: Exercise[]
}

interface DroppableWorkoutProps {
  workout: Workout
  onExerciseDelete: (exerciseId: string) => void
  onTypeChange: (type: string) => void
}

export function DroppableWorkout({
  workout,
  onExerciseDelete,
  onTypeChange,
}: DroppableWorkoutProps) {
  const [isOver, setIsOver] = useState(false);
  const { setNodeRef, isOver: isDndOver } = useDroppable({
    id: workout.id,
    data: {
      type: 'workout',
      workout
    }
  });

  useEffect(() => {
    setIsOver(isDndOver);
  }, [isDndOver]);

  const [expandedExercises, setExpandedExercises] = useState<string[]>([])

  const toggleExerciseExpanded = (exerciseId: string) => {
    setExpandedExercises(prev =>
      prev.includes(exerciseId)
        ? prev.filter(id => id !== exerciseId)
        : [...prev, exerciseId]
    )
  }

  // Group exercises by parent-child relationship
  const exerciseGroups = workout.exercises.reduce((groups, exercise) => {
    if (!exercise.parentExerciseId) {
      // This is a parent exercise or a standalone exercise
      if (!groups[exercise.id]) {
        groups[exercise.id] = {
          parent: exercise,
          alternatives: []
        }
      } else {
        groups[exercise.id].parent = exercise
      }
    } else {
      // This is an alternative exercise
      if (!groups[exercise.parentExerciseId]) {
        groups[exercise.parentExerciseId] = {
          parent: null,
          alternatives: [exercise]
        }
      } else {
        groups[exercise.parentExerciseId].alternatives.push(exercise)
      }
    }
    return groups
  }, {} as Record<string, { parent: Exercise | null, alternatives: Exercise[] }>)

  return (
    <Card
      ref={setNodeRef}
      className={`relative transition-all duration-200 ${
        isOver 
          ? 'ring-2 ring-primary ring-offset-2 bg-primary/5 scale-[1.02]' 
          : ''
      }`}
    >
      <CardHeader className="p-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{workout.title}</CardTitle>
          <Select
            value={workout.type || ""}
            onValueChange={onTypeChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select workout type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="strength">Strength</SelectItem>
              <SelectItem value="cardio">Cardio</SelectItem>
              <SelectItem value="hiit">HIIT</SelectItem>
              <SelectItem value="flexibility">Flexibility</SelectItem>
              <SelectItem value="recovery">Recovery</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        {workout.exercises.length === 0 && (
          <div className="text-center p-4 border border-dashed rounded-md bg-muted/20">
            <p className="text-muted-foreground">Drag exercises here to add to this workout</p>
          </div>
        )}

        <div className="mb-3 p-3 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-md text-xs text-blue-700 flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button className="inline-flex items-center justify-center rounded-full h-6 w-6 bg-blue-100 text-blue-700 hover:bg-blue-200">
                  <Info className="h-4 w-4" />
                </button>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>Alternative exercises give your clients options when following the workout plan. They can choose between the main exercise or any of its alternatives.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <div>
            <p className="font-semibold text-sm mb-1">Alternative Exercises</p>
            <p><strong>Pro Tip:</strong> Drag and drop an exercise <strong>directly on top of another exercise</strong> to add it as an alternative option</p>
          </div>
        </div>

        <div className={`mt-4 space-y-2 transition-all duration-200 ${
          isOver ? 'opacity-50' : ''
        }`}>
          {Object.values(exerciseGroups)
            .filter(group => group.parent)
            .sort((a, b) => (a.parent?.order || 0) - (b.parent?.order || 0))
            .map(group => (
              <div key={group.parent?.id} className="space-y-1">
                <DraggableExercise
                  exercise={group.parent!}
                  onDelete={() => onExerciseDelete(group.parent!.id)}
                  hasAlternatives={group.alternatives.length > 0}
                  isExpanded={expandedExercises.includes(group.parent!.id)}
                  onToggleExpand={() => toggleExerciseExpanded(group.parent!.id)}
                />

                {expandedExercises.includes(group.parent!.id) && group.alternatives.length > 0 && (
                  <div className="pl-6 space-y-1 border-l-2 border-primary/30 ml-4">
                    {group.alternatives.map(alt => (
                      <DraggableExercise
                        key={alt.id}
                        exercise={alt}
                        onDelete={() => onExerciseDelete(alt.id)}
                        isAlternative={true}
                      />
                    ))}
                  </div>
                )}
              </div>
            ))}
        </div>

        {isOver && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="bg-primary/10 px-4 py-2 rounded-full">
              <span className="text-sm font-medium text-primary">Drop to add exercise</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
