import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function DELETE(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await request.json()
    const { confirmation } = body

    if (confirmation !== "delete my account") {
      return new NextResponse("Invalid confirmation", { status: 400 })
    }

    // Delete all user data
    await prisma.user.delete({
      where: {
        id: session.user.id,
      },
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[ACCOUNT_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 