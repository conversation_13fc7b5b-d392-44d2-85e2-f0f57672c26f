"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Dumbbell, Plus } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"



interface TrainingPlan {
  id: string
  title: string
  description: string
  is_template: boolean
  duration_weeks: number
  difficulty: string
}

interface TrainingPlanGridProps {
  plans: TrainingPlan[]
}

export function TrainingPlanGrid({ plans }: TrainingPlanGridProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("all")
  const [plansState, setPlans] = useState(plans)
  const [isLoading, setIsLoading] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null)
  const [isAddingPlan, setIsAddingPlan] = useState(false)

  const filteredPlans =
    activeTab === "all"
      ? plansState
      : activeTab === "templates"
        ? plansState.filter((plan) => plan.is_template)
        : plansState.filter((plan) => !plan.is_template)

  const handleDuplicatePlan = async (id: string) => {
    try {
      const response = await fetch(`/api/training-plans/${id}/duplicate`, {
        method: "POST",
      })

      if (!response.ok) {
        throw new Error("Failed to duplicate plan")
      }

      toast({
        title: "Plan duplicated",
        description: "The training plan has been duplicated.",
      })
      router.refresh()
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to duplicate the plan.",
      })
    }
  }

  const handleDeletePlan = async (id: string) => {
    try {
      setDeleteLoading(id)
      const response = await fetch(`/api/training-plans/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error("Failed to delete plan")
      }

      toast({
        title: "Plan deleted",
        description: "The training plan has been deleted.",
      })
      router.refresh()
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete the plan.",
      })
    } finally {
      setDeleteLoading(null)
    }
  }

  const fetchPlans = async () => {
    try {
      // Simulate API call
      setIsLoading(true)
      const response = await fetch('/api/training-plans')
      if (!response.ok) throw new Error('Failed to fetch training plans')
      const data = await response.json()
      setPlans(data)
    } catch (_error) {
      // Don't show error to user, just log it
      console.error('Error fetching plans')
    } finally {
      setIsLoading(false)
    }
  }

  // Function to delete a training plan
  const deletePlan = async (planId: string) => {
    try {
      setDeleteLoading(planId)
      const response = await fetch(`/api/training-plans/${planId}`, {
        method: 'DELETE',
      })

      if (!response.ok) throw new Error('Failed to delete plan')

      // Remove plan from state
      setPlans(prev => prev.filter(plan => plan.id !== planId))

      toast({
        title: 'Plan deleted',
        description: 'Training plan has been deleted successfully.',
      })

    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to delete training plan.',
        variant: 'destructive',
      })
    } finally {
      setDeleteLoading(null)
    }
  }

  const handleAddPlan = async (plan: TrainingPlan) => {
    try {
      const response = await fetch('/api/training-plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(plan),
      })

      if (!response.ok) {
        throw new Error("Failed to add plan")
      }

      toast({
        title: "Plan added",
        description: "The training plan has been added successfully.",
      })
      router.refresh()
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to add the plan.",
      })
    }
  }

  return (
    <div className="space-y-4">
      <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3 max-w-md">
          <TabsTrigger value="all">All Plans</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="custom">Custom Plans</TabsTrigger>
        </TabsList>
      </Tabs>

      {filteredPlans.length > 0 ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredPlans.map((plan) => (
            <Card key={plan.id} className="group relative cursor-pointer transition-all hover:shadow-md" onClick={() => {
                console.log('Card clicked, redirecting to:', `/dashboard/training-plans/preview/${plan.id}`);
                router.push(`/dashboard/training-plans/preview/${plan.id}`);
              }}>
              <div className="absolute inset-0 z-10"></div>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{plan.title}</CardTitle>
                    <CardDescription>{plan.is_template ? "Template" : "Custom Plan"}</CardDescription>
                  </div>
                  <DropdownMenu>
                    <div onClick={(e) => e.stopPropagation()}>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/training-plans/preview/${plan.id}`}>Preview Plan</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/training-plans/preview/${plan.id}`}>View Details</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/training-plans/${plan.id}/edit`}>Edit Plan</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDuplicatePlan(plan.id)}>Duplicate Plan</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDeletePlan(plan.id)}>Delete Plan</DropdownMenuItem>
                    </DropdownMenuContent>
                    </div>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-3">{plan.description}</p>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{plan.duration_weeks} weeks</span>
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Dumbbell className="h-3 w-3" />
                    <span>{plan.difficulty}</span>
                  </Badge>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between relative z-20" onClick={(e) => e.stopPropagation()}>
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/dashboard/training-plans/preview/${plan.id}`}>Preview Plan</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href={`/dashboard/training-plans/${plan.id}/assign`}>
                    <Users className="mr-1 h-4 w-4" />
                    Assign to Client
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground">
              {plansState.length === 0 ? "You haven&apos;t created any training plans yet." : "No plans match your filter."}
            </p>
            <Button className="mt-4" asChild>
              <Link href="/dashboard/training-plans/create">
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Plan
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

