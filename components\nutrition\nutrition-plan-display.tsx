'use client';

import { useState } from 'react';
import { Edit, ChevronDown, ChevronUp, Clock, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

interface Meal {
  id: string;
  name: string;
  description?: string;
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  timeOfDay?: string;
  foodSuggestions?: string;
}

interface DietPlan {
  id: string;
  title: string;
  description?: string;
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  meals: Meal[];
}

interface NutritionPlanDisplayProps {
  plan: DietPlan;
  onEdit?: () => void;
}

export function NutritionPlanDisplay({ plan, onEdit }: NutritionPlanDisplayProps) {
  const [expandedMeals, setExpandedMeals] = useState<string[]>([]);

  const toggleMeal = (mealId: string) => {
    setExpandedMeals(prev => 
      prev.includes(mealId) 
        ? prev.filter(id => id !== mealId) 
        : [...prev, mealId]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  // Sort meals by timeOfDay if available
  const sortedMeals = [...plan.meals].sort((a, b) => {
    if (!a.timeOfDay && !b.timeOfDay) return 0;
    if (!a.timeOfDay) return 1;
    if (!b.timeOfDay) return -1;
    return a.timeOfDay.localeCompare(b.timeOfDay);
  });

  // Calculate total macros from meals
  const totalCalories = plan.meals.reduce((sum, meal) => sum + (meal.calories || 0), 0);
  const totalProtein = plan.meals.reduce((sum, meal) => sum + (meal.protein || 0), 0);
  const totalCarbs = plan.meals.reduce((sum, meal) => sum + (meal.carbs || 0), 0);
  const totalFat = plan.meals.reduce((sum, meal) => sum + (meal.fat || 0), 0);

  // Calculate percentages for progress bars
  const caloriesPercentage = plan.calories ? Math.min(100, (totalCalories / plan.calories) * 100) : 0;
  const proteinPercentage = plan.protein ? Math.min(100, (totalProtein / plan.protein) * 100) : 0;
  const carbsPercentage = plan.carbs ? Math.min(100, (totalCarbs / plan.carbs) * 100) : 0;
  const fatPercentage = plan.fat ? Math.min(100, (totalFat / plan.fat) * 100) : 0;

  return (
    <div className="space-y-6">
      <Card className="overflow-hidden border-muted/50">
        <CardHeader className="pb-3 flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-xl font-bold">{plan.title}</CardTitle>
            <CardDescription>
              {plan.description || 'Personalized nutrition plan'}
              <div className="text-xs mt-1">Created on {formatDate(plan.createdAt)}</div>
            </CardDescription>
          </div>
          {onEdit && (
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Plan
            </Button>
          )}
        </CardHeader>
        <CardContent className="p-6 space-y-6">
          {/* Daily Targets */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Daily Targets</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="bg-muted/30">
                <CardContent className="p-4 space-y-2">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Calories</h4>
                    <div className="text-sm">
                      <span className="font-medium">{totalCalories}</span>
                      <span className="text-muted-foreground"> / {plan.calories || '—'} kcal</span>
                    </div>
                  </div>
                  <Progress value={caloriesPercentage} className="h-2" />
                </CardContent>
              </Card>
              <Card className="bg-muted/30">
                <CardContent className="p-4 space-y-2">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Protein</h4>
                    <div className="text-sm">
                      <span className="font-medium">{totalProtein}</span>
                      <span className="text-muted-foreground"> / {plan.protein || '—'} g</span>
                    </div>
                  </div>
                  <Progress value={proteinPercentage} className="h-2" />
                </CardContent>
              </Card>
              <Card className="bg-muted/30">
                <CardContent className="p-4 space-y-2">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Carbs</h4>
                    <div className="text-sm">
                      <span className="font-medium">{totalCarbs}</span>
                      <span className="text-muted-foreground"> / {plan.carbs || '—'} g</span>
                    </div>
                  </div>
                  <Progress value={carbsPercentage} className="h-2" />
                </CardContent>
              </Card>
              <Card className="bg-muted/30">
                <CardContent className="p-4 space-y-2">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Fat</h4>
                    <div className="text-sm">
                      <span className="font-medium">{totalFat}</span>
                      <span className="text-muted-foreground"> / {plan.fat || '—'} g</span>
                    </div>
                  </div>
                  <Progress value={fatPercentage} className="h-2" />
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Meals */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Daily Meals</h3>
            <div className="space-y-4">
              {sortedMeals.map((meal) => (
                <Card key={meal.id} className="overflow-hidden">
                  <div 
                    className={cn(
                      "flex items-center justify-between p-4 cursor-pointer hover:bg-muted/50 transition-colors",
                      expandedMeals.includes(meal.id) ? "border-b" : ""
                    )}
                    onClick={() => toggleMeal(meal.id)}
                  >
                    <div className="flex items-center space-x-3">
                      {meal.timeOfDay && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="h-3.5 w-3.5 mr-1" />
                          {meal.timeOfDay}
                        </div>
                      )}
                      <div>
                        <h4 className="font-medium">{meal.name}</h4>
                        {meal.calories && (
                          <div className="text-sm text-muted-foreground">
                            {meal.calories} kcal | {meal.protein}g protein | {meal.carbs}g carbs | {meal.fat}g fat
                          </div>
                        )}
                      </div>
                    </div>
                    <div>
                      {expandedMeals.includes(meal.id) ? (
                        <ChevronUp className="h-5 w-5 text-muted-foreground" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-muted-foreground" />
                      )}
                    </div>
                  </div>
                  {expandedMeals.includes(meal.id) && (
                    <CardContent className="p-4 bg-muted/20">
                      {meal.description && (
                        <div className="mb-4">
                          <h5 className="text-sm font-medium mb-1">Description</h5>
                          <p className="text-sm text-muted-foreground">{meal.description}</p>
                        </div>
                      )}
                      {meal.foodSuggestions && (
                        <div>
                          <h5 className="text-sm font-medium mb-1">Food Suggestions</h5>
                          <p className="text-sm text-muted-foreground whitespace-pre-line">{meal.foodSuggestions}</p>
                        </div>
                      )}
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>
          </div>

          {/* Additional Notes */}
          {plan.notes && (
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Additional Notes</h3>
              <Card className="bg-muted/30">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-2">
                    <Info className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
                    <p className="text-sm whitespace-pre-line">{plan.notes}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
