"use client"

import { EnhancedTrainerLandingPage } from "@/components/trainer/enhanced-trainer-landing-page"

export default function DemoEnhancedTrainerPage() {
  // Mock trainer data
  const trainer = {
    id: "trainer-alex-fitness",
    name: "<PERSON>",
    email: "<EMAIL>",
    bio: "Elite fitness coach with 12+ years of experience helping clients transform their bodies and lives. Specializing in strength training, body recomposition, and nutrition optimization for sustainable results.",
    avatarUrl: "https://images.unsplash.com/photo-1517838277536-f5f99be501cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
    socialLinks: {
      instagram: "https://instagram.com/alexfitness",
      twitter: "https://twitter.com/alexfitness",
      youtube: "https://youtube.com/@alexfitness",
      facebook: "https://facebook.com/alexfitness",
      website: "https://alexfitness.com"
    }
  }
  
  // Enhanced theme settings
  const theme = {
    primaryColor: "#FF5733",
    secondaryColor: "#3498DB",
    logoUrl: "https://images.unsplash.com/photo-1517838277536-f5f99be501cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
    bannerUrl: "https://images.unsplash.com/photo-1517836357463-d25dfeac3438?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
    fontFamily: "'Poppins', sans-serif",
    
    // Enhanced header options
    videoBannerUrl: "https://player.vimeo.com/external/370331493.hd.mp4?s=ce49c8c6268e46525b22dfe77a4417ef2d387cb1&profile_id=175&oauth2_token_id=57447761",
    overlayOpacity: 0.5,
    textColor: "#FFFFFF",
    buttonStyle: "pill" as const,
    buttonText: "Explore My Programs",
    showCredentials: true,
    credentials: ["NASM Certified", "Nutrition Expert", "12+ Years Experience"],
    testimonialHighlight: {
      quote: "Alex's program completely transformed my body and mindset. I've lost 30 pounds and feel stronger than ever!",
      author: "Sarah M., Lost 30 lbs"
    },
    
    // Section styling
    sectionBackground: "#f8f9fa",
    sectionTextColor: "#333333",
    
    // Footer options
    footerStyle: "expanded" as const,
    footerBackground: "#212529",
    footerTextColor: "#ffffff",
    showNewsletter: true,
    showMap: true,
    mapLocation: {
      address: "123 Fitness St, New York, NY 10001",
      embedUrl: "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d193595.15830869428!2d-74.11976397304903!3d40.69766374874431!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c24fa5d33f083b%3A0xc80b8f06e177fe62!2sNew%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2sca!4v1636421224932!5m2!1sen!2sca"
    },
    contactInfo: {
      email: "<EMAIL>",
      phone: "+****************",
      address: "123 Fitness St, New York, NY 10001"
    },
    footerLinks: [
      { title: "Programs", url: "#programs" },
      { title: "About Me", url: "#about" },
      { title: "Testimonials", url: "#testimonials" },
      { title: "Contact", url: "#contact" },
      { title: "Privacy Policy", url: "#privacy" }
    ],
    copyrightText: `© ${new Date().getFullYear()} Alex Fitness. All rights reserved.`
  }
  
  // Mock subscription tiers
  const subscriptionTiers = [
    {
      id: "tier-1",
      name: "Basic Membership",
      description: "Perfect for beginners looking to start their fitness journey",
      price: 29.99,
      features: [
        "Weekly workout plans",
        "Basic nutrition guidance",
        "Access to exercise library",
        "Monthly check-ins"
      ]
    },
    {
      id: "tier-2",
      name: "Premium Membership",
      description: "Comprehensive fitness and nutrition coaching for serious results",
      price: 79.99,
      features: [
        "Personalized workout plans",
        "Detailed nutrition coaching",
        "Weekly check-ins and adjustments",
        "Form check videos",
        "Priority support",
        "Access to exclusive content"
      ]
    },
    {
      id: "tier-3",
      name: "Elite Membership",
      description: "The ultimate fitness experience with 1-on-1 coaching",
      price: 149.99,
      features: [
        "Everything in Premium",
        "Bi-weekly 1-on-1 video calls",
        "Custom meal plans",
        "24/7 direct messaging",
        "Advanced progress tracking",
        "Personalized supplement advice"
      ]
    }
  ]
  
  // Mock digital products
  const digitalProducts = [
    {
      id: "product-1",
      title: "6-Week Shred Program",
      description: "Intensive fat loss program designed to help you lose fat while preserving muscle mass. Includes workout plans, nutrition guide, and supplement recommendations.",
      price: 49.99,
      thumbnailUrl: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: "product-2",
      title: "Muscle Building Blueprint",
      description: "Comprehensive 12-week program focused on building lean muscle mass with progressive overload training and optimized nutrition.",
      price: 69.99,
      thumbnailUrl: "https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: "product-3",
      title: "Complete Home Workout Guide",
      description: "No gym? No problem! This program requires minimal equipment and can be done entirely at home while still delivering amazing results.",
      price: 39.99,
      thumbnailUrl: "https://images.unsplash.com/photo-1518611012118-696072aa579a?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: "product-4",
      title: "Nutrition Mastery",
      description: "Learn how to optimize your nutrition for your specific goals with this comprehensive guide to macros, meal timing, and food selection.",
      price: 29.99,
      thumbnailUrl: "https://images.unsplash.com/photo-1498837167922-ddd27525d352?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
    },
    {
      id: "product-5",
      title: "Strength Foundations",
      description: "Master the fundamental compound movements with this detailed program focused on proper form and progressive strength gains.",
      price: 59.99,
      thumbnailUrl: "https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
    }
  ]
  
  // Mock coaching services
  const coachingServices = [
    {
      id: "coaching-1",
      name: "1-on-1 Fitness Coaching",
      description: "Personalized coaching tailored to your specific goals, body type, and lifestyle.",
      price: 199.99,
      duration: "month",
      features: [
        "Custom workout programming",
        "Nutrition plan and adjustments",
        "Weekly video calls",
        "Daily messaging support",
        "Form check videos",
        "Progress tracking"
      ]
    },
    {
      id: "coaching-2",
      name: "Competition Prep",
      description: "Specialized coaching for bodybuilding, physique, or fitness competitions.",
      price: 299.99,
      duration: "month",
      features: [
        "Contest-specific training",
        "Peak week protocols",
        "Posing practice",
        "Detailed nutrition and supplement timing",
        "Bi-weekly video calls",
        "24/7 priority support"
      ]
    }
  ]
  
  // Enhanced content
  const featuredProducts = [
    digitalProducts[0],
    digitalProducts[1],
    digitalProducts[4]
  ]
  
  const transformations = [
    {
      id: "transform-1",
      title: "John's 12-Week Transformation",
      description: "John lost 45 pounds and gained significant muscle definition following my 12-week shred program.",
      beforeImage: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      afterImage: "https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      duration: "12-week"
    },
    {
      id: "transform-2",
      title: "Maria's Strength Journey",
      description: "Maria increased her squat by 85 pounds and deadlift by 110 pounds in just 6 months.",
      beforeImage: "https://images.unsplash.com/photo-1518611012118-696072aa579a?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      afterImage: "https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      duration: "6-month"
    }
  ]
  
  const achievements = [
    {
      icon: "award",
      title: "NASM Master Trainer",
      description: "Certified by the National Academy of Sports Medicine with advanced specializations in nutrition and corrective exercise."
    },
    {
      icon: "trending",
      title: "Featured in Men's Health",
      description: "Multiple training programs and nutrition articles published in leading fitness publications."
    },
    {
      icon: "dumbbell",
      title: "Competitive Powerlifter",
      description: "State champion with elite totals in squat, bench press, and deadlift."
    }
  ]
  
  const testimonials = [
    {
      id: "testimonial-1",
      name: "Michael Johnson",
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
      text: "Working with Alex completely changed my approach to fitness. I've gained 15 pounds of muscle and my strength has skyrocketed. The personalized programming and nutrition advice were exactly what I needed.",
      rating: 5,
      program: "Muscle Building Blueprint",
      verified: true
    },
    {
      id: "testimonial-2",
      name: "Jessica Williams",
      avatar: "https://randomuser.me/api/portraits/women/44.jpg",
      text: "After struggling with my weight for years, Alex's program finally helped me lose 30 pounds and keep it off. The nutrition guidance was practical and sustainable, not just another crash diet.",
      rating: 5,
      program: "6-Week Shred Program",
      verified: true
    },
    {
      id: "testimonial-3",
      name: "David Chen",
      avatar: "https://randomuser.me/api/portraits/men/67.jpg",
      text: "As someone with a busy schedule, I appreciated how Alex's home workout program fit into my life. The results were still amazing despite minimal equipment.",
      rating: 4,
      program: "Complete Home Workout Guide",
      verified: true
    }
  ]
  
  const statistics = {
    clients: 500,
    experience: 12,
    programs: 15,
    certifications: 8
  }
  
  const instagramPosts = [
    {
      id: "insta-1",
      imageUrl: "https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      caption: "Morning workout session with @client_transformation #fitness #gains",
      likes: 245,
      url: "https://instagram.com/p/example1"
    },
    {
      id: "insta-2",
      imageUrl: "https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      caption: "Sharing my favorite post-workout meal #nutrition #recovery",
      likes: 189,
      url: "https://instagram.com/p/example2"
    },
    {
      id: "insta-3",
      imageUrl: "https://images.unsplash.com/photo-1517836357463-d25dfeac3438?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      caption: "Form is everything! Here's how to perfect your squat #technique",
      likes: 312,
      url: "https://instagram.com/p/example3"
    },
    {
      id: "insta-4",
      imageUrl: "https://images.unsplash.com/photo-1498837167922-ddd27525d352?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      caption: "Meal prep Sunday! Setting up for a successful week #mealprep",
      likes: 278,
      url: "https://instagram.com/p/example4"
    }
  ]
  
  return (
    <EnhancedTrainerLandingPage
      trainer={trainer}
      theme={theme}
      subscriptionTiers={subscriptionTiers}
      digitalProducts={digitalProducts}
      coachingServices={coachingServices}
      featuredProducts={featuredProducts}
      transformations={transformations}
      achievements={achievements}
      testimonials={testimonials}
      statistics={statistics}
      instagramPosts={instagramPosts}
    />
  )
}
