// Test script for checkout API
const fetch = require('node-fetch');

async function testCheckoutAPI() {
  console.log('Testing checkout API...');
  
  try {
    // Sample cart items
    const items = [
      {
        id: '1',
        title: 'Test Product',
        price: 19.99,
        quantity: 1,
        thumbnailUrl: '/placeholder.svg',
        productType: 'guide'
      }
    ];
    
    // Call the checkout API
    const response = await fetch('http://localhost:3000/api/checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Request-Time': Date.now().toString(),
      },
      body: JSON.stringify({
        items: items,
        discount: 0,
      }),
      credentials: 'include',
    });
    
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Checkout error response:', errorData);
      console.error('Response headers:', Object.fromEntries([...response.headers.entries()]));
    } else {
      const responseData = await response.json();
      console.log('Checkout success response:', responseData);
    }
  } catch (error) {
    console.error('Error during test:', error);
  }
}

// Run the test
testCheckoutAPI();
