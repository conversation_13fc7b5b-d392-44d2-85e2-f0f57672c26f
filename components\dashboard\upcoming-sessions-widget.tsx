'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Calendar,
  Clock,
  Video,
  MapPin,
  User,
  ExternalLink,
  CalendarDays,
  ArrowRight,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface UpcomingSession {
  id: string;
  title: string;
  description: string;
  scheduledDate: string;
  duration: number;
  status: string;
  type: string;
  location: string | null;
  videoConferenceUrl: string | null;
  calendlyEventId: string | null;
  calendlyEventUri: string | null;
  client: {
    id: string;
    name: string;
    email: string;
    avatarUrl: string | null;
  };
  timeUntil: string;
  formattedDate: string;
  formattedTime: string;
}

interface UpcomingSessionsResponse {
  sessions: UpcomingSession[];
  total: number;
}

export function UpcomingSessionsWidget() {
  const [sessions, setSessions] = useState<UpcomingSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchUpcomingSessions();
  }, []);

  const fetchUpcomingSessions = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/trainer/upcoming-sessions');

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', errorText);
        throw new Error(`Failed to fetch upcoming sessions: ${response.status}`);
      }

      const data: UpcomingSessionsResponse = await response.json();
      setSessions(data.sessions);
    } catch (error) {
      console.error('Error fetching upcoming sessions:', error);
      setError('Failed to load upcoming sessions');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load upcoming sessions. Please try again later.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-green-100 text-green-800';
      case 'rescheduled':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="h-4 w-4" />;
      case 'in-person':
        return <MapPin className="h-4 w-4" />;
      default:
        return <Calendar className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CalendarDays className="mr-2 h-5 w-5" />
            Upcoming Sessions
          </CardTitle>
          <CardDescription>Your scheduled coaching sessions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CalendarDays className="mr-2 h-5 w-5" />
            Upcoming Sessions
          </CardTitle>
          <CardDescription>Your scheduled coaching sessions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <Button variant="outline" onClick={fetchUpcomingSessions}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <CalendarDays className="mr-2 h-5 w-5" />
              Upcoming Sessions
            </CardTitle>
            <CardDescription>Your scheduled coaching sessions</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchUpcomingSessions}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/trainer/sessions">
                View All
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {sessions.length === 0 ? (
          <div className="text-center py-6">
            <CalendarDays className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-sm text-muted-foreground mb-4">
              No upcoming sessions scheduled
            </p>
            <p className="text-xs text-muted-foreground">
              Sessions will appear here when clients book through your Calendly link
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {sessions.slice(0, 5).map((session) => (
              <div
                key={session.id}
                className="flex items-center space-x-4 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
              >
                <Avatar className="h-10 w-10">
                  <AvatarImage 
                    src={session.client.avatarUrl || `https://avatar.vercel.sh/${session.client.name}`} 
                    alt={session.client.name} 
                  />
                  <AvatarFallback>
                    {session.client.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <p className="text-sm font-medium truncate">
                      {session.title}
                    </p>
                    <Badge variant="secondary" className={`text-xs ${getStatusColor(session.status)}`}>
                      {session.status}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {session.client.name}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {session.formattedDate} at {session.formattedTime}
                    </span>
                    <span className="flex items-center gap-1">
                      {getTypeIcon(session.type)}
                      {session.duration}min
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {session.timeUntil}
                  </Badge>
                  {session.videoConferenceUrl && (
                    <Button variant="ghost" size="sm" asChild>
                      <a 
                        href={session.videoConferenceUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-1"
                      >
                        <Video className="h-4 w-4" />
                      </a>
                    </Button>
                  )}
                </div>
              </div>
            ))}
            
            {sessions.length > 5 && (
              <div className="text-center pt-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/dashboard/trainer/sessions">
                    View {sessions.length - 5} more sessions
                  </Link>
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
