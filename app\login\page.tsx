"use client"

import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { signIn } from "next-auth/react"
import React, { useState, useEffect, Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle } from "lucide-react"

function LoginForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [googleLoading, setGoogleLoading] = useState(false)

  // Check for registration success
  useEffect(() => {
    if (searchParams.get('registered') === 'true') {
      setSuccess('Account created successfully! Please check your email to verify your account before logging in.')
    }

    // Check for verification success
    if (searchParams.get('verified') === 'true') {
      setSuccess('Your email has been successfully verified! You can now log in to your Clear-Coach account.')
    }

    // Check for auth errors
    const errorParam = searchParams.get('error')
    if (errorParam) {
      switch(errorParam) {
        case 'OAuthAccountNotLinked':
          setError('The email is already used with a different sign-in method. Please use the original sign-in method.')
          break;
        default:
          setError('An error occurred during authentication. Please try again.')
      }
    }
  }, [searchParams])

  // Auto-login in development mode
  useEffect(() => {
    const isDevelopment = process.env.NODE_ENV === 'development';
    const autoLogin = async () => {
      if (isDevelopment) {
        console.log("[DEV] Auto-login activated");
        try {
          // Use <EMAIL> with any password for development auto-login
          const result = await signIn("credentials", {
            email: "<EMAIL>",
            password: "devpassword",
            redirect: false,
          });

          if (!result?.error) {
            console.log("[DEV] Auto-login successful");
            router.push("/dashboard");
            router.refresh();
          } else {
            console.error("[DEV] Auto-login failed:", result.error);
            // If auto-login fails, the user can still log in manually
          }
        } catch (error) {
          console.error("[DEV] Auto-login error:", error);
        }
      }
    };

    autoLogin();
  }, [router]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)
    setLoading(true)

    const formData = new FormData(e.currentTarget)
    const email = formData.get("email") as string
    const password = formData.get("password") as string

    try {
      console.log("Attempting to sign in with:", email)
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
        callbackUrl: "/dashboard"
      })

      console.log("Sign in result:", result)

      if (result?.error) {
        setError("Invalid email or password")
      } else {
        // Clear any existing dev role cookies before redirecting
        if (process.env.NODE_ENV === 'development') {
          // We don't need to clear cookies here as they'll be overridden by the session
        }

        // Redirect to dashboard
        router.push("/dashboard")
        router.refresh()
      }
    } catch (error) {
      console.error("Login error:", error)
      setError("An error occurred. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    try {
      setGoogleLoading(true)
      // Google provider configured to use the standard callback flow
      await signIn("google", { callbackUrl: "/dashboard" })
      // Note: No need to handle result here as Google sign-in redirects to Google's consent page
    } catch (error) {
      setError("An error occurred with Google Sign-In. Please try again.")
      setGoogleLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          {process.env.NODE_ENV === 'development' && (
            <p className="mt-2 text-center text-sm text-indigo-600">
              Development mode: Attempting auto-login...
            </p>
          )}
        </div>

        {/* Google Sign In Button */}
        <div>
          <Button
            type="button"
            onClick={handleGoogleSignIn}
            disabled={googleLoading}
            className="group relative w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <span className="flex items-center">
              {googleLoading ? (
                "Connecting to Google..."
              ) : (
                <>
                  <svg className="h-5 w-5 mr-2" viewBox="0 0 24 24">
                    <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                      <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z" />
                      <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z" />
                      <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z" />
                      <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z" />
                    </g>
                  </svg>
                  Sign in with Google
                </>
              )}
            </span>
          </Button>

          <p className="text-center text-xs text-gray-500 mt-2">
            Don't have an account? Use the signup link below or sign in with Google to access your account.
          </p>
        </div>

        <div className="flex items-center justify-center">
          <div className="border-t border-gray-300 flex-grow mr-3" />
          <span className="text-sm text-gray-500">or</span>
          <div className="border-t border-gray-300 flex-grow ml-3" />
        </div>

        <form className="mt-6 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email" className="sr-only">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                placeholder="Email address"
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                placeholder="Password"
              />
            </div>
          </div>

          {/* Forgot Password Link */}
          <div className="flex items-center justify-end">
            <div className="text-sm">
              <Link href="/forgot-password" className="font-medium text-indigo-600 hover:text-indigo-500">
                Forgot your password?
              </Link>
            </div>
          </div>

          {error && (
            <div className="text-red-500 text-sm text-center">{error}</div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md flex items-center justify-center mb-4">
              <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
              <p className="text-sm">{success}</p>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {loading ? "Signing in..." : "Sign in"}
            </button>
          </div>
        </form>

        <div className="text-center">
          <Link
            href="/register"
            className="font-medium text-indigo-600 hover:text-indigo-500"
          >
            Don&apos;t have an account? Sign up
          </Link>
        </div>

        {/* Development mode quick login link - always visible in dev mode */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-4 text-center">
            <Link
              href="/dev-login"
              className="px-4 py-2 text-sm font-medium bg-green-100 text-green-800 rounded-md hover:bg-green-200"
            >
              🛠️ Dev Login (No Credentials)
            </Link>
            <p className="text-xs text-gray-500 mt-1">For development purposes only</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading...</div>}>
      <LoginForm />
    </Suspense>
  )
}

