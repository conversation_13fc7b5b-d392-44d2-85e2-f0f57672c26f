'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import {
  <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface WeightData {
  id: string;
  date: Date;
  weight: number;
  bodyFat?: number | null;
}

export function WeightAnalytics() {
  const [weightData, setWeightData] = useState<WeightData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentWeight, setCurrentWeight] = useState<number | null>(null);
  const [startWeight, setStartWeight] = useState<number | null>(null);
  const [weightChange, setWeightChange] = useState<number>(0);
  const { toast } = useToast();

  // Fetch weight data
  useEffect(() => {
    const fetchWeightData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/progress');
        if (!response.ok) {
          throw new Error('Failed to fetch weight data');
        }

        const data = await response.json();

        // Sort by date
        const sortedData = [...data].sort((a, b) =>
          new Date(a.date).getTime() - new Date(b.date).getTime()
        );

        setWeightData(sortedData);

        // Calculate current and starting weight
        if (sortedData.length > 0) {
          setCurrentWeight(sortedData[sortedData.length - 1].weight);
          setStartWeight(sortedData[0].weight);
          setWeightChange(sortedData[sortedData.length - 1].weight - sortedData[0].weight);
        }
      } catch (error) {
        console.error('Error fetching weight data:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load weight data',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchWeightData();
  }, [toast]);

  // Format data for chart
  const chartData = weightData.map(entry => ({
    date: format(new Date(entry.date), 'MMM dd'),
    weight: entry.weight,
    bodyFat: entry.bodyFat
  }));

  if (isLoading) {
    return <div className="text-center py-6">Loading weight data...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Current Weight
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentWeight} lbs</div>
            {weightData.length > 1 && (
              <p className="text-xs text-muted-foreground flex items-center">
                <span className={`flex items-center mr-1 ${weightChange < 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {weightChange < 0 ? (
                    <>
                      <TrendingDown className="h-3 w-3 mr-1" />
                      {Math.abs(weightChange).toFixed(1)} lbs
                    </>
                  ) : (
                    <>
                      <TrendingUp className="h-3 w-3 mr-1" />
                      {weightChange.toFixed(1)} lbs
                    </>
                  )}
                </span>
                since start
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Weight Tracking</CardTitle>
          <CardDescription>
            Track your weight over time
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[300px]">
          {weightData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis domain={['dataMin - 5', 'dataMax + 5']} />
                {weightData.some(data => data.bodyFat) && (
                  <YAxis yAxisId="right" orientation="right" domain={[0, 'dataMax + 5']} />
                )}
                <Tooltip formatter={(value, name) => [
                  `${value} ${name === 'Weight (lbs)' ? 'lbs' : '%'}`,
                  name
                ]} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="weight"
                  stroke="#8884d8"
                  name="Weight (lbs)"
                  activeDot={{ r: 8 }}
                />
                {weightData.some(data => data.bodyFat) && (
                  <Line
                    type="monotone"
                    dataKey="bodyFat"
                    stroke="#82ca9d"
                    name="Body Fat %"
                    yAxisId="right"
                  />
                )}
              </LineChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex h-full items-center justify-center">
              <p className="text-muted-foreground">No weight data available. Start tracking your weight to see progress.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
