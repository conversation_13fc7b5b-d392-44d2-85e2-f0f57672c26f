import { memo } from 'react'
import { Star } from 'lucide-react'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface Trainer {
  id: string
  name: string
  avatarUrl: string | null
  bio: string
  themeSettings: string
}

interface CachedTrainersSectionProps {
  trainers: Trainer[]
}

// Memoized trainer card component
const TrainerCard = memo(({ trainer }: { trainer: Trainer }) => {
  // Parse theme settings if available
  const themeColors = {
    primaryColor: '#3b82f6',
    secondaryColor: '#6366f1'
  }
  
  try {
    if (trainer.themeSettings && typeof trainer.themeSettings === 'string') {
      const parsed = JSON.parse(trainer.themeSettings)
      if (parsed.primaryColor) themeColors.primaryColor = parsed.primaryColor
      if (parsed.secondaryColor) themeColors.secondaryColor = parsed.secondaryColor
    }
  } catch (error) {
    // Use default colors if parsing fails
  }

  const initials = trainer.name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()

  return (
    <div 
      key={trainer.id} 
      className="rounded-lg border bg-card text-card-foreground shadow overflow-hidden"
    >
      {/* Header with gradient background */}
      <div 
        className="h-48 w-full relative"
        style={{
          background: `linear-gradient(to right, ${themeColors.primaryColor}, ${themeColors.secondaryColor})`
        }}
      >
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute bottom-0 left-0 w-full p-4">
          <div className="flex items-center gap-4">
            <div className="h-16 w-16 rounded-full border-2 border-white overflow-hidden bg-white">
              {trainer.avatarUrl ? (
                <img 
                  src={trainer.avatarUrl} 
                  alt={trainer.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="flex items-center justify-center w-full h-full text-xl font-bold text-primary bg-primary/10">
                  {initials}
                </div>
              )}
            </div>
            <div className="text-white">
              <h3 className="font-bold text-xl">{trainer.name}</h3>
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                ))}
                <span className="ml-1 text-sm">5.0</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <p className="text-muted-foreground line-clamp-3">{trainer.bio}</p>
        <div className="flex flex-wrap gap-2 mt-4">
          <Badge 
            style={{ backgroundColor: themeColors.primaryColor }}
            className="border-transparent bg-primary text-primary-foreground hover:bg-primary/80"
          >
            Strength Training
          </Badge>
          <Badge variant="outline">Nutrition</Badge>
          <Badge variant="outline">Personal Training</Badge>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t">
        <Button 
          asChild 
          className="w-full"
          style={{ 
            backgroundColor: themeColors.primaryColor, 
            color: 'white' 
          }}
        >
          <Link href={`/trainer-${trainer.name.toLowerCase().replace(/\s+/g, '-')}`}>
            Visit Profile
            <span className="ml-2 h-4 w-4">→</span>
          </Link>
        </Button>
      </div>
    </div>
  )
})

TrainerCard.displayName = 'TrainerCard'

// Main cached trainers section component
export const CachedTrainersSection = memo<CachedTrainersSectionProps>(({ trainers }) => {
  return (
    <section className="py-20 bg-muted">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">Featured Trainers</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Our platform hosts elite coaches from various fitness disciplines ready to help you 
            reach your goals.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {trainers.map((trainer) => (
            <TrainerCard key={trainer.id} trainer={trainer} />
          ))}
        </div>
        
        <div className="text-center mt-12">
          <Button variant="outline" size="lg" asChild>
            <Link href="/dashboard/trainers">
              View All Trainers
              <span className="ml-2 h-4 w-4">→</span>
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
})

CachedTrainersSection.displayName = 'CachedTrainersSection' 