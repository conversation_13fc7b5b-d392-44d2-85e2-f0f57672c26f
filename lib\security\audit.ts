/**
 * Security Audit Utility
 * 
 * This utility provides functions for checking security configurations
 * and identifying potential vulnerabilities in the application.
 */

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

interface SecurityCheckResult {
  status: 'pass' | 'fail' | 'warn';
  message: string;
  details?: string;
  recommendation?: string;
}

interface SecurityAuditResult {
  timestamp: string;
  environment: string;
  checks: Record<string, SecurityCheckResult>;
  overallStatus: 'pass' | 'fail' | 'warn';
  score: number;
  summary: string;
}

/**
 * Check if all required environment variables are set
 */
function checkEnvironmentVariables(): SecurityCheckResult {
  const requiredVars = [
    'NEXT_PUBLIC_API_URL',
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'CSRF_SECRET',
  ];
  
  const missingVars = requiredVars.filter(v => !process.env[v]);
  
  if (missingVars.length === 0) {
    return {
      status: 'pass',
      message: 'All required environment variables are set',
    };
  }
  
  return {
    status: 'fail',
    message: `Missing required environment variables: ${missingVars.join(', ')}`,
    recommendation: 'Set all required environment variables in .env or through your hosting provider.',
  };
}

/**
 * Check if CSRF protection is enabled
 */
function checkCSRFProtection(): SecurityCheckResult {
  if (!process.env.CSRF_SECRET) {
    return {
      status: 'fail',
      message: 'CSRF protection is not properly configured',
      details: 'CSRF_SECRET environment variable is not set',
      recommendation: 'Set a strong random CSRF_SECRET in your environment variables',
    };
  }
  
  if (process.env.CSRF_SECRET === 'change-me-with-a-secure-secret') {
    return {
      status: 'warn',
      message: 'CSRF protection is using the default secret',
      recommendation: 'Change the CSRF_SECRET to a strong random value',
    };
  }
  
  return {
    status: 'pass',
    message: 'CSRF protection is enabled',
  };
}

/**
 * Check if NextAuth secret is strong
 */
function checkNextAuthSecret(): SecurityCheckResult {
  const secret = process.env.NEXTAUTH_SECRET;
  
  if (!secret) {
    return {
      status: 'fail',
      message: 'NextAuth secret is not set',
      recommendation: 'Set a strong random NEXTAUTH_SECRET in your environment variables',
    };
  }
  
  if (secret.length < 32) {
    return {
      status: 'warn',
      message: 'NextAuth secret is potentially weak',
      details: 'Secret should be at least 32 characters long',
      recommendation: 'Generate a stronger NEXTAUTH_SECRET',
    };
  }
  
  // Check for default or easily guessable secrets
  const commonSecrets = ['secret', 'change-me', 'nextauth-secret', 'my-secret'];
  if (commonSecrets.some(s => secret.includes(s))) {
    return {
      status: 'warn',
      message: 'NextAuth secret may be using a common pattern',
      recommendation: 'Use a randomly generated string for NEXTAUTH_SECRET',
    };
  }
  
  return {
    status: 'pass',
    message: 'NextAuth secret appears to be properly configured',
  };
}

/**
 * Check if strong Content-Security-Policy headers are set
 */
function checkContentSecurityPolicy(): SecurityCheckResult {
  try {
    const nextConfig = require(path.join(process.cwd(), 'next.config.mjs'));
    
    // Check if there's a headers function
    if (nextConfig.headers && typeof nextConfig.headers === 'function') {
      const headersConfig = nextConfig.headers();
      
      // Look for CSP headers
      for (const route of headersConfig) {
        for (const header of route.headers || []) {
          if (header.key === 'Content-Security-Policy') {
            // Check if the CSP is too permissive
            if (header.value.includes("'unsafe-inline'") || 
                header.value.includes("'unsafe-eval'")) {
              return {
                status: 'warn',
                message: 'Content-Security-Policy includes unsafe directives',
                details: "CSP contains 'unsafe-inline' or 'unsafe-eval'",
                recommendation: "Consider removing 'unsafe-inline' and 'unsafe-eval' directives if possible",
              };
            }
            
            // Check if default-src is configured
            if (!header.value.includes('default-src')) {
              return {
                status: 'warn',
                message: 'Content-Security-Policy is missing default-src directive',
                recommendation: "Add a default-src directive to your CSP",
              };
            }
            
            return {
              status: 'pass',
              message: 'Content-Security-Policy is properly configured',
            };
          }
        }
      }
      
      return {
        status: 'warn',
        message: 'Content-Security-Policy header not found',
        recommendation: 'Add a Content-Security-Policy header in next.config.js',
      };
    }
  } catch (error) {
    return {
      status: 'warn',
      message: 'Could not check Content-Security-Policy',
      details: error instanceof Error ? error.message : String(error),
    };
  }
  
  return {
    status: 'warn',
    message: 'Content-Security-Policy header not found or config not accessible',
    recommendation: 'Add a Content-Security-Policy header in next.config.js',
  };
}

/**
 * Check for secure cookie configuration in the application
 */
function checkSecureCookies(): SecurityCheckResult {
  // Check for next-auth configuration
  try {
    const authConfig = fs.readFileSync(
      path.join(process.cwd(), 'app/api/auth/[...nextauth]/route.ts'), 
      'utf8'
    );
    
    // Check for secure cookie settings
    if (!authConfig.includes('secure:') || 
        (authConfig.includes('secure: false') && process.env.NODE_ENV === 'production')) {
      return {
        status: 'warn',
        message: 'Cookies may not be configured as secure in production',
        recommendation: 'Set secure: true for cookies in production environment',
      };
    }
    
    // Check for proper sameSite attribute
    if (!authConfig.includes('sameSite:') || authConfig.includes('sameSite: "none"')) {
      return {
        status: 'warn',
        message: 'Cookies may have insecure sameSite configuration',
        recommendation: 'Use sameSite: "lax" or "strict" for cookies',
      };
    }
    
    return {
      status: 'pass',
      message: 'Cookie security settings appear to be properly configured',
    };
  } catch (error) {
    // Maybe next-auth is not used or file is in a different location
    return {
      status: 'warn',
      message: 'Could not verify cookie security settings',
      details: 'Unable to locate or read auth configuration',
      recommendation: 'Ensure cookies are set with secure: true and appropriate sameSite policy',
    };
  }
}

/**
 * Check if package.json dependencies have known vulnerabilities
 * In a real implementation, this would call an API like Snyk or npm audit
 */
function checkDependencyVulnerabilities(): SecurityCheckResult {
  try {
    // This would typically call npm audit or a vulnerability database API
    // For demonstration, we'll do a simplified check
    const packageJson = JSON.parse(
      fs.readFileSync(path.join(process.cwd(), 'package.json'), 'utf8')
    );
    
    // Check for outdated React versions (example)
    const reactVersion = packageJson.dependencies.react;
    if (reactVersion && reactVersion.match(/\d+/)?.[0] < 17) {
      return {
        status: 'warn',
        message: 'Using potentially outdated React version',
        recommendation: 'Consider upgrading React to the latest version',
      };
    }
    
    // Recommend running npm audit manually
    return {
      status: 'warn',
      message: 'Manual vulnerability check recommended',
      recommendation: 'Run `npm audit` to check for package vulnerabilities',
    };
  } catch (error) {
    return {
      status: 'warn',
      message: 'Could not check dependencies for vulnerabilities',
      details: error instanceof Error ? error.message : String(error),
      recommendation: 'Run `npm audit` manually to check for vulnerabilities',
    };
  }
}

/**
 * Check for proper XSS protection measures
 */
function checkXSSProtection(): SecurityCheckResult {
  try {
    // Check if our XSS protection utility is being used
    const xssUtilPath = path.join(process.cwd(), 'lib/security/xss.ts');
    if (!fs.existsSync(xssUtilPath)) {
      return {
        status: 'warn',
        message: 'XSS protection utility not found',
        recommendation: 'Implement XSS protection utilities and use them throughout the application',
      };
    }
    
    // Check middleware for XSS protection headers
    const middlewarePath = path.join(process.cwd(), 'middleware.ts');
    if (fs.existsSync(middlewarePath)) {
      const middleware = fs.readFileSync(middlewarePath, 'utf8');
      if (!middleware.includes('X-XSS-Protection')) {
        return {
          status: 'warn',
          message: 'X-XSS-Protection header not found in middleware',
          recommendation: 'Add X-XSS-Protection header in middleware',
        };
      }
    }
    
    return {
      status: 'pass',
      message: 'XSS protection measures appear to be in place',
    };
  } catch (error) {
    return {
      status: 'warn',
      message: 'Could not verify XSS protection measures',
      details: error instanceof Error ? error.message : String(error),
      recommendation: 'Implement proper XSS protection throughout the application',
    };
  }
}

/**
 * Check for proper HTTP security headers
 */
function checkSecurityHeaders(): SecurityCheckResult {
  // This would typically check a live URL, but we'll check the config
  try {
    const middlewarePath = path.join(process.cwd(), 'middleware.ts');
    if (fs.existsSync(middlewarePath)) {
      const middleware = fs.readFileSync(middlewarePath, 'utf8');
      
      const requiredHeaders = [
        'X-Content-Type-Options',
        'Referrer-Policy'
      ];
      
      const missingHeaders = requiredHeaders.filter(header => 
        !middleware.includes(header)
      );
      
      if (missingHeaders.length > 0) {
        return {
          status: 'warn',
          message: `Missing security headers: ${missingHeaders.join(', ')}`,
          recommendation: 'Add all recommended security headers in middleware',
        };
      }
      
      return {
        status: 'pass',
        message: 'Security headers appear to be properly configured',
      };
    } else {
      return {
        status: 'warn',
        message: 'Middleware not found to check security headers',
        recommendation: 'Implement middleware with proper security headers',
      };
    }
  } catch (error) {
    return {
      status: 'warn',
      message: 'Could not verify security headers',
      details: error instanceof Error ? error.message : String(error),
      recommendation: 'Implement middleware with proper security headers',
    };
  }
}

/**
 * Check if secure API hooks are being used
 */
function checkSecureApiHooks(): SecurityCheckResult {
  try {
    // Scan client components for fetch usage
    const clientComponentGlobs = [
      'app/**/*.tsx',
      'components/**/*.tsx',
      'app/**/*.jsx',
      'components/**/*.jsx'
    ];
    
    let secureHookUsage = 0;
    let unsecureFetchUsage = 0;
    let totalFetchUsage = 0;
    
    // This would typically use a more robust scanning approach
    // For demonstration, we'll do a simplified check
    for (const glob of clientComponentGlobs) {
      const files = require('glob').sync(glob, { cwd: process.cwd() });
      
      for (const file of files) {
        const content = fs.readFileSync(path.join(process.cwd(), file), 'utf8');
        
        // Count secure hook usage
        const safeQueryUsage = (content.match(/useSafeQuery/g) || []).length;
        const safeMutationUsage = (content.match(/useSafeMutation/g) || []).length;
        const safeFormUsage = (content.match(/useSafeForm/g) || []).length;
        
        secureHookUsage += safeQueryUsage + safeMutationUsage + safeFormUsage;
        
        // Count direct fetch usage (potentially unsafe)
        const fetchUsage = (content.match(/fetch\(/g) || []).length;
        const csrfFetchUsage = (content.match(/fetchWithCSRF/g) || []).length;
        
        totalFetchUsage += fetchUsage;
        unsecureFetchUsage += fetchUsage - csrfFetchUsage;
      }
    }
    
    if (unsecureFetchUsage === 0 && secureHookUsage > 0) {
      return {
        status: 'pass',
        message: 'Secure API hooks are being used throughout the application',
        details: `Found ${secureHookUsage} instances of secure hooks and 0 instances of unsecured fetch`,
      };
    } else if (unsecureFetchUsage > 0 && secureHookUsage > 0) {
      return {
        status: 'warn',
        message: 'Secure API hooks are used, but some unsecured fetch calls were found',
        details: `Found ${secureHookUsage} instances of secure hooks and ${unsecureFetchUsage} potential instances of unsecured fetch`,
        recommendation: 'Replace all direct fetch calls with useSafeQuery, useSafeMutation, or fetchWithCSRF'
      };
    } else if (secureHookUsage === 0) {
      return {
        status: 'fail',
        message: 'Secure API hooks are not being used',
        details: `Found ${unsecureFetchUsage} instances of potentially unsecured fetch calls`,
        recommendation: 'Implement useSafeQuery and useSafeMutation hooks for all API calls'
      };
    }
    
    return {
      status: 'warn',
      message: 'Could not conclusively determine secure hook usage',
      recommendation: 'Manually check that all API calls use secure hooks'
    };
  } catch (error) {
    return {
      status: 'warn',
      message: 'Could not check for secure hook usage',
      details: error instanceof Error ? error.message : String(error),
      recommendation: 'Ensure all API calls use the useSafeQuery and useSafeMutation hooks'
    };
  }
}

/**
 * Run a complete security audit
 */
export async function runSecurityAudit(): Promise<SecurityAuditResult> {
  // Load environment variables from .env file
  dotenv.config();
  
  const checks: Record<string, SecurityCheckResult> = {
    environmentVariables: checkEnvironmentVariables(),
    csrfProtection: checkCSRFProtection(),
    nextAuthSecret: checkNextAuthSecret(),
    contentSecurityPolicy: checkContentSecurityPolicy(),
    securityHeaders: checkSecurityHeaders(),
    secureCookies: checkSecureCookies(),
    xssProtection: checkXSSProtection(),
    dependencyVulnerabilities: checkDependencyVulnerabilities(),
    secureApiHooks: checkSecureApiHooks(),
  };
  
  // Calculate overall status and score
  const statuses = Object.values(checks).map(c => c.status);
  const overallStatus = statuses.includes('fail') ? 'fail' : 
                        statuses.includes('warn') ? 'warn' : 'pass';
  
  const score = Math.round(
    (statuses.filter(s => s === 'pass').length / statuses.length) * 100
  );
  
  // Generate a summary of findings
  let summary = '';
  if (overallStatus === 'fail') {
    summary = 'Critical security issues were found that need immediate attention.';
  } else if (overallStatus === 'warn') {
    summary = 'Some security concerns were identified that should be addressed.';
  } else {
    summary = 'Security audit passed with no major concerns.';
  }
  
  const warnings = Object.entries(checks)
    .filter(([_, result]) => result.status !== 'pass')
    .map(([name, result]) => `${name}: ${result.message}`);
  
  if (warnings.length > 0) {
    summary += ' Issues to address: ' + warnings.join('; ');
  }
  
  return {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    checks,
    overallStatus,
    score,
    summary,
  };
}

/**
 * Print security audit results to console
 */
export function printSecurityAuditResults(results: SecurityAuditResult): void {
  console.log('\n=== SECURITY AUDIT RESULTS ===');
  console.log(`Timestamp: ${results.timestamp}`);
  console.log(`Environment: ${results.environment}`);
  console.log(`Overall Status: ${results.overallStatus.toUpperCase()}`);
  console.log(`Score: ${results.score}%`);
  console.log(`Summary: ${results.summary}\n`);
  
  for (const [checkName, result] of Object.entries(results.checks)) {
    const icon = result.status === 'pass' ? '✅' : 
                 result.status === 'warn' ? '⚠️' : '❌';
    
    console.log(`${icon} ${checkName}: ${result.message}`);
    
    if (result.details) {
      console.log(`   Details: ${result.details}`);
    }
    
    if (result.recommendation) {
      console.log(`   Recommendation: ${result.recommendation}`);
    }
    
    console.log('');
  }
}

/**
 * Generate a cryptographically secure random string
 * @param length Length of the string to generate
 * @returns A secure random string
 */
export function generateSecureString(length = 32): string {
  return crypto.randomBytes(length).toString('hex');
} 