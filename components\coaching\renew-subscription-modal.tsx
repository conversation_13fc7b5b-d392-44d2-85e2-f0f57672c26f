"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { renewCoachingRelationship } from "@/app/actions/coaching"
import { Sparkles, DollarSign } from "lucide-react"

interface RenewSubscriptionModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: (updatedClient: { relationshipId: string; monthlyFee: number; status: string }) => void
  clientName: string
  relationshipId: string
  currentFee: number
}

export function RenewSubscriptionModal({
  isOpen,
  onClose,
  onSuccess,
  clientName,
  relationshipId,
  currentFee,
}: RenewSubscriptionModalProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [monthlyFee, setMonthlyFee] = useState(currentFee.toString())
  const [subscriptionDuration, setSubscriptionDuration] = useState("1")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const newFee = monthlyFee ? parseFloat(monthlyFee) : currentFee
      const duration = parseInt(subscriptionDuration)
      const updatedRelationship = await renewCoachingRelationship(relationshipId, newFee, duration)

      toast.success("Subscription renewed successfully")
      onSuccess?.({
        relationshipId: updatedRelationship.id,
        monthlyFee: newFee,
        status: "active"
      })
      onClose()
      router.refresh()
    } catch (error) {
      console.error("Error renewing subscription:", error)
      toast.error(error instanceof Error ? error.message : "Failed to renew subscription")
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px] bg-gradient-to-b from-background to-muted/50 border-primary/10">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Sparkles className="h-5 w-5 text-primary" />
            Renew Premium Coaching
          </DialogTitle>
          <DialogDescription className="text-muted-foreground/80">
            Renew the premium coaching subscription for {clientName}. You can optionally update their monthly fee.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="monthlyFee" className="text-sm font-medium">
                Monthly Fee
              </Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="monthlyFee"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder={currentFee.toString()}
                  value={monthlyFee}
                  onChange={(e) => setMonthlyFee(e.target.value)}
                  className="pl-9 bg-muted/30 border-primary/10 focus-visible:ring-primary/20"
                />
              </div>
              <p className="text-[13px] text-muted-foreground">
                Leave empty to keep the current fee of ${currentFee}
              </p>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="subscriptionDuration" className="text-right">
                Duration (months)
              </Label>
              <Input
                id="subscriptionDuration"
                type="number"
                min="1"
                max="12"
                value={subscriptionDuration}
                onChange={(e) => setSubscriptionDuration(e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="rounded-lg border border-primary/10 bg-primary/5 p-4">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-primary" />
                Premium Benefits
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-center gap-2">
                  • 1:1 Personalized coaching
                </li>
                <li className="flex items-center gap-2">
                  • Custom workout plans
                </li>
                <li className="flex items-center gap-2">
                  • Direct coach access
                </li>
                <li className="flex items-center gap-2">
                  • Progress tracking
                </li>
              </ul>
            </div>
          </div>

          <DialogFooter className="gap-2 sm:gap-0">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
              className="hover:bg-primary/5 hover:text-primary hover:border-primary/20"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-primary hover:bg-primary/90"
            >
              {loading ? (
                <>
                  <Sparkles className="mr-2 h-4 w-4 animate-spin" />
                  Renewing...
                </>
              ) : (
                <>
                  <DollarSign className="mr-2 h-4 w-4" />
                  Renew Subscription
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 