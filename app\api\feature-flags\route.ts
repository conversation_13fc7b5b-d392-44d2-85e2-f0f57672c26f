import { NextResponse } from "next/server";
import { getServerAuthSession } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET() {
  const session = await getServerAuthSession();
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const features = [
      { id: "premium-coaching", name: "Premium Coaching", description: "1:1 coaching services", enabled: true },
      { id: "subscription-plans", name: "Subscription Plans", description: "Subscription-based workout programs", enabled: true },
      { id: "digital-products", name: "Digital Products", description: "Digital product store", enabled: true }
    ];
    
    return NextResponse.json({ features });
  } catch (error) {
    console.error("Error fetching feature flags:", error);
    return NextResponse.json({ error: "Failed to fetch feature flags" }, { status: 500 });
  }
}
