import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Only trainers and admins can access analytics
    if (session.user.role !== "trainer" && session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const userId = session.user.id

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get("timeRange") || "month"

    // Calculate date range
    const now = new Date()
    let startDate = new Date()

    switch (timeRange) {
      case "week":
        startDate.setDate(now.getDate() - 7)
        break
      case "month":
        startDate.setMonth(now.getMonth() - 1)
        break
      case "quarter":
        startDate.setMonth(now.getMonth() - 3)
        break
      case "year":
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setMonth(now.getMonth() - 1) // Default to month
    }

    // Get active clients count
    const activeClients = await prisma.subscription.count({
      where: {
        trainerId: userId,
        status: "active"
      },
      distinct: ["clientId"]
    })

    // Get total revenue
    const subscriptions = await prisma.subscription.findMany({
      where: {
        trainerId: userId,
        createdAt: {
          gte: startDate
        }
      },
      include: {
        tier: true
      }
    })

    // Calculate revenue from subscriptions
    const subscriptionRevenue = subscriptions.reduce((total, sub) => {
      return total + (sub.tier?.price || 0)
    }, 0)

    // Get product sales
    const productSales = await prisma.productPurchase.count({
      where: {
        product: {
          trainerId: userId
        },
        createdAt: {
          gte: startDate
        }
      }
    })

    // Get coaching services
    const coachingServices = await prisma.coachingService.count({
      where: {
        trainerId: userId
      }
    })

    // Get training plans
    const trainingPlans = await prisma.trainingPlanTemplate.count({
      where: {
        trainerId: userId
      }
    })

    // Return analytics data
    return NextResponse.json({
      activeClients,
      revenue: subscriptionRevenue,
      productSales,
      coachingServices,
      trainingPlans,
      timeRange
    })

  } catch (error) {
    console.error("Error fetching analytics:", error)
    return NextResponse.json(
      { error: "An error occurred while fetching analytics data" },
      { status: 500 }
    )
  }
}
