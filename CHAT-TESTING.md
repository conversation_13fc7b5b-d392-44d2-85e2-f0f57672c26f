# 1:1 Chat Testing Guide

This guide provides instructions on how to test the 1:1 chat functionality between trainers and clients.

## Setup Options

There are several ways to test the 1:1 chat functionality:

### Option 1: Run the Setup Script (Recommended)

This option creates real users and relationships in the database:

1. Run the setup script:
   ```bash
   npx ts-node scripts/setup-coaching-users.js
   ```

2. This script will:
   - Create a trainer user (if it doesn't exist)
   - Create a client user (if it doesn't exist)
   - Create trainer and client profiles
   - Establish a coaching relationship between them
   - Create a conversation with a test message

3. After running the script, you can:
   - Login as trainer: http://localhost:3000/api/auth/dev-login?role=trainer
   - Access the chat: http://localhost:3000/dashboard/coaching-chat
   - Login as client: http://localhost:3000/api/auth/dev-login?role=client
   - Access the chat: http://localhost:3000/dashboard/coaching-chat

### Option 2: Use the Real Chat Test Page

This page attempts to use real data but falls back to localStorage if the API fails:

1. Navigate to: http://localhost:3000/real-chat-test
2. Click the "Setup Coaching Relationship" button
3. Test the chat functionality with both trainer and client roles

### Option 3: Use the Mock Chat

This is a fully simulated chat that doesn't require any database connection:

1. Navigate to: http://localhost:3000/mock-chat
2. Test the chat interface with simulated responses

### Option 4: Use the Standalone Chat

This chat saves messages to localStorage for persistence:

1. Navigate to: http://localhost:3000/standalone-chat
2. Test the chat interface with simulated responses
3. Messages will persist between page refreshes

## Testing as Different Roles

### Testing as a Client

1. Login as a client:
   ```
   http://localhost:3000/api/auth/dev-login?role=client
   ```

2. Navigate to the coaching chat:
   ```
   http://localhost:3000/dashboard/coaching-chat
   ```

3. You should see your conversation with the trainer
4. Send messages and see responses

### Testing as a Trainer

1. Login as a trainer:
   ```
   http://localhost:3000/api/auth/dev-login?role=trainer
   ```

2. Navigate to the coaching chat:
   ```
   http://localhost:3000/dashboard/coaching-chat
   ```

3. You should see your conversations with clients
4. Select a conversation and send messages

### Testing as a Premium Client

1. Login as a premium client:
   ```
   http://localhost:3000/api/auth/dev-login?role=premiumClient
   ```

2. Navigate to the coaching chat:
   ```
   http://localhost:3000/dashboard/coaching-chat
   ```

3. The experience should be similar to a regular client but with premium styling

## Troubleshooting

If you encounter issues with the real chat functionality:

1. **Database Connection Issues**:
   - Use the mock chat or standalone chat options which don't require database connections
   - Check the server logs for database connection errors

2. **Missing Coaching Relationship**:
   - Run the setup script again to ensure the relationship exists
   - Use the "Setup Coaching Relationship" button on the Real Chat Test page

3. **API Errors**:
   - Check the browser console for API error messages
   - Check the server logs for backend errors

4. **UI Issues**:
   - Try clearing your browser cache
   - Ensure you're logged in with the correct role

## Implementation Details

The 1:1 chat functionality consists of:

1. **Database Models**:
   - `User`: Represents trainers and clients
   - `CoachingRelationship`: Connects trainers and clients
   - `Conversation`: Represents a chat conversation
   - `Message`: Individual messages in a conversation

2. **API Endpoints**:
   - `/api/coaching/conversations`: Get all conversations for the current user
   - `/api/coaching/messages`: Get messages for a conversation or send a new message

3. **UI Components**:
   - `CoachingChat`: The main chat interface component
   - `CoachingChatFallback`: Shown when no conversations are found

4. **Test Utilities**:
   - `setup-coaching-users.js`: Script to set up test users and relationships
   - `/real-chat-test`: Page for testing with real or simulated data
   - `/mock-chat`: Fully simulated chat interface
   - `/standalone-chat`: Chat interface with localStorage persistence
