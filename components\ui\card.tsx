"use client"

import { cva, type VariantProps } from "class-variance-authority"
import { motion } from "framer-motion"
import * as React from "react"
import { cn } from "@/lib/utils"

const cardVariants = cva(
  "rounded-lg border border-border bg-card text-card-foreground shadow-sm transition-all duration-200",
  {
    variants: {
      variant: {
        default: "bg-card hover:shadow-md",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent/5 hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "border-none bg-transparent shadow-none hover:bg-accent/5 hover:text-accent-foreground",
        premium: "bg-gradient-to-r from-primary/5 to-accent/5 border-l-4 border-l-primary text-card-foreground hover:shadow-primary/10",
        accent: "bg-accent/10 text-accent-foreground border-accent/20 hover:bg-accent/20",
        interactive: "cursor-pointer hover:scale-[1.02] active:scale-[0.98] hover:shadow-md",
        glassy: "bg-white/10 dark:bg-gray-900/10 backdrop-blur-lg border-white/20 dark:border-gray-800/30 shadow-xl",
        gradient: "border-none bg-gradient-to-br from-primary/10 via-card to-accent/10 hover:shadow-lg",
        elevated: "shadow-md hover:shadow-xl border-transparent bg-card",
        minimal: "border-none shadow-none bg-transparent p-0",
      },
      size: {
        default: "p-6",
        sm: "p-4",
        lg: "p-8",
        xl: "p-10",
        compact: "p-2",
        none: "",
      },
      animation: {
        none: "",
        subtle: "",
        bounce: "",
        slide: "",
        fade: "",
      },
      hover: {
        none: "",
        raise: "transition-all hover:-translate-y-1 hover:shadow-lg",
        glow: "transition-all hover:shadow-lg hover:shadow-primary/10 dark:hover:shadow-accent/10",
        border: "transition-all hover:border-primary dark:hover:border-accent",
        scale: "transition-all hover:scale-[1.02]",
        highlight: "transition-all before:absolute before:inset-0 before:rounded-lg before:bg-primary/5 before:opacity-0 hover:before:opacity-100 before:transition-opacity relative before:-z-10",
      },
      rounded: {
        default: "rounded-lg",
        md: "rounded-md",
        sm: "rounded-sm",
        lg: "rounded-xl",
        xl: "rounded-2xl",
        full: "rounded-full",
        none: "rounded-none",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      animation: "none",
      hover: "none",
      rounded: "default",
    },
  }
)

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  asChild?: boolean
  as?: "div" | "section" | "article" | "main" | "aside"
  animate?: boolean
}

const Card = React.forwardRef<
  HTMLDivElement,
  CardProps
>(({ className, variant, size, animation, hover, rounded, asChild = false, animate = false, as = "div", style, ...props }, ref) => {
  const Component = as
  
  // Animation variants for framer-motion
  const motionVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    },
    bounce: {
      scale: [1, 1.02, 1],
      transition: { duration: 0.4 }
    },
    slide: {
      x: [50, 0],
      opacity: [0, 1],
      transition: { duration: 0.4 }
    },
    fade: {
      opacity: [0, 1],
      transition: { duration: 0.4 }
    }
  }
  
  // Apply animation based on prop
  let animationVariant = {}
  if (animate) {
    switch (animation) {
      case "bounce":
        animationVariant = motionVariants.bounce
        break
      case "slide":
        animationVariant = motionVariants.slide
        break
      case "fade":
        animationVariant = motionVariants.fade
        break
      case "subtle":
        animationVariant = motionVariants.visible
        break
      default:
        break
    }
  }
  
  // Only use motion.div if animation is requested
  if (animate) {
    return (
      <motion.div
        ref={ref}
        className={cn(cardVariants({ variant, size, animation, hover, rounded, className }))}
        initial={animation !== "none" ? "hidden" : false}
        animate={animation !== "none" ? "visible" : false}
        variants={motionVariants}
        style={style}
        {...props as any}
      />
    )
  }
  
  // Otherwise use regular div
  return (
    <Component
      ref={ref}
      className={cn(cardVariants({ variant, size, animation, hover, rounded, className }))}
      style={style}
      {...props}
    />
  )
})
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-xl font-semibold leading-tight tracking-tight text-foreground",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center pt-4", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
}
