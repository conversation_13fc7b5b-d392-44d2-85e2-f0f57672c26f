import { z } from 'zod';

/**
 * Email validation schema
 */
export const emailSchema = z
  .string()
  .min(1, { message: 'Email is required' })
  .email({ message: 'Invalid email address' });

/**
 * Password validation schema
 */
export const passwordSchema = z
  .string()
  .min(8, { message: 'Password must be at least 8 characters long' })
  .regex(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
  .regex(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
  .regex(/[0-9]/, { message: 'Password must contain at least one number' })
  .regex(/[^A-Za-z0-9]/, { message: 'Password must contain at least one special character' });

/**
 * Name validation schema
 */
export const nameSchema = z
  .string()
  .min(2, { message: 'Name must be at least 2 characters long' })
  .max(50, { message: 'Name must be less than 50 characters' })
  .regex(/^[a-zA-Z\s'-]+$/, { message: 'Name contains invalid characters' });

/**
 * Phone number validation schema
 */
export const phoneSchema = z
  .string()
  .regex(/^\+?[0-9]{10,15}$/, { message: 'Invalid phone number format' });

/**
 * Credit card number validation schema
 */
export const creditCardSchema = z
  .string()
  .regex(/^[0-9]{16}$/, { message: 'Card number must be 16 digits' });

/**
 * CVC validation schema
 */
export const cvcSchema = z
  .string()
  .regex(/^[0-9]{3,4}$/, { message: 'CVC must be 3 or 4 digits' });

/**
 * Expiry date validation schema (MM/YY format)
 */
export const expiryDateSchema = z
  .string()
  .regex(/^(0[1-9]|1[0-2])\/([0-9]{2})$/, { message: 'Expiry date must be in MM/YY format' })
  .refine((val) => {
    const [month, year] = val.split('/');
    const expiryDate = new Date(2000 + parseInt(year), parseInt(month) - 1);
    const currentDate = new Date();
    return expiryDate > currentDate;
  }, { message: 'Card has expired' });

/**
 * Create a login form schema
 */
export const loginFormSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, { message: 'Password is required' }),
});

/**
 * Create a registration form schema
 */
export const registrationFormSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

/**
 * Create a checkout form schema
 */
export const checkoutFormSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  cardNumber: creditCardSchema,
  expiryDate: expiryDateSchema,
  cvc: cvcSchema,
  country: z.string().min(1, { message: 'Country is required' }),
});

/**
 * Type definition for common form validation errors
 */
export type FormValidationError = {
  field: string;
  message: string;
};

/**
 * Validate form data against a schema
 * @param schema Zod schema to validate against
 * @param formData Form data to validate
 * @returns Array of validation errors or empty array if valid
 */
export function validateForm<T>(
  schema: z.ZodType<T>, 
  formData: unknown
): FormValidationError[] {
  try {
    schema.parse(formData);
    return [];
  } catch (error) {
    if (error instanceof z.ZodError) {
      return error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message
      }));
    }
    return [{ field: 'form', message: 'An unexpected error occurred' }];
  }
} 