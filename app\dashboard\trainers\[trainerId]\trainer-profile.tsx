"use client"

import { <PERSON>mbbellIcon, InfoIcon, Loader2, CheckCircle2, MessageSquare, ExternalLink } from "lucide-react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { useState } from "react"
import { toast } from "sonner"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { RequestCoachingDialog } from "@/components/coaching/request-coaching-dialog"

interface SubscriptionTier {
  id: string
  name: string
  description: string
  price: number
  features: string[]
  stripePriceId: string
}

interface SocialLinks {
  instagram?: string
  twitter?: string
  tiktok?: string
  youtube?: string
  website?: string
}

interface Trainer {
  id: string
  name: string | null
  email: string | null
  bio: string | null
  avatarUrl: string | null
  socialLinks: SocialLinks | string
  role: string
  slug: string
  subscriptionTiers: SubscriptionTier[]
  isSubscribed: boolean
  availableCoachingSpots: number
}

interface TrainerProfileProps {
  trainer: Trainer
  userRole: string | null
}

export default function TrainerProfile({ trainer, userRole }: TrainerProfileProps) {
  const router = useRouter()
  const [isSubscribing, setIsSubscribing] = useState(false)
  const [isApplying, setIsApplying] = useState(false)
  const [selectedTierId, setSelectedTierId] = useState<string | null>(null)

  // Parse social links if they're a string
  const socialLinks = typeof trainer.socialLinks === 'string'
    ? JSON.parse(trainer.socialLinks as string)
    : trainer.socialLinks

  // Function to handle subscription for content access
  const handleSubscribe = async (tierId: string) => {
    setIsSubscribing(true)
    setSelectedTierId(tierId)

    try {
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1500));

      const selectedTier = trainer.subscriptionTiers.find(t => t.id === tierId);

      // Set subscription status and features based on tier
      if (selectedTier) {
        // In a real app, this would be handled by the backend
        // For development, we'll set cookies to simulate subscription status
        if (process.env.NODE_ENV === 'development') {
          // All tiers get access to Week 1 of the program
          document.cookie = 'dev_subscription_active=true; path=/';
          document.cookie = `dev_subscription_tier=${selectedTier.name.toLowerCase()}; path=/`;

          // Only Premium and Elite tiers get premium features including the training tracker
          if (selectedTier.name === 'Premium' || selectedTier.name === 'Elite') {
            document.cookie = 'dev_premium_status=true; path=/';
            document.cookie = 'dev_tracker_enabled=true; path=/';
          }
        }
      }

      toast.success(`Successfully subscribed to ${trainer.name}'s ${selectedTier?.name} plan!`);
      toast.info('You now have access to Week 1 of the program!');

      // Redirect to the workouts page to show immediate access
      router.push('/dashboard/workouts/current');
    } catch (error) {
      console.error('Subscription simulation error:', error);
      toast.error("Failed to subscribe. Please try again.");
    } finally {
      setIsSubscribing(false);
    }
  }

  // Function to handle applying for 1:1 coaching
  const applyForCoaching = async () => {
    setIsApplying(true)

    try {
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast.success(`Application for 1:1 coaching with ${trainer.name} submitted!`);
      toast.info("The trainer will review your application and contact you soon.");

      // Redirect to the my-coaching page
      router.push('/dashboard/my-coaching');
    } catch (error) {
      console.error('Coaching application error:', error);
      toast.error("Failed to submit application. Please try again.");
    } finally {
      setIsApplying(false);
    }
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Trainer Profile Header */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-6 items-center md:items-start">
            <Avatar className="h-24 w-24 border-2 border-primary">
              <AvatarImage
                src={trainer.avatarUrl || ""}
                alt={trainer.name || "Trainer"}
              />
              <AvatarFallback>
                {trainer.name?.[0]?.toUpperCase() || "T"}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 text-center md:text-left">
              <h1 className="text-3xl font-bold">{trainer.name}</h1>

              <div className="flex flex-wrap gap-2 mt-3 justify-center md:justify-start">
                <Badge variant="secondary">Strength Training</Badge>
                <Badge variant="secondary">Nutrition</Badge>
                <Badge variant="secondary">Fitness Coach</Badge>
              </div>

              <p className="mt-4 max-w-2xl">
                {trainer.bio || "No bio available."}
              </p>
            </div>

            <div className="flex flex-col gap-2 min-w-[140px]">

              {userRole === "client" && (
                <div>
                  {trainer.availableCoachingSpots > 0 ? (
                    <div className="flex flex-col space-y-2">
                      <Badge className="self-end">
                        {trainer.availableCoachingSpots} coaching spots available
                      </Badge>
                      <RequestCoachingDialog
                        trainerId={trainer.id}
                        trainerName={trainer.name || "this trainer"}
                        requestType="direct"
                        trigger={
                          <Button>
                            <DumbbellIcon className="mr-2 h-4 w-4" />
                            Apply for 1:1 Coaching
                          </Button>
                        }
                      />
                    </div>
                  ) : (
                    <Button variant="outline" disabled>
                      <DumbbellIcon className="mr-2 h-4 w-4" />
                      No Coaching Spots Available
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for different sections */}
      <Tabs defaultValue="subscription">
        <TabsList className="grid grid-cols-2 md:grid-cols-3 mb-4">
          <TabsTrigger value="subscription">Subscription Plans</TabsTrigger>
          <TabsTrigger value="programs">Training Programs</TabsTrigger>
          <TabsTrigger value="about">About</TabsTrigger>
        </TabsList>

        {/* Subscription Plans Tab */}
        <TabsContent value="subscription" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Subscription Plans</h2>
            <div className="flex items-center space-x-2">
              <InfoIcon className="h-4 w-4 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">
                Access to training content, not 1:1 coaching
              </p>
            </div>
          </div>

          {userRole === "client" ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {trainer.subscriptionTiers.map((tier) => (
                <Card key={tier.id} className={selectedTierId === tier.id ? "border-primary" : ""}>
                  <CardHeader>
                    <CardTitle>{tier.name}</CardTitle>
                    <CardDescription>{tier.description}</CardDescription>
                    <div className="mt-2">
                      <span className="text-3xl font-bold">${tier.price}</span>
                      <span className="text-muted-foreground">/month</span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-48">
                      <ul className="space-y-2">
                        {tier.features.map((feature, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </ScrollArea>
                  </CardContent>
                  <CardFooter>
                    {trainer.isSubscribed ? (
                      <Button className="w-full" variant="outline" disabled>
                        Already Subscribed
                      </Button>
                    ) : (
                      <Button
                        className="w-full"
                        onClick={() => handleSubscribe(tier.id)}
                        disabled={isSubscribing}
                      >
                        {isSubscribing && selectedTierId === tier.id ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>Subscribe</>
                        )}
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <InfoIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-medium mb-2">Subscription Plans</h3>
                  <p className="text-muted-foreground max-w-md mx-auto">
                    {userRole === "trainer" ?
                      "As a trainer, you can't subscribe to other trainers." :
                      "As an admin, you can view subscription plans but cannot subscribe."}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Training Programs Tab */}
        <TabsContent value="programs">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <DumbbellIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-xl font-medium mb-2">Training Programs</h3>
                <p className="text-muted-foreground max-w-md mx-auto">
                  {trainer.name}'s training programs will be displayed here. Subscribe to access premium content.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* About Tab */}
        <TabsContent value="about">
          <Card>
            <CardContent className="pt-6">
              <h3 className="text-xl font-medium mb-4">About {trainer.name}</h3>
              <p className="mb-4">
                {trainer.bio || "No additional information available."}
              </p>

              {Object.keys(socialLinks || {}).length > 0 && (
                <>
                  <Separator className="my-4" />
                  <h4 className="font-medium mb-2">Connect with {trainer.name}</h4>
                  <div className="flex gap-4">
                    {socialLinks.instagram && (
                      <a href={socialLinks.instagram} target="_blank" rel="noopener noreferrer" className="text-pink-600 hover:underline">
                        Instagram
                      </a>
                    )}
                    {socialLinks.twitter && (
                      <a href={socialLinks.twitter} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">
                        Twitter
                      </a>
                    )}
                    {socialLinks.youtube && (
                      <a href={socialLinks.youtube} target="_blank" rel="noopener noreferrer" className="text-red-600 hover:underline">
                        YouTube
                      </a>
                    )}
                    {socialLinks.website && (
                      <a href={socialLinks.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        Website
                      </a>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}