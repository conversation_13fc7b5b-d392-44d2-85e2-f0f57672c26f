import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getExerciseIcon(category?: string): string {
  switch (category?.toLowerCase()) {
    case "strength":
    case "weights":
      return "Dumbbell"
    case "cardio":
      return "Timer"
    case "endurance":
      return "Activity"
    case "flexibility":
      return "Heart"
    case "cycling":
      return "Bike"
    default:
      return "Award"
  }
}

// Template for generating AI training plans
export function generateTrainingPlanTemplate(plan?: any) {
  const template = `# AI Training Plan Generator Template
  
## INSTRUCTIONS
Please create a comprehensive training plan based on the following requirements.
Include exercises with sets, reps, and other relevant details.
Structure the response using the format below to enable easy import back into our training system.

## BASIC INFORMATION
- Title: [Enter training plan title]
- Description: [Enter brief description]
- Target Level: [Beginner/Intermediate/Advanced]
- Category: [e.g., Strength, Cardio, etc.]
- Duration: [Number of weeks]
- Workouts Per Week: [Number of workouts per week]

## WEEKLY STRUCTURE
${plan ? generatePlanContent(plan) : generateEmptyWeeklyStructure()}

## EXERCISE DESCRIPTIONS
For each exercise, provide:
- Name
- Category (Strength, Cardio, Flexibility, etc.)
- Sets & Reps or Duration
- Brief description of proper form
- Target muscles
- Optional: Video link (YouTube URL)

IMPORTANT: Please maintain the format with clear week and exercise delineations for easy parsing.
`
  return template
}

// Parse a training plan from AI-generated text
export function parseAITrainingPlan(text: string) {
  try {
    // Extract basic information
    const title = extractValue(text, 'Title:', /Title:\s*([^\n]+)/)
    const description = extractValue(text, '', /Description:\s*([^\n]+)/)
    const targetLevel = extractValue(text, 'Beginner', /Target Level:\s*([^\n]+)/)
    const category = extractValue(text, '', /Category:\s*([^\n]+)/)
    const duration = parseInt(extractValue(text, '1', /Duration:\s*(\d+)/)) || 1
    const workoutsPerWeek = parseInt(extractValue(text, '3', /Workouts Per Week:\s*(\d+)/)) || 3

    // Extract weeks and exercises using regex patterns
    const weekPatterns = text.match(/Week \d+[\s\S]*?(?=Week \d+|$)/g) || []
    
    const weeks = weekPatterns.map((weekText, index) => {
      const exercises = extractExercises(weekText)
      
      return {
        id: `week-${index + 1}`,
        exercises
      }
    })

    // If no weeks were found or parsing failed, create at least one week
    if (weeks.length === 0) {
      weeks.push({
        id: 'week-1',
        exercises: []
      })
    }

    // Create the plan object
    return {
      id: `plan-${Date.now()}`,
      title,
      description,
      type: "personal",
      targetLevel,
      category,
      price: 0,
      weeks,
      workoutsPerWeek,
      createdAt: new Date().toISOString()
    }
  } catch (error) {
    console.error("Error parsing AI training plan:", error)
    // Return a basic plan structure if parsing fails
    return {
      id: `plan-${Date.now()}`,
      title: "New AI Plan",
      description: "Generated from AI template",
      type: "personal",
      targetLevel: "Beginner",
      weeks: [{ id: "week-1", exercises: [] }],
      createdAt: new Date().toISOString()
    }
  }
}

// Helper function to extract values using regex
function extractValue(text: string, defaultValue: string, regex: RegExp): string {
  const match = text.match(regex)
  return match && match[1] ? match[1].trim() : defaultValue
}

// Helper function to extract exercises from week text
function extractExercises(weekText: string) {
  const exerciseMatches = weekText.match(/Exercise \d+[\s\S]*?(?=Exercise \d+|$)/g) || []
  
  return exerciseMatches.map((exText, index) => {
    const name = extractValue(exText, `Exercise ${index + 1}`, /Exercise \d+[:\s]*([^\n]+)/)
    const category = extractValue(exText, 'Strength', /Category[:\s]*([^\n]+)/)
    
    // Extract sets and reps
    const setsReps = exText.match(/Sets[:\s]*(\d+)[^\d]+(\d+)/i)
    const sets = setsReps ? parseInt(setsReps[1]) : 3
    const reps = setsReps ? parseInt(setsReps[2]) : 10
    
    // Extract duration for cardio exercises
    const duration = extractValue(exText, '', /Duration[:\s]*([^\n]+)/)
    
    // Extract target muscles
    const targetMusclesText = extractValue(exText, '', /Target muscles[:\s]*([^\n]+)/)
    const targetMuscles = targetMusclesText ? targetMusclesText.split(',').map(m => m.trim()) : []
    
    // Extract video URL
    const video = extractValue(exText, '', /Video[:\s]*([^\n]+)/)
    
    // Extract description
    const description = extractValue(exText, '', /Description[:\s]*([^\n]+)/)
    
    return {
      id: `ex-${Date.now()}-${index}`,
      name,
      sets,
      reps,
      weight: 0,
      category,
      duration,
      targetMuscles,
      video,
      description
    }
  })
}

// Generate empty weekly structure for the template
function generateEmptyWeeklyStructure() {
  let template = ''
  for (let week = 1; week <= 4; week++) {
    template += `### Week ${week}\n`
    for (let exercise = 1; exercise <= 3; exercise++) {
      template += `
Exercise ${exercise}: [Name]
Category: [Strength/Cardio/etc]
Sets: [Number] x Reps: [Number]
Target muscles: [List muscles]
Description: [Brief description]
Video: [Optional YouTube URL]

`
    }
    template += '\n'
  }
  return template
}

// Generate content from existing plan
function generatePlanContent(plan: any) {
  let content = ''
  
  if (plan.weeks && plan.weeks.length > 0) {
    plan.weeks.forEach((week: any, weekIndex: number) => {
      content += `### Week ${weekIndex + 1}\n`
      
      if (week.exercises && week.exercises.length > 0) {
        week.exercises.forEach((exercise: any, exIndex: number) => {
          content += `
Exercise ${exIndex + 1}: ${exercise.name}
Category: ${exercise.category || 'Strength'}
Sets: ${exercise.sets} x Reps: ${exercise.reps}
${exercise.duration ? `Duration: ${exercise.duration}\n` : ''}
Target muscles: ${exercise.targetMuscles ? exercise.targetMuscles.join(', ') : ''}
Description: ${exercise.description || ''}
${exercise.video ? `Video: ${exercise.video}` : ''}

`
        })
      } else {
        content += `
Exercise 1: [Name]
Category: [Strength/Cardio/etc]
Sets: [Number] x Reps: [Number]
Target muscles: [List muscles]
Description: [Brief description]
Video: [Optional YouTube URL]

`
      }
      
      content += '\n'
    })
  } else {
    content = generateEmptyWeeklyStructure()
  }
  
  return content
}

export function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
