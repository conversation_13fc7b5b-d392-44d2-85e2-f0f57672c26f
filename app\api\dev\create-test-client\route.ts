import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(req: Request) {
  // Only allow in development mode
  if (process.env.NODE_ENV !== "development") {
    return new NextResponse("Not available in production", { status: 403 })
  }

  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  // Only trainers can create test clients
  if (session.user.role !== "trainer") {
    return NextResponse.json({ error: "Only trainers can create test clients" }, { status: 403 })
  }

  const trainerId = session.user.id

  try {
    // Create a test client user
    const testClientUser = await prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {
        name: "Test Client",
        role: "client",
      },
      create: {
        id: "test-client-id",
        email: "<EMAIL>",
        name: "Test Client",
        role: "client",
        password: "testpassword", // This is just for development
        emailVerified: new Date(),
      },
    })

    // First, ensure the trainer has a trainer profile
    const trainerProfile = await prisma.trainerProfile.upsert({
      where: { userId: trainerId },
      update: {},
      create: {
        user: {
          connect: { id: trainerId }
        }
      }
    })

    // Create or update client profile
    const clientProfile = await prisma.clientProfile.upsert({
      where: { userId: testClientUser.id },
      update: {
        assignedTrainer: {
          connect: { id: trainerProfile.id }
        }
      },
      create: {
        user: {
          connect: { id: testClientUser.id }
        },
        assignedTrainer: {
          connect: { id: trainerProfile.id }
        }
      },
      include: {
        user: true,
        assignedTrainer: {
          include: {
            user: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: "Test client created successfully",
      client: clientProfile
    })
  } catch (error) {
    console.error("Error creating test client:", error)
    return NextResponse.json({ error: "Failed to create test client" }, { status: 500 })
  }
}
