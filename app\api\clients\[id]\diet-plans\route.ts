import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET handler to retrieve a client's diet plans
export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const clientId = params.id;

    // Get the trainer's ID
    const trainer = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!trainer) {
      return NextResponse.json({ error: "Trainer not found" }, { status: 404 });
    }

    // Check if the client exists and is assigned to this trainer
    const client = await prisma.user.findFirst({
      where: {
        id: clientId,
        role: "client",
        OR: [
          {
            clientCoachingRelationships: {
              some: {
                trainerId: trainer.id,
                status: "active"
              }
            }
          },
          {
            clientSubscriptions: {
              some: {
                trainerId: trainer.id,
              },
            },
          },
        ],
      },
    });

    if (!client) {
      return NextResponse.json(
        { error: "Client not found or not assigned to this trainer" },
        { status: 404 }
      );
    }

    // Get the client's diet plan assignments
    const dietPlanAssignments = await prisma.dietPlanAssignment.findMany({
      where: {
        clientId: clientId,
        assignedById: trainer.id,
      },
      include: {
        dietPlan: {
          include: {
            meals: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(dietPlanAssignments);
  } catch (error) {
    console.error("Error fetching client diet plans:", error);
    return NextResponse.json(
      { error: "Failed to fetch client diet plans" },
      { status: 500 }
    );
  }
}
