"use server"

import { auth } from "@clerk/nextjs/server"
import { Exercise } from "@prisma/client"
import { db } from "@/lib/db"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { revalidatePath } from "next/cache"

export async function deleteExercise(exerciseId: string): Promise<void> {
  const { userId } = await auth()
  if (!userId) {
    throw new Error("Unauthorized")
  }

  const exercise = await db.exercise.findUnique({
    where: { id: exerciseId },
  })

  if (!exercise) {
    throw new Error("Exercise not found")
  }

  // Check authorization through a different method since workout relation is not available
  const isAuthorized = exercise.createdBy === userId
  if (!isAuthorized) {
    throw new Error("Unauthorized to delete this exercise")
  }

  await db.exercise.delete({
    where: { id: exerciseId },
  })
}

export async function updateExercise(exerciseId: string, data: Partial<Exercise>): Promise<void> {
  const { userId } = await auth()
  if (!userId) {
    throw new Error("Unauthorized")
  }

  const exercise = await db.exercise.findUnique({
    where: { id: exerciseId },
  })

  if (!exercise) {
    throw new Error("Exercise not found")
  }

  // Check authorization through a different method since workout relation is not available
  const isAuthorized = exercise.createdBy === userId
  if (!isAuthorized) {
    throw new Error("Unauthorized to update this exercise")
  }

  await db.exercise.update({
    where: { id: exerciseId },
    data,
  })

  // Revalidate a more general path since we don't have the specific training plan ID
  revalidatePath(`/dashboard/training-plans`)
}

export async function reorderExercises(workoutId: string, exercises: Exercise[]): Promise<void> {
  const { userId } = await auth()
  if (!userId) {
    throw new Error("Unauthorized")
  }

  if (exercises.length === 0) return

  // Check if all exercises belong to the user
  const exerciseIds = exercises.map(e => e.id)
  const existingExercises = await db.exercise.findMany({
    where: {
      id: { in: exerciseIds },
      createdBy: userId
    }
  })

  if (existingExercises.length !== exerciseIds.length) {
    throw new Error("Not authorized to reorder some of these exercises")
  }

  // Update all exercises in a transaction
  await db.$transaction(
    exercises.map((exercise, index) =>
      db.exercise.update({
        where: { id: exercise.id },
        data: { createdAt: new Date(Date.now() + index) },
      })
    )
  )
}

export async function addExercisesToWorkout(workoutId: string, exerciseIds: string[]) {
  const session = await getServerSession(authOptions)
  const userId = session?.user?.id

  if (!userId) {
    throw new Error("Unauthorized")
  }

  // Get the template exercises
  const templateExercises = await db.exercise.findMany({
    where: {
      id: { in: exerciseIds },
      isTemplate: true,
    },
  })

  // Determine the starting order
  const lastExercise = await db.exercise.findFirst({
    where: { workoutId },
    orderBy: { order: "desc" },
  })
  let order = (lastExercise?.order || 0) + 1

  // Create new exercises based on templates
  const newExercises = templateExercises.map((template: Exercise) => ({
    name: template.name,
    description: template.description,
    sets: template.sets,
    reps: template.reps,
    duration: template.duration,
    restTime: template.restTime,
    videoUrl: template.videoUrl,
    muscleGroup: template.muscleGroup,
    type: template.type,
    thumbnailUrl: template.thumbnailUrl,
    calories: template.calories,
    difficulty: template.difficulty,
    equipment: template.equipment,
    order: order++,
    workoutId,
    isTemplate: false,
    createdBy: userId,
  }))

  await db.exercise.createMany({ data: newExercises })


}