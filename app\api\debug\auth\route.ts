import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

/**
 * Debug endpoint to check authentication and user data
 * This helps identify issues with user sessions and database records
 */
export async function GET() {
  try {
    // Get current session
    const session = await getServerSession(authOptions)
    const isAuthenticated = !!session?.user
    
    // Prepare response data
    const response: any = {
      isAuthenticated,
      sessionData: {
        id: session?.user?.id || null,
        email: session?.user?.email || null,
        name: session?.user?.name || null,
      },
      dbUser: null,
      diagnostics: {}
    }
    
    // Check if user exists in database
    if (isAuthenticated && session?.user?.id) {
      const dbUser = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          createdAt: true,
          updatedAt: true
        }
      })
      
      response.dbUser = dbUser
      response.diagnostics.userExists = !!dbUser
      
      // If user exists, check product create permission
      if (dbUser) {
        // Count existing products by this user
        const productCount = await prisma.product.count({
          where: { athleteId: dbUser.id }
        })
        
        response.diagnostics.productCount = productCount
        response.diagnostics.canCreateProduct = dbUser.role === 'athlete' || dbUser.role === 'admin'
      }
      
      // Check if user ID format is correct
      if (session.user.id) {
        response.diagnostics.idFormat = {
          value: session.user.id,
          length: session.user.id.length,
          isValidFormat: /^[a-zA-Z0-9-_]+$/.test(session.user.id)
        }
      }
    }
    
    // Add tips based on diagnostics
    response.tips = []
    
    if (!isAuthenticated) {
      response.tips.push("You are not authenticated. Please log in.")
    } else if (!response.diagnostics.userExists) {
      response.tips.push("Your session user ID doesn't match any user in the database. Try logging out and back in.")
    } else if (!response.diagnostics.canCreateProduct) {
      response.tips.push(`Your role (${response.dbUser?.role}) doesn't allow product creation. Only athletes can create products.`)
    }
    
    if (response.diagnostics.idFormat && !response.diagnostics.idFormat.isValidFormat) {
      response.tips.push("Your user ID format appears invalid. Check for special characters.")
    }
    
    // Return detailed response
    return NextResponse.json(response)
  } catch (error) {
    console.error("[AUTH_DEBUG_ERROR]", error)
    return NextResponse.json({
      error: "Failed to check authentication status",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
} 