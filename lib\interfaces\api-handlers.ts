import { NextResponse } from "next/server"

export interface GetHandler {
  get(req: Request, userId: string, params?: any): Promise<NextResponse>
}

export interface PostHandler {
  post(req: Request, userId: string, params?: any): Promise<NextResponse>
}

export interface PutHandler {
  put(req: Request, userId: string, params?: any): Promise<NextResponse>
}

export interface DeleteHandler {
  delete(req: Request, userId: string, params?: any): Promise<NextResponse>
}

export interface CrudHandler extends Get<PERSON><PERSON><PERSON>, <PERSON>Hand<PERSON>, PutHandler, DeleteHandler {}
