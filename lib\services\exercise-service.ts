import { Exercise, Prisma } from "@prisma/client"
import { IExerciseRepository, ExerciseRepository } from "../repositories/exercise-repository"

export class ExerciseService {
  private static repository: IExerciseRepository = new ExerciseRepository()

  /**
   * Set a custom repository implementation (useful for testing)
   */
  static setRepository(repository: IExerciseRepository) {
    this.repository = repository
  }

  /**
   * Find an exercise by ID
   */
  static async findById(id: string): Promise<Exercise | null> {
    return this.repository.findById(id)
  }

  /**
   * Create a new exercise
   */
  static async create(data: Prisma.ExerciseCreateInput): Promise<Exercise> {
    return this.repository.create(data)
  }

  /**
   * Update an exercise
   */
  static async update(id: string, data: Partial<Exercise>): Promise<Exercise> {
    return this.repository.update(id, data)
  }

  /**
   * Delete an exercise
   */
  static async delete(id: string): Promise<Exercise> {
    return this.repository.delete(id)
  }

  /**
   * Find multiple exercises
   */
  static async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.ExerciseWhereInput
    orderBy?: Prisma.ExerciseOrderByWithRelationInput
  }): Promise<Exercise[]> {
    return this.repository.findMany(params)
  }

  /**
   * Find exercises by trainer ID
   */
  static async findByTrainerId(trainerId: string): Promise<Exercise[]> {
    return this.repository.findByTrainerId(trainerId)
  }

  /**
   * Search exercises
   */
  static async searchExercises(query: string, trainerId?: string): Promise<Exercise[]> {
    return this.repository.searchExercises(query, trainerId)
  }

  /**
   * Find template exercises
   */
  static async findTemplates(difficulty?: string): Promise<Exercise[]> {
    return this.repository.findTemplates(difficulty)
  }
}
