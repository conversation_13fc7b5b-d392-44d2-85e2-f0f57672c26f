import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// Development-only API endpoint to create a user
// This should only be used in non-production environments
export async function POST(request: Request) {
  // Safety check - only run in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: "This endpoint is only available in development mode" }, { status: 403 })
  }
  
  try {
    const { email, name, role = 'athlete' } = await request.json()
    
    if (!email || !name) {
      return NextResponse.json({ 
        error: "Email and name are required" 
      }, { status: 400 })
    }
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })
    
    if (existingUser) {
      return NextResponse.json({ 
        message: "User already exists", 
        user: {
          id: existingUser.id,
          email: existingUser.email,
          name: existingUser.name,
          role: existingUser.role
        }
      })
    }
    
    // Create a new user for development
    const newUser = await prisma.$queryRaw`
      INSERT INTO "User" (
        "id", "email", "name", "password", "role", "bio", "createdAt", "updatedAt"
      ) VALUES (
        gen_random_uuid(), ${email}, ${name}, 'dev-password', ${role}, 'Development user', NOW(), NOW()
      ) RETURNING "id", "email", "name", "role";
    `;
    
    return NextResponse.json({
      message: "Development user created successfully",
      user: Array.isArray(newUser) ? newUser[0] : newUser
    })
  } catch (error) {
    console.error("[DEV_CREATE_USER_ERROR]", error)
    return NextResponse.json({
      error: "Failed to create development user",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
} 