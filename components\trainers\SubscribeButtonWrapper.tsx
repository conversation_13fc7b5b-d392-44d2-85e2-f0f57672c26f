"use client"

import { Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Button } from "@/components/ui/button"

interface SubscribeButtonWrapperProps {
  trainerSlug: string
}

export function SubscribeButtonWrapper({ trainerSlug }: SubscribeButtonWrapperProps) {
  const router = useRouter()
  const { data: session, status } = useSession()

  const handleClick = () => {
    if (status === "loading") {
      return // Prevent action while session is loading
    }

    const trainerProfileUrl = `/${trainerSlug}`;

    if (status === "unauthenticated") {
      // Redirect to login, passing the target trainer profile as callback
      router.push(`/login?callbackUrl=${encodeURIComponent(trainerProfileUrl)}`)
    } else if (status === "authenticated") {
      // Redirect directly to the trainer profile page
      router.push(trainerProfileUrl)
    }
    // Handle potential errors or other statuses if necessary
  }

  return (
    <Button
      size="sm"
      variant="premium"
      rounded="lg"
      onClick={handleClick}
      disabled={status === "loading"}
    >
      {status === "loading" ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        "Subscribe"
      )}
    </Button>
  )
}