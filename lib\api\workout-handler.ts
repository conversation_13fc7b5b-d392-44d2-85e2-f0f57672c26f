import { NextResponse } from "next/server"
import { BaseApiHandler } from "./base-api-handler"
import { WorkoutService } from "../services/workout-service"

export class WorkoutHandler extends BaseApiHandler {
  /**
   * Get all workouts
   */
  protected async get(req: Request, userId: string): Promise<NextResponse> {
    const { searchParams } = new URL(req.url)
    const trainerId = searchParams.get("trainerId") || userId
    
    const workouts = await WorkoutService.findByTrainerId(trainerId)
    return NextResponse.json(workouts)
  }

  /**
   * Create a new workout
   */
  protected async post(req: Request, userId: string): Promise<NextResponse> {
    const data = await req.json()
    
    const workout = await WorkoutService.create({
      name: data.name,
      description: data.description,
      day: data.day,
      week: data.week,
      trainingPlanId: data.trainingPlanId,
      trainer: {
        connect: {
          id: userId
        }
      }
    })
    
    return NextResponse.json(workout)
  }

  /**
   * Update a workout (not implemented in main route)
   */
  protected async put(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete a workout (not implemented in main route)
   */
  protected async delete(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}

export class WorkoutByIdHandler extends BaseApiHandler {
  /**
   * Get a workout by ID
   */
  protected async get(req: Request, userId: string, params: { workoutId: string }): Promise<NextResponse> {
    const workout = await WorkoutService.findByIdWithExercises(params.workoutId)
    
    if (!workout) {
      return NextResponse.json({ error: "Workout not found" }, { status: 404 })
    }
    
    return NextResponse.json(workout)
  }

  /**
   * Create a workout (not implemented in [id] route)
   */
  protected async post(req: Request, userId: string, params: { workoutId: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Update a workout
   */
  protected async put(req: Request, userId: string, params: { workoutId: string }): Promise<NextResponse> {
    const data = await req.json()
    
    // Check if the workout exists
    const workout = await WorkoutService.findById(params.workoutId)
    
    if (!workout) {
      return NextResponse.json({ error: "Workout not found" }, { status: 404 })
    }
    
    // Check if the user owns the workout
    if (workout.trainerId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }
    
    const updatedWorkout = await WorkoutService.update(params.workoutId, data)
    
    return NextResponse.json(updatedWorkout)
  }

  /**
   * Delete a workout
   */
  protected async delete(req: Request, userId: string, params: { workoutId: string }): Promise<NextResponse> {
    // Check if the workout exists
    const workout = await WorkoutService.findById(params.workoutId)
    
    if (!workout) {
      return NextResponse.json({ error: "Workout not found" }, { status: 404 })
    }
    
    // Check if the user owns the workout
    if (workout.trainerId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }
    
    await WorkoutService.delete(params.workoutId)
    
    return NextResponse.json({ success: true })
  }
}

export class WorkoutExercisesHandler extends BaseApiHandler {
  /**
   * Get exercises for a workout
   */
  protected async get(req: Request, userId: string, params: { workoutId: string }): Promise<NextResponse> {
    const workout = await WorkoutService.findByIdWithExercises(params.workoutId)
    
    if (!workout) {
      return NextResponse.json({ error: "Workout not found" }, { status: 404 })
    }
    
    return NextResponse.json(workout.exercises)
  }

  /**
   * Add an exercise to a workout
   */
  protected async post(req: Request, userId: string, params: { workoutId: string }): Promise<NextResponse> {
    const data = await req.json()
    
    // Check if the workout exists
    const workout = await WorkoutService.findById(params.workoutId)
    
    if (!workout) {
      return NextResponse.json({ error: "Workout not found" }, { status: 404 })
    }
    
    // Check if the user owns the workout
    if (workout.trainerId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }
    
    const exercise = await WorkoutService.addExerciseToWorkout(params.workoutId, data)
    
    return NextResponse.json(exercise)
  }

  /**
   * Update exercises in a workout (not implemented)
   */
  protected async put(req: Request, userId: string, params: { workoutId: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete all exercises from a workout (not implemented)
   */
  protected async delete(req: Request, userId: string, params: { workoutId: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}
