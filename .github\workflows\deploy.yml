name: Deploy to EC2

on:
  push:
    branches:
      - development

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Deploy to EC2
      uses: appleboy/ssh-action@master
      with:
        host: **************
        username: ubuntu
        key: ${{ secrets.EC2_SSH_KEY }}
        script: |
          cd ~/app
          git fetch origin
          git checkout development
          git pull origin development
          docker-compose down
          docker-compose up -d --build
