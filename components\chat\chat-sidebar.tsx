"use client"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"

interface ChatSidebarProps {
  conversations: any[]
  activeConversation: any
  onSelectConversation: (conversation: any) => void
  userRole: string
}

export function ChatSidebar({ conversations, activeConversation, onSelectConversation, userRole }: ChatSidebarProps) {
  return (
    <div className="w-64 border-r">
      <div className="p-4 border-b">
        <h3 className="font-medium">Conversations</h3>
      </div>
      <ScrollArea className="h-[calc(100vh-13rem-57px)]">
        <div className="p-2">
          {conversations.length > 0 ? (
            conversations.map((conversation) => (
              <button
                key={conversation.id}
                className={`w-full flex items-center gap-3 p-2 rounded-md transition-colors ${
                  activeConversation?.id === conversation.id ? "bg-muted" : "hover:bg-muted/50"
                }`}
                onClick={() => onSelectConversation(conversation)}
              >
                <Avatar className="h-9 w-9">
                  <AvatarImage
                    src={conversation.users.avatar_url || "/placeholder.svg?height=36&width=36"}
                    alt={conversation.users.full_name}
                  />
                  <AvatarFallback>{conversation.users.full_name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="text-left">
                  <p className="text-sm font-medium">{conversation.users.full_name}</p>
                  <p className="text-xs text-muted-foreground">
                    {new Date(conversation.updated_at).toLocaleDateString()}
                  </p>
                </div>
              </button>
            ))
          ) : (
            <div className="p-4 text-center text-muted-foreground">No conversations yet</div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

