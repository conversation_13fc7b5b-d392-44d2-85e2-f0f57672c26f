import { compare } from "bcryptjs"
import { NextAuthOptions } from "next-auth"
import { getServerSession } from "next-auth/next"
import CredentialsProvider from "next-auth/providers/credentials"
import GoogleProvider from "next-auth/providers/google"
import { prisma } from "@/lib/prisma"
import logger from "@/lib/logger"

export type UserRole = "admin" | "trainer" | "client"

// Maximum login attempts before temporary lockout
const MAX_LOGIN_ATTEMPTS = 5
const LOCKOUT_DURATION_MINUTES = 15 // in minutes

// Track failed login attempts
const loginAttempts = new Map<string, { count: number; timestamp: number }>()

// Helper to check if we're in development mode
const isDev = process.env.NODE_ENV === 'development'

export const authOptions: NextAuthOptions = {
  // No adapter - we'll handle OAuth accounts manually
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: "/login",
    error: "/auth/error",
  },
  debug: isDev,
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          logger.warn({}, "Login attempt without email or password");
          return null;
        }

        // 1. Check if user is locked out due to too many failed attempts
        const userAttempts = loginAttempts.get(credentials.email)
        if (userAttempts) {
          const { count, timestamp } = userAttempts
          if (count >= MAX_LOGIN_ATTEMPTS) {
            // Check if lockout period is over
            const lockoutTime = timestamp + (LOCKOUT_DURATION_MINUTES * 60 * 1000)
            const remainingMinutes = Math.ceil((lockoutTime - Date.now()) / (60 * 1000))
            if (Date.now() < lockoutTime) {
              logger.warn({ email: credentials.email }, `Login attempt failed due to lockout. ${remainingMinutes} min remaining.`);
              throw new Error(`Too many login attempts. Please try again in ${remainingMinutes} minutes.`)
            }
            // Lockout period is over, reset attempts
            loginAttempts.delete(credentials.email)
          }
        }

        // 2. Find the user
        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
          select: {
            id: true,
            email: true,
            name: true,
            password: true,
            role: true,
            emailVerified: true,
            lastLoginAt: true,
            trainerProfile: { select: { id: true } }
          },
        })

        if (!user) {
          const attempts = loginAttempts.get(credentials.email) || { count: 0, timestamp: Date.now() }
          attempts.count++
          loginAttempts.set(credentials.email, attempts)
          logger.warn({ email: credentials.email }, "Login attempt failed: User not found.");
          return null
        }

        // 3. Check password
        const passwordValid = await compare(credentials.password, user.password!)
        if (!passwordValid) {
          const attempts = loginAttempts.get(credentials.email) || { count: 0, timestamp: Date.now() }
          attempts.count++
          loginAttempts.set(credentials.email, attempts)
          logger.warn({ email: credentials.email }, "Login attempt failed: Invalid password.");
          return null
        }

        // 4. Check if email is verified (skip in development mode)
        if (!user.emailVerified && process.env.NODE_ENV !== 'development') {
          logger.warn({ email: credentials.email }, "Login attempt failed: Email not verified.");
          throw new Error("Please verify your email before logging in.")
        }

        // Reset login attempts on successful login
        loginAttempts.delete(credentials.email)

        // Update last login timestamp
        await prisma.user.update({
          where: { id: user.id },
          data: { lastLoginAt: new Date() },
        })

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role as UserRole,
          emailVerified: user.emailVerified,
          image: null,
          trainerProfileId: user.trainerProfile?.id || null
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account, profile }) {
      // Initial sign in
      if (user) {
        token.id = user.id
        token.role = user.role as UserRole
        token.email = user.email
        token.name = user.name
      }

      // If this is Google sign-in, find or create the user
      if (account?.provider === "google" && profile?.email) {
        try {
          // First, check if this user exists
          let dbUser = await prisma.user.findUnique({
            where: { email: profile.email.toLowerCase() },
            include: { trainerProfile: { select: { id: true } } }
          });

          // If user doesn't exist, create a new one
          if (!dbUser) {
            logger.info({ email: profile.email }, "Creating new user via Google OAuth");

            dbUser = await prisma.user.create({
              data: {
                email: profile.email.toLowerCase(),
                name: profile.name || 'Google User',
                emailVerified: new Date(), // Auto-verify Google users
                role: "client" as UserRole, // Default role
                password: "", // Add required password field with empty string
                passwordChangeRequired: true, // Flag that this user should set a password
              },
              include: { trainerProfile: { select: { id: true } } }
            });
          }
          // If user exists but isn't verified, verify them
          else if (!dbUser.emailVerified) {
            await prisma.user.update({
              where: { id: dbUser.id },
              data: { emailVerified: new Date() }
            });
          }

          // Update token with user data
          if (dbUser) {
            token.id = dbUser.id;
            token.role = dbUser.role as UserRole;
            token.emailVerified = true;
            token.trainerProfileId = dbUser.trainerProfile?.id || null;

            // Update last login
            await prisma.user.update({
              where: { id: dbUser.id },
              data: { lastLoginAt: new Date() }
            });

            logger.info({ userId: dbUser.id, email: dbUser.email }, "Google user signed in successfully");
          }
        } catch (error) {
          logger.error({ error, email: profile.email }, "Error processing Google sign-in");
        }
      }

      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string
        session.user.role = token.role as UserRole
        session.user.image = token.picture as string || null

        if (!session.user.id) {
          logger.error({ token }, "No user ID found in session token during session callback.")
        }
      }
      return session
    },
  },
  events: {
    async signIn({ user, account }) {
      logger.info({ userId: user.id, email: user.email, provider: account?.provider }, "User signed in");
    },
    async signOut({ session }) {
      if (session?.user) {
        logger.info({ userId: session.user.id, email: session.user.email }, "User signed out");
      }
    },
    async createUser({ user }) {
      logger.info({ userId: user.id, email: user.email }, "User created via adapter");
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
}

// Helper function to get the session
export async function getServerAuthSession() {
  return await getServerSession(authOptions)
}

// Alias for backward compatibility
export const getAuthSession = getServerAuthSession;