import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Preview API endpoint called with ID:', params.id);
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { id } = params;

    if (!id) {
      return new NextResponse('Training plan ID is required', { status: 400 });
    }

    // Fetch the training plan template with trainer information
    const trainingPlan = await prisma.trainingPlanTemplate.findUnique({
      where: {
        id: id,
      },
      include: {
        trainer: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
          },
        },
      },
    });

    if (!trainingPlan) {
      return new NextResponse('Training plan not found', { status: 404 });
    }

    // Parse the weeks JSON data
    let weeks = [];
    try {
      if (typeof trainingPlan.weeks === 'string') {
        weeks = JSON.parse(trainingPlan.weeks);
      } else {
        weeks = trainingPlan.weeks;
      }
    } catch (error) {
      console.error('Error parsing weeks data:', error);
      weeks = [];
    }

    // Format the response
    const formattedPlan = {
      id: trainingPlan.id,
      title: trainingPlan.title,
      description: trainingPlan.description || '',
      difficulty: trainingPlan.difficulty || 'beginner',
      type: trainingPlan.type,
      duration_weeks: weeks.length,
      trainer: trainingPlan.trainer ? {
        id: trainingPlan.trainer.id,
        name: trainingPlan.trainer.name,
        avatar: trainingPlan.trainer.avatarUrl,
      } : undefined,
      weeks: weeks,
      createdAt: trainingPlan.createdAt,
      updatedAt: trainingPlan.updatedAt,
    };

    return NextResponse.json(formattedPlan);
  } catch (error) {
    console.error('[TRAINING_PLAN_PREVIEW_GET]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}
