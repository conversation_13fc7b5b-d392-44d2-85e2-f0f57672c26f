"use client"

import { ExternalLink, Info, <PERSON><PERSON>, Link as LinkIcon, Globe, Instagram, Twitter, Youtube, Facebook, Linkedin } from "lucide-react"
import { TrainerCustomizationOptions } from "@/components/trainer/trainer-customization-options"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { useState, useEffect } from "react"
import { SketchPicker } from "react-color"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"

interface ThemeSettings {
  primaryColor: string
  secondaryColor: string
  logoUrl: string | null
  bannerUrl: string | null
  fontFamily: string

  // Enhanced header options
  videoBannerUrl?: string | null
  overlayOpacity?: number
  textColor?: string
  buttonStyle?: 'rounded' | 'square' | 'pill'
  buttonText?: string
  showCredentials?: boolean
  credentials?: string[]
  testimonialHighlight?: {
    quote: string
    author: string
  } | null

  // Section styling
  sectionBackground?: string
  sectionTextColor?: string

  // Footer options
  footerStyle?: 'minimal' | 'standard' | 'expanded'
  footerBackground?: string
  footerTextColor?: string
  showNewsletter?: boolean
  showMap?: boolean
  mapLocation?: {
    address: string
    embedUrl: string
  }
  contactInfo?: {
    email?: string
    phone?: string
    address?: string
  }
  footerLinks?: Array<{
    title: string
    url: string
  }>
  copyrightText?: string
}

interface SocialLinks {
  instagram?: string
  twitter?: string
  youtube?: string
  facebook?: string
  linkedin?: string
  website?: string
}

interface TrainerProfile {
  id: string
  name: string | null
  email: string | null
  bio: string | null
  avatarUrl: string | null
  socialLinks: SocialLinks | string
  themeSettings?: ThemeSettings | string
  slug?: string
}

export default function ProfileCustomizationPage() {
  const router = useRouter()
  const { data: session } = useSession()
  const { toast } = useToast()

  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [profile, setProfile] = useState<TrainerProfile | null>(null)
  const [slug, setSlug] = useState("")
  const [socialLinks, setSocialLinks] = useState<SocialLinks>({})
  const [theme, setTheme] = useState<ThemeSettings>({
    primaryColor: "#0ea5e9",
    secondaryColor: "#7c3aed",
    logoUrl: null,
    bannerUrl: null,
    fontFamily: "Inter, sans-serif"
  })
  const [colorPickerOpen, setColorPickerOpen] = useState<string | null>(null)
  const [showPreviewLink, setShowPreviewLink] = useState(false)

  // Fetch profile data
  useEffect(() => {
    if (!session?.user) return

    // Make sure user is a trainer
    if (session.user.role !== "trainer" && session.user.role !== "admin") {
      router.push("/dashboard")
      return
    }

    fetchProfile()
  }, [session, router])

  const fetchProfile = async () => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/trainer/profile`)

      if (!response.ok) {
        throw new Error("Failed to fetch profile")
      }

      const data = await response.json()
      setProfile(data)

      // Set initial state from profile data
      setSlug(data.slug || "")

      // Parse social links
      if (data.socialLinks) {
        setSocialLinks(
          typeof data.socialLinks === "string"
            ? JSON.parse(data.socialLinks)
            : data.socialLinks
        )
      } else {
        setSocialLinks({})
      }

      // Parse theme settings
      if (data.themeSettings) {
        setTheme(
          typeof data.themeSettings === "string"
            ? JSON.parse(data.themeSettings)
            : data.themeSettings
        )
      } else {
        // Set default theme settings
        setTheme({
          primaryColor: "#4A90E2",
          secondaryColor: "#50E3C2",
          logoUrl: null,
          bannerUrl: null,
          fontFamily: "Inter, sans-serif"
        })
      }

      // Show preview link if slug is set
      setShowPreviewLink(!!data.slug)
    } catch (error) {
      console.error("Error fetching profile:", error)
      toast({
        title: "Error",
        description: "Failed to load profile data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    if (!profile) return

    try {
      setIsSaving(true)

      const response = await fetch(`/api/trainer/profile`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: profile.id,
          socialLinks,
          themeSettings: theme,
          slug
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update profile")
      }

      const updatedProfile = await response.json()
      setProfile(updatedProfile)

      // Show preview link if slug was set
      setShowPreviewLink(!!updatedProfile.slug)

      toast({
        title: "Success",
        description: "Profile and customizations saved successfully",
      })
    } catch (error) {
      console.error("Error updating profile:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update profile",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleSocialLinkChange = (platform: keyof SocialLinks, value: string) => {
    setSocialLinks(prev => ({
      ...prev,
      [platform]: value
    }))
  }

  const handleColorChange = (color: { hex: string }, type: 'primary' | 'secondary') => {
    setTheme(prev => ({
      ...prev,
      [type === 'primary' ? 'primaryColor' : 'secondaryColor']: color.hex
    }))
  }

  const handleFontChange = (font: string) => {
    setTheme(prev => ({
      ...prev,
      fontFamily: font
    }))
  }

  const handleImageUrlChange = (type: 'logo' | 'banner', url: string) => {
    setTheme(prev => ({
      ...prev,
      [type === 'logo' ? 'logoUrl' : 'bannerUrl']: url || null
    }))
  }

  if (!session?.user) {
    return null
  }

  if (isLoading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">
            Loading Profile...
          </h1>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">
          Profile Customization
        </h1>

        <div className="flex items-center gap-4">
          {showPreviewLink && (
            <Button
              variant="outline"
              onClick={() => window.open(`/${slug}`, '_blank')}
              className="gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Preview Landing Page
            </Button>
          )}

          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {!showPreviewLink && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Set up your custom URL</AlertTitle>
            <AlertDescription>
              Create a URL slug to get your own personalized landing page that you can share with clients.
            </AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="branding">
          <TabsList className="mb-6">
            <TabsTrigger value="branding">
              <Palette className="mr-2 h-4 w-4" />
              Branding
            </TabsTrigger>
            <TabsTrigger value="details">
              <Info className="mr-2 h-4 w-4" />
              Profile Details
            </TabsTrigger>
            <TabsTrigger value="links">
              <LinkIcon className="mr-2 h-4 w-4" />
              Social Links
            </TabsTrigger>
            {session?.user?.role === "admin" && (
              <TabsTrigger value="advanced">
                <Globe className="mr-2 h-4 w-4" />
                Advanced Options
              </TabsTrigger>
            )}
          </TabsList>

          {/* Branding Tab */}
          <TabsContent value="branding">
            {session?.user?.role !== "admin" && (
              <Alert className="mb-6">
                <Info className="h-4 w-4" />
                <AlertTitle>Basic Customization Available</AlertTitle>
                <AlertDescription>
                  As a trainer, you can customize basic branding elements. For advanced customization options, please contact an administrator.
                </AlertDescription>
              </Alert>
            )}
            {session?.user?.role === "admin" ? (
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Theme Colors</CardTitle>
                    <CardDescription>
                      Choose colors that represent your brand
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="primary-color">Primary Color</Label>
                      <div className="flex gap-2">
                        <div
                          className="h-10 w-10 rounded-md cursor-pointer border"
                          style={{ backgroundColor: theme.primaryColor }}
                          onClick={() => setColorPickerOpen(colorPickerOpen === 'primary' ? null : 'primary')}
                        />
                        <Input
                          id="primary-color"
                          value={theme.primaryColor}
                          onChange={(e) => handleColorChange({ hex: e.target.value }, 'primary')}
                        />
                      </div>
                      {colorPickerOpen === 'primary' && (
                        <div className="absolute z-10 mt-2">
                          <SketchPicker
                            color={theme.primaryColor}
                            onChange={(color) => handleColorChange(color, 'primary')}
                          />
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="secondary-color">Secondary Color</Label>
                      <div className="flex gap-2">
                        <div
                          className="h-10 w-10 rounded-md cursor-pointer border"
                          style={{ backgroundColor: theme.secondaryColor }}
                          onClick={() => setColorPickerOpen(colorPickerOpen === 'secondary' ? null : 'secondary')}
                        />
                        <Input
                          id="secondary-color"
                          value={theme.secondaryColor}
                          onChange={(e) => handleColorChange({ hex: e.target.value }, 'secondary')}
                        />
                      </div>
                      {colorPickerOpen === 'secondary' && (
                        <div className="absolute z-10 mt-2">
                          <SketchPicker
                            color={theme.secondaryColor}
                            onChange={(color) => handleColorChange(color, 'secondary')}
                          />
                        </div>
                      )}
                    </div>

                    <div className="mt-6 border p-4 rounded-md" style={{
                      background: `linear-gradient(135deg, ${theme.primaryColor}, ${theme.secondaryColor})`,
                      color: 'white'
                    }}>
                      <p className="text-center font-semibold mb-2">Preview</p>
                      <div className="h-20 flex items-center justify-center">
                        <div className="text-center">
                          <p className="font-bold text-xl">Your Brand</p>
                          <p className="text-sm opacity-80">Custom color scheme</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Images & Fonts</CardTitle>
                    <CardDescription>
                      Add your logo and banner images
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="logo-url">Logo URL</Label>
                      <Input
                        id="logo-url"
                        placeholder="https://example.com/logo.png"
                        value={theme.logoUrl || ""}
                        onChange={(e) => handleImageUrlChange('logo', e.target.value)}
                      />
                      <p className="text-sm text-muted-foreground">
                        Square image recommended (400x400px)
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="banner-url">Banner URL</Label>
                      <Input
                        id="banner-url"
                        placeholder="https://example.com/banner.jpg"
                        value={theme.bannerUrl || ""}
                        onChange={(e) => handleImageUrlChange('banner', e.target.value)}
                      />
                      <p className="text-sm text-muted-foreground">
                        Widescreen image recommended (1920x500px)
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="font-family">Font Family</Label>
                      <select
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        value={theme.fontFamily}
                        onChange={(e) => handleFontChange(e.target.value)}
                      >
                        <option value="Inter, sans-serif">Inter (Default)</option>
                        <option value="'Roboto', sans-serif">Roboto</option>
                        <option value="'Poppins', sans-serif">Poppins</option>
                        <option value="'Montserrat', sans-serif">Montserrat</option>
                        <option value="'Playfair Display', serif">Playfair Display</option>
                      </select>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <TrainerCustomizationOptions
                theme={theme}
                onThemeChange={setTheme}
                onSave={handleSave}
                isSaving={isSaving}
              />
            )}
          </TabsContent>

          {/* Profile Details Tab */}
          <TabsContent value="details">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>
                  Update your profile details and bio
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="profile-url">Custom URL</Label>
                  <div className="flex">
                    <div className="flex items-center px-3 border rounded-l-md bg-muted">
                      <span className="text-sm text-muted-foreground">{window.location.origin}/</span>
                    </div>
                    <Input
                      id="profile-url"
                      className="rounded-l-none"
                      placeholder="your-name"
                      value={slug}
                      onChange={(e) => setSlug(e.target.value)}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Choose a simple, memorable URL for your profile page
                  </p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label htmlFor="bio">Bio</Label>
                  <textarea
                    id="bio"
                    className="flex min-h-32 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    placeholder="Tell potential clients about yourself and your expertise..."
                    value={bio}
                    onChange={(e) => setBio(e.target.value)}
                  />
                  <p className="text-sm text-muted-foreground">
                    This will appear on your profile page and help clients understand your training philosophy.
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleSave}
                  disabled={isSaving || !slug}
                >
                  {isSaving ? "Saving..." : "Save Details"}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Social Links Tab */}
          <TabsContent value="links">
            <Card>
              <CardHeader>
                <CardTitle>Social Media Links</CardTitle>
                <CardDescription>
                  Connect your social media profiles
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="instagram">
                      <div className="flex items-center gap-2">
                        <Instagram className="h-4 w-4" />
                        Instagram
                      </div>
                    </Label>
                    <Input
                      id="instagram"
                      placeholder="https://instagram.com/username"
                      value={socialLinks.instagram || ""}
                      onChange={(e) => handleSocialLinkChange('instagram', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="twitter">
                      <div className="flex items-center gap-2">
                        <Twitter className="h-4 w-4" />
                        Twitter
                      </div>
                    </Label>
                    <Input
                      id="twitter"
                      placeholder="https://twitter.com/username"
                      value={socialLinks.twitter || ""}
                      onChange={(e) => handleSocialLinkChange('twitter', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="youtube">
                      <div className="flex items-center gap-2">
                        <Youtube className="h-4 w-4" />
                        YouTube
                      </div>
                    </Label>
                    <Input
                      id="youtube"
                      placeholder="https://youtube.com/c/channel"
                      value={socialLinks.youtube || ""}
                      onChange={(e) => handleSocialLinkChange('youtube', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="facebook">
                      <div className="flex items-center gap-2">
                        <Facebook className="h-4 w-4" />
                        Facebook
                      </div>
                    </Label>
                    <Input
                      id="facebook"
                      placeholder="https://facebook.com/page"
                      value={socialLinks.facebook || ""}
                      onChange={(e) => handleSocialLinkChange('facebook', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="linkedin">
                      <div className="flex items-center gap-2">
                        <Linkedin className="h-4 w-4" />
                        LinkedIn
                      </div>
                    </Label>
                    <Input
                      id="linkedin"
                      placeholder="https://linkedin.com/in/profile"
                      value={socialLinks.linkedin || ""}
                      onChange={(e) => handleSocialLinkChange('linkedin', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="website">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        Website
                      </div>
                    </Label>
                    <Input
                      id="website"
                      placeholder="https://yourwebsite.com"
                      value={socialLinks.website || ""}
                      onChange={(e) => handleSocialLinkChange('website', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? "Saving..." : "Save Social Links"}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Advanced Options Tab */}
          <TabsContent value="advanced">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Enhanced Header</CardTitle>
                  <CardDescription>
                    Advanced options for your landing page header
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="video-banner">Video Banner URL</Label>
                    <Input
                      id="video-banner"
                      placeholder="https://example.com/video.mp4"
                      value={theme.videoBannerUrl || ""}
                      onChange={(e) => setTheme(prev => ({
                        ...prev,
                        videoBannerUrl: e.target.value || null
                      }))}
                    />
                    <p className="text-sm text-muted-foreground">
                      MP4 video for a dynamic background (optional)
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="overlay-opacity">Overlay Opacity</Label>
                    <Input
                      id="overlay-opacity"
                      type="number"
                      min="0"
                      max="1"
                      step="0.1"
                      placeholder="0.5"
                      value={theme.overlayOpacity || "0.5"}
                      onChange={(e) => setTheme(prev => ({
                        ...prev,
                        overlayOpacity: parseFloat(e.target.value) || 0.5
                      }))}
                    />
                    <p className="text-sm text-muted-foreground">
                      Darkness of the banner overlay (0-1)
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="text-color">Header Text Color</Label>
                    <div className="flex gap-2">
                      <div
                        className="h-10 w-10 rounded-md cursor-pointer border"
                        style={{ backgroundColor: theme.textColor || "#FFFFFF" }}
                        onClick={() => setColorPickerOpen(colorPickerOpen === 'text' ? null : 'text')}
                      />
                      <Input
                        id="text-color"
                        value={theme.textColor || "#FFFFFF"}
                        onChange={(e) => setTheme(prev => ({
                          ...prev,
                          textColor: e.target.value
                        }))}
                      />
                    </div>
                    {colorPickerOpen === 'text' && (
                      <div className="absolute z-10 mt-2">
                        <SketchPicker
                          color={theme.textColor || "#FFFFFF"}
                          onChange={(color) => setTheme(prev => ({
                            ...prev,
                            textColor: color.hex
                          }))}
                        />
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="button-style">Button Style</Label>
                    <select
                      id="button-style"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={theme.buttonStyle || "rounded"}
                      onChange={(e) => setTheme(prev => ({
                        ...prev,
                        buttonStyle: e.target.value as 'rounded' | 'square' | 'pill'
                      }))}
                    >
                      <option value="rounded">Rounded</option>
                      <option value="square">Square</option>
                      <option value="pill">Pill</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="button-text">Button Text</Label>
                    <Input
                      id="button-text"
                      placeholder="Get Started Now"
                      value={theme.buttonText || ""}
                      onChange={(e) => setTheme(prev => ({
                        ...prev,
                        buttonText: e.target.value
                      }))}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Testimonials & Credentials</CardTitle>
                  <CardDescription>
                    Showcase your expertise and client results
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="show-credentials"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      checked={theme.showCredentials || false}
                      onChange={(e) => setTheme(prev => ({
                        ...prev,
                        showCredentials: e.target.checked
                      }))}
                    />
                    <Label htmlFor="show-credentials">Show Credentials</Label>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="credentials">Credentials (comma separated)</Label>
                    <Input
                      id="credentials"
                      placeholder="NASM Certified, Nutrition Expert"
                      value={(theme.credentials || []).join(", ")}
                      onChange={(e) => setTheme(prev => ({
                        ...prev,
                        credentials: e.target.value.split(",").map(item => item.trim()).filter(Boolean)
                      }))}
                    />
                  </div>

                  <Separator className="my-4" />

                  <div className="space-y-2">
                    <Label htmlFor="testimonial-quote">Featured Testimonial Quote</Label>
                    <textarea
                      id="testimonial-quote"
                      className="flex min-h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Your program changed my life!"
                      value={theme.testimonialHighlight?.quote || ""}
                      onChange={(e) => setTheme(prev => ({
                        ...prev,
                        testimonialHighlight: {
                          ...prev.testimonialHighlight,
                          quote: e.target.value,
                          author: prev.testimonialHighlight?.author || ""
                        }
                      }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="testimonial-author">Testimonial Author</Label>
                    <Input
                      id="testimonial-author"
                      placeholder="John D., Lost 30 lbs"
                      value={theme.testimonialHighlight?.author || ""}
                      onChange={(e) => setTheme(prev => ({
                        ...prev,
                        testimonialHighlight: {
                          ...prev.testimonialHighlight,
                          author: e.target.value,
                          quote: prev.testimonialHighlight?.quote || ""
                        }
                      }))}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Section Styling</CardTitle>
                  <CardDescription>
                    Customize the appearance of content sections
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="section-bg">Section Background</Label>
                    <div className="flex gap-2">
                      <div
                        className="h-10 w-10 rounded-md cursor-pointer border"
                        style={{ backgroundColor: theme.sectionBackground || "#f8f9fa" }}
                        onClick={() => setColorPickerOpen(colorPickerOpen === 'sectionBg' ? null : 'sectionBg')}
                      />
                      <Input
                        id="section-bg"
                        value={theme.sectionBackground || "#f8f9fa"}
                        onChange={(e) => setTheme(prev => ({
                          ...prev,
                          sectionBackground: e.target.value
                        }))}
                      />
                    </div>
                    {colorPickerOpen === 'sectionBg' && (
                      <div className="absolute z-10 mt-2">
                        <SketchPicker
                          color={theme.sectionBackground || "#f8f9fa"}
                          onChange={(color) => setTheme(prev => ({
                            ...prev,
                            sectionBackground: color.hex
                          }))}
                        />
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="section-text">Section Text Color</Label>
                    <div className="flex gap-2">
                      <div
                        className="h-10 w-10 rounded-md cursor-pointer border"
                        style={{ backgroundColor: theme.sectionTextColor || "#333333" }}
                        onClick={() => setColorPickerOpen(colorPickerOpen === 'sectionText' ? null : 'sectionText')}
                      />
                      <Input
                        id="section-text"
                        value={theme.sectionTextColor || "#333333"}
                        onChange={(e) => setTheme(prev => ({
                          ...prev,
                          sectionTextColor: e.target.value
                        }))}
                      />
                    </div>
                    {colorPickerOpen === 'sectionText' && (
                      <div className="absolute z-10 mt-2">
                        <SketchPicker
                          color={theme.sectionTextColor || "#333333"}
                          onChange={(color) => setTheme(prev => ({
                            ...prev,
                            sectionTextColor: color.hex
                          }))}
                        />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Footer Options</CardTitle>
                  <CardDescription>
                    Customize your landing page footer
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="footer-style">Footer Style</Label>
                    <select
                      id="footer-style"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={theme.footerStyle || "standard"}
                      onChange={(e) => setTheme(prev => ({
                        ...prev,
                        footerStyle: e.target.value as 'minimal' | 'standard' | 'expanded'
                      }))}
                    >
                      <option value="minimal">Minimal</option>
                      <option value="standard">Standard</option>
                      <option value="expanded">Expanded</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="footer-bg">Footer Background</Label>
                    <div className="flex gap-2">
                      <div
                        className="h-10 w-10 rounded-md cursor-pointer border"
                        style={{ backgroundColor: theme.footerBackground || "#f9fafb" }}
                        onClick={() => setColorPickerOpen(colorPickerOpen === 'footerBg' ? null : 'footerBg')}
                      />
                      <Input
                        id="footer-bg"
                        value={theme.footerBackground || "#f9fafb"}
                        onChange={(e) => setTheme(prev => ({
                          ...prev,
                          footerBackground: e.target.value
                        }))}
                      />
                    </div>
                    {colorPickerOpen === 'footerBg' && (
                      <div className="absolute z-10 mt-2">
                        <SketchPicker
                          color={theme.footerBackground || "#f9fafb"}
                          onChange={(color) => setTheme(prev => ({
                            ...prev,
                            footerBackground: color.hex
                          }))}
                        />
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="footer-text">Footer Text Color</Label>
                    <div className="flex gap-2">
                      <div
                        className="h-10 w-10 rounded-md cursor-pointer border"
                        style={{ backgroundColor: theme.footerTextColor || "#333333" }}
                        onClick={() => setColorPickerOpen(colorPickerOpen === 'footerText' ? null : 'footerText')}
                      />
                      <Input
                        id="footer-text"
                        value={theme.footerTextColor || "#333333"}
                        onChange={(e) => setTheme(prev => ({
                          ...prev,
                          footerTextColor: e.target.value
                        }))}
                      />
                    </div>
                    {colorPickerOpen === 'footerText' && (
                      <div className="absolute z-10 mt-2">
                        <SketchPicker
                          color={theme.footerTextColor || "#333333"}
                          onChange={(color) => setTheme(prev => ({
                            ...prev,
                            footerTextColor: color.hex
                          }))}
                        />
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="show-newsletter"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      checked={theme.showNewsletter || false}
                      onChange={(e) => setTheme(prev => ({
                        ...prev,
                        showNewsletter: e.target.checked
                      }))}
                    />
                    <Label htmlFor="show-newsletter">Show Newsletter Signup</Label>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="copyright">Copyright Text</Label>
                    <Input
                      id="copyright"
                      placeholder={`© ${new Date().getFullYear()} Your Name. All rights reserved.`}
                      value={theme.copyrightText || ""}
                      onChange={(e) => setTheme(prev => ({
                        ...prev,
                        copyrightText: e.target.value
                      }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}