/**
 * Sanitizes a string to prevent XSS attacks
 * @param input The input string to sanitize
 * @returns The sanitized string
 */
export function sanitizeString(input: string): string {
  if (!input) return '';
  
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Recursively sanitizes an object or array
 * @param data The data to sanitize
 * @returns The sanitized data
 */
export function sanitizeData<T extends object | string[]>(data: T): T {
  if (Array.isArray(data)) {
    return data.map(item => {
      if (typeof item === 'string') {
        return sanitizeString(item);
      }
      if (typeof item === 'object' && item !== null) {
        return sanitizeData(item as object);
      }
      return item;
    }) as T;
  }
  
  const sanitized: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeData(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized as T;
}

/**
 * Sanitizes form data
 * @param formData The form data to sanitize
 * @returns The sanitized form data
 */
export function sanitizeFormData(formData: FormData): FormData {
  const sanitizedFormData = new FormData();
  
  for (const [key, value] of formData.entries()) {
    if (typeof value === 'string') {
      sanitizedFormData.append(key, sanitizeString(value));
    } else {
      sanitizedFormData.append(key, value);
    }
  }
  
  return sanitizedFormData;
}

/**
 * Sanitizes URL parameters
 * @param params The URL parameters to sanitize
 * @returns The sanitized URL parameters
 */
export function sanitizeUrlParams(params: URLSearchParams): URLSearchParams {
  const sanitizedParams = new URLSearchParams();
  
  for (const [key, value] of params.entries()) {
    sanitizedParams.append(key, sanitizeString(value));
  }
  
  return sanitizedParams;
} 