"use client"

import { cva, type VariantProps } from "class-variance-authority"
import { motion, AnimatePresence, PanInfo, Variants } from "framer-motion"
import { X } from "lucide-react"
import * as React from "react"
import { cn } from "@/lib/utils"

const drawerVariants = cva(
  "fixed inset-0 z-50 flex",
  {
    variants: {
      position: {
        top: "items-start",
        bottom: "items-end",
        left: "justify-start",
        right: "justify-end",
      },
    },
    defaultVariants: {
      position: "right",
    },
  }
)

export interface DrawerProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof drawerVariants> {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  shouldScaleBackground?: boolean
  children: React.ReactNode
  showCloseButton?: boolean
  closeButtonClassName?: string
  closeButtonPosition?: "inside" | "outside"
  closeButton?: React.ReactNode
  overlay?: boolean
  lockScroll?: boolean
  dragToClose?: boolean
  preventScroll?: boolean
  rootClassName?: string
}

const AnimatedDrawer = React.forwardRef<HTMLDivElement, DrawerProps>(({
  className,
  position = "right",
  open = false,
  onOpenChange,
  shouldScaleBackground = true,
  children,
  showCloseButton = true,
  closeButtonClassName,
  closeButtonPosition = "inside",
  closeButton,
  overlay = true,
  lockScroll = true,
  dragToClose = true,
  preventScroll = true,
  rootClassName,
  ...props
}, ref) => {
  // Prevent body scroll when drawer is open
  React.useEffect(() => {
    if (preventScroll) {
      const originalStyle = window.getComputedStyle(document.body).overflow
      if (open) {
        document.body.style.overflow = "hidden"
      } else {
        document.body.style.overflow = originalStyle
      }
      
      return () => {
        document.body.style.overflow = originalStyle
      }
    }
  }, [open, preventScroll])

  // Handle escape key
  React.useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === "Escape" && open) {
        onOpenChange?.(false)
      }
    }
    
    window.addEventListener("keydown", handleEsc)
    return () => window.removeEventListener("keydown", handleEsc)
  }, [open, onOpenChange])

  // Handle dragging for touch gestures
  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    if (!dragToClose) return
    
    const threshold = 100 // Minimum pixels to trigger close
    
    if (position === "bottom" && info.offset.y > threshold) {
      onOpenChange?.(false)
    } else if (position === "top" && info.offset.y < -threshold) {
      onOpenChange?.(false)
    } else if (position === "left" && info.offset.x < -threshold) {
      onOpenChange?.(false)
    } else if (position === "right" && info.offset.x > threshold) {
      onOpenChange?.(false)
    }
  }

  // Animation variants based on position
  const getVariants = (): Variants => {
    switch (position) {
      case "top":
        return {
          open: { y: 0, transition: { type: "spring", damping: 25, stiffness: 300 } },
          closed: { y: "-100%", transition: { type: "spring", damping: 25, stiffness: 300 } },
        }
      case "bottom":
        return {
          open: { y: 0, transition: { type: "spring", damping: 25, stiffness: 300 } },
          closed: { y: "100%", transition: { type: "spring", damping: 25, stiffness: 300 } },
        }
      case "left":
        return {
          open: { x: 0, transition: { type: "spring", damping: 25, stiffness: 300 } },
          closed: { x: "-100%", transition: { type: "spring", damping: 25, stiffness: 300 } },
        }
      case "right":
      default:
        return {
          open: { x: 0, transition: { type: "spring", damping: 25, stiffness: 300 } },
          closed: { x: "100%", transition: { type: "spring", damping: 25, stiffness: 300 } },
        }
    }
  }

  // Size classes based on position
  const getSizeClasses = () => {
    switch (position) {
      case "top":
      case "bottom":
        return "h-auto max-h-[85vh] w-full"
      case "left":
      case "right":
        return "h-full max-w-md w-full"
      default:
        return ""
    }
  }

  // Determine drag constraints
  const getDragConstraints = () => {
    switch (position) {
      case "top":
        return { top: 0, bottom: 0 }
      case "bottom":
        return { top: 0, bottom: 0 }
      case "left":
        return { left: 0, right: 0 }
      case "right":
        return { left: 0, right: 0 }
      default:
        return {}
    }
  }

  // Default close button element
  const defaultCloseButton = (
    <button
      onClick={() => onOpenChange?.(false)}
      className={cn(
        "absolute rounded-full p-2 bg-background/80 backdrop-blur-sm text-foreground shadow-md hover:bg-background",
        closeButtonPosition === "inside" && position === "top" && "top-2 right-2",
        closeButtonPosition === "inside" && position === "bottom" && "bottom-2 right-2",
        closeButtonPosition === "inside" && position === "left" && "top-2 left-2",
        closeButtonPosition === "inside" && position === "right" && "top-2 right-2",
        closeButtonPosition === "outside" && position === "top" && "top-2 right-2 -translate-y-full",
        closeButtonPosition === "outside" && position === "bottom" && "bottom-2 right-2 translate-y-full",
        closeButtonPosition === "outside" && position === "left" && "top-2 left-2 -translate-x-full",
        closeButtonPosition === "outside" && position === "right" && "top-2 right-2 translate-x-full",
        closeButtonClassName
      )}
    >
      <X className="h-4 w-4" />
    </button>
  )

  // Different drag directions based on position
  const getDragDirection = () => {
    switch (position) {
      case "top":
      case "bottom":
        return "y"
      case "left":
      case "right":
        return "x"
      default:
        return ""
    }
  }

  return (
    <AnimatePresence>
      {open && (
        <div
          className={cn("fixed inset-0 z-50", rootClassName)}
          ref={ref}
          {...props}
        >
          {/* Backdrop with click to close */}
          {overlay && (
            <motion.div
              className="fixed inset-0 bg-background/80 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => onOpenChange?.(false)}
            />
          )}
          
          {/* Main drawer content */}
          <div className={cn(drawerVariants({ position }))}>
            <motion.div
              className={cn(
                "bg-background shadow-lg border border-border",
                getSizeClasses(),
                position === "top" && "rounded-b-xl",
                position === "bottom" && "rounded-t-xl",
                position === "left" && "rounded-r-xl",
                position === "right" && "rounded-l-xl",
                className
              )}
              initial="closed"
              animate="open"
              exit="closed"
              variants={getVariants()}
              drag={dragToClose ? getDragDirection() as "x" | "y" | false : false}
              dragConstraints={getDragConstraints()}
              onDragEnd={handleDragEnd}
            >
              {children}
              {showCloseButton && (closeButton || defaultCloseButton)}
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  )
})

AnimatedDrawer.displayName = "AnimatedDrawer"

// Helper components
const DrawerTrigger = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    onOpenChange?: (open: boolean) => void
    open?: boolean
  }
>(({ onClick, onOpenChange, open, ...props }, ref) => (
  <button
    ref={ref}
    onClick={(e) => {
      onClick?.(e)
      onOpenChange?.(!open)
    }}
    {...props}
  />
))
DrawerTrigger.displayName = "DrawerTrigger"

const DrawerContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("p-6 overflow-auto max-h-full", className)}
    {...props}
  />
))
DrawerContent.displayName = "DrawerContent"

const DrawerHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("px-6 pt-6 pb-2 sticky top-0 bg-background z-10", className)}
    {...props}
  />
))
DrawerHeader.displayName = "DrawerHeader"

const DrawerFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("px-6 py-4 flex justify-end gap-2 sticky bottom-0 bg-background border-t", className)}
    {...props}
  />
))
DrawerFooter.displayName = "DrawerFooter"

const DrawerTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h2
    ref={ref}
    className={cn("text-lg font-semibold", className)}
    {...props}
  />
))
DrawerTitle.displayName = "DrawerTitle"

const DrawerDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
DrawerDescription.displayName = "DrawerDescription"

export {
  AnimatedDrawer,
  DrawerTrigger,
  DrawerContent,
  DrawerHeader,
  DrawerFooter,
  DrawerTitle,
  DrawerDescription,
} 