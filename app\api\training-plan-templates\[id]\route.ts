// API route handler for training plan templates by ID
import { TrainingPlanTemplateByIdHandler } from "@/lib/api/training-plan-template-handler"
import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

const handler = new TrainingPlanTemplateByIdHandler()

export async function GET(req: Request, { params }: { params: { id: string } }) {
  try {
    console.log(`GET /api/training-plan-templates/${params.id} - Request received`)
    const session = await getServerSession(authOptions)
    console.log('User session:', session?.user?.email, 'Role:', session?.user?.role)

    return handler.handleGet(req, params)
  } catch (error) {
    console.error(`GET /api/training-plan-templates/${params.id} - Error:`, error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  try {
    console.log(`PUT /api/training-plan-templates/${params.id} - Request received`)
    const session = await getServerSession(authOptions)
    console.log('User session:', session?.user?.email, 'Role:', session?.user?.role)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    return handler.handlePut(req, params)
  } catch (error) {
    console.error(`PUT /api/training-plan-templates/${params.id} - Error:`, error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(req: Request, { params }: { params: { id: string } }) {
  try {
    console.log(`DELETE /api/training-plan-templates/${params.id} - Request received`)
    const session = await getServerSession(authOptions)
    console.log('User session:', session?.user?.email, 'Role:', session?.user?.role)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    return handler.handleDelete(req, params)
  } catch (error) {
    console.error(`DELETE /api/training-plan-templates/${params.id} - Error:`, error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}