"use client"

import { <PERSON>, <PERSON>, Refresh<PERSON><PERSON> } from "lucide-react"
import { useRout<PERSON> } from "next/navigation"
import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
} from "@/components/ui/card"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { 
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { toast } from "@/components/ui/use-toast"
import { trainerServiceType, trainerServiceConfigs } from "@/lib/feature-flags"

interface Trainer {
  id: string
  name: string
  email: string
  trainerSettings?: {
    id: string
    serviceType: string
    enableStore: boolean
    enableSubscriptions: boolean
    enablePremiumCoaching: boolean
  } | null
}

interface ServiceTypeManagerProps {
  trainers: Trainer[]
}

export function ServiceTypeManager({ trainers }: ServiceTypeManagerProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({})
  const [editedTrainers, setEditedTrainers] = useState<Record<string, {
    serviceType: string
    enableStore: boolean
    enableSubscriptions: boolean
    enablePremiumCoaching: boolean
  }>>({})

  // Function to handle service type change
  const handleServiceTypeChange = (trainerId: string, serviceType: string) => {
    const config = trainerServiceConfigs[serviceType as trainerServiceType]
    
    setEditedTrainers({
      ...editedTrainers,
      [trainerId]: {
        serviceType,
        ...config
      }
    })
  }

  // Function to toggle individual feature
  const toggleFeature = (trainerId: string, feature: 'enableStore' | 'enableSubscriptions' | 'enablePremiumCoaching') => {
    setEditedTrainers({
      ...editedTrainers,
      [trainerId]: {
        ...getTrainerSettings(trainerId),
        serviceType: 'custom',
        [feature]: !getTrainerSettings(trainerId)[feature]
      }
    })
  }

  // Get current settings for a trainer (either edited or original)
  const getTrainerSettings = (trainerId: string) => {
    if (editedTrainers[trainerId]) {
      return editedTrainers[trainerId]
    }
    
    const trainer = trainers.find(t => t.id === trainerId)
    
    if (trainer?.trainerSettings) {
      return {
        serviceType: trainer.trainerSettings.serviceType,
        enableStore: trainer.trainerSettings.enableStore,
        enableSubscriptions: trainer.trainerSettings.enableSubscriptions,
        enablePremiumCoaching: trainer.trainerSettings.enablePremiumCoaching
      }
    }
    
    return {
      serviceType: 'full-service',
      enableStore: true,
      enableSubscriptions: true,
      enablePremiumCoaching: true
    }
  }

  // Function to save changes
  const saveChanges = async (trainerId: string) => {
    if (!editedTrainers[trainerId]) return
    
    setIsLoading({ ...isLoading, [trainerId]: true })
    
    try {
      const response = await fetch(`/api/trainers/${trainerId}/service-settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editedTrainers[trainerId]),
      })
      
      if (!response.ok) {
        throw new Error('Failed to update service settings')
      }
      
      // Remove from edited trainers on success
      const newEditedTrainers = { ...editedTrainers }
      delete newEditedTrainers[trainerId]
      setEditedTrainers(newEditedTrainers)
      
      toast({
        title: "Success",
        description: "Service settings updated successfully",
      })
      
      router.refresh()
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update service settings",
      })
    } finally {
      setIsLoading({ ...isLoading, [trainerId]: false })
    }
  }

  // Function to get a feature indicator
  const getFeatureIndicator = (enabled: boolean) => {
    return enabled 
      ? <Check className="h-4 w-4 text-green-500" /> 
      : <X className="h-4 w-4 text-red-500" />
  }

  // Function to check if trainer has unsaved changes
  const hasUnsavedChanges = (trainerId: string) => {
    return !!editedTrainers[trainerId]
  }

  return (
    <div className="space-y-6">
      <Table>
        <TableCaption>List of trainers and their service configurations</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[250px]">Trainer</TableHead>
            <TableHead>Service Type</TableHead>
            <TableHead className="text-center">Store</TableHead>
            <TableHead className="text-center">Subscriptions</TableHead>
            <TableHead className="text-center">Premium Coaching</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {trainers.map((trainer) => {
            const settings = getTrainerSettings(trainer.id)
            const isEdited = hasUnsavedChanges(trainer.id)
            const isProcessing = isLoading[trainer.id]
            
            return (
              <TableRow key={trainer.id} className={isEdited ? "bg-muted/30" : ""}>
                <TableCell className="font-medium">
                  <div>{trainer.name}</div>
                  <div className="text-xs text-muted-foreground">{trainer.email}</div>
                </TableCell>
                <TableCell>
                  <Select 
                    value={settings.serviceType} 
                    onValueChange={(value) => handleServiceTypeChange(trainer.id, value)}
                    disabled={isProcessing}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select service type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full-service">Full Service</SelectItem>
                      <SelectItem value="products-only">Products Only</SelectItem>
                      <SelectItem value="subscriptions-only">Subscriptions Only</SelectItem>
                      <SelectItem value="coaching-only">Coaching Only</SelectItem>
                      <SelectItem value="custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                  {isEdited && (
                    <Badge variant="outline" className="ml-2 text-xs">
                      Edited
                    </Badge>
                  )}
                </TableCell>
                <TableCell className="text-center">
                  <Switch 
                    checked={settings.enableStore} 
                    onCheckedChange={() => toggleFeature(trainer.id, 'enableStore')}
                    disabled={isProcessing}
                  />
                </TableCell>
                <TableCell className="text-center">
                  <Switch 
                    checked={settings.enableSubscriptions} 
                    onCheckedChange={() => toggleFeature(trainer.id, 'enableSubscriptions')}
                    disabled={isProcessing}
                  />
                </TableCell>
                <TableCell className="text-center">
                  <Switch 
                    checked={settings.enablePremiumCoaching} 
                    onCheckedChange={() => toggleFeature(trainer.id, 'enablePremiumCoaching')}
                    disabled={isProcessing}
                  />
                </TableCell>
                <TableCell className="text-right">
                  {isEdited && (
                    <Button 
                      variant="default" 
                      size="sm" 
                      onClick={() => saveChanges(trainer.id)}
                      disabled={isProcessing}
                    >
                      {isProcessing ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : 'Save'}
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
      
      {trainers.length === 0 && (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground">
              No trainers available to configure.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 