'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card"
import { Calendar } from "lucide-react"
import { format } from "date-fns"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface Call {
  id: string
  clientName: string
  date: Date
  joinLink?: string
}

interface CalendarViewProps {
  calls: Call[]
}

export function CalendarView({ calls }: CalendarViewProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Upcoming 1:1 Calls</CardTitle>
        <CardDescription>Your scheduled coaching sessions</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {calls.map((call) => (
            <div key={call.id} className="flex items-center justify-between p-4 rounded-lg border">
              <div className="flex items-center gap-4">
                <div className="p-2 bg-primary/10 rounded-full">
                  <Calendar className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-medium">{call.clientName}</h4>
                  <p className="text-sm text-muted-foreground">
                    {format(call.date, "MMMM d, yyyy 'at' h:mm a")}
                  </p>
                </div>
              </div>
              {call.joinLink && (
                <Button asChild variant="outline">
                  <Link href={call.joinLink} target="_blank">
                    Join Call
                  </Link>
                </Button>
              )}
            </div>
          ))}
          {calls.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No upcoming calls scheduled
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 