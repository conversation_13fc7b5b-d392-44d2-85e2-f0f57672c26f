import { db } from "../lib/db"

async function main() {
  try {
    // Find a test user
    const user = await db.user.findFirst({
      where: {
        email: "<EMAIL>",
      },
    })

    if (!user) {
      console.log("Test user not found. Please create a test user first.")
      process.exit(1)
    }

    // Generate 30 days of sample progress data
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 30)

    const sampleData = Array.from({ length: 30 }, (_, i) => {
      const date = new Date(startDate)
      date.setDate(date.getDate() + i)

      // Generate realistic weight data with small variations
      const baseWeight = 180 // Starting weight
      const weightVariation = Math.sin(i * 0.2) * 2 // Creates a slight wave pattern
      const weight = baseWeight - (i * 0.2) + weightVariation // Gradual weight loss with variations

      // Generate realistic body fat data
      const baseBodyFat = 20 // Starting body fat percentage
      const bodyFatVariation = Math.sin(i * 0.2) * 0.5
      const bodyFat = baseBodyFat - (i * 0.1) + bodyFatVariation // Gradual reduction with variations

      // Sample measurements
      const measurements = {
        chest: Math.round(42 - (i * 0.05) + (Math.random() * 0.2)),
        waist: Math.round(34 - (i * 0.08) + (Math.random() * 0.2)),
        hips: Math.round(38 - (i * 0.03) + (Math.random() * 0.2)),
        arms: Math.round(14 - (i * 0.02) + (Math.random() * 0.1)),
      }

      return {
        clientId: user.id,
        date,
        weight: Number(weight.toFixed(1)),
        bodyFat: Number(bodyFat.toFixed(1)),
        measurements,
      }
    })

    // Delete existing progress data for the user
    await db.progress.deleteMany({
      where: {
        clientId: user.id,
      },
    })

    // Insert sample data
    for (const data of sampleData) {
      await db.progress.create({
        data,
      })
    }

    console.log("Sample progress data created successfully!")
  } catch (error) {
    console.error("Error seeding progress data:", error)
    process.exit(1)
  } finally {
    await db.$disconnect()
  }
}

main() 