"use client"

import { Bell, LogOut, <PERSON>u, User, Library, ExternalLink } from "lucide-react"
import { useRouter } from "next/navigation"
import { signOut, useSession } from "next-auth/react"
import { useState, useEffect } from "react"
import { ModeToggle } from "@/components/mode-toggle"
import { CartDropdown } from "@/components/shop/cart-dropdown"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet"
import { useToast } from "@/components/ui/use-toast"

// Simple component to render role switcher buttons with client-side paths
function DevRoleSwitcher({ userRole }: { userRole?: string }) {
  const [currentPath, setCurrentPath] = useState<string>('/dashboard')

  // Set the current path after mount to avoid window reference during SSR
  useEffect(() => {
    setCurrentPath(window.location.pathname)
  }, [])

  return (
    <div className="flex items-center gap-2 px-3 py-1 bg-yellow-100 dark:bg-yellow-900 rounded-md">
      <Badge variant={userRole === "admin" ? "default" : "outline"}>
        <a href={`/api/dev/switch-role?role=admin&returnTo=${encodeURIComponent(currentPath)}`} className="px-2">Admin</a>
      </Badge>
      <Badge variant={userRole === "trainer" ? "default" : "outline"}>
        <a href={`/api/dev/switch-role?role=trainer&returnTo=${encodeURIComponent(currentPath)}`} className="px-2">Trainer</a>
      </Badge>
      <Badge variant={userRole === "client" ? "default" : "outline"}>
        <a href={`/api/dev/switch-role?role=client&returnTo=${encodeURIComponent(currentPath)}`} className="px-2">Client</a>
      </Badge>
    </div>
  )
}

type User = {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  role: "admin" | "trainer" | "client";
};

interface DashboardHeaderProps {
  user: User;
  effectiveRole: string;
}

export function DashboardHeader({ user, effectiveRole }: DashboardHeaderProps) {
  const router = useRouter()
  const { data: session } = useSession()
  const { toast } = useToast()
  const [notifications, setNotifications] = useState([])

  const handleSignOut = async () => {
    // Clear any dev role override
    if (typeof window !== 'undefined') {
      localStorage.removeItem("dev_override_role")
    }
    await signOut({ redirect: false })
    router.push("/login")
  }

  const fetchNotifications = async () => {
    try {
      const response = await fetch("/api/notifications")
      if (!response.ok) throw new Error("Failed to fetch notifications")
      const data = await response.json()
      setNotifications(data)
    } catch (error) {
      toast({
        title: "Error",
        description: "Could not load notifications",
        variant: "destructive",
      })
    }
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 hidden md:flex">
          <a className="mr-6 flex items-center space-x-2" href="/">
            <span className="hidden font-bold sm:inline-block">
              Fitness Platform
            </span>
          </a>
          {process.env.NODE_ENV === "development" && (effectiveRole === 'admin' || effectiveRole === 'trainer') && <DevRoleSwitcher userRole={effectiveRole} />}
        </div>
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="w-full flex-1 md:w-auto md:flex-none">
            {/* Add search or other controls here */}
          </div>
          <nav className="flex items-center space-x-2">
            <CartDropdown />
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              asChild
            >
              <a href="/dashboard/library">
                <Library className="h-4 w-4" />
                <span className="sr-only">My Library</span>
              </a>
            </Button>
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  className="relative h-8 w-8 rounded-full"
                  onClick={fetchNotifications}
                >
                  <Bell className="h-4 w-4" />
                  {notifications.length > 0 && (
                    <span className="absolute -right-1 -top-1 h-4 w-4 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
                      {notifications.length}
                    </span>
                  )}
                  <span className="sr-only">Notifications</span>
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Notifications</SheetTitle>
                </SheetHeader>
                <ScrollArea className="h-[calc(100vh-8rem)] mt-4">
                  {notifications.length > 0 ? (
                    <div className="space-y-4">
                      {notifications.map((notification: any) => (
                        <div
                          key={notification.id}
                          className="flex flex-col space-y-1 border-b pb-4 last:border-0"
                        >
                          <p className="text-sm font-medium">{notification.title}</p>
                          <p className="text-sm text-muted-foreground">
                            {notification.message}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(notification.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-muted-foreground py-4">
                      No new notifications
                    </p>
                  )}
                </ScrollArea>
              </SheetContent>
            </Sheet>
            <ModeToggle />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="relative h-8 w-8 rounded-full"
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={user?.image || ""}
                      alt={user?.name || ""}
                    />
                    <AvatarFallback>
                      {user?.name?.[0]?.toUpperCase() || "U"}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {user?.name}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user?.email}
                    </p>
                    {effectiveRole && (
                      <p className="text-xs mt-1 font-semibold capitalize text-primary">
                        Role: {effectiveRole}
                      </p>
                    )}
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {session?.user?.role === "trainer" && (
                  <DropdownMenuItem onClick={() => router.push(`/${session.user.name?.toLowerCase().replace(/\s+/g, "-") || session.user.email?.split("@")[0]}`)}>
                    <ExternalLink className="mr-2 h-4 w-4" />
                    <span>Public Profile</span>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={() => router.push("/dashboard/trainer-profile")}>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push("/settings")}>
                  <Menu className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </nav>
        </div>
      </div>
    </header>
  )
}

