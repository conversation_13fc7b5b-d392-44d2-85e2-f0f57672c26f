generator client {
  provider = "prisma-client-js"
  seed     = "node prisma/seed.js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                        String                 @id @default(cuid())
  email                     String                 @unique
  name                      String?
  password                  String
  role                      String                 @default("user")
  status                    String                 @default("active")
  avatarUrl                 String?
  slug                      String?                @unique // URL slug for trainer landing page
  bio                       String?                // Trainer bio/description
  socialLinks               Json?                  // Social media links
  themeSettings             Json?                  // Additional theme settings
  emailVerified             DateTime?
  createdAt                 DateTime               @default(now())
  updatedAt                 DateTime               @updatedAt
  lastLoginAt               DateTime?
  preferences               Json?
  clientProfile             ClientProfile?
  conversationsStarted      Conversation[]         @relation("User1Conversations")
  conversationsJoined       Conversation[]         @relation("User2Conversations")
  createdExercises          Exercise[]             @relation("CreatedExercises")
  dietPlans                 DietPlan[]
  trainerSettings           trainerSettings?
  sentMessages              Message[]              @relation("SentMessages")
  receivedMessages          Message[]              @relation("ReceivedMessages")
  notifications             Notification[]         @relation("UserNotifications")
  progress                  Progress[]
  workoutLogs              WorkoutLog[]
  nutritionLogs             NutritionLog[]
  trainerSupportTickets     SupportTicket[]        @relation("TrainerSupport")
  userSupportTickets        SupportTicket[]        @relation("UserSupportTickets")
  ticketResponses           TicketResponse[]
  trainerCoachingRelationships CoachingRelationship[] @relation("TrainerCoachingRelationships")
  clientCoachingRelationships CoachingRelationship[] @relation("ClientCoachingRelationships")
  calendlyUserId           String?                    // Trainer's Calendly user ID
  trainerProfile            TrainerProfile?
  clientDietPlanAssignments DietPlanAssignment[]   @relation("ClientDietPlanAssignments")
  assignedDietPlans         DietPlanAssignment[]   @relation("AssignedDietPlans")
  trainingPlanTemplates     TrainingPlanTemplate[]
  clientTrainingPlans       TrainingPlanTemplate[] @relation("ClientTrainingPlans")
  passwordChangeRequired    Boolean                @default(false)
  measurements             Measurement[]
  exerciseLogs             ExerciseLog[]
  streaks                  Streak[]
  achievements             Achievement[]

  @@index([email])
  @@index([role])
  @@index([status])
  @@index([slug])
  @@index([createdAt])
  @@index([name])
}

model Exercise {
  id               String   @id @default(cuid())
  name             String
  sets             Int?
  reps             Int?
  workoutId        String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  description      String?
  duration         Int?
  restTime         Int?
  videoUrl         String?
  muscleGroup      String?
  order            Int      @default(0)
  type             String?
  thumbnailUrl     String?
  calories         Int?
  createdBy        String?
  difficulty       String?
  equipment        String?
  isTemplate       Boolean  @default(false)
  weight           Int?
  parentExerciseId String?  // Reference to parent exercise if this is an alternative
  isAlternative    Boolean  @default(false) // Flag to indicate if this is an alternative exercise
  creator          User?    @relation("CreatedExercises", fields: [createdBy], references: [id])

  @@index([type])
  @@index([muscleGroup])
  @@index([workoutId])
  @@index([order])
  @@index([isTemplate])
  @@index([createdBy])
  @@index([parentExerciseId])
  @@index([isAlternative])
}

model DietPlan {
  id          String               @id @default(cuid())
  title       String
  description String?
  trainerId   String
  calories    Int?
  protein     Int?
  carbs       Int?
  fat         Int?
  notes       String?
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  trainer     User                 @relation(fields: [trainerId], references: [id])
  assignments DietPlanAssignment[]
  meals       Meal[]
}

model Meal {
  id              String   @id @default(cuid())
  name            String
  description     String?
  calories        Int?
  protein         Int?
  carbs           Int?
  fat             Int?
  timeOfDay       String?
  foodSuggestions String?
  dietPlanId      String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  dietPlan        DietPlan @relation(fields: [dietPlanId], references: [id], onDelete: Cascade)
}

model Progress {
  id        String   @id @default(cuid())
  weight    Float?
  bodyFat   Float?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  date      DateTime
  notes     String?
  userId    String
  user      User     @relation(fields: [userId], references: [id])
}

model Message {
  id             String       @id @default(cuid())
  senderId       String
  receiverId     String
  content        String
  createdAt      DateTime     @default(now())
  conversationId String
  updatedAt      DateTime     @updatedAt
  read           Boolean      @default(false)
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  receiver       User         @relation("ReceivedMessages", fields: [receiverId], references: [id], onDelete: Cascade)
  sender         User         @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)

  @@index([conversationId])
  @@index([senderId])
  @@index([receiverId])
  @@index([createdAt])
  @@index([read])
  @@index([conversationId, createdAt])
}

model SupportTicket {
  id          String           @id @default(cuid())
  status      String
  userId      String
  trainerId   String
  description String
  title       String
  trainer     User             @relation("TrainerSupport", fields: [trainerId], references: [id])
  user        User             @relation("UserSupportTickets", fields: [userId], references: [id])
  responses   TicketResponse[]
}

model TicketResponse {
  id              String        @id @default(cuid())
  message         String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  supportTicketId String
  userId          String
  isFromTrainer   Boolean       @default(false)
  supportTicket   SupportTicket @relation(fields: [supportTicketId], references: [id], onDelete: Cascade)
  user            User          @relation(fields: [userId], references: [id])
}

model trainerSettings {
  id                    String   @id @default(cuid())
  userId                String   @unique
  serviceType           String   @default("premium-coaching")
  enablePremiumCoaching Boolean  @default(true)
  // Landing page settings
  bannerImageUrl        String?  // URL for the landing page background image
  logoUrl               String?  // URL for the trainer's logo
  primaryColor          String?  @default("#3b82f6") // Primary brand color
  secondaryColor        String?  @default("#6366f1") // Secondary brand color
  fontFamily            String?  @default("Inter, sans-serif") // Font family
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Conversation {
  id            String    @id @default(cuid())
  user1Id       String
  user2Id       String
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastMessageAt DateTime?
  user1         User      @relation("User1Conversations", fields: [user1Id], references: [id], onDelete: Cascade)
  user2         User      @relation("User2Conversations", fields: [user2Id], references: [id], onDelete: Cascade)
  messages      Message[]

  @@index([user1Id])
  @@index([user2Id])
  @@index([lastMessageAt])
  @@index([user1Id, user2Id])
}

model ClientProfile {
  id                String          @id @default(cuid())
  userId            String          @unique
  assignedTrainerId String?
  weightTarget      Float?          // Target weight in lbs
  bodyFatTarget     Float?          // Target body fat percentage
  calorieTarget     Int?            // Daily calorie target
  proteinTarget     Float?          // Daily protein target in grams
  carbTarget        Float?          // Daily carb target in grams
  fatTarget         Float?          // Daily fat target in grams
  assignedTrainer   TrainerProfile? @relation("ClientAssignedTrainer", fields: [assignedTrainerId], references: [id])
  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([assignedTrainerId])
}

model TrainerProfile {
  id      String          @id @default(cuid())
  userId  String          @unique
  clients ClientProfile[] @relation("ClientAssignedTrainer")
  user    User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model DietPlanAssignment {
  id           String    @id @default(cuid())
  clientId     String
  dietPlanId   String
  assignedById String?
  startDate    DateTime
  endDate      DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  assignedBy   User?     @relation("AssignedDietPlans", fields: [assignedById], references: [id])
  client       User      @relation("ClientDietPlanAssignments", fields: [clientId], references: [id], onDelete: Cascade)
  dietPlan     DietPlan  @relation(fields: [dietPlanId], references: [id], onDelete: Cascade)
}

model TrainingPlanTemplate {
  id          String   @id @default(cuid())
  title       String
  description String?
  difficulty  String?
  type        String   @default("template") // "template" or "personalized"
  clientId    String?  // Null for templates, client ID for personalized plans
  trainerId   String
  weeks       Json
  featured    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  trainer     User     @relation(fields: [trainerId], references: [id])
  client      User?    @relation("ClientTrainingPlans", fields: [clientId], references: [id])

  @@index([trainerId])
  @@index([clientId])
}

model TrainerInquiry {
  id         String   @id @default(cuid())
  name       String
  email      String
  message    String
  status     String   @default("new")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  adminNotes String?

  @@index([status])
  @@index([email])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime
  createdAt  DateTime @default(now())
  id         String   @id @default(cuid())
  updatedAt  DateTime @updatedAt

  @@unique([identifier, token])
  @@index([expires])
}

model PasswordResetToken {
  id         String   @id @default(cuid())
  identifier String
  token      String   @unique
  expires    DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([identifier, token])
  @@index([expires])
}

model CoachingRelationship {
  id        String   @id @default(cuid())
  trainerId String
  clientId  String
  status    String   @default("active")
  startDate DateTime @default(now())
  endDate   DateTime?
  expirationDate DateTime  // New field for subscription expiration
  plan      String?
  notes     String?
  monthlyFee Decimal @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  trainer   User     @relation("TrainerCoachingRelationships", fields: [trainerId], references: [id])
  client    User     @relation("ClientCoachingRelationships", fields: [clientId], references: [id])

  @@index([trainerId])
  @@index([clientId])
  @@index([trainerId, clientId])
  @@index([status])
  @@index([startDate])
  @@index([endDate])
  @@index([expirationDate])  // Add index for expiration date
}

model NutritionLog {
  id        String    @id @default(cuid())
  clientId  String
  date      DateTime
  mealType  String    // breakfast, lunch, dinner, snack
  name      String
  calories  Int?
  protein   Float?
  carbs     Float?
  fat       Float?
  notes     String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  client    User      @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@index([clientId])
  @@index([date])
}

model Measurement {
  id            String    @id @default(cuid())
  userId        String
  date          DateTime  @default(now())
  weight        Float?
  bodyFat       Float?
  waist         Float?
  chest         Float?
  arms          Float?
  thighs        Float?
  notes         String?
  frontPhotoUrl String?
  sidePhotoUrl  String?
  backPhotoUrl  String?
  // Health metrics
  sleepHours    Float?    // Hours of sleep
  stressLevel   Int?      // Stress level (1-10)
  coffeeCount   Int?      // Cups of coffee per day
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([date])
}

model ExerciseLog {
  id           String    @id @default(cuid())
  userId       String
  exerciseName String
  sets         Int?
  reps         Int?
  weight       Float?
  duration     Int?
  notes        String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  workoutLogId String?
  workoutLog   WorkoutLog? @relation(fields: [workoutLogId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([exerciseName])
  @@index([workoutLogId])
}

model WorkoutLog {
  id           String    @id @default(cuid())
  clientId     String
  workoutId    String
  date         DateTime
  duration     Int?
  notes        String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  exercises    WorkoutLogExercise[]
  client       User      @relation(fields: [clientId], references: [id], onDelete: Cascade)
  exerciseLogs ExerciseLog[]

  @@index([clientId])
  @@index([workoutId])
  @@index([date])
}

model WorkoutLogExercise {
  id           String    @id @default(cuid())
  workoutLogId String
  exerciseName String
  sets         Int?
  reps         Int?
  weight       Float?
  duration     Int?
  notes        String?
  rpe          Int?
  rir          Int?
  completed    Boolean   @default(true)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  workoutLog   WorkoutLog @relation(fields: [workoutLogId], references: [id], onDelete: Cascade)

  @@index([workoutLogId])
}

model Streak {
  id           String    @id @default(cuid())
  userId       String
  type         String    // nutrition, measurement, sleep, etc.
  currentCount Int       @default(0)
  longestCount Int       @default(0)
  lastLoggedAt DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, type])
  @@index([userId])
  @@index([type])
}

model Achievement {
  id          String    @id @default(cuid())
  userId      String
  type        String    // streak, milestone, etc.
  name        String
  description String
  earnedAt    DateTime  @default(now())
  iconName    String?   // For frontend icon display
  level       Int       @default(1) // For tiered achievements
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([type])
}

model Log {
  id        String   @id @default(cuid())
  timestamp DateTime @default(now())
  level     String   // e.g., "INFO", "WARNING", "ERROR"
  message   String   @db.Text
  service   String?  // Optional: Service/module where the log originated
  details   Json?    // Optional: Any additional JSON details/context

  @@index([timestamp])
  @@index([level])
  @@index([service])
}

model Notification {
  id          String   @id @default(cuid())
  userId      String
  title       String
  message     String
  type        String   // e.g., "message", "system"
  read        Boolean  @default(false)
  actionLink  String?  // Link to navigate to when notification is clicked
  sourceId    String?  // ID of the related entity (e.g., conversationId, messageId)
  sourceType  String?  // Type of the related entity (e.g., "conversation", "message")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([read])
  @@index([type])
  @@index([createdAt])
}