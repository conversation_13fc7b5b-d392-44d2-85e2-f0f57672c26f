// Optimized data fetching utility that implements caching, SWR pattern, and retries

const CACHE_PREFIX = 'app-data-cache-v1:'
const DEFAULT_CACHE_TIME = 5 * 60 * 1000 // 5 minutes
const DEFAULT_STALE_TIME = 3 * 60 * 1000 // 3 minutes

type FetchOptions = {
  headers?: HeadersInit
  method?: string
  body?: BodyInit
  credentials?: RequestCredentials
  mode?: RequestMode
  cache?: RequestCache
  signal?: AbortSignal
}

type CacheOptions = {
  cacheTime?: number // How long to cache in milliseconds
  staleTime?: number // When to consider data stale and trigger background fetch
  retries?: number // Number of retries on failure
  retryDelay?: number // Base delay between retries (will use exponential backoff)
  dedupingInterval?: number // Prevent duplicate requests within this time window
  localStorageCache?: boolean // Whether to persist in localStorage
  forceRefresh?: boolean // Bypass cache and force fresh data
}

interface CacheEntry<T> {
  data: T
  timestamp: number
}

// Keep in-memory cache for better performance
const memoryCache = new Map<string, CacheEntry<any>>()
const pendingRequests = new Map<string, Promise<any>>()
const dedupingTimers = new Map<string, number>()

/**
 * Optimized data fetching utility that implements:
 * - In-memory caching with expiration
 * - Optional localStorage persistence
 * - Stale-while-revalidate pattern
 * - Request deduping
 * - Retry logic with exponential backoff
 * - Background fetches for stale data
 */
export async function fetchWithCache<T = any>(
  url: string,
  fetchOptions: FetchOptions = {},
  cacheOptions: CacheOptions = {}
): Promise<T> {
  const {
    cacheTime = DEFAULT_CACHE_TIME,
    staleTime = DEFAULT_STALE_TIME,
    retries = 3,
    retryDelay = 300,
    dedupingInterval = 500,
    localStorageCache = false,
    forceRefresh = false,
  } = cacheOptions

  const cacheKey = `${CACHE_PREFIX}${url}:${JSON.stringify(fetchOptions)}`

  // Determine if we should use a cached version or fetch fresh data
  if (!forceRefresh) {
    // Check for pending requests to avoid duplicate fetches
    if (pendingRequests.has(cacheKey)) {
      return pendingRequests.get(cacheKey)!
    }

    // Check memory cache first for better performance
    const cachedEntry = memoryCache.get(cacheKey)
    
    if (cachedEntry) {
      const now = Date.now()
      const isStale = now - cachedEntry.timestamp > staleTime
      
      // If data is still fresh, return it immediately
      if (!isStale) {
        return cachedEntry.data
      }
      
      // If data is stale but not expired, trigger background refresh and return stale data
      if (now - cachedEntry.timestamp < cacheTime) {
        // Only trigger a background refresh if we haven't recently done so
        if (!dedupingTimers.has(cacheKey)) {
          const timerId = window.setTimeout(() => {
            dedupingTimers.delete(cacheKey)
            // Refresh in background without awaiting
            fetchWithCache(url, fetchOptions, { 
              ...cacheOptions, 
              forceRefresh: true 
            }).catch(() => {
              // Silent catch - background refresh can fail silently
            })
          }, dedupingInterval)
          
          dedupingTimers.set(cacheKey, timerId)
        }
        
        return cachedEntry.data
      }
    }
    
    // Check localStorage cache if enabled
    if (localStorageCache && typeof window !== 'undefined') {
      try {
        const storedCache = localStorage.getItem(cacheKey)
        
        if (storedCache) {
          const parsedCache: CacheEntry<T> = JSON.parse(storedCache)
          const now = Date.now()
          
          // Add to memory cache for faster access next time
          memoryCache.set(cacheKey, parsedCache)
          
          // Similar logic as memory cache
          if (now - parsedCache.timestamp < staleTime) {
            return parsedCache.data
          }
          
          if (now - parsedCache.timestamp < cacheTime) {
            // Background refresh
            if (!dedupingTimers.has(cacheKey)) {
              const timerId = window.setTimeout(() => {
                dedupingTimers.delete(cacheKey)
                fetchWithCache(url, fetchOptions, { 
                  ...cacheOptions, 
                  forceRefresh: true 
                }).catch(() => {})
              }, dedupingInterval)
              
              dedupingTimers.set(cacheKey, timerId)
            }
            
            return parsedCache.data
          }
        }
      } catch (error) {
        // If localStorage fails, continue to fetch
        console.error('Error accessing localStorage:', error)
      }
    }
  }

  // If we get here, we need to fetch fresh data
  const fetchData = async (): Promise<T> => {
    let lastError: Error | null = null
    let attempts = 0
    
    while (attempts <= retries) {
      try {
        const response = await fetch(url, {
          ...fetchOptions,
          // Ensure headers are properly merged
          headers: {
            ...(fetchOptions.headers || {}),
          },
        })
        
        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}: ${response.statusText}`)
        }
        
        const data = await response.json()
        
        // Cache successful result
        const cacheEntry: CacheEntry<T> = {
          data,
          timestamp: Date.now(),
        }
        
        // Update memory cache
        memoryCache.set(cacheKey, cacheEntry)
        
        // Update localStorage if enabled
        if (localStorageCache && typeof window !== 'undefined') {
          try {
            localStorage.setItem(cacheKey, JSON.stringify(cacheEntry))
          } catch (error) {
            console.error('Error saving to localStorage:', error)
          }
        }
        
        return data
      } catch (error) {
        lastError = error as Error
        attempts++
        
        if (attempts <= retries) {
          // Exponential backoff with jitter
          const delay = retryDelay * Math.pow(2, attempts - 1) * (0.5 + Math.random() * 0.5)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }
    
    throw lastError || new Error('Failed to fetch data after retries')
  }
  
  // Create a promise for this request and store it to dedupe concurrent calls
  const fetchPromise = fetchData()
  pendingRequests.set(cacheKey, fetchPromise)
  
  try {
    return await fetchPromise
  } finally {
    // Clean up the pending request reference
    pendingRequests.delete(cacheKey)
  }
}

/**
 * Clear the entire cache or a specific entry
 */
export function clearCache(specificUrl?: string, specificOptions?: FetchOptions): void {
  if (specificUrl) {
    const cacheKey = `${CACHE_PREFIX}${specificUrl}:${JSON.stringify(specificOptions || {})}`
    memoryCache.delete(cacheKey)
    
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem(cacheKey)
      } catch (e) {
        console.error('Error removing from localStorage:', e)
      }
    }
  } else {
    // Clear all cache
    memoryCache.clear()
    
    if (typeof window !== 'undefined') {
      try {
        const keysToRemove: string[] = []
        
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith(CACHE_PREFIX)) {
            keysToRemove.push(key)
          }
        }
        
        keysToRemove.forEach(key => localStorage.removeItem(key))
      } catch (e) {
        console.error('Error clearing localStorage cache:', e)
      }
    }
  }
}

/**
 * Prefetch data and cache it for later use
 */
export function prefetchData(
  url: string,
  fetchOptions: FetchOptions = {},
  cacheOptions: CacheOptions = {}
): void {
  if (typeof window !== 'undefined') {
    // Use requestIdleCallback if available, or setTimeout as fallback
    const scheduleLoad = 
      'requestIdleCallback' in window
        ? (window as any).requestIdleCallback
        : (cb: Function) => setTimeout(cb, 1)

    scheduleLoad(() => {
      fetchWithCache(url, fetchOptions, cacheOptions).catch(() => {
        // Silent catch - prefetch can fail silently
      })
    })
  }
} 