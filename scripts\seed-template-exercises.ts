import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const templateExercises = [
  {
    name: "Squat",
    description: "Basic squat exercise targeting lower body muscles",
    sets: 3,
    reps: 12,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=SW_C1A-rejs",
    type: "strength",
    difficulty: "beginner"
  },
  {
    name: "Push-up",
    description: "Classic push-up exercise for chest and arms",
    sets: 3,
    reps: 10,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=IODxDxX7oi4",
    type: "strength",
    difficulty: "beginner"
  },
  {
    name: "Pull-up",
    description: "Upper body pulling exercise",
    sets: 3,
    reps: 8,
    restTime: 90,
    videoUrl: "https://www.youtube.com/watch?v=eGo4IYlbE5g",
    type: "strength",
    difficulty: "intermediate"
  },
  {
    name: "Deadlift",
    description: "Compound exercise targeting back and legs",
    sets: 3,
    reps: 8,
    restTime: 90,
    videoUrl: "https://www.youtube.com/watch?v=op9kVnSso6Q",
    type: "strength",
    difficulty: "intermediate"
  },
  {
    name: "Running",
    description: "Cardio exercise for endurance",
    duration: 30,
    videoUrl: "https://www.youtube.com/watch?v=5OMTijvTy_s",
    type: "cardio",
    difficulty: "beginner"
  },
  {
    name: "Plank",
    description: "Core strengthening exercise",
    duration: 60,
    restTime: 30,
    videoUrl: "https://www.youtube.com/watch?v=pSHjTRCQxIw",
    type: "strength",
    difficulty: "beginner"
  },
  {
    name: "Burpee",
    description: "Full body exercise combining strength and cardio",
    sets: 3,
    reps: 10,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=TU8QYVW0gDU",
    type: "cardio",
    difficulty: "intermediate"
  },
  {
    name: "Lunges",
    description: "Lower body exercise for legs and glutes",
    sets: 3,
    reps: 12,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=3XDriUn0udo",
    type: "strength",
    difficulty: "beginner"
  },
  {
    name: "Mountain Climbers",
    description: "Dynamic core and cardio exercise",
    duration: 45,
    restTime: 30,
    videoUrl: "https://www.youtube.com/watch?v=nmwgirgXLYM",
    type: "cardio",
    difficulty: "intermediate"
  },
  {
    name: "Dumbbell Row",
    description: "Back strengthening exercise",
    sets: 3,
    reps: 12,
    restTime: 60,
    videoUrl: "https://www.youtube.com/watch?v=pYcpY20QaE8",
    type: "strength",
    difficulty: "beginner"
  }
]

async function seedTemplateExercises() {
  try {
    // Delete existing template exercises
    await prisma.templateExercise.deleteMany()

    // Create new template exercises
    for (const exercise of templateExercises) {
      await prisma.templateExercise.create({
        data: exercise
      })
    }

    console.log('Successfully seeded template exercises')
  } catch (error) {
    console.error('Error seeding template exercises:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedTemplateExercises() 