"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2 } from "lucide-react"

interface RequestCoachingDialogProps {
  trainerId?: string
  trainerName?: string
  trigger: React.ReactNode
  requestType: "direct" | "dashboard" | "program"
  programId?: string
  programName?: string
}

export function RequestCoachingDialog({
  trainerId,
  trainerName = "a coach",
  trigger,
  requestType,
  programId,
  programName
}: RequestCoachingDialogProps) {
  const router = useRouter()
  const [isOpen, setIsOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [goals, setGoals] = useState("")
  const [experience, setExperience] = useState("")
  const [message, setMessage] = useState("")
  const [availability, setAvailability] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1500))

      // In a real app, this would be an API call to submit the coaching request
      // const response = await fetch('/api/coaching/request', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     trainerId,
      //     goals,
      //     experience,
      //     message,
      //     availability,
      //     requestType,
      //     programId,
      //   }),
      // })

      // if (!response.ok) throw new Error('Failed to submit request')

      toast.success(`Application for 1:1 coaching submitted!`, {
        description: `Your request has been sent to ${trainerName}.`
      })

      // In development, set a cookie to simulate a coaching request
      if (process.env.NODE_ENV === 'development') {
        // Store request details in localStorage for persistence
        const requestData = {
          trainerId,
          trainerName,
          requestType,
          programId,
          programName,
          goals,
          experience,
          message,
          availability,
          timestamp: new Date().toISOString(),
          status: 'pending'
        };

        // Get existing requests or initialize empty array
        const existingRequests = JSON.parse(localStorage.getItem('coachingRequests') || '[]');
        existingRequests.push(requestData);
        localStorage.setItem('coachingRequests', JSON.stringify(existingRequests));

        // Set cookie to indicate active coaching request
        document.cookie = 'dev_coaching_request=true; path=/';
      }

      // Close the dialog
      setIsOpen(false)

      // Reset form
      setGoals("")
      setExperience("")
      setMessage("")
      setAvailability("")

      // Redirect to the my-coaching page
      router.push('/dashboard/my-coaching')
    } catch (error) {
      console.error('Coaching request error:', error)
      toast.error("Failed to submit request", {
        description: "Please try again later."
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Request 1:1 Coaching</DialogTitle>
          <DialogDescription>
            {requestType === "direct" && `Apply for personalized coaching with ${trainerName}.`}
            {requestType === "dashboard" && "Apply for personalized coaching with one of our expert trainers."}
            {requestType === "program" && `Request coaching support for your ${programName || "program"}.`}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 pt-4">
          {requestType === "dashboard" && (
            <div className="space-y-2">
              <Label htmlFor="trainer">Preferred Trainer</Label>
              <Input
                id="trainer"
                placeholder="Enter trainer name (optional)"
              />
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="goals">Your Fitness Goals</Label>
            <Textarea
              id="goals"
              placeholder="What are you hoping to achieve?"
              value={goals}
              onChange={(e) => setGoals(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="experience">Fitness Experience</Label>
            <Textarea
              id="experience"
              placeholder="Describe your current fitness level and experience"
              value={experience}
              onChange={(e) => setExperience(e.target.value)}
              required
            />
          </div>

          {requestType === "program" && (
            <div className="space-y-2">
              <Label htmlFor="message">Specific Questions</Label>
              <Textarea
                id="message"
                placeholder="What specific questions do you have about this program?"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                required
              />
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="availability">Your Availability</Label>
            <Textarea
              id="availability"
              placeholder="When are you typically available for coaching sessions?"
              value={availability}
              onChange={(e) => setAvailability(e.target.value)}
              required
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                "Submit Request"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
