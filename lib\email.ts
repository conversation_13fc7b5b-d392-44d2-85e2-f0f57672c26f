// Email sending helper that integrates with your preferred email service provider

import nodemailer from 'nodemailer'
import { Resend } from "resend"

// Create a nodemailer transporter
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_SERVER,
  port: Number(process.env.EMAIL_PORT) || 587,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
  secure: process.env.EMAIL_SECURE === 'true',
})

type EmailPayload = {
  to: string
  subject: string
  html: string
  from?: string
}

export const sendEmail = async (data: EmailPayload) => {
  const { to, subject, html, from = process.env.EMAIL_FROM || '<EMAIL>' } = data

  try {
    await transporter.sendMail({
      from,
      to,
      subject,
      html,
    })
  } catch (error) {
    console.error('Email send error:', error)
    throw new Error('Failed to send email')
  }
}

type SupportTicketData = {
  userEmail: string
  userName: string
  ticketId: string
  subject: string
  message: string
}

export const sendSupportTicketNotification = async (data: SupportTicketData) => {
  const { userEmail, userName, ticketId, subject, message } = data

  // Email to admin/support team
  await sendEmail({
    to: process.env.SUPPORT_EMAIL || '<EMAIL>',
    subject: `New Support Ticket: ${subject}`,
    html: `
      <h1>New Support Ticket</h1>
      <p><strong>From:</strong> ${userName} (${userEmail})</p>
      <p><strong>Ticket ID:</strong> ${ticketId}</p>
      <p><strong>Subject:</strong> ${subject}</p>
      <p><strong>Message:</strong></p>
      <p>${message}</p>
    `,
  })

  // Confirmation email to user
  await sendEmail({
    to: userEmail,
    subject: 'Your Support Request Has Been Received',
    html: `
      <h1>Support Request Received</h1>
      <p>Hello ${userName},</p>
      <p>Thank you for contacting our support team. We have received your request and will respond within 24 hours.</p>
      <p><strong>Ticket ID:</strong> ${ticketId}</p>
      <p><strong>Subject:</strong> ${subject}</p>
      <p>Please keep this information for reference.</p>
      <p>Best regards,<br>The Support Team</p>
    `,
  })
}

type PurchaseConfirmationData = {
  userEmail: string
  userName: string
  orderId: string
  productName: string
  price: string
  downloadLink?: string
}

export const sendPurchaseConfirmation = async (data: PurchaseConfirmationData) => {
  const { userEmail, userName, orderId, productName, price, downloadLink } = data

  await sendEmail({
    to: userEmail,
    subject: 'Your Purchase Confirmation',
    html: `
      <h1>Thank You for Your Purchase!</h1>
      <p>Hello ${userName},</p>
      <p>Your order has been successfully processed.</p>
      <p><strong>Order ID:</strong> ${orderId}</p>
      <p><strong>Product:</strong> ${productName}</p>
      <p><strong>Price:</strong> ${price}</p>
      ${downloadLink ? `<p><strong>Download:</strong> <a href="${downloadLink}">Click here to download</a></p>` : ''}
      <p>Thank you for your business!</p>
      <p>Best regards,<br>The Team</p>
    `,
  })
}

/**
 * Sends a welcome email to a new user
 */
export async function sendWelcomeEmail(email: string, name: string): Promise<boolean> {
  try {
    await sendEmail({
      to: email,
      subject: 'Welcome to our Fitness Platform!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1>Welcome to our Fitness Platform, ${name}!</h1>
          <p>We're excited to have you join our community of fitness enthusiasts.</p>
          <p>Here are a few things you can do to get started:</p>
          <ul>
            <li>Complete your profile</li>
            <li>Browse training programs</li>
            <li>Check out our subscription options</li>
          </ul>
          <p>If you have any questions, please don't hesitate to contact our support team.</p>
          <p>Stay fit!</p>
          <p>The Fitness Platform Team</p>
        </div>
      `
    });
    return true; // Return true on success
  } catch (error) {
    console.error("Failed to send welcome email:", error);
    return false; // Return false on failure
  }
}

/**
 * Sends a purchase confirmation email
 */
export async function sendPurchaseConfirmationEmail(
  email: string, 
  name: string, 
  productName: string, 
  orderTotal: number
): Promise<boolean> {
  try {
    await sendEmail({
      to: email,
      subject: `Order Confirmation - ${productName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1>Thank you for your purchase!</h1>
          <p>Hello ${name},</p>
          <p>We're confirming your purchase of <strong>${productName}</strong>.</p>
          <p><strong>Order Total:</strong> $${orderTotal.toFixed(2)}</p>
          <p>You can access your purchase in your account library. If you have any questions about your order, please contact our support team.</p>
          <p>Enjoy your training!</p>
          <p>The Fitness Platform Team</p>
        </div>
      `
    });
    return true; // Return true on success
  } catch (error) {
    console.error("Failed to send purchase confirmation email:", error);
    return false; // Return false on failure
  }
}

/**
 * Sends a support ticket confirmation
 */
export async function sendSupportTicketConfirmation(
  email: string,
  name: string,
  ticketId: string,
  subject: string
): Promise<boolean> {
  try {
    await sendEmail({
      to: email,
      subject: `Support Ticket Received - ${subject}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1>Your Support Request Has Been Received</h1>
          <p>Hello ${name},</p>
          <p>We've received your support request and our team will get back to you as soon as possible.</p>
          <p><strong>Ticket ID:</strong> ${ticketId}</p>
          <p><strong>Subject:</strong> ${subject}</p>
          <p>You can view the status of your ticket in your account dashboard.</p>
          <p>Thank you for your patience!</p>
          <p>The Fitness Platform Support Team</p>
        </div>
      `
    });
    return true; // Return true on success
  } catch (error) {
    console.error("Failed to send support ticket confirmation:", error);
    return false; // Return false on failure
  }
}

const resend = new Resend(process.env.RESEND_API_KEY)
const FROM_EMAIL = process.env.EMAIL_FROM || "<EMAIL>"
const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"

/**
 * Sends a verification email to the user.
 * @param email - User's email address.
 * @param token - The original (unhashed) verification token.
 */
async function sendVerificationEmail(email: string, token: string) {
  const verificationLink = `${BASE_URL}/api/auth/verify-email?token=${token}`
  
  try {
    await resend.emails.send({
      from: FROM_EMAIL,
      to: email,
      subject: "Verify your email address for ClearCoach",
      html: `
        <h1>Welcome to ClearCoach!</h1>
        <p>Please click the link below to verify your email address and activate your account:</p>
        <p><a href="${verificationLink}" target="_blank">Verify Email Address</a></p>
        <p>If you did not request this email, please ignore it.</p>
        <p>This link will expire in 1 hour.</p>
        <hr>
        <p>Link: ${verificationLink}</p> 
      `,
    })
    console.log(`Verification email sent successfully to ${email}`)
  } catch (error) {
    console.error(`Failed to send verification email to ${email}:`, error)
    // Decide if registration should fail if email fails. For now, we log it.
    // In production, might want to queue this or handle failure more robustly.
    throw new Error("Failed to send verification email.") // Optionally rethrow to inform user
  }
} 