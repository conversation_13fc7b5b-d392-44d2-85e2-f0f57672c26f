"use client"

import { <PERSON>ag<PERSON><PERSON><PERSON><PERSON>x<PERSON>, Droppable, Draggable } from '@hello-pangea/dnd'
import {
  Plus,
  MoreV<PERSON><PERSON>,
  <PERSON><PERSON>,
  Trash,
  Edit,
  Dumbbell,
} from "lucide-react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// Define types
interface Exercise {
  id: string
  name: string
  sets?: number | null
  reps?: number | null
  description?: string | null
  duration?: number | null
  restTime?: number | null
  videoUrl?: string | null
  muscleGroup?: string | null
  order: number
  type?: string | null
  thumbnailUrl?: string | null
  weight?: number | null
}

interface Workout {
  id: string
  title: string
  description?: string | null
  type: string
  order: number
  exercises: Exercise[]
}

interface Week {
  id: string
  weekNumber: number
  order: number
  workouts: Workout[]
}

interface WeekCardProps {
  week: Week
  onEditWeek: (week: Week) => void
  onDeleteWeek: (weekId: string) => void
  onDuplicateWeek: (week: Week) => void
  onAddWorkout: (weekId: string) => void
  onEditWorkout: (workout: Workout) => void
  onDeleteWorkout: (workoutId: string) => void
  onDuplicateWorkout: (workout: Workout) => void
  onAddExercise: (workoutId: string) => void
  onEditExercise: (exercise: Exercise) => void
  onDeleteExercise: (exerciseId: string) => void
  onDuplicateExercise: (exercise: Exercise) => void
  onReorderExercises: (workoutId: string, exercises: Exercise[]) => void
  onDropExercise: (workoutId: string) => void
  draggedExercise: Exercise | null
}

export function WeekCard({
  week,
  onEditWeek,
  onDeleteWeek,
  onDuplicateWeek,
  onAddWorkout,
  onEditWorkout,
  onDeleteWorkout,
  onDuplicateWorkout,
  onAddExercise,
  onEditExercise,
  onDeleteExercise,
  onDuplicateExercise,
  onReorderExercises,
  onDropExercise,
  draggedExercise,
}: WeekCardProps) {
  const [expandedWorkout, setExpandedWorkout] = useState<string | null>(null)
  const [editingExercise, setEditingExercise] = useState<string | null>(null)
  const [exerciseParams, setExerciseParams] = useState<Record<string, any>>({})
  const [isDraggingOver, setIsDraggingOver] = useState<string | null>(null)

  const handleExpandWorkout = (workoutId: string) => {
    setExpandedWorkout(expandedWorkout === workoutId ? null : workoutId)
  }

  const handleDragEnd = (result: any, workoutId: string) => {
    const { destination, source } = result

    // Dropped outside the list
    if (!destination) {
      return
    }

    // Did not move anywhere
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return
    }

    const workout = week.workouts.find(w => w.id === workoutId)
    if (!workout) return

    // Clone exercises
    const newExercises = [...workout.exercises]
    
    // Reorder the array
    const [removed] = newExercises.splice(source.index, 1)
    newExercises.splice(destination.index, 0, removed)
    
    // Update exercise order values
    const reorderedExercises = newExercises.map((exercise, index) => ({
      ...exercise,
      order: index,
    }))
    
    // Call the parent handler to update the state and backend
    onReorderExercises(workoutId, reorderedExercises)
  }

  const handleStartEdit = (exercise: Exercise) => {
    setEditingExercise(exercise.id)
    setExerciseParams({
      sets: exercise.sets,
      reps: exercise.reps,
      duration: exercise.duration,
      restTime: exercise.restTime,
      weight: exercise.weight
    })
  }

  const handleParamChange = (param: string, value: string) => {
    setExerciseParams(prev => ({
      ...prev,
      [param]: value === '' ? null : Number(value)
    }))
  }

  const handleSaveExercise = (exercise: Exercise) => {
    const updatedExercise = {
      ...exercise,
      ...exerciseParams
    }
    onEditExercise(updatedExercise)
    setEditingExercise(null)
  }

  const handleCancelEdit = () => {
    setEditingExercise(null)
    setExerciseParams({})
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, workoutId: string) => {
    e.preventDefault();
    if (draggedExercise) {
      onDropExercise(workoutId);
    }
  }

  const allowDrop = (e: React.DragEvent<HTMLDivElement>) => {
    return true;
  }

  // Add a debug log function to help diagnose drag & drop issues
  const logDropAction = (message: string, workoutId: string) => {
    console.log(`%c${message}: ${workoutId}`, 'color: green; font-weight: bold');
  };

  return (
    <Card className="w-full shadow-sm bg-white">
      <CardHeader className="flex flex-row items-center justify-between bg-slate-50 border-b">
        <div>
          <CardTitle className="text-xl font-bold text-primary">Week {week.weekNumber}</CardTitle>
          <CardDescription className="text-sm text-muted-foreground">
            {week.workouts.length} workout{week.workouts.length !== 1 ? 's' : ''}
          </CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => onAddWorkout(week.id)}
            className="hover:bg-primary/10"
          >
            <Plus className="mr-2 h-4 w-4" /> Add Workout
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="icon" variant="ghost" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEditWeek(week)}>
                <Edit className="mr-2 h-4 w-4" /> Edit Week
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDuplicateWeek(week)}>
                <Copy className="mr-2 h-4 w-4" /> Duplicate Week
              </DropdownMenuItem>
              <DropdownMenuItem 
                className="text-red-600" 
                onClick={() => onDeleteWeek(week.id)}
              >
                <Trash className="mr-2 h-4 w-4" /> Delete Week
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="grid gap-4 p-4 pt-6">
        {week.workouts.sort((a, b) => a.order - b.order).map((workout) => (
          <Card 
            key={workout.id} 
            className={`overflow-hidden transition-colors duration-200 shadow-sm border
              ${draggedExercise ? 'border-primary border-2 border-dashed bg-primary/5' : 'border-muted-foreground/10'}`}
            onDragOver={(e) => {
              // Always prevent default to allow drop
              e.preventDefault();
              e.stopPropagation();
              console.log("Dragging over workout area:", workout.id);
              setIsDraggingOver(workout.id);
            }}
            onDrop={(e) => {
              // Always prevent default
              e.preventDefault();
              e.stopPropagation();
              console.log("DROP DETECTED in workout:", workout.id);
              
              // Check if we have a dragged exercise from the context
              if (draggedExercise) {
                console.log("Found dragged exercise:", draggedExercise.name);
                onDropExercise(workout.id);
              } else {
                // Try to get exercise info from dataTransfer if available
                const exerciseId = e.dataTransfer.getData("text/plain");
                if (exerciseId) {
                  console.log("Found exercise ID in dataTransfer:", exerciseId);
                  onDropExercise(workout.id);
                } else {
                  console.error("No dragged exercise available");
                }
              }
            }}
          >
            <CardHeader className="flex flex-row items-center justify-between p-3 bg-accent/40">
              <div className="flex-1">
                <CardTitle className="text-base font-bold">{workout.title}</CardTitle>
                <CardDescription className="text-xs">{workout.type}</CardDescription>
              </div>
              <div className="flex items-center gap-1">
                <Button 
                  size="sm" 
                  variant={expandedWorkout === workout.id ? "default" : "outline"}
                  className="h-8 text-xs"
                  onClick={() => handleExpandWorkout(workout.id)}
                >
                  {expandedWorkout === workout.id 
                    ? 'Hide Exercises' 
                    : `View ${workout.exercises.length} Exercise${workout.exercises.length !== 1 ? 's' : ''}`}
                </Button>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  className="h-8 w-8 px-0"
                  onClick={() => onAddExercise(workout.id)}
                >
                  <Dumbbell className="h-4 w-4" />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="icon" variant="ghost" className="h-8 w-8">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onEditWorkout(workout)}>
                      <Edit className="mr-2 h-4 w-4" /> Edit Workout
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onDuplicateWorkout(workout)}>
                      <Copy className="mr-2 h-4 w-4" /> Duplicate Workout
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      className="text-red-600" 
                      onClick={() => onDeleteWorkout(workout.id)}
                    >
                      <Trash className="mr-2 h-4 w-4" /> Delete Workout
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            
            {expandedWorkout === workout.id && (
              <CardContent 
                className="p-3"
              >
                <DragDropContext onDragEnd={(result) => handleDragEnd(result, workout.id)}>
                  <Droppable droppableId={`workout-${workout.id}`}>
                    {(provided, snapshot) => (
                      <div
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        className={`space-y-2 rounded-md p-3 min-h-[120px] transition-colors
                          ${snapshot.isDraggingOver ? 'bg-blue-100' : ''}
                          ${draggedExercise ? 'bg-green-50 border border-green-300' : ''}`}
                        onDragOver={(e) => {
                          if (draggedExercise) {
                            e.preventDefault();
                            setIsDraggingOver(workout.id);
                            e.currentTarget.classList.add('border-primary');
                          }
                        }}
                        onDragLeave={(e) => {
                          setIsDraggingOver(null);
                          e.currentTarget.classList.remove('border-primary');
                        }}
                        onDrop={(e) => {
                          if (draggedExercise) {
                            e.preventDefault();
                            logDropAction('Dropping exercise into workout area', workout.id);
                            onDropExercise(workout.id);
                          }
                        }}
                      >
                        {workout.exercises.length === 0 ? (
                          <div
                            className="p-6 text-center rounded-md flex flex-col items-center justify-center h-40
                              bg-blue-50 border-2 border-dashed border-blue-300
                              cursor-pointer hover:bg-blue-100 transition-all duration-200"
                            onClick={() => {
                              console.log("Clicked on empty workout area:", workout.id);
                              onAddExercise(workout.id);
                            }}
                            onDragOver={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              console.log("Dragging over empty workout:", workout.id);
                              setIsDraggingOver(workout.id);
                            }}
                            onDragLeave={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              console.log("Drag left empty workout");
                              setIsDraggingOver(null);
                            }}
                            onDrop={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              console.log("DROP EVENT DETECTED in empty workout:", workout.id);
                              if (draggedExercise) {
                                logDropAction('Dropping exercise into empty workout', workout.id);
                                onDropExercise(workout.id);
                              } else {
                                console.error("No dragged exercise available when drop occurred");
                              }
                            }}
                          >
                            <div className="w-16 h-16 mb-2 rounded-full bg-blue-100 flex items-center justify-center">
                              <Dumbbell className="h-8 w-8 text-blue-500" />
                            </div>
                            <p className="font-medium text-blue-700">No exercises added to this workout yet</p>
                            <p className="text-sm text-blue-500 mt-1">Click here to add an exercise</p>
                            <Button 
                              variant="outline"
                              size="sm"
                              className="mt-4 bg-white hover:bg-blue-100"
                              onClick={(e) => {
                                e.stopPropagation();
                                console.log("Add Exercise button clicked for workout:", workout.id);
                                onAddExercise(workout.id);
                              }}
                            >
                              <Plus className="h-4 w-4 mr-1" /> Add Exercise
                            </Button>
                          </div>
                        ) : (
                          <>
                            {workout.exercises
                              .sort((a, b) => a.order - b.order)
                              .map((exercise, index) => (
                                <Draggable
                                  key={exercise.id}
                                  draggableId={exercise.id}
                                  index={index}
                                >
                                  {(provided, snapshot) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      className={`p-3 rounded-md transition-all
                                        ${snapshot.isDragging ? 'shadow-lg border-2 border-primary' : 'shadow-sm border border-muted-foreground/10'}
                                        ${editingExercise === exercise.id ? 'bg-accent/30' : 'bg-card'}`}
                                    >
                                      <div className="flex items-center justify-between">
                                        <div className="font-medium text-primary/90">{exercise.name}</div>
                                        <div className="flex items-center gap-1">
                                          {editingExercise !== exercise.id ? (
                                            <Button 
                                              size="sm" 
                                              variant="ghost" 
                                              className="h-8 text-xs"
                                              onClick={() => handleStartEdit(exercise)}
                                            >
                                              <Edit className="h-3 w-3 mr-1" /> Edit
                                            </Button>
                                          ) : (
                                            <>
                                              <Button 
                                                size="sm" 
                                                variant="default" 
                                                className="h-8 text-xs"
                                                onClick={() => handleSaveExercise(exercise)}
                                              >
                                                Save
                                              </Button>
                                              <Button 
                                                size="sm" 
                                                variant="outline" 
                                                className="h-8 text-xs"
                                                onClick={handleCancelEdit}
                                              >
                                                Cancel
                                              </Button>
                                            </>
                                          )}
                                          <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                              <Button size="icon" variant="ghost" className="h-8 w-8">
                                                <MoreVertical className="h-4 w-4" />
                                              </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                              <DropdownMenuItem onClick={() => onEditExercise(exercise)}>
                                                <Edit className="mr-2 h-4 w-4" /> Edit Details
                                              </DropdownMenuItem>
                                              <DropdownMenuItem onClick={() => onDuplicateExercise(exercise)}>
                                                <Copy className="mr-2 h-4 w-4" /> Duplicate
                                              </DropdownMenuItem>
                                              <DropdownMenuItem 
                                                className="text-red-600" 
                                                onClick={() => onDeleteExercise(exercise.id)}
                                              >
                                                <Trash className="mr-2 h-4 w-4" /> Delete
                                              </DropdownMenuItem>
                                            </DropdownMenuContent>
                                          </DropdownMenu>
                                        </div>
                                      </div>
                                      
                                      {/* Exercise parameters */}
                                      <div className="mt-3 grid grid-cols-4 gap-2 bg-accent/20 p-2 rounded-md">
                                        {editingExercise === exercise.id ? (
                                          <>
                                            <div className="space-y-1">
                                              <label className="text-xs font-medium text-muted-foreground">Sets</label>
                                              <input
                                                type="number"
                                                className="w-full p-1 text-sm border rounded h-8"
                                                value={exerciseParams.sets === null ? '' : exerciseParams.sets}
                                                onChange={(e) => handleParamChange('sets', e.target.value)}
                                                min="0"
                                              />
                                            </div>
                                            <div className="space-y-1">
                                              <label className="text-xs font-medium text-muted-foreground">Reps</label>
                                              <input
                                                type="number"
                                                className="w-full p-1 text-sm border rounded h-8"
                                                value={exerciseParams.reps === null ? '' : exerciseParams.reps}
                                                onChange={(e) => handleParamChange('reps', e.target.value)}
                                                min="0"
                                              />
                                            </div>
                                            <div className="space-y-1">
                                              <label className="text-xs font-medium text-muted-foreground">Weight (kg)</label>
                                              <input
                                                type="number"
                                                className="w-full p-1 text-sm border rounded h-8"
                                                value={exerciseParams.weight === null ? '' : exerciseParams.weight}
                                                onChange={(e) => handleParamChange('weight', e.target.value)}
                                                min="0"
                                                step="0.5"
                                              />
                                            </div>
                                            <div className="space-y-1">
                                              <label className="text-xs font-medium text-muted-foreground">Rest (sec)</label>
                                              <input
                                                type="number"
                                                className="w-full p-1 text-sm border rounded h-8"
                                                value={exerciseParams.restTime === null ? '' : exerciseParams.restTime}
                                                onChange={(e) => handleParamChange('restTime', e.target.value)}
                                                min="0"
                                              />
                                            </div>
                                          </>
                                        ) : (
                                          <>
                                            <div className="text-center bg-white rounded p-1 shadow-sm">
                                              <span className="text-xs font-medium text-muted-foreground block mb-1">Sets</span>
                                              <span className="font-medium text-sm">{exercise.sets ?? '-'}</span>
                                            </div>
                                            <div className="text-center bg-white rounded p-1 shadow-sm">
                                              <span className="text-xs font-medium text-muted-foreground block mb-1">Reps</span>
                                              <span className="font-medium text-sm">{exercise.reps ?? '-'}</span>
                                            </div>
                                            <div className="text-center bg-white rounded p-1 shadow-sm">
                                              <span className="text-xs font-medium text-muted-foreground block mb-1">Weight</span>
                                              <span className="font-medium text-sm">{exercise.weight ? `${exercise.weight} kg` : '-'}</span>
                                            </div>
                                            <div className="text-center bg-white rounded p-1 shadow-sm">
                                              <span className="text-xs font-medium text-muted-foreground block mb-1">Rest</span>
                                              <span className="font-medium text-sm">{exercise.restTime ? `${exercise.restTime}s` : '-'}</span>
                                            </div>
                                          </>
                                        )}
                                      </div>
                                      
                                      {/* Additional exercise details in footer */}
                                      <div className="mt-2 flex text-xs text-muted-foreground gap-2">
                                        {exercise.muscleGroup && (
                                          <span className="px-2 py-0.5 bg-accent/50 rounded-full text-xs">
                                            {exercise.muscleGroup}
                                          </span>
                                        )}
                                        {exercise.type && (
                                          <span className="px-2 py-0.5 bg-accent/30 rounded-full text-xs">
                                            {exercise.type}
                                          </span>
                                        )}
                                        {exercise.duration && (
                                          <span className="px-2 py-0.5 bg-accent/20 rounded-full text-xs">
                                            {exercise.duration}s
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                </Draggable>
                              ))}
                            {provided.placeholder}
                          </>
                        )}
                        
                        {/* Drag indicator when workout has exercises */}
                        {workout.exercises.length > 0 && draggedExercise && (
                          <div className="p-2 text-center border-2 border-dashed border-primary rounded-md bg-primary/5 mt-2">
                            <span className="text-sm font-medium text-primary">Drop {draggedExercise.name} here</span>
                          </div>
                        )}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              </CardContent>
            )}
            
            {expandedWorkout !== workout.id && (
              <CardContent className="p-2">
                <div 
                  className={`py-3 text-center rounded-md transition-colors
                    ${draggedExercise ? 'bg-primary/10 text-primary font-medium' : 'text-muted-foreground'}`}
                >
                  {draggedExercise 
                    ? `Drop ${draggedExercise.name} here to add to this workout` 
                    : workout.exercises.length > 0
                      ? `${workout.exercises.length} exercise${workout.exercises.length !== 1 ? 's' : ''}`
                      : 'No exercises yet'}
                </div>
              </CardContent>
            )}
          </Card>
        ))}
        
        {week.workouts.length === 0 && (
          <div className="p-8 text-center bg-accent/10 border border-dashed rounded-md">
            <h3 className="text-base font-medium mb-2">No workouts in this week yet</h3>
            <p className="mb-4 text-sm text-muted-foreground">Start by adding a workout to this week</p>
            <Button onClick={() => onAddWorkout(week.id)}>
              <Plus className="mr-2 h-4 w-4" />
              Add First Workout
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 