import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  try {
    // Get the authenticated user
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;
    console.log(`PUT /api/clients/${params.id}/training-plan/update - Request from user ${userId}`);

    // Get the plan data from the request body
    const planData = await req.json();
    console.log('Plan data to update:', planData);

    // Find the user (client)
    const user = await prisma.user.findUnique({
      where: { id: params.id }
    });

    if (!user) {
      console.error(`User not found with ID ${params.id}`);
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    console.log('Found user:', {
      id: user.id,
      name: user.name,
      email: user.email
    });

    // Find the existing plan
    const existingPlan = await prisma.trainingPlanTemplate.findFirst({
      where: {
        clientId: user.id,
        type: 'personalized'
      }
    });

    if (!existingPlan) {
      console.error(`No personalized plan found for user ${user.id}`);
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    console.log('Found existing plan:', {
      id: existingPlan.id,
      title: existingPlan.title,
      clientId: existingPlan.clientId
    });

    // Update the plan with all fields
    const updatedPlan = await prisma.trainingPlanTemplate.update({
      where: { id: existingPlan.id },
      data: {
        title: planData.title,
        description: planData.description,
        type: 'personalized', // Always use 'personalized' for client plans
        weeks: planData.weeks,
        // Add any other fields that need to be updated
      }
    });

    console.log('Plan updated successfully:', {
      id: updatedPlan.id,
      title: updatedPlan.title,
      description: updatedPlan.description
    });

    return NextResponse.json(updatedPlan);
  } catch (error) {
    console.error('Error updating training plan:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
