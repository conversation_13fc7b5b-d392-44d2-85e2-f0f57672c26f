import { NextResponse, type NextRequest } from "next/server";
import { encode } from "next-auth/jwt";
import { prisma } from "@/lib/prisma"; // Import Prisma client

type UserRole = "admin" | "trainer" | "client";

// This route is for development use only
export async function GET(req: NextRequest) {
  // Only allow in development mode
  if (process.env.NODE_ENV !== "development") {
    return new NextResponse("Not available in production", { status: 403 });
  }

  const searchParams = req.nextUrl.searchParams;
  const roleParam = searchParams.get("role") || "admin";
  const returnTo = searchParams.get("returnTo") || "/dashboard";
  const clearParam = searchParams.get("clear");
  const userIdParam = searchParams.get("userId");

  // Check if we need to clear the role override
  if (clearParam === "true") {
    console.log(`[<PERSON>gin] Clearing role override`);
    const response = NextResponse.redirect(new URL(returnTo, req.url));

    // Clear the cookies
    response.cookies.set("dev_override_role", "", {
      path: "/",
      maxAge: 0
    });
    response.cookies.set("dev_premium_status", "", {
      path: "/",
      maxAge: 0
    });

    return response;
  }

  // Check for premium status in query params and cookies
  const premiumParam = searchParams.get('premium');
  const cookies = req.cookies;
  const premiumCookie = cookies.get('dev_premium_status')?.value;

  // Premium status priority: query param > cookie > default (false)
  const isPremium = premiumParam !== null
    ? premiumParam === 'true'
    : premiumCookie === 'true';

  console.log(`[Dev Login] Premium status check: param=${premiumParam}, cookie=${premiumCookie}, final=${isPremium}`);

  // Type validation
  if (!["admin", "trainer", "client"].includes(roleParam)) {
    return new NextResponse("Invalid role. Must be admin, trainer, or client", {
      status: 400
    });
  }

  // Now we know it's a valid role
  const role = roleParam as UserRole;
  // All clients are premium by default
  const isPremiumUser = role === 'client' ? true : isPremium;

  // Use the provided userId if available, otherwise use the default
  const userId = userIdParam || "dev-user-id";

  // If a specific userId is provided, try to get the user's actual name and email
  let userName = `Development ${role.charAt(0).toUpperCase() + role.slice(1)}${isPremiumUser ? ' (Premium)' : ''}`;
  let userEmail = `dev-${role}@example.com`;

  // Log the userId being used
  console.log(`[Dev Login] Using userId: ${userId}`);

  // If a specific userId is provided, try to get the user from the database
  if (userIdParam) {
    try {
      const existingUser = await prisma.user.findUnique({
        where: { id: userIdParam },
        select: { name: true, email: true }
      });

      if (existingUser) {
        userName = existingUser.name || userName;
        userEmail = existingUser.email || userEmail;
        console.log(`[Dev Login] Found existing user: ${userName} (${userEmail})`);
      }
    } catch (error) {
      console.error(`[Dev Login] Error finding user with ID ${userIdParam}:`, error);
      // Continue with default values if there's an error
    }
  }

  try {
    // 1. Check if user exists by ID
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (existingUser) {
      // Update existing user
      await prisma.user.update({
        where: { id: userId },
        data: {
          role: role,
          name: userName,
          // Don't update email to avoid unique constraint violations
        },
      });
    } else {
      // Check if email exists
      const emailExists = await prisma.user.findUnique({
        where: { email: userEmail },
      });

      if (emailExists) {
        // Use a different email to avoid conflicts
        userEmail = `dev-${role}-${Date.now()}@example.com`;
      }

      // Create new user
      await prisma.user.create({
        data: {
          id: userId,
          email: userEmail,
          name: userName,
          password: "devpassword", // Added placeholder password
          role: role,
          emailVerified: new Date(), // Mark email as verified for dev login
        },
      });
    }

    // 2. Create JWT token
    const token = await encode({
      token: {
        id: userId,
        email: userEmail,
        name: userName,
        role: role,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60),
        jti: "dev-jwt-id-" + Math.random().toString(36).substring(2)
      },
      secret: process.env.NEXTAUTH_SECRET!,
    });

    // 3. Create response and set cookies
    const response = NextResponse.redirect(new URL(returnTo, req.url));

    const cookieOptions = {
      httpOnly: true,
      path: "/",
      secure: false, // Set directly to false as this route is dev-only
      maxAge: 30 * 24 * 60 * 60, // 30 days
      sameSite: "lax" as const,
    };

    response.cookies.set("next-auth.session-token", token, cookieOptions);
    response.cookies.set("next-auth.csrf-token", `devcsrftoken|${Math.random().toString(36).substring(2)}`, cookieOptions);
    response.cookies.set("dev_override_role", roleParam, {
      path: "/",
      maxAge: 30 * 24 * 60 * 60
    });

    // Set premium status cookie
    response.cookies.set("dev_premium_status", isPremiumUser ? "true" : "false", {
      path: "/",
      maxAge: 30 * 24 * 60 * 60
    });

    console.log(`[Dev Login] Set role to ${roleParam}, premium status to ${isPremiumUser}`);
    console.log(`[Dev Login] Cookies set: dev_override_role=${roleParam}, dev_premium_status=${isPremiumUser ? "true" : "false"}`);

    // Add debug info to the response
    response.headers.set("X-Debug-Role", roleParam);
    response.headers.set("X-Debug-Premium", isPremiumUser ? "true" : "false");

    return response;
  } catch (error) {
    console.error("Dev login error:", error);
    return new NextResponse("Authentication error", { status: 500 });
  }
}