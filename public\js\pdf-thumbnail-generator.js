/**
 * Client-side PDF thumbnail generator
 * This script can be included in the page to generate thumbnails from PDFs
 */

class PdfThumbnailGenerator {
  /**
   * Generate a thumbnail from a PDF URL
   * @param {string} pdfUrl - URL of the PDF file
   * @param {number} width - Width of the thumbnail
   * @param {number} height - Height of the thumbnail
   * @returns {Promise<string>} - Data URL of the thumbnail
   */
  static async generateThumbnail(pdfUrl, width = 400, height = 500) {
    // Check if PDF.js is loaded
    if (typeof pdfjsLib === 'undefined') {
      console.error('PDF.js library not loaded');
      return this.generatePlaceholder(width, height);
    }
    
    try {
      // Load the PDF document
      const loadingTask = pdfjsLib.getDocument(pdfUrl);
      const pdf = await loadingTask.promise;
      
      // Get the first page
      const page = await pdf.getPage(1);
      
      // Calculate scale to fit the thumbnail dimensions
      const viewport = page.getViewport({ scale: 1 });
      const scale = Math.min(width / viewport.width, height / viewport.height);
      const scaledViewport = page.getViewport({ scale });
      
      // Create a canvas to render the page
      const canvas = document.createElement('canvas');
      canvas.width = scaledViewport.width;
      canvas.height = scaledViewport.height;
      const context = canvas.getContext('2d');
      
      // Render the page
      const renderContext = {
        canvasContext: context,
        viewport: scaledViewport
      };
      
      await page.render(renderContext).promise;
      
      // Return the canvas as a data URL
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.error('Error generating PDF thumbnail:', error);
      return this.generatePlaceholder(width, height);
    }
  }
  
  /**
   * Generate a placeholder thumbnail
   * @param {number} width - Width of the thumbnail
   * @param {number} height - Height of the thumbnail
   * @returns {string} - Data URL of the placeholder
   */
  static generatePlaceholder(width = 400, height = 500) {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    // Draw background
    ctx.fillStyle = '#f0f4f8';
    ctx.fillRect(0, 0, width, height);
    
    // Draw PDF text
    ctx.font = 'bold 24px Arial';
    ctx.fillStyle = '#4a5568';
    ctx.textAlign = 'center';
    ctx.fillText('PDF', width / 2, height / 2);
    
    // Draw a document icon outline
    ctx.strokeStyle = '#3182ce';
    ctx.lineWidth = 3;
    const iconSize = 100;
    const x = (width - iconSize) / 2;
    const y = (height - iconSize) / 2 - 30;
    
    // Document outline
    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineTo(x, y + iconSize);
    ctx.lineTo(x + iconSize, y + iconSize);
    ctx.lineTo(x + iconSize, y + 20);
    ctx.lineTo(x + iconSize - 20, y);
    ctx.closePath();
    ctx.stroke();
    
    // Folded corner
    ctx.beginPath();
    ctx.moveTo(x + iconSize - 20, y);
    ctx.lineTo(x + iconSize - 20, y + 20);
    ctx.lineTo(x + iconSize, y + 20);
    ctx.stroke();
    
    // Lines representing text
    ctx.beginPath();
    ctx.moveTo(x + 20, y + 40);
    ctx.lineTo(x + iconSize - 20, y + 40);
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(x + 20, y + 60);
    ctx.lineTo(x + iconSize - 20, y + 60);
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(x + 20, y + 80);
    ctx.lineTo(x + iconSize - 40, y + 80);
    ctx.stroke();
    
    return canvas.toDataURL('image/png');
  }
}

// Make it available globally
window.PdfThumbnailGenerator = PdfThumbnailGenerator;
