import { ChevronRight, Utensils } from "lucide-react"
import Link from "next/link"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

interface DietPlanListProps {
  plans: any[]
}

export function DietPlanList({ plans }: DietPlanListProps) {
  return (
    <div className="flex flex-col gap-4">
      <h3 className="text-lg font-medium">Your Nutrition Plans</h3>

      {plans.length > 0 ? (
        plans.map((plan) => (
          <Card key={plan.id}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>{plan.diet_plans.title}</CardTitle>
                  <CardDescription className="flex items-center gap-1 mt-1">
                    <Avatar className="h-5 w-5">
                      <AvatarImage
                        src={plan.diet_plans.users.avatar_url || "/placeholder.svg?height=20&width=20"}
                        alt={plan.diet_plans.users.full_name}
                      />
                      <AvatarFallback>{plan.diet_plans.users.full_name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <span>By {plan.diet_plans.users.full_name}</span>
                  </CardDescription>
                </div>
                <Badge variant={plan.status === "active" ? "default" : "outline"}>{plan.status}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-3">{plan.diet_plans.description}</p>
              <div className="flex gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Utensils className="h-4 w-4 text-muted-foreground" />
                  <span>{plan.diet_plans.calories_per_day} calories/day</span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" className="w-full">
                <Link href={`/dashboard/nutrition/${plan.diet_plans.id}`}>
                  <span>View Plan</span>
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        ))
      ) : (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground text-sm">
              You don&apos;t have any diet plans yet. Create your first one now!
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

