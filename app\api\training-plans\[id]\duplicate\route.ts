import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(
  request: Request,
  context: any // Use 'any' workaround
) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) { 
    return new NextResponse("Unauthorized", { status: 401 })
  }

  let duplicatedPlanId: string | null = null; // Keep track of the new plan ID

  try {
    const trainingPlanId = context?.params?.id;
    if (!trainingPlanId) {
        return new NextResponse("Training plan ID missing in URL", { status: 400 });
    }

    const originalPlan = await prisma.trainingPlan.findUnique({
      where: { id: trainingPlanId },
      include: {
        weeks: {
          orderBy: { order: 'asc' },
          include: {
            workouts: {
              orderBy: { order: 'asc' },
              include: { exercises: { orderBy: { order: 'asc' } } }
            }
          }
        }
      }
    });

    if (!originalPlan) {
      return new NextResponse("Training plan not found", { status: 404 })
    }

    if (originalPlan.athleteId !== session.user.id) {
      return new NextResponse("Forbidden", { status: 403 })
    }

    // Step 1: Create the new TrainingPlan shell
    const duplicatedPlanShell = await prisma.trainingPlan.create({
      data: {
        title: `${originalPlan.title} (Copy)`,
        description: originalPlan.description,
        difficulty: originalPlan.difficulty,
        athleteId: session.user.id,
      }
    });
    duplicatedPlanId = duplicatedPlanShell.id; // Store the new ID

    // Step 2: Iterate and create nested data (Weeks, Workouts, Exercises)
    for (const week of originalPlan.weeks) {
      const newWeek = await prisma.week.create({
        data: {
            weekNumber: week.weekNumber,
          order: week.order,
          trainingPlanId: duplicatedPlanId, // Link to new plan
        }
      });

      for (const workout of week.workouts) {
        const newWorkout = await prisma.workout.create({
          data: {
                title: workout.title,
                description: workout.description,
                type: workout.type,
            order: workout.order,
            weekId: newWeek.id, // Link to new week
            trainingPlanId: duplicatedPlanId, // Link to new plan
          }
        });

        if (workout.exercises.length > 0) {
            await prisma.exercise.createMany({
                data: workout.exercises.map((exercise) => ({
                    name: exercise.name,
                    description: exercise.description,
                    sets: exercise.sets,
                    reps: exercise.reps,
                    duration: exercise.duration,
                    restTime: exercise.restTime,
                    videoUrl: exercise.videoUrl,
                    muscleGroup: exercise.muscleGroup,
                    order: exercise.order,
                    type: exercise.type,
                    thumbnailUrl: exercise.thumbnailUrl,
                    calories: exercise.calories,
                    difficulty: exercise.difficulty,
                    equipment: exercise.equipment,
                    isTemplate: exercise.isTemplate,
                    createdBy: session.user?.id,
                    workoutId: newWorkout.id, // Link to new workout
                }))
            });
        }
      }
    }

    // Step 3: Fetch the fully duplicated plan with nested includes for the response
    const finalDuplicatedPlan = await prisma.trainingPlan.findUnique({
      where: { id: duplicatedPlanId },
      include: {
        weeks: {
          orderBy: { order: 'asc' },
          include: {
            workouts: {
              orderBy: { order: 'asc' },
              include: { exercises: { orderBy: { order: 'asc' } } }
            }
          }
        }
      }
    });

    return NextResponse.json(finalDuplicatedPlan);

  } catch (error) {
    console.error("[TRAINING_PLAN_DUPLICATE]", error);
    // Optional: Add cleanup logic here if duplication failed mid-way
    // e.g., delete the duplicatedPlanShell if it was created but nesting failed
    // if (duplicatedPlanId) { await prisma.trainingPlan.delete({ where: { id: duplicatedPlanId } }).catch(e => console.error("Cleanup failed:", e)); }
    return new NextResponse("Internal error duplicating training plan", { status: 500 })
  }
} 