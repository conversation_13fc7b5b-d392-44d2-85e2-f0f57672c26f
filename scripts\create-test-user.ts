import { PrismaClient } from "@prisma/client"
import { hash } from "bcryptjs"

const prisma = new PrismaClient()

async function main() {
  const email = process.env.TEST_USER_EMAIL || "<EMAIL>"
  const password = process.env.TEST_USER_PASSWORD || "test123"
  const fullName = process.env.TEST_USER_NAME || "Test User"

  // Check if test user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email }
  })

  if (existingUser) {
    console.log("Test user already exists")
    return
  }

  // Create test user
  const hashedPassword = await hash(password, 10)
  const user = await prisma.user.create({
    data: {
      email,
      password: hashedPassword,
      fullName,
      role: "user",
    },
  })

  console.log("Test user created successfully:", user)
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 