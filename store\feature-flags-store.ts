import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { FeatureFlags, defaultFeatureFlags, getDevFeatureFlags } from '@/lib/feature-flags';

interface FeatureFlagsState {
  flags: FeatureFlags;
  setFlags: (flags: Partial<FeatureFlags>) => void;
  resetFlags: () => void;
}

// Create the feature flags store with persistence
export const useFeatureFlagsStore = create<FeatureFlagsState>()(
  persist(
    (set) => ({
      flags: { ...defaultFeatureFlags },
      
      setFlags: (newFlags: Partial<FeatureFlags>) => 
        set((state) => ({ 
          flags: { ...state.flags, ...newFlags } 
        })),
        
      resetFlags: () => 
        set(() => ({ 
          flags: { ...defaultFeatureFlags } 
        })),
    }),
    {
      name: 'feature-flags-storage',
      
      // Initialize feature flags from cookies in development mode (for previewing)
      onRehydrateStorage: () => (state) => {
        if (process.env.NODE_ENV === 'development') {
          const devFlags = getDevFeatureFlags();
          if (devFlags && state) {
            state.setFlags(devFlags);
          }
        }
      }
    }
  )
);

// Create a hook for accessing feature flags and checking if a feature is enabled
export function useFeatureFlag(flagName: keyof FeatureFlags): boolean {
  return useFeatureFlagsStore((state) => state.flags[flagName]);
} 