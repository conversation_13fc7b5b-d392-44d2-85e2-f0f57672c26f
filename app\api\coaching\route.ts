import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  try {
    // Get the user session
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    
    const { searchParams } = new URL(request.url)
    const action = searchParams.get("action")
    const userRole = session.user.role

    // Common params
    const workoutId = searchParams.get("workoutId")
    
    // Validate user role for specific actions
    switch (action) {
      case "clients": {
        // Get all active coaching clients - for trainers and admins
        if (userRole !== "trainer" && userRole !== "admin") {
          return NextResponse.json({ error: "Forbidden" }, { status: 403 })
        }
        
        const clients = await prisma.coachingRelationship.findMany({
          where: {
            trainerId: session.user.id,
            status: "active",
          },
          include: {
            client: {
              select: {
                id: true,
                name: true,
                email: true,
                avatarUrl: true,
              }
            },
            plan: true,
          }
        })
        
        return NextResponse.json({ clients })
      }
      
      case "inquiries": {
        // Get pending coaching inquiries - for trainers and admins
        if (userRole !== "trainer" && userRole !== "admin") {
          return NextResponse.json({ error: "Forbidden" }, { status: 403 })
        }
        
        const inquiries = await prisma.coachingInquiry.findMany({
          where: {
            trainerId: session.user.id,
            status: "pending",
          },
          include: {
            athlete: {
              select: {
                id: true,
                name: true,
                email: true,
                avatarUrl: true,
              }
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        })
        
        return NextResponse.json({ inquiries })
      }
      
      case "my-coaching": {
        // Get client's active coaching relationship and related data
        if (userRole !== "client") {
          return NextResponse.json({ error: "Forbidden" }, { status: 403 })
        }
        
        const coaching = await prisma.coachingRelationship.findFirst({
          where: {
            clientId: session.user.id,
            status: "active",
          },
          include: {
            trainer: {
              select: {
                id: true,
                name: true,
                email: true,
                avatarUrl: true,
              }
            },
            plan: true,
          }
        })
        
        if (!coaching) {
          return NextResponse.json({ 
            coaching: null,
            upcomingWorkouts: [],
            completedWorkouts: [],
            nextSession: null,
            unreadMessages: 0
          })
        }
        
        // Get upcoming workouts
        const now = new Date()
        
        const upcomingWorkouts = await prisma.workout.findMany({
          where: {
            coachingRelationshipId: coaching.id,
            scheduledDate: {
              gte: now,
            },
            isCompleted: false,
          },
          orderBy: {
            scheduledDate: "asc",
          },
          take: 5,
        })
        
        // Get latest completed workouts
        const completedWorkouts = await prisma.workout.findMany({
          where: {
            coachingRelationshipId: coaching.id,
            isCompleted: true,
          },
          orderBy: {
            completedDate: "desc",
          },
          take: 5,
        })
        
        // Get next coaching session
        const nextSession = await prisma.coachingSession.findFirst({
          where: {
            coachingRelationshipId: coaching.id,
            scheduledDate: {
              gte: now,
            }
          },
          orderBy: {
            scheduledDate: "asc",
          },
        })
        
        // Get count of unread messages
        const unreadMessages = await prisma.message.count({
          where: {
            receiverId: session.user.id,
            senderId: coaching.trainerId,
            read: false,
          },
        })
        
        return NextResponse.json({ 
          coaching, 
          upcomingWorkouts, 
          completedWorkouts, 
          nextSession,
          unreadMessages
        })
      }
      
      case "workout": {
        // Get a specific workout details with exercises
        if (!workoutId) {
          return NextResponse.json({ error: "Workout ID is required" }, { status: 400 })
        }
        
        const workout = await prisma.workout.findUnique({
          where: {
            id: workoutId,
          },
          include: {
            exercises: {
              orderBy: {
                order: "asc",
              },
            },
            coachingRelationship: {
              include: {
                trainer: {
                  select: {
                    id: true,
                    name: true,
                  }
                },
                client: {
                  select: {
                    id: true,
                    name: true,
                  }
                }
              }
            }
          }
        })
        
        if (!workout) {
          return NextResponse.json({ error: "Workout not found" }, { status: 404 })
        }
        
        // Check permissions
        if (
          userRole === "client" && workout.coachingRelationship.clientId !== session.user.id ||
          userRole === "trainer" && workout.coachingRelationship.trainerId !== session.user.id
        ) {
          return NextResponse.json({ error: "You don't have access to this workout" }, { status: 403 })
        }
        
        return NextResponse.json({ workout })
      }
      
      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 })
    }
  } catch (error) {
    console.error("Error in coaching API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
} 