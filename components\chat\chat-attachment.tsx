'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { File, FileText, Image as FileImage, FileType, Archive, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ImagePreviewModal } from '@/components/ui/image-preview-modal';

export interface Attachment {
  id: string;
  url: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  mimeType: string;
}

interface ChatAttachmentProps {
  attachment: Attachment;
  className?: string;
}

export function ChatAttachment({ attachment, className = '' }: ChatAttachmentProps) {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  // Get file extension
  const getFileExtension = (fileName: string): string => {
    return fileName.split('.').pop()?.toLowerCase() || '';
  };

  // Get appropriate icon based on file type
  const getFileIcon = () => {
    const ext = getFileExtension(attachment.fileName);

    if (attachment.fileType === 'image') {
      return <FileImage className="h-6 w-6 text-blue-500" />;
    } else if (ext === 'pdf') {
      return <FileType className="h-6 w-6 text-red-500" />;
    } else if (['zip', 'rar', '7z'].includes(ext)) {
      return <Archive className="h-6 w-6 text-yellow-500" />;
    } else if (['txt', 'doc', 'docx'].includes(ext)) {
      return <FileText className="h-6 w-6 text-green-500" />;
    } else {
      return <File className="h-6 w-6 text-gray-500" />;
    }
  };

  return (
    <>
      <div style={{ background: 'transparent', padding: 0, margin: 0, border: 'none' }}>
        {attachment.fileType === 'image' ? (
          <div
            className="relative cursor-pointer group inline-block"
            onClick={() => setIsPreviewOpen(true)}
            style={{ background: 'transparent' }}
          >
            {/* Just the image, no styling at all */}
            <img
              src={attachment.url}
              alt={attachment.fileName}
              style={{
                maxWidth: '240px',
                display: 'block',
                borderRadius: '8px',
                background: 'transparent'
              }}
            />

            {/* Hover overlay with options */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-200 opacity-0 group-hover:opacity-100 flex items-center justify-center">
              <div className="flex gap-3">
                {/* Preview button */}
                <button
                  className="w-9 h-9 rounded-full bg-white flex items-center justify-center text-gray-800 hover:bg-gray-100 transition-colors shadow-md"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsPreviewOpen(true);
                  }}
                  aria-label="Preview image"
                >
                  <Eye className="h-4 w-4" />
                </button>

                {/* Download button */}
                <button
                  className="w-9 h-9 rounded-full bg-white flex items-center justify-center text-gray-800 hover:bg-gray-100 transition-colors shadow-md"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(attachment.url, '_blank');
                  }}
                  aria-label="Download image"
                >
                  <File className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* File size indicator */}
            <div className="absolute bottom-2 right-2 px-2 py-0.5 bg-black/60 rounded text-xs text-white/90">
              {formatFileSize(attachment.fileSize)}
            </div>
          </div>
        ) : (
          <div className="flex items-center">
            <div className="mr-3 flex h-10 w-10 items-center justify-center rounded bg-muted">
              {getFileIcon()}
            </div>
            <div className="flex-1 min-w-0">
              <div className="truncate font-medium">{attachment.fileName}</div>
              <div className="text-xs text-muted-foreground">
                {formatFileSize(attachment.fileSize)}
              </div>
            </div>
            <Button
              size="sm"
              variant="ghost"
              className="ml-2 flex-shrink-0"
              onClick={() => window.open(attachment.url, '_blank')}
            >
              Download
            </Button>
          </div>
        )}
      </div>

      {/* Image Preview Modal */}
      <ImagePreviewModal
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        imageUrl={attachment.url}
        fileName={attachment.fileName}
      />
    </>
  );
}
