import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { z } from "zod"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// Validation schema for profile updates
const profileUpdateSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  socialLinks: z.any().optional(),
  themeSettings: z.any().optional(),
  slug: z.string().optional()
})

// GET trainer profile
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Only trainers and admins can access this endpoint
    if (!["trainer", "admin"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden - insufficient permissions" }, { status: 403 })
    }

    // Get ID from session (or allow admin to fetch specific trainer)
    const { searchParams } = new URL(request.url)
    const trainerId = searchParams.get("id") || session.user.id

    // Admin can view any trainer profile, trainers can only view their own
    if (session.user.role !== "admin" && trainerId !== session.user.id) {
      return NextResponse.json({ error: "Forbidden - you can only access your own profile" }, { status: 403 })
    }

    // Fetch trainer profile
    const trainer = await prisma.user.findUnique({
      where: { id: trainerId },
      select: {
        id: true,
        name: true,
        email: true,
        avatarUrl: true,
        role: true
      }
    })

    // Add default values for missing fields
    if (trainer) {
      trainer.socialLinks = {}
      trainer.themeSettings = {}
      trainer.slug = ''
    }

    if (!trainer) {
      return NextResponse.json({ error: "Trainer not found" }, { status: 404 })
    }

    return NextResponse.json(trainer)
  } catch (error) {
    console.error("[TRAINER_PROFILE_GET]", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// UPDATE trainer profile
export async function PATCH(request: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Only trainers and admins can update profiles
    if (!["trainer", "admin"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden - insufficient permissions" }, { status: 403 })
    }

    // Validate request body
    const body = await request.json()
    const validationResult = profileUpdateSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json({
        error: "Invalid request data",
        details: validationResult.error.format()
      }, { status: 400 })
    }

    const { id, name, socialLinks, themeSettings, slug } = validationResult.data

    // Admin can update any trainer profile, trainers can only update their own
    if (session.user.role !== "admin" && id !== session.user.id) {
      return NextResponse.json({ error: "Forbidden - you can only update your own profile" }, { status: 403 })
    }

    // Validate slug uniqueness if provided
    if (slug) {
      const existingUser = await prisma.user.findFirst({
        where: {
          slug,
          id: { not: id }
        }
      })

      if (existingUser) {
        return NextResponse.json(
          { error: "A user with this slug already exists" },
          { status: 400 }
        )
      }
    }

    // Update trainer profile
    const updatedTrainer = await prisma.user.update({
      where: { id },
      data: {
        name
      },
      select: {
        id: true,
        name: true,
        email: true,
        avatarUrl: true
      }
    })

    // Add default values for missing fields
    if (updatedTrainer) {
      updatedTrainer.socialLinks = socialLinks || {}
      updatedTrainer.themeSettings = themeSettings || {}
      updatedTrainer.slug = slug || ''
    }

    return NextResponse.json(updatedTrainer)
  } catch (error) {
    console.error("[TRAINER_PROFILE_PATCH]", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}