import { GET, POST } from '@/app/api/diet-plans/route'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'

// Mock next-auth
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    dietPlan: {
      findMany: jest.fn(),
      create: jest.fn(),
    },
  },
}))

describe('Diet Plans API', () => {
  const mockUser = {
    id: '1',
    role: 'admin',
  }

  beforeEach(() => {
    ;(getServerSession as jest.Mock).mockResolvedValue({ user: mockUser })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/diet-plans', () => {
    it('returns 401 when user is not authenticated', async () => {
      ;(getServerSession as jest.Mock).mockResolvedValue(null)
      
      const request = new Request('http://localhost:3000/api/diet-plans')
      const response = await GET(request)
      expect(response?.status).toBe(401)
    })

    it('returns diet plans for authenticated user', async () => {
      const mockPlans = [
        {
          id: '1',
          title: 'Test Plan',
          description: 'Test Description',
          athleteId: '1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ]

      ;(prisma.dietPlan.findMany as jest.Mock).mockResolvedValue(mockPlans)

      const request = new Request('http://localhost:3000/api/diet-plans')
      const response = await GET(request)
      expect(response?.status).toBe(200)
      expect(await response?.json()).toEqual(mockPlans)
    })

    it('handles database errors', async () => {
      ;(prisma.dietPlan.findMany as jest.Mock).mockRejectedValue(new Error('Database error'))

      const request = new Request('http://localhost:3000/api/diet-plans')
      const response = await GET(request)
      expect(response?.status).toBe(500)
    })
  })

  describe('POST /api/diet-plans', () => {
    it('returns 401 when user is not authenticated', async () => {
      ;(getServerSession as jest.Mock).mockResolvedValue(null)
      
      const request = new Request('http://localhost:3000/api/diet-plans', {
        method: 'POST',
        body: JSON.stringify({}),
      })
      const response = await POST(request)
      expect(response?.status).toBe(401)
    })

    it('creates a new diet plan', async () => {
      const planData = {
        title: 'New Plan',
        description: 'New Description',
        meals: [
          {
            name: 'Breakfast',
            description: 'Morning meal',
            calories: 500,
            protein: 30,
            carbs: 60,
            fats: 20,
          },
        ],
      }

      const mockCreatedPlan = {
        id: '1',
        ...planData,
        athleteId: '1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      ;(prisma.dietPlan.create as jest.Mock).mockResolvedValue(mockCreatedPlan)

      const request = {
        headers: {
          get: (name: string) => name === 'content-type' ? 'application/json' : null,
        },
        json: async () => planData,
      } as unknown as Request

      const response = await POST(request)

      expect(response?.status).toBe(200)
      expect(await response?.json()).toEqual(mockCreatedPlan)
    })

    it('handles validation errors', async () => {
      const invalidData = {
        title: '', // Missing required field
        description: 'Test Description',
      }

      const request = {
        headers: {
          get: (name: string) => name === 'content-type' ? 'application/json' : null,
        },
        json: async () => invalidData,
      } as unknown as Request

      const response = await POST(request)

      expect(response?.status).toBe(400)
    })

    it('handles database errors', async () => {
      const planData = {
        title: 'New Plan',
        description: 'New Description',
        meals: [],
      }

      ;(prisma.dietPlan.create as jest.Mock).mockRejectedValue(new Error('Database error'))

      const request = {
        headers: {
          get: (name: string) => name === 'content-type' ? 'application/json' : null,
        },
        json: async () => planData,
      } as unknown as Request

      const response = await POST(request)

      expect(response?.status).toBe(500)
    })
  })
}) 