'use client'

import { motion, AnimatePresence, Variants } from 'framer-motion'
import React, { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'

export type PageTransitionEffect = 
  | 'fade' 
  | 'slide-up' 
  | 'slide-down' 
  | 'slide-left' 
  | 'slide-right'
  | 'zoom'
  | 'scale'
  | 'rotate'
  | 'flip-x'
  | 'flip-y'
  | 'none'

interface PageTransitionProps {
  children: React.ReactNode
  effect?: PageTransitionEffect
  duration?: number
  className?: string
  hasBackground?: boolean
  backgroundClassName?: string
  staggerChildren?: boolean
  childrenDelay?: number
  customVariants?: Variants
  onAnimationComplete?: () => void
  key?: string | number
}

/**
 * PageTransition component applies animated transitions to page content
 * for a more polished UI experience between route changes or state transitions.
 */
export function PageTransition({
  children,
  effect = 'fade',
  duration = 0.4,
  className,
  hasBackground = false,
  backgroundClassName,
  staggerChildren = false,
  childrenDelay = 0.1,
  customVariants,
  onAnimationComplete,
  key,
}: PageTransitionProps) {
  const [isClient, setIsClient] = useState(false)
  
  // Only enable animations after component has mounted on client
  useEffect(() => {
    setIsClient(true)
  }, [])
  
  // Define base animation variants
  const getVariants = (): Variants => {
    if (customVariants) return customVariants
    
    switch (effect) {
      case 'fade':
        return {
          hidden: { opacity: 0 },
          visible: { 
            opacity: 1,
            transition: { duration }
          },
          exit: { 
            opacity: 0,
            transition: { duration: duration * 0.75 }
          }
        }
      case 'slide-up':
        return {
          hidden: { y: 20, opacity: 0 },
          visible: { 
            y: 0, 
            opacity: 1,
            transition: { 
              duration,
              type: 'spring',
              damping: 25,
              stiffness: 300
            }
          },
          exit: { 
            y: -20, 
            opacity: 0,
            transition: { duration: duration * 0.75 }
          }
        }
      case 'slide-down':
        return {
          hidden: { y: -20, opacity: 0 },
          visible: { 
            y: 0, 
            opacity: 1,
            transition: { 
              duration,
              type: 'spring',
              damping: 25,
              stiffness: 300
            }
          },
          exit: { 
            y: 20, 
            opacity: 0,
            transition: { duration: duration * 0.75 }
          }
        }
      case 'slide-left':
        return {
          hidden: { x: 20, opacity: 0 },
          visible: { 
            x: 0, 
            opacity: 1,
            transition: { 
              duration,
              type: 'spring',
              damping: 25,
              stiffness: 300
            }
          },
          exit: { 
            x: -20, 
            opacity: 0,
            transition: { duration: duration * 0.75 }
          }
        }
      case 'slide-right':
        return {
          hidden: { x: -20, opacity: 0 },
          visible: { 
            x: 0, 
            opacity: 1,
            transition: { 
              duration,
              type: 'spring',
              damping: 25,
              stiffness: 300
            }
          },
          exit: { 
            x: 20, 
            opacity: 0,
            transition: { duration: duration * 0.75 }
          }
        }
      case 'zoom':
        return {
          hidden: { scale: 0.95, opacity: 0 },
          visible: { 
            scale: 1, 
            opacity: 1,
            transition: { 
              duration,
              type: 'spring',
              damping: 25,
              stiffness: 300
            }
          },
          exit: { 
            scale: 1.05, 
            opacity: 0,
            transition: { duration: duration * 0.75 }
          }
        }
      case 'scale':
        return {
          hidden: { scale: 0.9, opacity: 0 },
          visible: { 
            scale: 1, 
            opacity: 1,
            transition: { duration }
          },
          exit: { 
            scale: 0.9, 
            opacity: 0,
            transition: { duration: duration * 0.75 }
          }
        }
      case 'rotate':
        return {
          hidden: { rotate: -5, opacity: 0, scale: 0.95 },
          visible: { 
            rotate: 0, 
            opacity: 1,
            scale: 1,
            transition: { duration }
          },
          exit: { 
            rotate: 5, 
            opacity: 0,
            scale: 0.95,
            transition: { duration: duration * 0.75 }
          }
        }
      case 'flip-x':
        return {
          hidden: { rotateX: 90, opacity: 0 },
          visible: { 
            rotateX: 0, 
            opacity: 1,
            transition: { duration }
          },
          exit: { 
            rotateX: -90, 
            opacity: 0,
            transition: { duration: duration * 0.75 }
          }
        }
      case 'flip-y':
        return {
          hidden: { rotateY: 90, opacity: 0 },
          visible: { 
            rotateY: 0, 
            opacity: 1,
            transition: { duration }
          },
          exit: { 
            rotateY: -90, 
            opacity: 0,
            transition: { duration: duration * 0.75 }
          }
        }
      case 'none':
        return {
          hidden: {},
          visible: {},
          exit: {}
        }
      default:
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1, transition: { duration } },
          exit: { opacity: 0, transition: { duration: duration * 0.75 } }
        }
    }
  }
  
  // Variants for staggered children animations
  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: 'beforeChildren',
        staggerChildren: staggerChildren ? childrenDelay : 0,
        duration: staggerChildren ? 0 : duration,
      },
    },
    exit: {
      opacity: 0,
      transition: {
        when: 'afterChildren',
        staggerChildren: staggerChildren ? childrenDelay / 2 : 0,
        staggerDirection: -1,
        duration: staggerChildren ? 0 : duration * 0.75,
      },
    },
  }
  
  // If not yet on client, render without animations
  if (!isClient) {
    return <div className={className}>{children}</div>
  }
  
  return (
    <AnimatePresence mode="wait" onExitComplete={onAnimationComplete}>
      <motion.div
        key={key}
        className={cn('relative', className)}
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={staggerChildren ? containerVariants : getVariants()}
        onAnimationComplete={() => {
          if (!staggerChildren) onAnimationComplete?.()
        }}
      >
        {hasBackground && (
          <motion.div
            className={cn(
              'absolute inset-0 -z-10 bg-background rounded-lg', 
              backgroundClassName
            )}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: duration * 0.5 }}
          />
        )}
        {staggerChildren ? (
          React.Children.map(children, (child, index) => {
            if (!React.isValidElement(child)) return child
            
            return (
              <motion.div
                key={index}
                variants={getVariants()}
                transition={{ delay: index * childrenDelay }}
              >
                {child}
              </motion.div>
            )
          })
        ) : (
          children
        )}
      </motion.div>
    </AnimatePresence>
  )
}

// Additional specialized components for common transition patterns

export function SlideIn({
  children,
  direction = 'up',
  duration = 0.4,
  className,
  delay = 0,
}: {
  children: React.ReactNode
  direction?: 'up' | 'down' | 'left' | 'right'
  duration?: number
  className?: string
  delay?: number
}) {
  const directionMap = {
    up: 'slide-up',
    down: 'slide-down',
    left: 'slide-left',
    right: 'slide-right',
  }
  
  return (
    <PageTransition
      effect={directionMap[direction] as PageTransitionEffect}
      duration={duration}
      className={className}
      customVariants={{
        hidden: { opacity: 0, y: direction === 'up' ? 20 : direction === 'down' ? -20 : 0, x: direction === 'left' ? 20 : direction === 'right' ? -20 : 0 },
        visible: { 
          opacity: 1, 
          y: 0, 
          x: 0,
          transition: { 
            duration,
            delay,
            type: 'spring',
            damping: 25,
            stiffness: 300
          }
        },
        exit: { 
          opacity: 0,
          transition: { duration: duration * 0.75 }
        }
      }}
    >
      {children}
    </PageTransition>
  )
}

export function FadeIn({
  children,
  duration = 0.4,
  className,
  delay = 0,
}: {
  children: React.ReactNode
  duration?: number
  className?: string
  delay?: number
}) {
  return (
    <PageTransition
      effect="fade"
      duration={duration}
      className={className}
      customVariants={{
        hidden: { opacity: 0 },
        visible: { 
          opacity: 1,
          transition: { 
            duration,
            delay
          }
        },
        exit: { 
          opacity: 0,
          transition: { duration: duration * 0.75 }
        }
      }}
    >
      {children}
    </PageTransition>
  )
}

export function StaggerChildren({
  children,
  staggerDelay = 0.1,
  duration = 0.4,
  effect = 'fade',
  className,
}: {
  children: React.ReactNode
  staggerDelay?: number
  duration?: number
  effect?: PageTransitionEffect
  className?: string
}) {
  return (
    <PageTransition
      effect={effect}
      duration={duration}
      className={className}
      staggerChildren={true}
      childrenDelay={staggerDelay}
    >
      {children}
    </PageTransition>
  )
} 