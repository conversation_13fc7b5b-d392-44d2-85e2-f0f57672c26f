'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import {
  RadarChart, Radar, PolarGrid, PolarAngleAxis, PolarRadiusAxis,
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';

interface HealthData {
  date: string;
  sleep: number;
  stress: number;
  energy: number;
  recovery: number;
}

export function HealthAnalytics() {
  // For now, we'll use mock data since there's no API endpoint for health metrics yet
  // In a real implementation, this would fetch from an API
  const [healthData, setHealthData] = useState<HealthData[]>([
    { date: "Mar 13", sleep: 7.5, stress: 4, energy: 8, recovery: 7 },
    { date: "Mar 14", sleep: 8, stress: 3, energy: 8.5, recovery: 8 },
    { date: "Mar 15", sleep: 7, stress: 5, energy: 7, recovery: 6 },
    { date: "Mar 16", sleep: 8.5, stress: 2, energy: 9, recovery: 9 },
    { date: "Mar 17", sleep: 7.5, stress: 4, energy: 7.5, recovery: 7 },
    { date: "Mar 18", sleep: 8, stress: 3, energy: 8, recovery: 8 },
    { date: "Mar 19", sleep: 7, stress: 4, energy: 7.5, recovery: 7 },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Calculate averages
  const avgSleep = healthData.reduce((sum, day) => sum + day.sleep, 0) / healthData.length;
  const avgRecovery = healthData.reduce((sum, day) => sum + day.recovery, 0) / healthData.length;

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Sleep Quality</CardTitle>
          <CardDescription>
            Track your sleep duration and quality
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={healthData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis domain={[0, 10]} />
              <Tooltip formatter={(value) => [`${value} hrs`, 'Sleep']} />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="sleep" 
                stroke="#8884d8" 
                name="Sleep (hrs)" 
                activeDot={{ r: 8 }} 
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Wellness Metrics</CardTitle>
          <CardDescription>
            Daily energy, stress, and recovery metrics
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart cx="50%" cy="50%" outerRadius={150} data={healthData}>
              <PolarGrid />
              <PolarAngleAxis dataKey="date" />
              <PolarRadiusAxis domain={[0, 10]} />
              <Radar name="Energy" dataKey="energy" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
              <Radar name="Stress" dataKey="stress" stroke="#ff8042" fill="#ff8042" fillOpacity={0.6} />
              <Radar name="Recovery" dataKey="recovery" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.6} />
              <Legend />
              <Tooltip />
            </RadarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="text-center text-sm text-muted-foreground mt-4">
        <p>Note: Health metrics tracking is in beta. Connect your wearable device for real-time data.</p>
      </div>
    </div>
  );
}
