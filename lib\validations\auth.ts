import * as z from "zod"

// Schema for user registration
export const registerSchema = z.object({
  fullName: z.string().min(2, { message: "Full name must be at least 2 characters." }).max(100),
  email: z.string().email({ message: "Please enter a valid email address." }),
  password: z.string()
    .min(8, { message: "Password must be at least 8 characters long." })
    .max(100)
    .regex(/[A-Z]/, { message: "Password must contain at least one uppercase letter." })
    .regex(/[a-z]/, { message: "Password must contain at least one lowercase letter." })
    .regex(/[0-9]/, { message: "Password must contain at least one number." })
    .regex(/[^A-Za-z0-9]/, { message: "Password must contain at least one special character." }),
  // Role is handled server-side, not needed in validation here
});

// Schema for password reset request (only needs email)
export const resetPasswordRequestSchema = z.object({
    email: z.string().email({ message: "Please enter a valid email address." }),
});

// Schema for setting a new password during reset
export const resetPasswordSchema = z.object({
  token: z.string().min(1, { message: "Token is required." }), // Basic check, API validates format/existence
  password: z.string()
    .min(8, { message: "Password must be at least 8 characters long." })
    .max(100)
    .regex(/[A-Z]/, { message: "Password must contain at least one uppercase letter." })
    .regex(/[a-z]/, { message: "Password must contain at least one lowercase letter." })
    .regex(/[0-9]/, { message: "Password must contain at least one number." })
    .regex(/[^A-Za-z0-9]/, { message: "Password must contain at least one special character." }),
}); 