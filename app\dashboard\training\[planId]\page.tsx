"use client"

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ArrowLeft,
  PlayCircle,
  CheckCircle2,
  ChevronRight,
  InfoIcon,
  BarChart,
  MessageSquare,
  FileText
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import React, { useState, useEffect } from "react"
import { toast } from "sonner"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface TrainingPlanDetailProps {
  params: Promise<{ planId: string }>
}

export default function TrainingPlanDetail({ params }: TrainingPlanDetailProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [plan, setPlan] = useState<any>(null)

  // Unwrap params with React.use()
  const unwrappedParams = React.use(params) as { planId: string }

  useEffect(() => {
    // Mock data for specific training plans
    const mockPlans = {
      "1": {
        id: "1",
        title: "Beginner Strength Program",
        description: "8-week program designed for beginners to build foundational strength. Focus on developing proper form and establishing a consistent workout routine.",
        difficulty: "beginner",
        trainer: {
          id: "tr1",
          name: "Sarah Johnson",
          avatarUrl: null,
        },
        startDate: "2023-10-15",
        endDate: "2023-12-10",
        progress: 35,
        totalWorkouts: 24,
        completedWorkouts: 8,
        nextWorkout: {
          id: "w1",
          title: "Upper Body Strength",
          scheduledFor: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
          exercises: [
            { name: "Bench Press", sets: 3, reps: "8-10", rest: "90 sec" },
            { name: "Bent-over Rows", sets: 3, reps: "10-12", rest: "60 sec" },
            { name: "Overhead Press", sets: 3, reps: "8-10", rest: "90 sec" },
            { name: "Lat Pulldowns", sets: 3, reps: "10-12", rest: "60 sec" },
            { name: "Bicep Curls", sets: 3, reps: "12-15", rest: "45 sec" },
            { name: "Tricep Extensions", sets: 3, reps: "12-15", rest: "45 sec" }
          ]
        },
        upcomingWorkouts: [
          {
            id: "w2",
            title: "Lower Body Strength",
            scheduledFor: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days from now
          },
          {
            id: "w3",
            title: "Full Body Circuit",
            scheduledFor: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days from now
          }
        ],
        completedWorkoutHistory: [
          {
            id: "w0",
            title: "Full Body Introduction",
            completedOn: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
            performance: "Good"
          }
        ]
      },
      "2": {
        id: "2",
        title: "Core Conditioning",
        description: "4-week core strengthening and stabilization program. This program targets all aspects of your core musculature with a focus on building stability and endurance.",
        difficulty: "intermediate",
        trainer: {
          id: "tr2",
          name: "Michael Torres",
          avatarUrl: null,
        },
        startDate: "2023-11-01",
        endDate: "2023-11-28",
        progress: 50,
        totalWorkouts: 12,
        completedWorkouts: 6,
        nextWorkout: {
          id: "cw1",
          title: "Core Stability Circuit",
          scheduledFor: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(), // 18 hours from now
          exercises: [
            { name: "Plank", sets: 3, reps: "30-60 sec hold", rest: "45 sec" },
            { name: "Russian Twists", sets: 3, reps: "15 each side", rest: "30 sec" },
            { name: "Dead Bug", sets: 3, reps: "10 each side", rest: "30 sec" },
            { name: "Bird Dog", sets: 3, reps: "10 each side", rest: "30 sec" },
            { name: "Hollow Hold", sets: 3, reps: "20-30 sec", rest: "45 sec" }
          ]
        },
        upcomingWorkouts: [
          {
            id: "cw2",
            title: "Dynamic Core Workout",
            scheduledFor: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
          }
        ],
        completedWorkoutHistory: [
          {
            id: "cw0",
            title: "Core Assessment",
            completedOn: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
            performance: "Average"
          }
        ]
      }
    };

    // Get the plan based on ID - use unwrappedParams.planId instead of params.planId
    const foundPlan = mockPlans[unwrappedParams.planId as keyof typeof mockPlans];

    // Simulate loading delay
    const timer = setTimeout(() => {
      if (foundPlan) {
        setPlan(foundPlan);
      }
      setIsLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, [unwrappedParams.planId]); // Dependency on unwrappedParams.planId instead of params.planId

  // Start workout function
  const startWorkout = () => {
    if (!plan?.nextWorkout) {
      toast.error("No workout available to start");
      return;
    }

    // Show loading toast
    toast.loading("Preparing your workout session...");

    // Simulate API request delay
    setTimeout(() => {
      toast.dismiss();
      toast.success("Workout started! Good luck with your session!", {
        description: `${plan.nextWorkout.title} - ${plan.nextWorkout.exercises.length} exercises`,
        action: {
          label: "View Guide",
          onClick: () => {
            // This could open a modal with exercise guide in a real app
            toast.info("Exercise guide would open here");
          }
        }
      });

      // In a real app, this would redirect to a workout session page
      router.push(`/dashboard/workout-session/${plan.nextWorkout.id}`);
    }, 800);
  };

  if (isLoading) {
    return (
      <div className="container py-10 flex justify-center">
        <div className="animate-spin h-8 w-8 border-t-2 border-primary rounded-full"></div>
      </div>
    );
  }

  if (!plan) {
    return (
      <div className="container py-10">
        <Button variant="outline" asChild className="mb-8">
          <Link href="/dashboard/workouts/current">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to My Workouts
          </Link>
        </Button>

        <Card className="mx-auto max-w-md text-center p-6">
          <CardContent className="pt-6">
            <InfoIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h2 className="text-2xl font-bold mb-2">Training Plan Not Found</h2>
            <p className="text-muted-foreground mb-6">
              The training plan you're looking for doesn't exist or you don't have access to it.
            </p>
            <Button asChild>
              <Link href="/dashboard/workouts/current">
                View My Workouts
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit'
    });
  };

  return (
    <div className="container py-10">
      <Button variant="outline" asChild className="mb-8">
        <Link href="/dashboard/my-training-plans">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Training Plans
        </Link>
      </Button>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          <Card className="border-2 border-primary/20 overflow-hidden">
            <div className="h-24 bg-gradient-to-r from-primary/20 to-primary/5 flex items-center px-6">
              <div>
                <h1 className="text-2xl font-bold">{plan.title}</h1>
                <p className="text-muted-foreground">{plan.trainer.name} • {formatDate(plan.startDate)} to {formatDate(plan.endDate)}</p>
              </div>
            </div>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <p className="text-muted-foreground">{plan.description}</p>

                <div className="flex items-center gap-3 p-3 bg-muted/40 rounded-lg">
                  <Avatar className="h-12 w-12 border-2 border-primary/20">
                    <AvatarImage src={plan.trainer.avatarUrl || ""} />
                    <AvatarFallback>{plan.trainer.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center">
                      <div className="font-medium text-lg">{plan.trainer.name}</div>
                      <Badge variant="outline" className="ml-2">{plan.difficulty}</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">Your Coach</div>
                  </div>
                  <Button variant="outline" size="sm" className="ml-auto" asChild>
                    <Link href={`/dashboard/messages?trainerId=${plan.trainer.id}`} onClick={() => {
                      toast.info(`Starting conversation with ${plan.trainer.name}`, {
                        description: "You can now message your trainer about this training plan."
                      });
                    }}>
                      <MessageSquare className="mr-2 h-4 w-4" />
                      Message Coach
                    </Link>
                  </Button>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                  <div className="bg-muted/50 p-4 rounded-lg text-center">
                    <div className="text-muted-foreground text-xs uppercase font-medium mb-1">Total Workouts</div>
                    <div className="text-2xl font-bold">{plan.totalWorkouts}</div>
                  </div>
                  <div className="bg-muted/50 p-4 rounded-lg text-center">
                    <div className="text-muted-foreground text-xs uppercase font-medium mb-1">Completed</div>
                    <div className="text-2xl font-bold">{plan.completedWorkouts}</div>
                  </div>
                  <div className="bg-muted/50 p-4 rounded-lg text-center">
                    <div className="text-muted-foreground text-xs uppercase font-medium mb-1">Remaining</div>
                    <div className="text-2xl font-bold">{plan.totalWorkouts - plan.completedWorkouts}</div>
                  </div>
                  <div className="bg-muted/50 p-4 rounded-lg text-center">
                    <div className="text-muted-foreground text-xs uppercase font-medium mb-1">Progress</div>
                    <div className="text-2xl font-bold">{plan.progress}%</div>
                  </div>
                </div>

                <div className="mt-2">
                  <Progress value={plan.progress} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="next-workout" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="next-workout">Next Workout</TabsTrigger>
              <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>

            <TabsContent value="next-workout" className="space-y-4">
              <Card className="border-2 border-primary/20">
                <CardHeader className="pb-2 border-b">
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle className="text-xl">{plan.nextWorkout.title}</CardTitle>
                      <CardDescription className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4" />
                        {formatDate(plan.nextWorkout.scheduledFor)} at {formatTime(plan.nextWorkout.scheduledFor)}
                      </CardDescription>
                    </div>
                    <Button onClick={startWorkout} className="bg-green-600 hover:bg-green-700 text-white">
                      <PlayCircle className="mr-2 h-4 w-4" />
                      Start Workout
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  <h3 className="text-sm font-medium text-muted-foreground mb-3">Exercises ({plan.nextWorkout.exercises.length})</h3>
                  <div className="space-y-3">
                    {plan.nextWorkout.exercises.map((exercise: any, index: number) => (
                      <div key={index} className="border rounded-md p-3 hover:bg-muted/30 transition-colors">
                        <div className="flex items-center">
                          <div className="bg-primary/10 text-primary font-medium h-6 w-6 rounded-full flex items-center justify-center text-sm mr-3">
                            {index + 1}
                          </div>
                          <div className="font-medium">{exercise.name}</div>
                        </div>
                        <div className="grid grid-cols-3 gap-2 mt-2 text-sm ml-9">
                          <div className="flex items-center">
                            <span className="bg-muted h-5 w-5 rounded-full flex items-center justify-center mr-1 text-xs">S</span>
                            <span className="text-muted-foreground mr-1">Sets:</span>
                            <span className="font-medium">{exercise.sets}</span>
                          </div>
                          <div className="flex items-center">
                            <span className="bg-muted h-5 w-5 rounded-full flex items-center justify-center mr-1 text-xs">R</span>
                            <span className="text-muted-foreground mr-1">Reps:</span>
                            <span className="font-medium">{exercise.reps}</span>
                          </div>
                          <div className="flex items-center">
                            <span className="bg-muted h-5 w-5 rounded-full flex items-center justify-center mr-1 text-xs">T</span>
                            <span className="text-muted-foreground mr-1">Rest:</span>
                            <span className="font-medium">{exercise.rest}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter className="bg-muted/50 border-t pt-4">
                  <Button className="w-full bg-green-600 hover:bg-green-700 text-white" onClick={startWorkout}>
                    <PlayCircle className="mr-2 h-4 w-4" />
                    Start Workout
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="upcoming" className="space-y-4">
              <Card>
                <CardHeader className="pb-2 border-b">
                  <CardTitle>Upcoming Workouts</CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="space-y-3">
                    {plan.upcomingWorkouts.length > 0 ? (
                      plan.upcomingWorkouts.map((workout: any) => (
                        <div
                          key={workout.id}
                          className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/30 cursor-pointer transition-colors"
                          onClick={() => {
                            toast.info(`Preview: ${workout.title}`, {
                              description: `Scheduled for ${formatDate(workout.scheduledFor)}`,
                              action: {
                                label: "View Details",
                                onClick: () => {
                                  router.push(`/dashboard/training/workout/${workout.id}`);
                                }
                              }
                            });
                          }}
                        >
                          <div className="flex items-center">
                            <div className="bg-muted h-10 w-10 rounded-full flex items-center justify-center mr-3">
                              <Dumbbell className="h-5 w-5 text-muted-foreground" />
                            </div>
                            <div>
                              <div className="font-medium">{workout.title}</div>
                              <div className="text-sm text-muted-foreground flex items-center">
                                <Calendar className="mr-2 h-3 w-3" />
                                {formatDate(workout.scheduledFor)}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <Badge variant="outline">Upcoming</Badge>
                            <ChevronRight className="h-5 w-5 text-muted-foreground ml-2" />
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-12 bg-muted/30 rounded-lg">
                        <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                        <h3 className="text-lg font-medium mb-1">No upcoming workouts</h3>
                        <p className="text-sm text-muted-foreground">
                          You've completed all scheduled workouts
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <Card>
                <CardHeader className="pb-2 border-b">
                  <CardTitle>Workout History</CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="space-y-3">
                    {plan.completedWorkoutHistory.length > 0 ? (
                      plan.completedWorkoutHistory.map((workout: any) => (
                        <div
                          key={workout.id}
                          className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/30 cursor-pointer transition-colors"
                          onClick={() => {
                            toast.info(`${workout.title} - Performance Analysis`, {
                              description: `Completed on ${formatDate(workout.completedOn)}. Performance rating: ${workout.performance}`,
                              action: {
                                label: "View Full Report",
                                onClick: () => {
                                  router.push(`/dashboard/training/workout-history/${workout.id}`);
                                }
                              }
                            });
                          }}
                        >
                          <div className="flex items-center">
                            <div className="bg-green-100 dark:bg-green-900 h-10 w-10 rounded-full flex items-center justify-center mr-3">
                              <CheckCircle2 className="h-5 w-5 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <div className="font-medium">{workout.title}</div>
                              <div className="text-sm text-muted-foreground">
                                Completed {formatDate(workout.completedOn)}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <div className={`px-2 py-1 rounded text-xs font-medium ${
                              workout.performance === "Good" ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300" :
                              workout.performance === "Average" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300" :
                              "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                            }`}>
                              {workout.performance}
                            </div>
                            <BarChart className="h-5 w-5 text-muted-foreground ml-2" />
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-12 bg-muted/30 rounded-lg">
                        <BarChart className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                        <h3 className="text-lg font-medium mb-1">No workout history</h3>
                        <p className="text-sm text-muted-foreground">
                          Complete your first workout to see it here
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <Card className="border-2 border-primary/20">
            <CardHeader className="pb-3 border-b">
              <CardTitle className="flex items-center text-lg">
                <Clock className="mr-2 h-5 w-5 text-primary" />
                Next Session
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 h-12 w-12 rounded-full flex items-center justify-center">
                    <Dumbbell className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <div className="font-medium text-lg">{plan.nextWorkout.title}</div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Calendar className="mr-2 h-4 w-4" />
                      {formatDate(plan.nextWorkout.scheduledFor)}
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Clock className="mr-2 h-4 w-4" />
                      {formatTime(plan.nextWorkout.scheduledFor)}
                    </div>
                  </div>
                </div>

                <Button className="w-full bg-green-600 hover:bg-green-700 text-white" onClick={startWorkout}>
                  <PlayCircle className="mr-2 h-4 w-4" />
                  Start Workout
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3 border-b">
              <CardTitle className="flex items-center text-lg">
                <FileText className="mr-2 h-5 w-5" />
                Nutrition Plan
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  A personalized nutrition plan to support your training goals
                </p>

                <div className="bg-muted/50 rounded-lg p-3">
                  <h3 className="font-medium mb-1">Daily targets:</h3>
                  <div className="grid grid-cols-3 gap-2 text-center">
                    <div>
                      <div className="text-xs text-muted-foreground">Calories</div>
                      <div className="font-medium">2,400</div>
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground">Protein</div>
                      <div className="font-medium">180g</div>
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground">Carbs</div>
                      <div className="font-medium">220g</div>
                    </div>
                  </div>
                </div>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    toast.info("Nutrition Plan", {
                      description: "Opening nutrition plan associated with this training program",
                      action: {
                        label: "View Meal Plans",
                        onClick: () => router.push(`/dashboard/nutrition/${plan.id}`)
                      }
                    });
                  }}
                >
                  View Nutrition Plan
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3 border-b">
              <CardTitle className="flex items-center text-lg">
                <MessageSquare className="mr-2 h-5 w-5" />
                Contact Trainer
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="flex items-center gap-3 mb-4">
                <Avatar>
                  <AvatarImage src={plan.trainer.avatarUrl || ""} />
                  <AvatarFallback>{plan.trainer.name[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{plan.trainer.name}</div>
                  <div className="text-sm text-muted-foreground">Your Coach</div>
                </div>
              </div>
              <Button asChild variant="outline" className="w-full">
                <Link href={`/dashboard/messages?trainerId=${plan.trainer.id}`} onClick={() => {
                  toast.info(`Starting conversation with ${plan.trainer.name}`, {
                    description: "You can now message your trainer about this training plan."
                  });
                }}>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Message {plan.trainer.name.split(' ')[0]}
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}