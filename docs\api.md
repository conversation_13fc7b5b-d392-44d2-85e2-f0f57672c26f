# Clear Coach API Documentation

This document provides details on the available API endpoints, their parameters, and expected responses.

## Authentication

### POST /api/auth/register

Register a new user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "name": "<PERSON>"
}
```

**Response:**
```json
{
  "id": "user_id",
  "email": "<EMAIL>",
  "name": "<PERSON>",
  "role": "client"
}
```

### POST /api/auth/login

Log in an existing user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response:**
```json
{
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "role": "client"
  }
}
```

## Products

### GET /api/products

Get a list of products.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `type`: Filter by product type (optional)

**Response:**
```json
{
  "products": [
    {
      "id": "product_id",
      "title": "Product Title",
      "description": "Product description",
      "price": 29.99,
      "type": "digital",
      "thumbnailUrl": "https://example.com/thumbnail.jpg"
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "pages": 10
  }
}
```

### GET /api/products/:id

Get a single product by ID.

**Response:**
```json
{
  "id": "product_id",
  "title": "Product Title",
  "description": "Product description",
  "price": 29.99,
  "type": "digital",
  "thumbnailUrl": "https://example.com/thumbnail.jpg",
  "fileUrl": "https://example.com/file.pdf"
}
```

## Cart

### GET /api/cart

Get the current user's cart.

**Response:**
```json
{
  "items": [
    {
      "id": "cart_item_id",
      "productId": "product_id",
      "quantity": 1,
      "product": {
        "title": "Product Title",
        "price": 29.99,
        "thumbnailUrl": "https://example.com/thumbnail.jpg"
      }
    }
  ],
  "subtotal": 29.99
}
```

### POST /api/cart

Add an item to the cart.

**Request Body:**
```json
{
  "productId": "product_id",
  "quantity": 1
}
```

**Response:**
```json
{
  "id": "cart_item_id",
  "productId": "product_id",
  "quantity": 1
}
```

## Checkout

### POST /api/checkout

Create a checkout session.

**Request Body:**
```json
{
  "returnUrl": "https://example.com/success"
}
```

**Response:**
```json
{
  "id": "checkout_session_id",
  "url": "https://checkout.stripe.com/..."
}
```

## Subscriptions

### GET /api/subscription-tiers

Get available subscription tiers.

**Response:**
```json
[
  {
    "id": "tier_id",
    "name": "Basic",
    "description": "Basic subscription tier",
    "price": 9.99,
    "features": ["Feature 1", "Feature 2"]
  }
]
```

### POST /api/subscriptions

Subscribe to a tier.

**Request Body:**
```json
{
  "tierId": "tier_id"
}
```

**Response:**
```json
{
  "id": "subscription_id",
  "status": "active",
  "startDate": "2023-01-01T00:00:00Z",
  "endDate": "2023-02-01T00:00:00Z",
  "tier": {
    "id": "tier_id",
    "name": "Basic"
  }
}
```

## Workouts

### POST /api/workouts/feedback

Save feedback for a workout.

**Request Body:**
```json
{
  "workoutId": "workout_id",
  "clientId": "client_id",
  "feedback": "Great job on the workout! Your form is improving."
}
```

**Response:**
```json
{
  "id": "workout_id",
  "notes": "Great job on the workout! Your form is improving.",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

## Subscription Access

### POST /api/subscriptions/access

Check access to a training plan.

**Request Body:**
```json
{
  "clientId": "client_id",
  "trainingPlanId": "training_plan_id",
  "updateProgress": true
}
```

**Response:**
```json
{
  "hasAccess": true
}
```

Or if access is denied:

```json
{
  "hasAccess": false,
  "reason": "time-locked",
  "unlockWeek": 4,
  "currentWeek": 2
}
```
