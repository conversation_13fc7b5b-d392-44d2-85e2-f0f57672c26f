"use client"

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface Progress {
  id: string
  date: Date
  weight: number
  bodyFat: number | null
  measurements: any
  createdAt: Date
  updatedAt: Date
  clientId: string
}

interface ProgressChartProps {
  entries: Progress[]
}

export function ProgressChart({ entries }: ProgressChartProps) {
  const sortedEntries = [...entries].sort((a, b) =>
    new Date(a.date).getTime() - new Date(b.date).getTime()
  )

  // Format data for Recharts
  const chartData = sortedEntries.map(entry => ({
    date: new Date(entry.date).toLocaleDateString(),
    weight: entry.weight,
    bodyFat: entry.bodyFat
  }))

  return (
    <Card>
      <CardHeader>
        <CardTitle>Progress Chart</CardTitle>
        <CardDescription>Track your weight and body fat percentage over time</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis domain={['dataMin - 5', 'dataMax + 5']} />
              {chartData.some(data => data.bodyFat) && (
                <YAxis yAxisId="right" orientation="right" domain={[0, 'dataMax + 5']} />
              )}
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="weight"
                stroke="#8884d8"
                name="Weight (lbs)"
                activeDot={{ r: 8 }}
              />
              {chartData.some(data => data.bodyFat) && (
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="bodyFat"
                  stroke="#82ca9d"
                  name="Body Fat %"
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}

