# ARG NODE_VERSION=22
# ARG PNPM_VERSION=10

# ==== Base Stage ====
FROM node:22-slim AS base

# Install pnpm
# ARG PNPM_VERSION
RUN npm install -g pnpm@10

# Set working directory
WORKDIR /app

# Copy package manifests
COPY package.json pnpm-lock.yaml* ./

# ==== Dependencies Stage ====
FROM base AS dependencies

# Install ALL dependencies (including devDependencies)
RUN pnpm install

# Copy Prisma schema
COPY prisma ./prisma/

# Generate Prisma Client
RUN pnpm exec prisma generate

# ==== Development Stage ====
FROM base AS development
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    netcat-openbsd \
    openssl \
    && rm -rf /var/lib/apt/lists/*

# Copy package manifests
COPY package.json pnpm-lock.yaml* ./

# Install ALL dependencies
RUN pnpm install

# Copy types directory if it exists
COPY types ./types/

# Copy the rest of the application code
COPY . .

# Generate Prisma Client
RUN pnpm exec prisma generate

# Install additional Next.js dependencies
RUN pnpm add @swc/helpers @swc/core

# Remove global Next.js installation to avoid version conflicts
RUN npm uninstall -g next
RUN npm install -g npm@latest

# Clean up any existing Next.js cache
RUN rm -rf .next

# Set NODE_ENV to development
ENV NODE_ENV=development

# Copy and set execute permission for entrypoint script
COPY entrypoint.sh /entrypoint.sh
COPY start.sh /start.sh
RUN chmod +x /entrypoint.sh && \
    chmod +x /start.sh && \
    sed -i 's/\r$//' /entrypoint.sh && \
    sed -i 's/\r$//' /start.sh

# Expose ports
EXPOSE 3000
EXPOSE 5555

# Use the start.sh script to start the application
CMD ["/start.sh"]

# ==== Production Stage ====
FROM node:22-slim AS production
WORKDIR /app

# Set NODE_ENV to production
ENV NODE_ENV=production

# Copy necessary files from previous stages
COPY --from=dependencies /app/node_modules ./node_modules
COPY --from=dependencies /app/package.json ./
COPY --from=dependencies /app/pnpm-lock.yaml* ./
COPY --from=dependencies /app/prisma ./prisma
COPY --from=dependencies /app/node_modules/.prisma ./node_modules/.prisma

# Copy types directory if it exists
COPY types ./types/

# Copy application code
COPY . .

# Expose port
EXPOSE 3000

# Command to run the production application
CMD ["pnpm", "start"]