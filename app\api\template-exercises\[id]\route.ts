import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function PATCH(
  request: Request,
  context: any // Use 'any' workaround
) {
  try {
    const session = await getServerSession(authOptions)
    // Allow only admins to PATCH template exercises
    if (!session?.user?.id || session.user.role !== "admin") {
      return new NextResponse("Forbidden: Admins only", { status: 403 })
    }

    // Safely access params
    const templateId = context?.params?.id;
    if (!templateId) {
      return new NextResponse("Template Exercise ID missing in URL", { status: 400 });
    }

    const body = await request.json()
    // Destructure expected fields for a template exercise
    const {
      name, description, sets, reps, duration, restTime,
      videoUrl, type, difficulty, muscleGroup, equipment, thumbnailUrl
      // Add any other fields relevant to your template exercise model
     } = body

    // Add validation for required fields
    if (!name || typeof name !== 'string') {
      return new NextResponse("Missing or invalid required fields (e.g., name)", { status: 400 })
    }

    // Prepare update data - use Prisma field names
    // Assuming your model is 'Exercise' with isTemplate=true
    const updateData: Record<string, any> = {};
    const allowedFields = ['name', 'description', 'sets', 'reps', 'duration', 'restTime', 'videoUrl', 'type', 'difficulty', 'muscleGroup', 'equipment', 'thumbnailUrl'];
    
    for (const field of allowedFields) {
        if (body[field] !== undefined) {
            updateData[field] = body[field];
        }
    }
    updateData.isTemplate = true; // Ensure it remains a template

    if (Object.keys(updateData).length <= 1) { // Only isTemplate added
        return new NextResponse("No valid fields provided for update", { status: 400 });
    }

    const exercise = await prisma.exercise.update({
      where: {
        id: templateId,
        isTemplate: true, // Ensure we only update templates
      },
      data: updateData,
    })

    return NextResponse.json(exercise)
  } catch (error) {
    console.error("[TEMPLATE_EXERCISE_UPDATE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function DELETE(
  request: Request,
  context: any // Use 'any' workaround
) {
  try {
    const session = await getServerSession(authOptions)
    // Allow only admins to DELETE template exercises
    if (!session?.user?.id || session.user.role !== "admin") {
      return new NextResponse("Forbidden: Admins only", { status: 403 })
    }

    // Safely access params
    const templateId = context?.params?.id;
    if (!templateId) {
      return new NextResponse("Template Exercise ID missing in URL", { status: 400 });
    }

    // Use Exercise model with isTemplate filter
    await prisma.exercise.deleteMany({
      where: {
        id: templateId,
        isTemplate: true, // Ensure we only delete templates
      },
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[TEMPLATE_EXERCISE_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 