'use client';

import Link from "next/link";
import { Spark<PERSON>, Target } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface UpgradeMessageProps {
  tier: string;
  className?: string;
}

export function UpgradeMessage({ tier, className }: UpgradeMessageProps) {
  // Different messages based on tier
  const messages = {
    basic: {
      title: "Unlock the full experience",
      description: "Get access to analytics, coaching, and premium content with our Premium plan.",
      cta: "Upgrade to Premium",
      icon: Sparkles,
      href: "/dashboard/upgrade",
    },
    mid: {
      title: "Unlock the full experience",
      description: "Get access to advanced analytics, nutrition tracking, and premium content.",
      cta: "Upgrade to Premium",
      icon: Sparkles,
      href: "/dashboard/upgrade",
    },
    premium: {
      title: "Want results faster?",
      description: "Work 1-on-1 with your trainer for personalized coaching and faster results.",
      cta: "Add 1:1 Coaching",
      icon: Target,
      href: "/dashboard/coaching/request",
    },
    coaching: null, // No upgrade message for coaching tier
  };

  const message = messages[tier as keyof typeof messages];

  // Don't render anything for coaching tier
  if (!message) return null;

  const Icon = message.icon;

  return (
    <div className="relative overflow-hidden rounded-xl">
      <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 opacity-70"></div>
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

      <Card className={`bg-transparent backdrop-blur-sm border-primary/10 shadow-md ${className}`}>
        <CardContent className="p-5 flex flex-col md:flex-row md:items-center justify-between gap-4 relative z-10">
          <div className="flex items-center gap-4">
            <div className="h-12 w-12 rounded-full bg-background/80 flex items-center justify-center shadow-sm">
              <Icon className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold text-lg">{message.title}</h3>
              <p className="text-sm text-muted-foreground mt-1">{message.description}</p>
            </div>
          </div>

          <Button asChild className="bg-primary hover:bg-primary/90 md:self-center md:flex-shrink-0 h-11">
            <Link href={message.href}>
              {message.cta}
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
