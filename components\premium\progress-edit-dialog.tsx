'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";

interface ProgressEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentEntry: any;
  onSuccess: () => void;
}

export function ProgressEditDialog({ open, onOpenChange, currentEntry, onSuccess }: ProgressEditDialogProps) {
  const { toast } = useToast();

  const handleSuccess = () => {
    onOpenChange(false);
    toast({
      title: "Success",
      description: "Progress entry updated successfully",
    });
    onSuccess();
  };

  const [weight, setWeight] = useState<number>(currentEntry?.weight || 0);
  const [bodyFat, setBodyFat] = useState<number | undefined>(currentEntry?.bodyFat);
  const [waist, setWaist] = useState<number | undefined>(currentEntry?.waist);
  const [chest, setChest] = useState<number | undefined>(currentEntry?.chest);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/progress/${currentEntry.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          weight,
          bodyFat,
          measurements: {
            waist,
            chest,
          },
          date: currentEntry.date,
          notes: `Updated on ${format(new Date(), 'PPP')}`,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update progress');
      }

      handleSuccess();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update progress entry",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Progress Entry</DialogTitle>
          <DialogDescription>
            Update your progress details
          </DialogDescription>
        </DialogHeader>
        {currentEntry && (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="weight">Weight (lbs)</Label>
              <Input
                id="weight"
                type="number"
                step="0.1"
                value={weight}
                onChange={(e) => setWeight(parseFloat(e.target.value) || 0)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="bodyFat">Body Fat %</Label>
              <Input
                id="bodyFat"
                type="number"
                step="0.1"
                value={bodyFat || ''}
                onChange={(e) => setBodyFat(e.target.value ? parseFloat(e.target.value) : undefined)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="waist">Waist (inches)</Label>
                <Input
                  id="waist"
                  type="number"
                  step="0.1"
                  value={waist || ''}
                  onChange={(e) => setWaist(e.target.value ? parseFloat(e.target.value) : undefined)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="chest">Chest (inches)</Label>
                <Input
                  id="chest"
                  type="number"
                  step="0.1"
                  value={chest || ''}
                  onChange={(e) => setChest(e.target.value ? parseFloat(e.target.value) : undefined)}
                />
              </div>
            </div>

            <div className="pt-4 flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
