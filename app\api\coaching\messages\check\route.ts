import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export const dynamic = 'force-dynamic'

// This endpoint checks for new messages since a given timestamp
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;
    
    // Get the last update time from the query parameters
    const lastUpdateTime = request.nextUrl.searchParams.get("lastUpdateTime");
    const conversationId = request.nextUrl.searchParams.get("conversationId");
    
    // If no last update time is provided, return an error
    if (!lastUpdateTime) {
      return NextResponse.json({ error: "Last update time is required" }, { status: 400 });
    }
    
    // Parse the last update time
    const lastUpdate = new Date(lastUpdateTime);
    
    // Check for new messages
    const hasNewMessages = await prisma.message.findFirst({
      where: {
        receiverId: userId,
        createdAt: {
          gt: lastUpdate,
        },
        ...(conversationId ? { conversationId } : {}),
      },
    });
    
    // Check for new notifications
    const hasNewNotifications = await prisma.notification.findFirst({
      where: {
        userId,
        createdAt: {
          gt: lastUpdate,
        },
        read: false,
      },
    });
    
    // Get the total unread count
    const totalUnreadCount = await prisma.notification.count({
      where: {
        userId,
        read: false,
      },
    });
    
    // Return the result
    return NextResponse.json({
      hasNewMessages: !!hasNewMessages,
      hasNewNotifications: !!hasNewNotifications,
      totalUnreadCount,
      conversationUpdates: conversationId ? {
        hasNewMessages: !!hasNewMessages,
      } : null,
    });
  } catch (error) {
    console.error("Error checking for updates:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
