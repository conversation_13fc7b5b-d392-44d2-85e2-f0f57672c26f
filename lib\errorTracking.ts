/**
 * Error tracking service for monitoring and reporting errors
 */

// Configuration for the error tracking service
interface ErrorTrackingConfig {
  environment: 'development' | 'test' | 'production';
  apiEndpoint?: string;
  maxBreadcrumbs: number;
  sampleRate: number;
}

// Default configuration
const defaultConfig: ErrorTrackingConfig = {
  environment: process.env.NODE_ENV as 'development' | 'test' | 'production',
  maxBreadcrumbs: 100,
  sampleRate: 1.0, // 100% of errors
};

// Breadcrumb type for tracking user actions
interface Breadcrumb {
  type: 'navigation' | 'ui' | 'http' | 'info';
  category: string;
  message: string;
  timestamp: number;
  data?: Record<string, any>;
}

// Error tracking service
class ErrorTrackingService {
  private breadcrumbs: Breadcrumb[] = [];
  public config: ErrorTrackingConfig;
  private initialized = false;
  private userContext: any = null;

  constructor() {
    this.config = { ...defaultConfig };
  }

  /**
   * Initialize the error tracking service
   */
  public init(options: Partial<ErrorTrackingConfig> = {}): void {
    if (this.initialized) {
      return; // Only initialize once
    }

    this.config = { ...defaultConfig, ...options };
    this.initialized = true;
    console.log(`ErrorTracking initialized in ${this.config.environment} mode`);

    // Set up global error handlers in browser environment
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        this.captureException(event.error || new Error(event.message));
      });

      window.addEventListener('unhandledrejection', (event) => {
        this.captureException(event.reason || new Error('Unhandled Promise rejection'));
      });
    }
  }

  /**
   * Add a breadcrumb to track user actions
   */
  public addBreadcrumb(breadcrumb: Omit<Breadcrumb, 'timestamp'>): void {
    if (!this.initialized) {
      console.warn('Error tracking not initialized');
      return;
    }

    const fullBreadcrumb: Breadcrumb = {
      ...breadcrumb,
      timestamp: Date.now(),
    };

    this.breadcrumbs.push(fullBreadcrumb);

    // Maintain max breadcrumbs limit
    if (this.breadcrumbs.length > this.config.maxBreadcrumbs) {
      this.breadcrumbs = this.breadcrumbs.slice(-this.config.maxBreadcrumbs);
    }
  }

  /**
   * Set user context for error tracking
   */
  public setUser(user: { id?: string; email?: string; role?: string }): void {
    this.userContext = user;
  }

  /**
   * Capture a message and send it to the error tracking service
   */
  public captureMessage(message: string, severity: string = 'info', source: string = 'app'): void {
    if (!this.initialized) {
      console.warn('Error tracking not initialized');
      return;
    }

    const event = {
      message,
      severity,
      source,
      timestamp: new Date(),
      context: {
        user: this.userContext,
        metadata: {
          breadcrumbs: [...this.breadcrumbs]
        }
      }
    };

    this.processEvent(event);
  }

  /**
   * Capture an exception and send it to the error tracking service
   */
  public captureException(error: Error | string | any, severity: string = 'error', source: string = 'app'): void {
    if (!this.initialized) {
      console.warn('Error tracking not initialized');
      return;
    }

    // Sample errors based on sample rate
    if (Math.random() > this.config.sampleRate) {
      return;
    }

    // Handle different error types
    let errorMessage: string;
    let errorStack: string | undefined;

    if (error instanceof Error) {
      errorMessage = error.message;
      errorStack = error.stack;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else {
      errorMessage = String(error);
    }

    const event = {
      message: errorMessage,
      stack: errorStack,
      severity,
      source,
      timestamp: new Date(),
      context: {
        user: this.userContext,
        metadata: {
          breadcrumbs: [...this.breadcrumbs]
        }
      }
    };

    this.processEvent(event);
  }

  /**
   * Process an error event before sending
   */
  public processEvent(event: any): any {
    // Add user context if available
    if (typeof window !== 'undefined' && (window as any).__USER_CONTEXT__) {
      event.user = (window as any).__USER_CONTEXT__;
    }

    // In production, send to API endpoint
    if (this.config.environment === 'production' && this.config.apiEndpoint) {
      this.sendToApi(event);
    } else {
      // In development, log to console
      console.error('Error captured:', event);
    }

    return event;
  }

  /**
   * Send error data to API endpoint
   */
  private async sendToApi(data: any): Promise<void> {
    if (!this.config.apiEndpoint) return;

    try {
      await fetch(this.config.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
    } catch (err) {
      console.error('Error sending to API:', err);
    }
  }

  /**
   * Clear all breadcrumbs
   */
  public clearBreadcrumbs(): void {
    this.breadcrumbs = [];
  }
}

// Create singleton instance
const errorTracking = new ErrorTrackingService();

/**
 * Capture an exception with the error tracking service
 */
export function captureException(error: Error | string | any, severity: string = 'error', source: string = 'app'): void {
  errorTracking.captureException(error, severity, source);
}

/**
 * Capture a message with the error tracking service
 */
export function captureMessage(message: string, severity: string = 'info', source: string = 'app'): void {
  errorTracking.captureMessage(message, severity, source);
}

/**
 * Add a breadcrumb to track user actions
 */
export function addBreadcrumb(breadcrumb: Omit<Breadcrumb, 'timestamp'>): void {
  errorTracking.addBreadcrumb(breadcrumb);
}

/**
 * Set user context for error tracking
 */
export function setUser(user: { id?: string; email?: string; role?: string }): void {
  errorTracking.setUser(user);
}

/**
 * Initialize the error tracking service
 */
export function init(options: Partial<ErrorTrackingConfig> = {}): void {
  errorTracking.init(options);
}

/**
 * Clear all breadcrumbs
 */
export function clearBreadcrumbs(): void {
  errorTracking.clearBreadcrumbs();
}

/**
 * Higher-order function to wrap a function with error tracking
 */
export function withErrorTracking<T extends (...args: any[]) => any>(fn: T): T {
  return ((...args: Parameters<T>): ReturnType<T> => {
    try {
      const result = fn(...args);

      // Handle promises
      if (result instanceof Promise) {
        return result.catch(error => {
          console.error('Async error caught:', error);
          captureException(error);
          throw error;
        }) as ReturnType<T>;
      }

      return result;
    } catch (error) {
      console.error('Error caught:', error);
      captureException(error as Error);
      throw error;
    }
  }) as T;
}

// Export the singleton for testing
export { errorTracking };

// Export types
export type { Breadcrumb, ErrorTrackingConfig };
