// API route handler for exercise by ID
import { ExerciseByIdHandler } from "@/lib/api/exercise-handler";

const handler = new ExerciseByIdHandler();

export async function GET(req: Request, { params }: { params: { exerciseId: string } }) {
  return handler.handleGet(req, params);
}

export async function PUT(req: Request, { params }: { params: { exerciseId: string } }) {
  return handler.handlePut(req, params);
}

export async function PATCH(req: Request, { params }: { params: { exerciseId: string } }) {
  return handler.handlePut(req, params);
}

export async function DELETE(req: Request, { params }: { params: { exerciseId: string } }) {
  return handler.handleDelete(req, params);
}