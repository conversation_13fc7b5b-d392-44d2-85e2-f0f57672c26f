/**
 * Performance monitoring utility
 * Tracks and reports key performance metrics for the application
 */

import { useEffect } from 'react'

// Define extended performance entry interfaces
interface PerformanceEntryWithProcessingStart extends PerformanceEntry {
  processingStart: number;
  startTime: number;
}

interface LayoutShiftEntry extends PerformanceEntry {
  value: number;
  hadRecentInput: boolean;
}

interface ResourceEntry extends PerformanceResourceTiming {
  transferSize: number;
}

type PerformanceMetrics = {
  fcp?: number // First Contentful Paint
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  ttfb?: number // Time to First Byte
  navigationTime?: number // Navigation timing
  resourceTime?: {[key: string]: number} // Resource timing
  memoryInfo?: any // Memory information
  customMetrics: {[key: string]: number} // Custom timing metrics
}

const metrics: PerformanceMetrics = {
  customMetrics: {}
}

// Collection function for Web Vitals
function collectWebVitals() {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
    return
  }

  // LCP - Largest Contentful Paint
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries()
      const lastEntry = entries[entries.length - 1]
      if (lastEntry) {
        metrics.lcp = lastEntry.startTime
      }
    }).observe({ type: 'largest-contentful-paint', buffered: true })
  } catch (e) {
    console.warn('LCP measurement not supported', e)
  }

  // FCP - First Contentful Paint
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries()
      if (entries.length > 0) {
        metrics.fcp = entries[0].startTime
      }
    }).observe({ type: 'paint', buffered: true })
  } catch (e) {
    console.warn('FCP measurement not supported', e)
  }

  // FID - First Input Delay
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries()
      if (entries.length > 0) {
        const entry = entries[0] as PerformanceEntryWithProcessingStart
        metrics.fid = entry.processingStart - entry.startTime
      }
    }).observe({ type: 'first-input', buffered: true })
  } catch (e) {
    console.warn('FID measurement not supported', e)
  }

  // CLS - Cumulative Layout Shift
  try {
    let clsValue = 0
    const clsEntries: PerformanceEntry[] = []
    
    const entryHandler = (entries: PerformanceEntry[]) => {
      entries.forEach(entry => {
        // Only count layout shifts without recent user input
        const layoutShiftEntry = entry as LayoutShiftEntry
        if (!layoutShiftEntry.hadRecentInput) {
          clsValue += layoutShiftEntry.value
          clsEntries.push(entry)
          metrics.cls = clsValue
        }
      })
    }
    
    new PerformanceObserver(entryList => {
      entryHandler(entryList.getEntries())
    }).observe({type: 'layout-shift', buffered: true})
  } catch (e) {
    console.warn('CLS measurement not supported', e)
  }

  // TTFB - Time to First Byte
  try {
    const navigationEntries = performance.getEntriesByType('navigation')
    if (navigationEntries.length > 0) {
      metrics.ttfb = (navigationEntries[0] as PerformanceNavigationTiming).responseStart
      metrics.navigationTime = (navigationEntries[0] as PerformanceNavigationTiming).loadEventEnd
    }
  } catch (e) {
    console.warn('Navigation Timing not supported', e)
  }

  // Collect resource timing data for key resources
  try {
    metrics.resourceTime = {}
    performance.getEntriesByType('resource').forEach(entry => {
      const resourceEntry = entry as ResourceEntry
      const url = new URL(entry.name)
      const path = url.pathname
      const extension = path.split('.').pop()?.toLowerCase()
      
      if (extension && ['js', 'css', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension)) {
        const category = `${extension}-files`
        if (!metrics.resourceTime![category]) {
          metrics.resourceTime![category] = 0
        }
        metrics.resourceTime![category] += entry.duration
      }
      
      // Track large resources
      if (resourceEntry.transferSize && resourceEntry.transferSize > 100000) {
        const name = path.split('/').pop() || path
        metrics.resourceTime![`large-${name}`] = entry.duration
      }
    })
  } catch (e) {
    console.warn('Resource timing not supported', e)
  }

  // Memory info (only available in Chrome)
  try {
    if ((performance as any).memory) {
      metrics.memoryInfo = {
        jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize
      }
    }
  } catch (e) {
    console.warn('Memory info not available', e)
  }
}

// API to track custom metrics
export function trackTiming(name: string, startTime: number) {
  if (typeof window === 'undefined') return

  const endTime = performance.now()
  const duration = endTime - startTime
  metrics.customMetrics[name] = duration
  return duration
}

// Mark the start of a custom timing
export function startTiming(name: string) {
  if (typeof window === 'undefined') return 0
  return performance.now()
}

// Track component render time
export function trackComponentRender(componentName: string, duration: number) {
  if (typeof window === 'undefined') return
  
  const key = `component-${componentName}`
  if (!metrics.customMetrics[key]) {
    metrics.customMetrics[key] = 0
  }
  
  // Keep a running average
  const current = metrics.customMetrics[key]
  metrics.customMetrics[key] = current ? (current + duration) / 2 : duration
}

// Report metrics to your analytics service
export function reportMetrics() {
  if (typeof window === 'undefined' || !metrics) return
  
  // Simple console report during development
  if (process.env.NODE_ENV === 'development') {
    console.group('📊 Performance Metrics')
    
    // Core Web Vitals
    console.log('📃 Core Web Vitals')
    metrics.fcp && console.log(`  ⏱️ First Contentful Paint: ${Math.round(metrics.fcp)}ms`)
    metrics.lcp && console.log(`  ⏱️ Largest Contentful Paint: ${Math.round(metrics.lcp)}ms`)
    metrics.fid && console.log(`  ⏱️ First Input Delay: ${Math.round(metrics.fid)}ms`)
    metrics.cls && console.log(`  ⏱️ Cumulative Layout Shift: ${metrics.cls.toFixed(3)}`)
    metrics.ttfb && console.log(`  ⏱️ Time To First Byte: ${Math.round(metrics.ttfb)}ms`)
    metrics.navigationTime && console.log(`  ⏱️ Total Page Load: ${Math.round(metrics.navigationTime)}ms`)
    
    // Resource Timings
    if (metrics.resourceTime && Object.keys(metrics.resourceTime).length > 0) {
      console.log('📦 Resource Timing')
      Object.entries(metrics.resourceTime).forEach(([name, time]) => {
        console.log(`  ⏱️ ${name}: ${Math.round(time)}ms`)
      })
    }
    
    // Custom Metrics
    if (Object.keys(metrics.customMetrics).length > 0) {
      console.log('🔧 Custom Timing')
      Object.entries(metrics.customMetrics).forEach(([name, time]) => {
        console.log(`  ⏱️ ${name}: ${Math.round(time)}ms`)
      })
    }
    
    // Memory info
    if (metrics.memoryInfo) {
      console.log('💾 Memory Usage')
      console.log(`  🧠 Used JS Heap: ${Math.round(metrics.memoryInfo.usedJSHeapSize / 1048576)}MB`)
      console.log(`  🧠 Total JS Heap: ${Math.round(metrics.memoryInfo.totalJSHeapSize / 1048576)}MB`)
      console.log(`  🧠 JS Heap Limit: ${Math.round(metrics.memoryInfo.jsHeapSizeLimit / 1048576)}MB`)
    }
    
    console.groupEnd()
  }
  
  // Send metrics to analytics in production
  if (process.env.NODE_ENV === 'production') {
    // If you have an analytics endpoint, send the data there
    try {
      if ('navigator' in window && 'sendBeacon' in navigator) {
        // Use sendBeacon for non-blocking metric reporting
        navigator.sendBeacon('/api/performance-metrics', JSON.stringify(metrics))
      } else {
        // Fallback to fetch for older browsers
        fetch('/api/performance-metrics', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(metrics),
          // Use keepalive to ensure the request completes
          keepalive: true
        }).catch(err => console.error('Error reporting metrics:', err))
      }
    } catch (e) {
      console.error('Failed to send metrics', e)
    }
  }
}

// React hook to collect performance metrics
export function usePerformanceMonitoring(reportOnUnmount = true) {
  useEffect(() => {
    // Collect the metrics as soon as the component mounts
    collectWebVitals()
    
    // Report metrics when user leaves
    const reportOnVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        reportMetrics()
      }
    }
    
    document.addEventListener('visibilitychange', reportOnVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', reportOnVisibilityChange)
      
      // Optionally report metrics when the component unmounts
      if (reportOnUnmount) {
        reportMetrics()
      }
    }
  }, [reportOnUnmount])
}

// React hook to track component render times
export function useComponentTimer(componentName: string) {
  useEffect(() => {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      trackComponentRender(componentName, endTime - startTime)
    }
  }, [componentName])
} 