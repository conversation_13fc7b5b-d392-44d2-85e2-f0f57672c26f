import { <PERSON><PERSON><PERSON>, <PERSON>, ArrowRight, Activity, Heart } from "lucide-react"
import Link from "next/link"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export default async function TrainingPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user?.email) {
    redirect("/login")
  }

  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    include: {
      trainingPlans: {
        include: {
          workouts: true,
        },
      },
      workoutLogs: {
        include: {
          exercises: true,
        },
        orderBy: {
          date: "desc",
        },
      },
    },
  })

  if (!user) {
    redirect("/login")
  }

  // Mock training data
  const mockWorkouts = [
    {
      id: "1",
      title: "Upper Body Strength",
      description: "Focus on chest, shoulders, and triceps",
      duration: 45,
      exercises: 5,
      scheduled: "Today, 5:00 PM",
      completed: false
    },
    {
      id: "2",
      title: "Core Stability",
      description: "Strengthen your abdominals and lower back",
      duration: 30,
      exercises: 4,
      scheduled: "Tomorrow, 7:00 AM",
      completed: false
    },
    {
      id: "3",
      title: "Lower Body Power",
      description: "Focus on quads, hamstrings, and glutes",
      duration: 50,
      exercises: 6,
      scheduled: "Wednesday, 6:00 PM",
      completed: false
    },
    {
      id: "4",
      title: "HIIT Cardio",
      description: "High-intensity interval training for cardiovascular fitness",
      duration: 25,
      exercises: 8,
      scheduled: "Thursday, 7:30 AM",
      completed: false
    }
  ]

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">My Training</h2>
        <Button asChild>
          <Link href="/dashboard/workouts/current">
            View All Programs
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Stats cards */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Workout</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Today</div>
            <p className="text-xs text-muted-foreground">
              Upper Body Strength at 5:00 PM
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Programs</CardTitle>
            <Dumbbell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">
              Strength and Core Focus
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Weekly Progress</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">75%</div>
            <p className="text-xs text-muted-foreground">
              3/4 workouts completed
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Activity Streak</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">5 days</div>
            <p className="text-xs text-muted-foreground">
              Keep it up!
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <h3 className="text-xl font-semibold">Upcoming Workouts</h3>

        <div className="grid gap-4 md:grid-cols-2">
          {mockWorkouts.map((workout) => (
            <Card key={workout.id} className="overflow-hidden">
              <CardHeader>
                <CardTitle>{workout.title}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">{workout.description}</p>

                <div className="grid grid-cols-3 gap-2 text-sm">
                  <div>
                    <p className="text-muted-foreground">Duration:</p>
                    <p className="font-medium">{workout.duration} mins</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Exercises:</p>
                    <p className="font-medium">{workout.exercises}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Scheduled:</p>
                    <p className="font-medium">{workout.scheduled}</p>
                  </div>
                </div>

                <div className="flex space-x-2 pt-2">
                  <Button asChild className="flex-1">
                    <Link href={`/dashboard/workout-session/${workout.id}`}>
                      Start Workout
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1" asChild>
                    <Link href={`/dashboard/training/${workout.id}`}>
                      View Details
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

