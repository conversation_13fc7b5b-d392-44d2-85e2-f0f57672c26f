'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Trash2, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface Meal {
  id?: string;
  name: string;
  description: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  timeOfDay?: string;
  foodSuggestions?: string;
}

interface DietPlan {
  id: string;
  title: string;
  description?: string;
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  meals: Meal[];
}

interface EditNutritionPlanProps {
  plan: DietPlan;
  onSuccess?: () => void;
  buttonProps?: React.ButtonHTMLAttributes<HTMLButtonElement>;
}

export function EditNutritionPlan({ plan, onSuccess, buttonProps }: EditNutritionPlanProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState<{
    title: string;
    description: string;
    targetCalories: number;
    targetProtein: number;
    targetCarbs: number;
    targetFat: number;
    notes: string;
    meals: Meal[];
  }>({
    title: '',
    description: '',
    targetCalories: 0,
    targetProtein: 0,
    targetCarbs: 0,
    targetFat: 0,
    notes: '',
    meals: [],
  });

  useEffect(() => {
    if (plan) {
      setFormData({
        title: plan.title || '',
        description: plan.description || '',
        targetCalories: plan.calories || 0,
        targetProtein: plan.protein || 0,
        targetCarbs: plan.carbs || 0,
        targetFat: plan.fat || 0,
        notes: plan.notes || '',
        meals: plan.meals.map(meal => ({
          id: meal.id,
          name: meal.name || '',
          description: meal.description || '',
          calories: meal.calories || 0,
          protein: meal.protein || 0,
          carbs: meal.carbs || 0,
          fat: meal.fat || 0,
          timeOfDay: meal.timeOfDay || '',
          foodSuggestions: meal.foodSuggestions || '',
        })),
      });
    }
  }, [plan]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    if (name.startsWith('meals.')) {
      const [_, index, field] = name.split('.');
      const updatedMeals = [...formData.meals];
      updatedMeals[Number(index)] = {
        ...updatedMeals[Number(index)],
        [field]: field === 'name' || field === 'description' || field === 'timeOfDay' || field === 'foodSuggestions'
          ? value
          : Number(value),
      };
      setFormData({ ...formData, meals: updatedMeals });
    } else {
      setFormData({
        ...formData,
        [name]: name === 'title' || name === 'description' || name === 'notes'
          ? value
          : Number(value)
      });
    }
  };

  const handleAddMeal = () => {
    setFormData({
      ...formData,
      meals: [
        ...formData.meals,
        {
          name: '',
          description: '',
          calories: 0,
          protein: 0,
          carbs: 0,
          fat: 0,
          timeOfDay: '',
          foodSuggestions: '',
        },
      ],
    });
  };

  const handleRemoveMeal = (index: number) => {
    const updatedMeals = [...formData.meals];
    updatedMeals.splice(index, 1);
    setFormData({ ...formData, meals: updatedMeals });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Update the diet plan
      const response = await fetch(`/api/diet-plans/${plan.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description,
          calories: formData.targetCalories,
          protein: formData.targetProtein,
          carbs: formData.targetCarbs,
          fat: formData.targetFat,
          notes: formData.notes,
          meals: formData.meals.map(meal => ({
            id: meal.id,
            name: meal.name,
            description: meal.description,
            calories: meal.calories,
            protein: meal.protein,
            carbs: meal.carbs,
            fat: meal.fat,
            timeOfDay: meal.timeOfDay,
            foodSuggestions: meal.foodSuggestions,
          })),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update nutrition plan');
      }

      toast({
        title: 'Success',
        description: 'Nutrition plan updated successfully',
      });

      setIsOpen(false);
      if (onSuccess) {
        onSuccess();
      }
      router.refresh();
    } catch (error) {
      console.error('Error updating nutrition plan:', error);
      toast({
        title: 'Error',
        description: 'Failed to update nutrition plan',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateTotalMacros = () => {
    const totalCalories = formData.meals.reduce((sum, meal) => sum + meal.calories, 0);
    const totalProtein = formData.meals.reduce((sum, meal) => sum + meal.protein, 0);
    const totalCarbs = formData.meals.reduce((sum, meal) => sum + meal.carbs, 0);
    const totalFat = formData.meals.reduce((sum, meal) => sum + meal.fat, 0);

    return { totalCalories, totalProtein, totalCarbs, totalFat };
  };

  const { totalCalories, totalProtein, totalCarbs, totalFat } = calculateTotalMacros();

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" {...buttonProps}>
          Edit Nutrition Plan
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>Edit Nutrition Plan</DialogTitle>
          <DialogDescription>
            Update the nutrition plan with specific macros and meal suggestions.
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="flex-grow pr-4 max-h-[calc(90vh-120px)] overflow-y-auto">
          <form onSubmit={handleSubmit} className="space-y-6 py-4">
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Plan Title</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="e.g., Weight Loss Nutrition Plan"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Brief description of the nutrition plan"
                    rows={3}
                  />
                </div>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Daily Targets</CardTitle>
                  <CardDescription>Set the daily nutritional targets for your client</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="targetCalories">Target Calories</Label>
                      <Input
                        id="targetCalories"
                        name="targetCalories"
                        type="number"
                        value={formData.targetCalories}
                        onChange={handleInputChange}
                        placeholder="e.g., 2000"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="targetProtein">Target Protein (g)</Label>
                      <Input
                        id="targetProtein"
                        name="targetProtein"
                        type="number"
                        value={formData.targetProtein}
                        onChange={handleInputChange}
                        placeholder="e.g., 150"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="targetCarbs">Target Carbs (g)</Label>
                      <Input
                        id="targetCarbs"
                        name="targetCarbs"
                        type="number"
                        value={formData.targetCarbs}
                        onChange={handleInputChange}
                        placeholder="e.g., 200"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="targetFat">Target Fat (g)</Label>
                      <Input
                        id="targetFat"
                        name="targetFat"
                        type="number"
                        value={formData.targetFat}
                        onChange={handleInputChange}
                        placeholder="e.g., 70"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Meals</CardTitle>
                      <CardDescription>Add meals with specific macros and suggestions</CardDescription>
                    </div>
                    <Button type="button" variant="outline" onClick={handleAddMeal}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Meal
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {formData.meals.map((meal, index) => (
                      <Card key={index} className="border border-muted">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <Input
                                  name={`meals.${index}.name`}
                                  value={meal.name}
                                  onChange={handleInputChange}
                                  placeholder="Meal name (e.g., Breakfast)"
                                  className="font-medium text-base"
                                />
                                <Input
                                  name={`meals.${index}.timeOfDay`}
                                  type="time"
                                  value={meal.timeOfDay}
                                  onChange={handleInputChange}
                                  className="w-32"
                                />
                              </div>
                              <Textarea
                                name={`meals.${index}.description`}
                                value={meal.description}
                                onChange={handleInputChange}
                                placeholder="Brief description of the meal"
                                className="text-sm text-muted-foreground"
                                rows={2}
                              />
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveMeal(index)}
                              disabled={formData.meals.length <= 1}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-4 gap-4">
                            <div className="space-y-1">
                              <Label htmlFor={`meals.${index}.calories`} className="text-xs">Calories</Label>
                              <Input
                                id={`meals.${index}.calories`}
                                name={`meals.${index}.calories`}
                                type="number"
                                value={meal.calories}
                                onChange={handleInputChange}
                                placeholder="0"
                              />
                            </div>
                            <div className="space-y-1">
                              <Label htmlFor={`meals.${index}.protein`} className="text-xs">Protein (g)</Label>
                              <Input
                                id={`meals.${index}.protein`}
                                name={`meals.${index}.protein`}
                                type="number"
                                value={meal.protein}
                                onChange={handleInputChange}
                                placeholder="0"
                              />
                            </div>
                            <div className="space-y-1">
                              <Label htmlFor={`meals.${index}.carbs`} className="text-xs">Carbs (g)</Label>
                              <Input
                                id={`meals.${index}.carbs`}
                                name={`meals.${index}.carbs`}
                                type="number"
                                value={meal.carbs}
                                onChange={handleInputChange}
                                placeholder="0"
                              />
                            </div>
                            <div className="space-y-1">
                              <Label htmlFor={`meals.${index}.fat`} className="text-xs">Fat (g)</Label>
                              <Input
                                id={`meals.${index}.fat`}
                                name={`meals.${index}.fat`}
                                type="number"
                                value={meal.fat}
                                onChange={handleInputChange}
                                placeholder="0"
                              />
                            </div>
                          </div>
                          <div className="mt-4 space-y-1">
                            <Label htmlFor={`meals.${index}.foodSuggestions`} className="text-xs">Food Suggestions</Label>
                            <Textarea
                              id={`meals.${index}.foodSuggestions`}
                              name={`meals.${index}.foodSuggestions`}
                              value={meal.foodSuggestions || ''}
                              onChange={handleInputChange}
                              placeholder="Suggested foods for this meal (e.g., eggs, oatmeal, berries)"
                              rows={3}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
                <CardFooter className="bg-muted/50 flex justify-between">
                  <div className="text-sm">
                    <div className="font-medium">Daily Totals:</div>
                    <div className="text-muted-foreground">
                      {totalCalories} kcal | {totalProtein}g protein | {totalCarbs}g carbs | {totalFat}g fat
                    </div>
                  </div>
                  <div className="text-sm">
                    <div className="font-medium">Targets:</div>
                    <div className="text-muted-foreground">
                      {formData.targetCalories} kcal | {formData.targetProtein}g protein | {formData.targetCarbs}g carbs | {formData.targetFat}g fat
                    </div>
                  </div>
                </CardFooter>
              </Card>

              <div className="space-y-2">
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  placeholder="Any additional notes or instructions for the client"
                  rows={4}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Updating...' : 'Update Nutrition Plan'}
              </Button>
            </div>
          </form>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
