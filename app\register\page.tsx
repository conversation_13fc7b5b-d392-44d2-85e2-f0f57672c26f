"use client"

import Link from "next/link"
import { useRouter } from "next/navigation"
import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2, CheckCircle } from "lucide-react"
import { signIn } from "next-auth/react"

// Type for Zod flattened errors (adjust based on actual structure if needed)
type FieldErrors = {
  fullName?: string[];
  email?: string[];
  password?: string[];
};

export default function RegisterPage() {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null) // Generic error message
  const [fieldErrors, setFieldErrors] = useState<FieldErrors>({}); // Field-specific errors
  const [loading, setLoading] = useState(false)
  const [googleLoading, setGoogleLoading] = useState(false)
  const [isRegistered, setIsRegistered] = useState(false)
  const [userEmail, setUserEmail] = useState("")

  const handleGoogleSignUp = async () => {
    try {
      setGoogleLoading(true)
      // Google provider configured to use the standard callback flow
      await signIn("google", { callbackUrl: "/dashboard" })
      // No need to handle result here as Google sign-in redirects to Google's consent page
    } catch (error) {
      setError("An error occurred with Google Sign-Up. Please try again.")
      setGoogleLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError(null)
    setFieldErrors({}) // Clear previous field errors
    setLoading(true)

    const formData = new FormData(e.currentTarget)
    const fullName = formData.get("fullName") as string
    const email = formData.get("email") as string
    const password = formData.get("password") as string
    
    // Save email for verification message
    setUserEmail(email)

    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          fullName,
          email,
          password,
          role: "client",
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        if (response.status === 400 && data.errors) {
          // Handle Zod validation errors
          setFieldErrors(data.errors);
          setError("Please fix the errors in the form."); // Set a generic message
        } else {
          // Handle other errors (e.g., 409 Conflict, 500 Server Error)
          setError(data.message || "Registration failed. Please try again.")
          setFieldErrors({}); // Clear field errors for non-validation issues
        }
      } else {
        // Success - but now we show verification message on same page
        setFieldErrors({});
        setError(null);
        setIsRegistered(true);
        // Don't redirect immediately anymore
        // router.push("/login?registered=true")
      }
    } catch (error) {
      setError("An unexpected error occurred. Please try again.")
      setFieldErrors({});
      console.error("Registration fetch error:", error);
    } finally {
      setLoading(false)
    }
  }

  // Show verification message if registered
  if (isRegistered) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md">
          <div className="text-center">
            <CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-4" />
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Registration Successful!
            </h2>
            <div className="text-md text-gray-600 mb-6">
              <p className="mb-2">We've sent a verification email to <strong>{userEmail}</strong></p>
              <p className="mb-4">Please check your inbox (and spam folder) and click the verification link to activate your account.</p>
              <p className="text-sm text-gray-500">The verification link will expire in 24 hours.</p>
            </div>
            <div className="mt-6 space-y-4">
              <Button
                onClick={() => router.push("/login")}
                className="w-full"
              >
                Go to Login
              </Button>
              <p className="text-sm text-gray-500">
                Didn't receive the email?{" "}
                <button 
                  className="text-indigo-600 hover:text-indigo-800 font-medium"
                  onClick={() => setIsRegistered(false)}
                >
                  Try again
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Create your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Join our fitness community and start your journey today
          </p>
        </div>
        
        {/* Google Sign Up Button */}
        <div>
          <Button
            type="button"
            onClick={handleGoogleSignUp}
            disabled={googleLoading}
            className="group relative w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <span className="flex items-center">
              {googleLoading ? (
                "Creating account with Google..."
              ) : (
                <>
                  <svg className="h-5 w-5 mr-2" viewBox="0 0 24 24">
                    <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                      <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z" />
                      <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z" />
                      <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z" />
                      <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z" />
                    </g>
                  </svg>
                  Sign up with Google
                </>
              )}
            </span>
          </Button>
          
          <p className="text-center text-xs text-gray-500 mt-2">
            Create an account instantly with Google - no email verification needed!
          </p>
        </div>
        
        <div className="flex items-center justify-center">
          <div className="border-t border-gray-300 flex-grow mr-3" />
          <span className="text-sm text-gray-500">or register with email</span>
          <div className="border-t border-gray-300 flex-grow ml-3" />
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm space-y-4"> 
            {/* Full Name Field */}
            <div>
              <Label htmlFor="fullName">Full name</Label>
              <Input
                id="fullName"
                name="fullName"
                type="text"
                required
                className={`appearance-none relative block w-full px-3 py-2 border ${fieldErrors.fullName ? 'border-red-500' : 'border-gray-300'} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm`}
                placeholder="Full name"
                disabled={loading}
              />
              {fieldErrors.fullName && (
                <p className="mt-1 text-xs text-red-600">{fieldErrors.fullName[0]}</p>
              )}
            </div>
            
            {/* Email Field */}
            <div>
              <Label htmlFor="email">Email address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                required
                className={`appearance-none relative block w-full px-3 py-2 border ${fieldErrors.email ? 'border-red-500' : 'border-gray-300'} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm`}
                placeholder="Email address"
                disabled={loading}
              />
               {fieldErrors.email && (
                <p className="mt-1 text-xs text-red-600">{fieldErrors.email[0]}</p>
              )}
            </div>
            
            {/* Password Field */}
            <div>
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                required
                className={`appearance-none relative block w-full px-3 py-2 border ${fieldErrors.password ? 'border-red-500' : 'border-gray-300'} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm`}
                placeholder="Password (min 8 chars, Aa, 1#, 1$)"
                disabled={loading}
              />
               {fieldErrors.password && (
                <p className="mt-1 text-xs text-red-600">{fieldErrors.password.join(", ")}</p> // Join if multiple password errors
              )}
            </div>
          </div>
          
          {/* Display generic error if any */}
          {error && !Object.keys(fieldErrors).length && (
            <div className="text-red-500 text-sm text-center">{error}</div>
          )}
          {/* Display message if there are field errors */}
           {error && Object.keys(fieldErrors).length > 0 && (
            <div className="text-red-500 text-sm text-center">{error}</div>
          )}

          <div>
            <Button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {loading ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Creating account...</> : "Create account"}
            </Button>
          </div>
        </form>

        <div className="text-center">
          <Link
            href="/login"
            className="font-medium text-indigo-600 hover:text-indigo-500"
          >
            Already have an account? Sign in
          </Link>
        </div>
      </div>
    </div>
  )
}

