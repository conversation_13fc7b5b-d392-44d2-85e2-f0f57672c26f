"use client"

import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { Exercise } from "@prisma/client"
import { ChevronDown, ChevronRight, Du<PERSON><PERSON>, Trash, Info } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface DraggableExerciseProps {
  exercise: Exercise
  onDelete: () => void
  hasAlternatives?: boolean
  isExpanded?: boolean
  onToggleExpand?: () => void
  isAlternative?: boolean
}

export function DraggableExercise({
  exercise,
  onDelete,
  hasAlternatives = false,
  isExpanded = false,
  onToggleExpand,
  isAlternative = false,
}: DraggableExerciseProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    over,
  } = useSortable({
    id: exercise.id,
    data: {
      type: 'exercise',
      exercise,
    },
  })

  // Check if another exercise is being dragged over this one
  const isBeingDraggedOver = Boolean(over)

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`${
        isAlternative ? 'bg-secondary/20 border-l-4 border-l-secondary' :
        hasAlternatives ? 'bg-card border-l-4 border-l-primary' : 'bg-card'
      } ${
        isBeingDraggedOver ? 'ring-2 ring-primary ring-offset-2 bg-primary/5 border-dashed' : ''
      } border shadow-sm hover:shadow-md hover:border-primary/50 transition-all duration-200`}
    >
      <CardContent className="p-3 flex items-center justify-between">
        <div className="flex items-center gap-2 flex-1">
          {hasAlternatives && onToggleExpand && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={(e) => {
                e.stopPropagation()
                onToggleExpand()
              }}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}

          {isBeingDraggedOver && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center justify-center rounded-full h-5 w-5 bg-primary/20 text-primary">
                    <Info className="h-3 w-3" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Drop here to add as an alternative exercise</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          <div
            {...attributes}
            {...listeners}
            className="flex items-center gap-2 cursor-grab active:cursor-grabbing"
          >
            <Dumbbell className="h-4 w-4 text-muted-foreground" />
            <div className="flex-1">
              <div className="font-medium flex items-center gap-2">
                {exercise.name}
                {hasAlternatives && (
                  <span className="inline-flex items-center rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary">
                    Alternatives
                  </span>
                )}
                {isAlternative && (
                  <span className="inline-flex items-center rounded-full bg-secondary/10 px-2 py-0.5 text-xs font-medium text-secondary">
                    Alternative
                  </span>
                )}
              </div>
              <div className="text-xs text-muted-foreground">
                {exercise.sets && `${exercise.sets} sets`}
                {exercise.sets && exercise.reps && ' × '}
                {exercise.reps && `${exercise.reps} reps`}
                {(exercise.sets || exercise.reps) && exercise.weight && ' · '}
                {exercise.weight && `${exercise.weight} kg`}
              </div>
            </div>
          </div>
        </div>

        <Button
          variant="ghost"
          size="icon"
          className="h-7 w-7 text-destructive hover:text-destructive/90 hover:bg-destructive/10"
          onClick={onDelete}
        >
          <Trash className="h-4 w-4" />
        </Button>
      </CardContent>
    </Card>
  )
}
