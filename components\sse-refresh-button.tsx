'use client';

import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { useState } from "react";

export default function SSERefreshButton() {
  const { data: session } = useSession();
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const refreshSSE = async () => {
    if (!session?.user?.id) return;
    
    setIsRefreshing(true);
    
    try {
      // First, try to fetch the test endpoint to trigger an SSE message
      const response = await fetch(`/api/sse/test?userId=${session.user.id}`);
      const data = await response.json();
      
      console.log('SSE refresh response:', data);
      
      // Dispatch a message-sent event to force UI updates
      window.dispatchEvent(new CustomEvent('message-sent'));
      
      // Also dispatch a message-received event
      window.dispatchEvent(new CustomEvent('message-received', {
        detail: { type: 'refresh', message: 'Manual refresh' }
      }));
      
      alert('SSE connection refreshed. Check console for details.');
    } catch (error) {
      console.error('Error refreshing SSE connection:', error);
      alert('Error refreshing SSE connection. Check console for details.');
    } finally {
      setIsRefreshing(false);
    }
  };
  
  return (
    <Button 
      variant="outline" 
      size="sm" 
      onClick={refreshSSE}
      disabled={isRefreshing}
      className="fixed bottom-4 left-4 z-50 bg-pink-100 hover:bg-pink-200 text-pink-800 border-pink-300"
    >
      {isRefreshing ? 'Refreshing...' : 'Refresh Messages'}
    </Button>
  );
}
