import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'You must be logged in to submit a support request' },
        { status: 401 }
      )
    }
    
    const { productId, subject, message } = await req.json()
    
    if (!productId || !message) {
      return NextResponse.json(
        { error: 'Product ID and message are required' },
        { status: 400 }
      )
    }
    
    const user = await prisma.user.findUnique({
      where: { email: session.user.email as string },
    })
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        athlete: true
      }
    })
    
    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }
    
    try {
      // Log the support request
      console.log('Support request received:', {
        productId,
        subject: subject || 'Support Request',
        message,
        userId: user.id,
        userEmail: user.email,
      })
      
      // In a real implementation, create a support ticket in the database
      // and send email notifications
      
      return NextResponse.json({ 
        success: true,
        message: 'Support request received successfully'
      })
    } catch (error) {
      console.error('Failed to process support request:', error)
      return NextResponse.json({ error: 'Failed to process support request' }, { status: 500 })
    }
  } catch (error) {
    console.error('Support request error:', error)
    return NextResponse.json(
      { error: 'Failed to create support ticket' },
      { status: 500 }
    )
  }
} 