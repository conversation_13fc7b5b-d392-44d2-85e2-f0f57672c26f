"use client";

import { Plus, Trash2 } from "lucide-react"
import React, { useState, useEffect, useCallback, useMemo } from "react"
import { toast } from "sonner"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { VideoPlayer } from "./video-player"

interface VideoManagerProps {
  workoutId: string
  isEditable?: boolean
}

interface Video {
  id: string
  title: string
  description?: string
  youtubeUrl: string
  thumbnailUrl?: string
  duration?: string
}

// Initial empty state for new video form
const EMPTY_VIDEO_STATE = {
  title: "",
  description: "",
  youtubeUrl: "",
  thumbnailUrl: "",
  duration: "",
};

export function VideoManager({ workoutId, isEditable = false }: VideoManagerProps) {
  const [videos, setVideos] = useState<Video[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newVideo, setNewVideo] = useState(EMPTY_VIDEO_STATE)

  // Memoize API URL
  const apiUrl = useMemo(() => `/api/videos?workoutId=${workoutId}`, [workoutId]);

  const fetchVideos = useCallback(async () => {
    try {
      const response = await fetch(apiUrl)
      if (!response.ok) throw new Error("Failed to fetch videos")
      const data = await response.json()
      setVideos(data.map((item: any) => item.video))
    } catch (_error) {
      console.error("Error fetching videos:", _error)
      toast.error("Failed to load videos")
    }
  }, [apiUrl])

  useEffect(() => {
    fetchVideos()
  }, [fetchVideos])

  const resetForm = useCallback(() => {
    setNewVideo(EMPTY_VIDEO_STATE);
    setShowAddForm(false);
  }, []);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setNewVideo(prev => ({
      ...prev,
      [id]: value
    }));
  }, []);

  const handleAddVideo = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      setIsLoading(true)
      const newVideoWithId = {
        ...newVideo,
        id: `video-${Date.now()}`
      }

      const response = await fetch("/api/videos", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...newVideoWithId,
          workoutId,
        }),
      })

      if (!response.ok) throw new Error("Failed to add video")

      toast.success("Video added successfully")
      resetForm();
      fetchVideos()
    } catch (_error) {
      toast.error("Failed to add video")
    } finally {
      setIsLoading(false)
    }
  }, [newVideo, workoutId, fetchVideos, resetForm])

  const handleDeleteVideo = useCallback(async (videoId: string) => {
    try {
      const response = await fetch(`/api/videos?videoId=${videoId}`, {
        method: "DELETE",
      })

      if (!response.ok) throw new Error("Failed to delete video")

      // Optimistically update UI
      setVideos(prev => prev.filter(video => video.id !== videoId));
      toast.success("Video deleted successfully")
    } catch (_error) {
      toast.error("Failed to delete video")
      // Refetch videos if optimistic update fails
      fetchVideos();
    }
  }, [fetchVideos])

  const toggleAddForm = useCallback(() => {
    setShowAddForm(prev => !prev);
  }, []);

  // Memoize the video components to prevent unnecessary re-renders
  const videoElements = useMemo(() => {
    return videos.map((video) => (
      <div key={video.id} className="relative">
        <VideoPlayer
          videoId={video.youtubeUrl}
          title={video.title}
          description={video.description}
          thumbnailUrl={video.thumbnailUrl}
        />
        {isEditable && (
          <Button
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2"
            onClick={() => handleDeleteVideo(video.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    ));
  }, [videos, isEditable, handleDeleteVideo]);

  return (
    <div className="space-y-6">
      {isEditable && (
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Workout Videos</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={toggleAddForm}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Video
          </Button>
        </div>
      )}

      {showAddForm && (
        <form onSubmit={handleAddVideo} className="space-y-4 p-4 border rounded-lg">
          <div>
            <label htmlFor="title" className="block text-sm font-medium mb-1">
              Title
            </label>
            <Input
              id="title"
              value={newVideo.title}
              onChange={handleInputChange}
              required
            />
          </div>
          <div>
            <label htmlFor="youtubeUrl" className="block text-sm font-medium mb-1">
              YouTube URL
            </label>
            <Input
              id="youtubeUrl"
              value={newVideo.youtubeUrl}
              onChange={handleInputChange}
              required
            />
          </div>
          <div>
            <label htmlFor="description" className="block text-sm font-medium mb-1">
              Description
            </label>
            <Textarea
              id="description"
              value={newVideo.description}
              onChange={handleInputChange}
            />
          </div>
          <div>
            <label htmlFor="duration" className="block text-sm font-medium mb-1">
              Duration (seconds)
            </label>
            <Input
              id="duration"
              type="number"
              value={newVideo.duration}
              onChange={handleInputChange}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={resetForm}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              Add Video
            </Button>
          </div>
        </form>
      )}

      <div className="space-y-6">
        {videoElements}
      </div>
    </div>
  )
} 