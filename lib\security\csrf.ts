import crypto from 'crypto';

// CSRF token header name
const CSRF_TOKEN_HEADER = 'X-CSRF-Token';
// CSRF token form field name
const CSRF_TOKEN_FIELD = '_csrf';
// Secret used for generating CSRF tokens
const CSRF_SECRET = process.env.CSRF_SECRET || 'change-me-with-a-secure-secret';
// Token expiry time (1 hour)
const TOKEN_EXPIRY = 60 * 60 * 1000;

/**
 * Generate a secure random token
 * @param length The length of the token to generate
 * @returns A random token string
 */
export function generateSecureToken(length = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Generate a secure CSRF token using HMAC
 * @param sessionId A unique session identifier
 * @returns The generated token
 */
export function generateCSRFToken(sessionId: string): string {
  // Create a timestamp for expiration checking
  const timestamp = Date.now();
  // Create a random salt for this token
  const salt = generateSecureToken(8);
  // Data to sign
  const data = `${sessionId}|${timestamp}|${salt}`;
  
  // Create an HMAC signature
  const hmac = crypto.createHmac('sha256', CSRF_SECRET);
  hmac.update(data);
  const signature = hmac.digest('hex');
  
  // Return the token with its timestamp and signature
  return Buffer.from(`${data}|${signature}`).toString('base64');
}

/**
 * Validate a CSRF token
 * @param token The token to validate
 * @param sessionId The session ID to validate against
 * @returns True if the token is valid, false otherwise
 */
export function validateCSRFToken(token: string, sessionId: string): boolean {
  try {
    // Decode the token
    const decoded = Buffer.from(token, 'base64').toString();
    const [storedSessionId, timestamp, salt, signature] = decoded.split('|');
    
    // Check if the session ID matches
    if (storedSessionId !== sessionId) {
      return false;
    }
    
    // Check if the token has expired
    if (parseInt(timestamp) + TOKEN_EXPIRY < Date.now()) {
      return false;
    }
    
    // Recreate the signature to verify it
    const data = `${sessionId}|${timestamp}|${salt}`;
    const hmac = crypto.createHmac('sha256', CSRF_SECRET);
    hmac.update(data);
    const expectedSignature = hmac.digest('hex');
    
    // Verify the signature using constant-time comparison
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    return false;
  }
}

/**
 * Extract the CSRF token from a request
 * @param request The request to extract the token from
 * @returns The token, or null if not found
 */
export function extractCSRFToken(request: Request): string | null {
  // Try to extract from header first
  const headerToken = request.headers.get(CSRF_TOKEN_HEADER);
  if (headerToken) {
    return headerToken;
  }
  
  // If not in header, try from body for POST/PUT/PATCH/DELETE requests
  // Note: In a real implementation, you'd need to clone the request and parse the body
  return null;
}

/**
 * Create a CSRF protection middleware for API routes
 * @param handler The API route handler
 * @returns The protected handler function
 */
export function withCSRFProtection(handler: Function) {
  return async (req: Request, ...args: any[]) => {
    // For GET requests, we typically don't need to validate the token
    if (req.method === 'GET') {
      return handler(req, ...args);
    }
    
    try {
      // Clone the request to preserve the body stream
      const clonedRequest = req.clone();
      
      // Get the token from the header
      const token = req.headers.get(CSRF_TOKEN_HEADER);
      
      // If there's no token, reject the request
      if (!token) {
        return new Response(
          JSON.stringify({ error: 'Missing CSRF token' }),
          { 
            status: 403, 
            headers: { 'Content-Type': 'application/json' } 
          }
        );
      }
      
      // Get the session ID (this would typically come from your auth system)
      // For example, if using next-auth you might get this from the session
      const sessionId = "user-session-id"; // Replace with actual session ID
      
      // Validate the token
      if (!validateCSRFToken(token, sessionId)) {
        return new Response(
          JSON.stringify({ error: 'Invalid CSRF token' }),
          { 
            status: 403, 
            headers: { 'Content-Type': 'application/json' } 
          }
        );
      }
      
      // Token is valid, proceed with the handler
      return handler(req, ...args);
    } catch (error) {
      console.error('CSRF validation error:', error);
      return new Response(
        JSON.stringify({ error: 'Invalid request' }),
        { 
          status: 400, 
          headers: { 'Content-Type': 'application/json' } 
        }
      );
    }
  };
}

/**
 * Get the CSRF token field name for forms
 * @returns The CSRF token field name
 */
export function getCSRFTokenFieldName(): string {
  return CSRF_TOKEN_FIELD;
}

/**
 * Get the CSRF token header name
 * @returns The CSRF token header name
 */
export function getCSRFTokenHeaderName(): string {
  return CSRF_TOKEN_HEADER;
} 