import * as fs from 'fs';
import * as path from 'path';
import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { v4 as uuidv4 } from "uuid";
import { authOptions } from "@/lib/auth";

// Allowed image types
const ALLOWED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/webp", "image/gif"];

// Max file size (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File | null;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json({
        error: `File size exceeds the maximum limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB`
      }, { status: 400 });
    }

    // Check file type
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
      return NextResponse.json({
        error: `Invalid file type. Allowed types: ${ALLOWED_IMAGE_TYPES.join(", ")}`
      }, { status: 400 });
    }

    // Get file extension
    const fileExtension = file.name.split('.').pop() || 'jpg';

    // Generate unique filename
    const fileName = `${uuidv4()}.${fileExtension}`;

    // Create upload directory path - handle Docker environment
    // In Docker, the app is mounted at /app
    const basePath = process.env.NODE_ENV === 'production' ? '/app' : process.cwd();
    const uploadDir = path.join(basePath, 'public', 'uploads', 'avatars');

    // Ensure the directory exists
    if (!fs.existsSync(uploadDir)) {
      console.log(`[AVATAR_UPLOAD] Creating directory: ${uploadDir}`);
      fs.mkdirSync(uploadDir, { recursive: true });
    } else {
      console.log(`[AVATAR_UPLOAD] Directory exists: ${uploadDir}`);
    }

    // Full path for the file
    const filePath = path.join(uploadDir, fileName);

    // Convert file to buffer and save to filesystem
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Write the file
    fs.writeFileSync(filePath, buffer);

    // Public URL for the file
    const fileUrl = `/uploads/avatars/${fileName}`;

    // Debug log
    console.log(`[AVATAR_UPLOAD] File saved to ${filePath}`);
    console.log(`[AVATAR_UPLOAD] Public URL: ${fileUrl}`);

    return NextResponse.json({
      url: fileUrl,
      fileName,
      fileType: file.type,
      fileSize: file.size
    });
  } catch (error) {
    console.error("[AVATAR_UPLOAD_ERROR]", error);
    return NextResponse.json({ error: "Failed to upload avatar" }, { status: 500 });
  }
}
