"use client"

import { useRouter } from "next/navigation"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"


import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"

interface Meal {
  name: string
  calories: number
  protein: number
  carbs: number
  fats: number
}

interface DietPlan {
  id: string
  title: string
  description: string
  is_template: boolean
  calories_per_day: number
  createdAt: string
  updatedAt: string
  meals: Meal[]
}

interface DietPlanGridProps {
  plans: DietPlan[]
  onDuplicatePlan?: (id: string) => void
  onDeletePlan?: (id: string) => void
}

export function DietPlanGrid({ plans, onDuplicatePlan, onDeletePlan }: DietPlanGridProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("all")

  const filteredPlans = plans.filter((plan: DietPlan) => {
    if (activeTab === "templates") return plan.is_template
    if (activeTab === "custom") return !plan.is_template
    return true
  })

  const handleDuplicate = async (id: string) => {
    try {
      await onDuplicatePlan?.(id)
      toast({
        title: "Success",
        description: "Plan duplicated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate plan",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (id: string) => {
    try {
      await onDeletePlan?.(id)
      toast({
        title: "Success",
        description: "Plan deleted successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete plan",
        variant: "destructive",
      })
    }
  }

  return (
    <Tabs defaultValue="all" onValueChange={setActiveTab}>
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="all">All Plans</TabsTrigger>
        <TabsTrigger value="templates">Templates</TabsTrigger>
        <TabsTrigger value="custom">Custom Plans</TabsTrigger>
      </TabsList>

      <TabsContent value={activeTab} className="mt-6">
        {filteredPlans.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              {activeTab === "templates"
                ? "No template plans found"
                : activeTab === "custom"
                ? "No custom plans found"
                : "No diet plans found"}
            </p>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredPlans.map((plan: DietPlan) => (
              <Card
                key={plan.id}
                className="cursor-pointer hover:bg-accent/50 transition-colors"
                onClick={() => router.push(`/dashboard/diet-plans/${plan.id}`)}
              >
                <CardHeader>
                  <CardTitle>{plan.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      {plan.description}
                    </p>
                    <p className="text-sm font-medium">
                      {plan.calories_per_day} calories/day
                    </p>
                    <div className="space-y-1">
                      {plan.meals.map((meal: Meal, index: number) => (
                        <div key={index} className="text-sm">
                          <span className="font-medium">{meal.name}</span>
                          <span className="text-muted-foreground">
                            {" "}
                            - {meal.calories} calories
                          </span>
                        </div>
                      ))}
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDuplicate(plan.id)
                        }}
                      >
                        Duplicate
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(plan.id)
                        }}
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </TabsContent>
    </Tabs>
  )
}

