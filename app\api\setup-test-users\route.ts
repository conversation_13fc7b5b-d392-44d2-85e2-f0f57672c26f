import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { hash } from "bcryptjs";

export async function GET() {
  try {
    // Only allow this in development
    if (process.env.NODE_ENV !== "development") {
      return new NextResponse("Only available in development", { status: 403 });
    }
    
    // Create a hashed password for test users
    const hashedPassword = await hash("password123", 10);
    
    // 1. Create or find a trainer user
    let trainer = await prisma.user.findFirst({
      where: {
        role: "trainer",
      },
    });
    
    if (!trainer) {
      trainer = await prisma.user.create({
        data: {
          name: "Test Trainer",
          email: "<EMAIL>",
          password: hashedPassword,
          role: "trainer",
          emailVerified: new Date(),
        },
      });
    }
    
    // 2. Create or find a client user
    let client = await prisma.user.findFirst({
      where: {
        role: "client",
      },
    });
    
    if (!client) {
      client = await prisma.user.create({
        data: {
          name: "Test Client",
          email: "<EMAIL>",
          password: hashedPassword,
          role: "client",
          emailVerified: new Date(),
        },
      });
    }
    
    // 3. Create trainer profile if it doesn't exist
    let trainerProfile = await prisma.trainerProfile.findUnique({
      where: {
        userId: trainer.id,
      },
    });
    
    if (!trainerProfile) {
      trainerProfile = await prisma.trainerProfile.create({
        data: {
          userId: trainer.id,
        },
      });
    }
    
    // 4. Create client profile if it doesn't exist
    let clientProfile = await prisma.clientProfile.findUnique({
      where: {
        userId: client.id,
      },
    });
    
    if (!clientProfile) {
      clientProfile = await prisma.clientProfile.create({
        data: {
          userId: client.id,
          assignedTrainerId: trainerProfile.id,
        },
      });
    }
    
    return NextResponse.json({
      success: true,
      trainer: {
        id: trainer.id,
        name: trainer.name,
        email: trainer.email,
      },
      client: {
        id: client.id,
        name: client.name,
        email: client.email,
      },
      trainerProfile: {
        id: trainerProfile.id,
      },
      clientProfile: {
        id: clientProfile.id,
      },
    });
  } catch (error) {
    console.error("Error setting up test users:", error);
    
    // Return more detailed error information in development
    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      }, { status: 500 });
    }
    
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
