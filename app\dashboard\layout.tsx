"use client"

import { <PERSON>u } from "lucide-react"
import { useSession } from "next-auth/react"
import { usePathname } from "next/navigation"
import React, { useState, useEffect } from "react"
import { DevRoleSwitcher } from "@/components/dashboard/dev-role-switcher"
import { MainNav } from "@/components/dashboard/main-nav"
import { MobileNav } from "@/components/dashboard/mobile-nav"
import { UserAccountNav } from "@/components/dashboard/user-account-nav"

import { NotificationBell } from "@/components/notifications/notification-bell"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { ThemeToggle, ThemeToggleWithLabel } from "@/components/theme-toggle"
import SSEListener from "@/components/sse-listener"
import SSERefreshButton from "@/components/sse-refresh-button"
import { BottomNav } from "@/components/ui/mobile/bottom-nav"
import Image from "next/image"
import Link from "next/link"

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { data: session, status } = useSession()
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false)
  // Role switcher is always visible in development mode

  const userRole = session?.user?.role || null

  // Add debug logging for premium status detection
  const isPremiumFromUser = (session?.user as any)?.subscriptionTier === 'premium'

  // In development, also check for dev cookie override
  const [isPremiumFromCookie, setIsPremiumFromCookie] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [showBottomNav, setShowBottomNav] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    setIsClient(true)

    // Check for developer premium cookie override in dev mode
    if (process.env.NODE_ENV === 'development') {
      const cookies = document.cookie.split(';')
      const premiumCookie = cookies.find(cookie => cookie.trim().startsWith('dev_premium_status='))

      if (premiumCookie) {
        const isDevPremium = premiumCookie.split('=')[1] === 'true'
        setIsPremiumFromCookie(isDevPremium)
      }
    }
  }, [])
  
  // Determine if bottom nav should be shown (not on workout session pages)
  useEffect(() => {
    if (isClient && pathname) {
      setShowBottomNav(!pathname.includes('/workout-session'))
    }
  }, [isClient, pathname])

  // Use cookie value in dev mode, real user data in production
  // Also check for role override cookie
  const [roleOverride, setRoleOverride] = useState<string | null>(null)

  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && isClient) {
      const cookies = document.cookie.split(';')
      const roleCookie = cookies.find(cookie => cookie.trim().startsWith('dev_override_role='))

      if (roleCookie) {
        const role = roleCookie.split('=')[1]
        setRoleOverride(role)

      }
    }
  }, [isClient])

  // All clients are now premium by default
  const isPremiumClient = true

  // Loading state for client-side data
  if (status === "loading" || (process.env.NODE_ENV === 'development' && !isClient)) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <LoadingSpinner size="md" />
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col bg-background">
      <header className="sticky top-0 z-40 glass-card bg-background/70 backdrop-blur-md shadow-sm">
        <div className="container flex h-16 items-center justify-between px-4 sm:px-8">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon-sm"
              rounded="full"
              className="mr-2 md:hidden"
              onClick={() => setIsMobileNavOpen(true)}
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
            <div className="hidden md:flex">
              <Link href="/dashboard" className="flex items-center">
                <Image
                  src="/logo/brand-logo.png"
                  alt="Clear-Coach"
                  width={180}
                  height={60}
                  className="h-12 w-auto"
                  priority
                />
              </Link>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {session?.user?.id && <NotificationBell userId={session.user.id} />}
            <div className="hidden sm:flex">
              <ThemeToggleWithLabel />
            </div>
            <div className="sm:hidden">
              <ThemeToggle />
            </div>
            <UserAccountNav
              user={{
                name: session?.user?.name || null,
                email: session?.user?.email || null,
                image: session?.user?.image || null,
              }}
            />
          </div>
        </div>
      </header>
      <div className="container grid flex-1 md:grid-cols-[220px_1fr] lg:grid-cols-[240px_1fr]">
        <aside className="hidden md:block">
          <div className="sticky top-16 overflow-y-auto py-6 pr-6">
            <MainNav userRole={userRole} />
          </div>
        </aside>
        <main className="flex-1 pt-6 pb-20 px-4 md:pb-12">
          {children}
        </main>
      </div>
      <MobileNav
        isOpen={isMobileNavOpen}
        onClose={() => setIsMobileNavOpen(false)}
        userRole={userRole}
      />
      
      {/* Bottom Navigation - only visible on mobile and not on workout session pages */}
      {isClient && showBottomNav && (
        <div className="md:hidden">
          <BottomNav />
        </div>
      )}

      {/* Role Switcher - only visible for admin/trainer in development */}
      {process.env.NODE_ENV === 'development' && (userRole === 'admin' || userRole === 'trainer') && (
        <div className="fixed bottom-4 right-4 z-50">
          <DevRoleSwitcher />
        </div>
      )}

      {/* SSE Listener for real-time updates */}
      {session?.user?.id && <SSEListener />}

      {/* SSE Refresh Button */}
      {session?.user?.id && <SSERefreshButton />}
    </div>
  )
}
