"use client"

import {
  CheckCircle,
  XCircle,
  Calendar,
  Mail,
  Phone,
  Clock,
  ArrowLeft,
  Loader2
} from "lucide-react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { useState, useEffect } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import Link from "next/link"

export default function InquiryDetailPage(props: any) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [inquiry, setInquiry] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [responseNote, setResponseNote] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)

  const inquiryId = props.params?.id;

  useEffect(() => {
    if (!inquiryId) {
        console.error("Inquiry ID is missing from params");
        setIsLoading(false);
        return;
    }

    if (status === "authenticated") {
      const fetchInquiry = async () => {
          try {
            // Example: const response = await fetch(`/api/coaching/inquiries/${inquiryId}`);
            // if (!response.ok) throw new Error('Failed to fetch inquiry');
            // const data = await response.json();
            // setInquiry(data);

             // Using mock data for now
            setInquiry({
              id: inquiryId,
              name: "Alex Wilson",
              email: "<EMAIL>",
              phone: "+****************",
              date: "2023-11-01",
              status: "PENDING", // PENDING, APPROVED, REJECTED
              goals: "Looking to build muscle and improve overall fitness. Need structure.",
              experience: "intermediate",
              availability: "Weekday evenings after 6pm ET and weekend mornings.",
              message: "History of shoulder injuries."
            });
          } catch (error) {
             console.error("Failed to fetch inquiry:", error);
          } finally {
            setIsLoading(false)
          }
      };
      fetchInquiry();
    }
  }, [inquiryId, status])

  useEffect(() => {
     if (status === "unauthenticated") {
        router.push("/login");
     }
  }, [status, router]);

  if (status === "loading" || isLoading) {
    return (
      <div className="flex min-h-[600px] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  const userRole = session?.user?.role
  if (userRole !== "trainer" && userRole !== "admin") {
    return (
      <div className="container py-10">
        <h1 className="text-2xl font-bold mb-6">Access Restricted</h1>
        <p>You don't have permission to access this page.</p>
      </div>
    )
  }

  if (!inquiry) {
    return (
      <div className="container py-10">
        <Card className="text-center p-8">
          <CardContent>
            <h3 className="text-xl font-semibold mb-2">Inquiry Not Found</h3>
            <p className="text-muted-foreground mb-4">
              We couldn&apos;t find the inquiry you&apos;re looking for, or the ID was missing.
            </p>
            <Button asChild>
              <Link href="/dashboard/coaching/inquiries">
                View All Inquiries
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const handleApprove = async () => {
    if (!inquiry) return;
    setIsProcessing(true)
    try {
      console.log("Approving inquiry:", inquiry.id, "with note:", responseNote)
      await new Promise(resolve => setTimeout(resolve, 1000))
      router.push("/dashboard/coaching?tab=clients")
    } catch (error) {
      console.error("Error approving inquiry:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleReject = async () => {
     if (!inquiry) return;
    setIsProcessing(true)
    try {
      console.log("Rejecting inquiry:", inquiry.id, "with note:", responseNote)
      await new Promise(resolve => setTimeout(resolve, 1000))
      router.push("/dashboard/coaching?tab=inquiries")
    } catch (error) {
      console.error("Error rejecting inquiry:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  const formatExperienceLevel = (level: string) => {
    switch (level) {
      case "beginner": return "Beginner (0-1 years)"
      case "intermediate": return "Intermediate (1-3 years)"
      case "advanced": return "Advanced (3+ years)"
      default: return level ? level.charAt(0).toUpperCase() + level.slice(1) : "Unknown"
    }
  }

  return (
    <div className="container py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={() => router.push("/dashboard/coaching?tab=inquiries")} className="mr-2">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Coaching Inquiry</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Applicant Information</CardTitle>
              <CardDescription>
                Review the details provided by the applicant
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium mb-2">Contact Details</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <div className="w-8 flex-shrink-0">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <span>{inquiry.email}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-8 flex-shrink-0">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <span>{inquiry.phone || "Not provided"}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-8 flex-shrink-0">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <span>Submitted on {new Date(inquiry.date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-8 flex-shrink-0">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <span>Available: {inquiry.availability}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Experience & Background</h3>
                  <div className="space-y-2">
                    <div className="flex items-start">
                      <div className="w-28 flex-shrink-0 text-muted-foreground">Experience:</div>
                      <span>{formatExperienceLevel(inquiry.experience)}</span>
                    </div>
                    <Separator />
                    <div className="flex items-start">
                      <div className="w-28 flex-shrink-0 text-muted-foreground">Status:</div>
                      <div className="font-medium">
                        {inquiry.status === "PENDING" ? (
                          <span className="text-amber-600">Pending Review</span>
                        ) : inquiry.status === "APPROVED" ? (
                          <span className="text-green-600">Approved</span>
                        ) : (
                          <span className="text-red-600">Rejected</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2">Goals</h3>
                <div className="bg-muted/40 p-4 rounded-md">
                  <p>{inquiry.goals}</p>
                </div>
              </div>

              {inquiry.message && (
                <div>
                  <h3 className="font-medium mb-2">Additional Information</h3>
                  <div className="bg-muted/40 p-4 rounded-md">
                    <p>{inquiry.message}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Your Response</CardTitle>
              <CardDescription>
                Approve or reject this coaching application
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="responseNote">Response Note (optional)</Label>
                  <Textarea
                    id="responseNote"
                    placeholder="Add any notes to include with your response..."
                    value={responseNote}
                    onChange={(e) => setResponseNote(e.target.value)}
                    className="mt-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    This note will be included in the email notification sent to the applicant.
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between border-t pt-6">
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" className="border-red-200 bg-red-50 hover:bg-red-100 text-red-600">
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject Application
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Reject Coaching Application?</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to reject this coaching application? This will notify the applicant that they were not accepted into your coaching program.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleReject}
                      className="bg-red-600 text-white hover:bg-red-700"
                      disabled={isProcessing}
                    >
                      {isProcessing ? "Processing..." : "Yes, Reject Application"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button className="bg-green-600 text-white hover:bg-green-700">
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve & Create Client
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Approve Coaching Application?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will create a new client account and send an invitation email to {inquiry.email} with instructions to complete their registration.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleApprove}
                      className="bg-green-600 text-white hover:bg-green-700"
                      disabled={isProcessing}
                    >
                      {isProcessing ? "Processing..." : "Yes, Approve & Create Client"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </CardFooter>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Coaching Program Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm">
                  This applicant is requesting to join your premium 1:1 coaching program.
                </p>
                <div className="bg-primary-foreground rounded-md p-3 space-y-2 text-sm">
                  <div className="font-medium">Premium 1:1 Coaching</div>
                  <div>$299.99/month</div>
                  <ul className="space-y-1 mt-2 pl-5 list-disc text-muted-foreground">
                    <li>Personal coaching sessions</li>
                    <li>Custom training plans</li>
                    <li>Custom diet plans</li>
                    <li>Direct coach access</li>
                    <li>Weekly check-ins</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Next Steps</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm">
                <p>If you approve this application:</p>
                <ol className="space-y-2 pl-5 list-decimal">
                  <li>A new coaching client will be created</li>
                  <li>The applicant will receive an email with registration instructions</li>
                  <li>You'll be able to create their custom coaching program</li>
                  <li>Payment processing will be set up automatically</li>
                </ol>
                <p className="text-muted-foreground mt-2">
                  Make sure to follow up with a welcome message once the client has registered.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 