"use client";

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { recursiveSanitize } from '@/lib/security/xss';
import { useCSRF } from './useCSRF';
import { globalAPICache, createCacheKey } from '../cache/apiCache';

// Query status type
export type QueryStatus = 'idle' | 'loading' | 'success' | 'error';

// Error with additional properties for API errors
export interface ApiError extends Error {
  status?: number;
  data?: any;
}

// Options for the useSafeQuery hook
export interface UseSafeQueryOptions<T> {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: any;
  headers?: HeadersInit;
  skip?: boolean;
  sanitize?: boolean;
  retry?: boolean;
  retryCount?: number;
  retryDelay?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: ApiError) => void;
  cacheKey?: string;
  dedupingInterval?: number;
  dependencies?: any[];
  cache?: boolean;
  cacheTTL?: number;
}

// Cache for storing fetched data
interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

const queryCache = new Map<string, CacheEntry<any>>();

/**
 * A custom hook for secure data fetching with CSRF protection and XSS sanitization
 * @param options The query options
 * @returns The query result and control functions
 */
export function useSafeQuery<T = any>({
  url,
  method = 'GET',
  body,
  headers,
  skip = false,
  sanitize = true,
  retry = true,
  retryCount = 3,
  retryDelay = 1000,
  onSuccess,
  onError,
  cacheKey,
  dedupingInterval = 10000, // 10 seconds
  dependencies = [],
  cache = true,
  cacheTTL,
}: UseSafeQueryOptions<T>) {
  // State for query result and status
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<ApiError | null>(null);
  const [status, setStatus] = useState<QueryStatus>('idle');
  const [retries, setRetries] = useState(0);

  // Get CSRF protection tools
  const { fetchWithCSRF } = useCSRF();

  // Use toast for error messages
  const { toast } = useToast();

  // Function to handle fetch errors
  const handleError = useCallback((err: any, statusCode?: number) => {
    const apiError: ApiError = err instanceof Error
      ? err
      : new Error(typeof err === 'string' ? err : 'An error occurred');

    if (statusCode) {
      apiError.status = statusCode;
    }

    setError(apiError);
    setStatus('error');

    if (onError) {
      onError(apiError);
    }

    // Show error toast for user feedback
    toast({
      title: 'Error',
      description: apiError.message || 'An error occurred while fetching data',
      variant: 'destructive',
    });
  }, [onError, toast]);

  // Function to execute the query
  const executeQuery = useCallback(async () => {
    if (skip) return;

    // Check cache first if cacheKey is provided
    if (cache && method === 'GET') {
      const key = cacheKey || createCacheKey(url, typeof body === 'object' ? body : undefined);
      const cachedData = globalAPICache.get(key);

      if (cachedData !== undefined) {
        setData(cachedData);
        setStatus('success');
        if (onSuccess) {
          onSuccess(cachedData);
        }
        return;
      }
    }

    setStatus('loading');

    try {
      // Build request options
      const requestOptions: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
      };

      // Add body for non-GET requests
      if (method !== 'GET' && body) {
        requestOptions.body = JSON.stringify(body);
      }

      // Execute request with CSRF protection
      const response = await fetchWithCSRF(url, requestOptions);

      // Handle HTTP errors
      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch {
          errorData = { message: 'Unknown error' };
        }

        const errorMessage = errorData.message || errorData.error || `HTTP error ${response.status}`;
        const error = new Error(errorMessage) as ApiError;
        error.status = response.status;
        error.data = errorData;

        throw error;
      }

      // Parse response data
      let responseData;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      // Sanitize data if required
      const sanitizedData = sanitize ? recursiveSanitize(responseData) : responseData;

      // Update state with success
      setData(sanitizedData);
      setStatus('success');
      setError(null);
      setRetries(0);

      // Cache the result if cacheKey is provided
      if (cache && method === 'GET') {
        const key = cacheKey || createCacheKey(url, typeof body === 'object' ? body : undefined);
        globalAPICache.set(key, sanitizedData, cacheTTL);
      }

      // Call onSuccess callback
      if (onSuccess) {
        onSuccess(sanitizedData);
      }

      return sanitizedData;
    } catch (err) {
      // Handle retries
      if (retry && retries < retryCount) {
        setRetries(prev => prev + 1);

        // Exponential backoff
        const delay = retryDelay * Math.pow(2, retries);

        setTimeout(() => {
          executeQuery();
        }, delay);

        return;
      }

      // Handle error if max retries reached
      handleError(err, (err as ApiError)?.status);
    }
  }, [
    url, method, body, headers, skip, sanitize, retry, retryCount, retryDelay,
    retries, cache, cacheKey, cacheTTL, fetchWithCSRF, handleError, onSuccess
  ]);

  // Execute query when dependencies change
  useEffect(() => {
    if (!skip) {
      executeQuery();
    }
  }, [skip, executeQuery]);

  // Function to manually refresh the data
  const refetch = useCallback(() => {
    setRetries(0);
    return executeQuery();
  }, [executeQuery]);

  // Function to manually update the data
  const updateData = useCallback((updater: (prev: T | null) => T) => {
    setData(prev => {
      const updated = updater(prev);

      // Update cache if cacheKey is provided
      if (cache && cacheKey) {
        const key = cacheKey || createCacheKey(url, typeof body === 'object' ? body : undefined);
        globalAPICache.set(key, updated, cacheTTL);
      }

      return updated;
    });
  }, [cache, cacheKey, url, body, cacheTTL]);

  // Function to clear the cache
  const clearCache = useCallback(() => {
    if (cache && cacheKey) {
      globalAPICache.delete(cacheKey);
    } else if (cache) {
      const key = createCacheKey(url, typeof body === 'object' ? body : undefined);
      globalAPICache.delete(key);
    }
  }, [cache, cacheKey, url, body]);

  // Reset state
  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setStatus('idle');
    setRetries(0);
    if (cache && cacheKey) {
      globalAPICache.delete(cacheKey);
    }
  }, [cache, cacheKey]);

  return {
    data,
    error,
    status,
    isLoading: status === 'loading',
    isSuccess: status === 'success',
    isError: status === 'error',
    refetch,
    updateData,
    clearCache,
    reset,
  };
}

/**
 * A custom hook for making mutations (POST, PUT, DELETE) with CSRF protection
 * @param options The mutation options
 * @returns The mutation function and state
 */
export function useSafeMutation<TData = any, TVariables = any>({
  url,
  method = 'POST',
  headers,
  sanitize = true,
  onSuccess,
  onError,
}: Omit<UseSafeQueryOptions<TData>, 'body' | 'skip'>) {
  // State for mutation result and status
  const [data, setData] = useState<TData | null>(null);
  const [error, setError] = useState<ApiError | null>(null);
  const [status, setStatus] = useState<QueryStatus>('idle');

  // Get CSRF protection tools
  const { fetchWithCSRF } = useCSRF();

  // Use toast for error messages
  const { toast } = useToast();

  // Function to execute the mutation
  const mutate = useCallback(async (variables?: TVariables) => {
    setStatus('loading');

    try {
      // Build request options
      const requestOptions: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
      };

      // Add body if variables are provided
      if (variables) {
        requestOptions.body = JSON.stringify(variables);
      }

      // Execute request with CSRF protection
      const response = await fetchWithCSRF(url, requestOptions);

      // Handle HTTP errors
      if (!response.ok) {
        let errorData;
        try {
          // Check if json method exists
          if (typeof response.json === 'function') {
            errorData = await response.json();
          } else {
            // Fallback for tests
            errorData = { message: 'Unknown error' };
          }
        } catch {
          errorData = { message: 'Unknown error' };
        }

        // Ensure we have a valid error object
        if (!errorData || typeof errorData !== 'object') {
          errorData = { message: 'Unknown error' };
        }

        // Create a safe error message
        let errorMessage = 'Unknown error';
        try {
          errorMessage = errorData.message || errorData.error || `HTTP error ${response.status || 'unknown'}`;
        } catch (e) {
          console.error('Error creating error message:', e);
        }

        // Create an ApiError object
        const error = new Error(errorMessage) as ApiError;
        error.status = response.status;
        error.data = errorData;

        throw error;
      }

      // Parse response data
      let responseData;
      try {
        // Check if headers exist and have a get method
        const contentType = response.headers && typeof response.headers.get === 'function'
          ? response.headers.get('content-type')
          : null;

        if (contentType && contentType.includes('application/json')) {
          responseData = await response.json();
        } else if (typeof response.json === 'function') {
          // Try to parse as JSON first if method exists
          try {
            responseData = await response.json();
          } catch (e) {
            // If JSON parsing fails, try text
            if (typeof response.text === 'function') {
              responseData = await response.text();
            } else {
              // Fallback if neither method is available
              responseData = response;
            }
          }
        } else if (typeof response.text === 'function') {
          responseData = await response.text();
        } else {
          // Fallback for mock responses in tests
          responseData = response;
        }
      } catch (error) {
        console.error('Error parsing response:', error);
        // Fallback to the raw response
        responseData = response;
      }

      // Sanitize data if required
      const sanitizedData = sanitize ? recursiveSanitize(responseData) : responseData;

      // Update state with success
      setData(sanitizedData);
      setStatus('success');
      setError(null);

      // Call onSuccess callback
      if (onSuccess) {
        onSuccess(sanitizedData);
      }

      return sanitizedData;
    } catch (err) {
      // Update state with error
      const apiError = err instanceof Error
        ? err as ApiError
        : new Error(typeof err === 'string' ? err : 'An error occurred');

      setError(apiError);
      setStatus('error');

      // Call onError callback
      if (onError) {
        onError(apiError);
      }

      // Show error toast for user feedback
      toast({
        title: 'Error',
        description: apiError.message || 'An error occurred',
        variant: 'destructive',
      });

      throw apiError;
    }
  }, [url, method, headers, sanitize, onSuccess, onError, fetchWithCSRF, toast]);

  // Reset state
  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setStatus('idle');
  }, []);

  return {
    mutate,
    data,
    error,
    status,
    isLoading: status === 'loading',
    isSuccess: status === 'success',
    isError: status === 'error',
    reset,
  };
}