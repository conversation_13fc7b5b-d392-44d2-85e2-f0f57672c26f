import * as fs from 'fs';
import * as path from 'path';
import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { v4 as uuidv4 } from "uuid";
import { authOptions } from "@/lib/auth";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { validateFile } from "@/lib/security/file-validation";

// Initialize the S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

// Define allowed file types for different upload categories
const ALLOWED_FILE_TYPES = {
  thumbnail: ["image/jpeg", "image/png", "image/webp", "image/gif"],
  product: ["application/pdf", "video/mp4", "audio/mpeg", "application/zip"],
};

// Max file size (50MB)
const MAX_FILE_SIZE = 50 * 1024 * 1024;

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only admins and trainers can upload files
    // Based on database schema, role can be "admin", "client", or "trainer"
    if (session.user.role !== "admin" && session.user.role !== "trainer") {
      return NextResponse.json({ error: "Only admins and trainers can upload files" }, { status: 403 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File | null;
    // Get upload type from form data or query parameter
    const uploadType = (formData.get("type") as string) || 
                      request.nextUrl.searchParams.get("type") || 
                      "product";

    if (!["thumbnail", "product"].includes(uploadType)) {
      return NextResponse.json({ error: "Invalid upload type" }, { status: 400 });
    }

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    const allowedTypes = ALLOWED_FILE_TYPES[uploadType as keyof typeof ALLOWED_FILE_TYPES];
    const validation = validateFile(file, allowedTypes, MAX_FILE_SIZE);
    
    // Skip validation for admin users
    if (session.user.role !== "admin" && !validation.valid) {
      return NextResponse.json({
        error: validation.error
      }, { status: 400 });
    }

    // Generate unique filename
    const fileName = `${uuidv4()}-${file.name.replace(/[^a-zA-Z0-9.-]/g, "")}`;
    
    let fileUrl = "";
    
    // Try S3 upload if credentials are available
    if (process.env.AWS_ACCESS_KEY_ID && 
        process.env.AWS_SECRET_ACCESS_KEY && 
        process.env.AWS_S3_BUCKET_NAME) {
      
      try {
        // Organize the s3 path by user and upload type
        const userId = session.user.id;
        const s3Key = `${userId}/${uploadType}/${fileName}`;
        
        // Convert file to buffer
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        
        // Upload to S3
        const command = new PutObjectCommand({
          Bucket: process.env.AWS_S3_BUCKET_NAME,
          Key: s3Key,
          Body: buffer,
          ContentType: file.type,
          ACL: "public-read",
        });
        
        await s3Client.send(command);
        
        // Get the public URL
        fileUrl = `https://${process.env.AWS_S3_BUCKET_NAME}.s3.amazonaws.com/${s3Key}`;
        
        console.log(`[UPLOAD_SUCCESS] S3 upload: ${fileUrl}`);
      } catch (error) {
        console.error("[S3_UPLOAD_ERROR]", error);
        // Fall back to local storage on S3 error
      }
    }
    
    // Fall back to local storage if S3 upload failed or credentials not available
    if (!fileUrl) {
      // Create upload directory path
      const uploadDir = path.join(process.cwd(), 'public', 'uploads', uploadType);

      // Ensure the directory exists
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      // Full path for the file
      const filePath = path.join(uploadDir, fileName);

      // Convert file to buffer and save to filesystem
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);

      // Write the file
      fs.writeFileSync(filePath, buffer);

      // Public URL for the file (accessible via /uploads/...)
      fileUrl = `/uploads/${uploadType}/${fileName}`;
      
      console.log(`[UPLOAD_SUCCESS] Local upload: ${fileUrl}`);
    }

    return NextResponse.json({
      url: fileUrl,
      fileName,
      fileType: file.type,
      fileSize: file.size
    });
  } catch (error) {
    console.error("[UPLOAD_ERROR]", error);
    return NextResponse.json({ error: "Failed to upload file" }, { status: 500 });
  }
}
