import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function PUT(
  req: Request,
  context: any // Use 'any' workaround
) {
  try {
    const session = await getServerSession(authOptions)

    // Verify authentication and admin role
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Only admins should be able to set service settings for trainers
    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden: Admins only" }, { status: 403 })
    }

    // Safely access params
    const trainerId = context?.params?.trainerId;
    if (!trainerId) {
      return new NextResponse("Trainer ID missing in URL", { status: 400 });
    }
    
    // Verify trainer exists
    const trainer = await prisma.user.findUnique({
      where: {
        id: trainerId,
        role: "trainer", // Ensure the user is actually a trainer
      },
      select: { id: true } // Only select needed field
    })

    if (!trainer) {
      return NextResponse.json({ error: "Trainer not found or user is not a trainer" }, { status: 404 })
    }

    // Get request body
    const body = await req.json()
    const { serviceType, enableStore, enableSubscriptions, enablePremiumCoaching } = body

    // Input validation (add more specific checks if needed)
    if (typeof serviceType !== "string" || !["full-service", "store-only", "subscriptions-only"].includes(serviceType)) { // Example validation
      return NextResponse.json({ error: "Invalid serviceType provided" }, { status: 400 })
    }

    if (typeof enableStore !== "boolean" || 
        typeof enableSubscriptions !== "boolean" || 
        typeof enablePremiumCoaching !== "boolean") {
      return NextResponse.json({ error: "Invalid boolean value for feature flags" }, { status: 400 })
    }

    // Upsert trainer settings
    const trainerSettings = await prisma.trainerSettings.upsert({
      where: {
        userId: trainerId,
      },
      update: {
        serviceType,
        enableStore,
        enableSubscriptions,
        enablePremiumCoaching,
      },
      create: {
        userId: trainerId,
        serviceType,
        enableStore,
        enableSubscriptions,
        enablePremiumCoaching,
      },
    })

    return NextResponse.json({
      message: "Service settings updated successfully",
      trainerSettings,
    })
  } catch (error) {
    console.error("[SERVICE_SETTINGS_UPDATE]", error)
    return NextResponse.json(
      { error: "An error occurred while updating service settings" },
      { status: 500 }
    )
  }
} 