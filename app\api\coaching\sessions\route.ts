import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const userRole = session.user.role;

    switch (action) {
      case 'upcoming': {
        // Get upcoming sessions for the current user
        const now = new Date();
        
        if (userRole === 'trainer') {
          // For trainers, get all upcoming sessions with clients
          const sessions = await prisma.coachingSession.findMany({
            where: {
              coachingRelationship: {
                trainerId: session.user.id,
                status: 'active',
              },
              scheduledDate: {
                gte: now,
              },
              status: {
                in: ['scheduled', 'rescheduled'],
              },
            },
            include: {
              coachingRelationship: {
                include: {
                  client: {
                    select: {
                      id: true,
                      name: true,
                      email: true,
                      avatarUrl: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              scheduledDate: 'asc',
            },
          });
          
          return NextResponse.json({ sessions });
        } else if (userRole === 'client') {
          // For clients, get all upcoming sessions with trainers
          const sessions = await prisma.coachingSession.findMany({
            where: {
              coachingRelationship: {
                clientId: session.user.id,
                status: 'active',
              },
              scheduledDate: {
                gte: now,
              },
              status: {
                in: ['scheduled', 'rescheduled'],
              },
            },
            include: {
              coachingRelationship: {
                include: {
                  trainer: {
                    select: {
                      id: true,
                      name: true,
                      email: true,
                      avatarUrl: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              scheduledDate: 'asc',
            },
          });
          
          return NextResponse.json({ sessions });
        } else {
          return NextResponse.json({ error: 'Invalid user role' }, { status: 403 });
        }
      }
      
      case 'past': {
        // Get past sessions for the current user
        const now = new Date();
        
        if (userRole === 'trainer') {
          // For trainers, get all past sessions with clients
          const sessions = await prisma.coachingSession.findMany({
            where: {
              coachingRelationship: {
                trainerId: session.user.id,
                status: 'active',
              },
              scheduledDate: {
                lt: now,
              },
            },
            include: {
              coachingRelationship: {
                include: {
                  client: {
                    select: {
                      id: true,
                      name: true,
                      email: true,
                      avatarUrl: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              scheduledDate: 'desc',
            },
            take: 10,
          });
          
          return NextResponse.json({ sessions });
        } else if (userRole === 'client') {
          // For clients, get all past sessions with trainers
          const sessions = await prisma.coachingSession.findMany({
            where: {
              coachingRelationship: {
                clientId: session.user.id,
                status: 'active',
              },
              scheduledDate: {
                lt: now,
              },
            },
            include: {
              coachingRelationship: {
                include: {
                  trainer: {
                    select: {
                      id: true,
                      name: true,
                      email: true,
                      avatarUrl: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              scheduledDate: 'desc',
            },
            take: 10,
          });
          
          return NextResponse.json({ sessions });
        } else {
          return NextResponse.json({ error: 'Invalid user role' }, { status: 403 });
        }
      }
      
      case 'session': {
        // Get details for a specific session
        const sessionId = searchParams.get('sessionId');
        
        if (!sessionId) {
          return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
        }
        
        const coachingSession = await prisma.coachingSession.findUnique({
          where: {
            id: sessionId,
          },
          include: {
            coachingRelationship: {
              include: {
                trainer: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    avatarUrl: true,
                  },
                },
                client: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    avatarUrl: true,
                  },
                },
              },
            },
          },
        });
        
        if (!coachingSession) {
          return NextResponse.json({ error: 'Session not found' }, { status: 404 });
        }
        
        // Check if the user has access to this session
        if (
          userRole === 'trainer' && coachingSession.coachingRelationship.trainer.id !== session.user.id ||
          userRole === 'client' && coachingSession.coachingRelationship.client.id !== session.user.id
        ) {
          return NextResponse.json({ error: 'You do not have access to this session' }, { status: 403 });
        }
        
        return NextResponse.json({ session: coachingSession });
      }
      
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in coaching sessions API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const userRole = session.user.role;
    const body = await request.json();

    switch (action) {
      case 'create': {
        // Create a new coaching session manually (not through Calendly)
        if (userRole !== 'trainer') {
          return NextResponse.json({ error: 'Only trainers can create sessions' }, { status: 403 });
        }
        
        const { 
          clientId, 
          title, 
          description, 
          scheduledDate, 
          duration, 
          type, 
          location, 
          notes,
          videoConferenceUrl 
        } = body;
        
        if (!clientId || !title || !scheduledDate || !duration) {
          return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
        }
        
        // Find or create coaching relationship
        let coachingRelationship = await prisma.coachingRelationship.findFirst({
          where: {
            trainerId: session.user.id,
            clientId,
            status: 'active',
          },
        });
        
        if (!coachingRelationship) {
          coachingRelationship = await prisma.coachingRelationship.create({
            data: {
              trainerId: session.user.id,
              clientId,
              status: 'active',
            },
          });
        }
        
        // Create coaching session
        const coachingSession = await prisma.coachingSession.create({
          data: {
            coachingRelationshipId: coachingRelationship.id,
            title,
            description: description || '',
            scheduledDate: new Date(scheduledDate),
            duration,
            status: 'scheduled',
            type: type || 'video',
            location: location || '',
            notes: notes || '',
            videoConferenceUrl: videoConferenceUrl || '',
          },
        });
        
        return NextResponse.json({ success: true, session: coachingSession });
      }
      
      case 'update': {
        // Update an existing coaching session
        const { sessionId, status, notes } = body;
        
        if (!sessionId) {
          return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
        }
        
        // Find the session
        const coachingSession = await prisma.coachingSession.findUnique({
          where: {
            id: sessionId,
          },
          include: {
            coachingRelationship: true,
          },
        });
        
        if (!coachingSession) {
          return NextResponse.json({ error: 'Session not found' }, { status: 404 });
        }
        
        // Check if the user has access to update this session
        if (
          userRole === 'trainer' && coachingSession.coachingRelationship.trainerId !== session.user.id ||
          userRole === 'client' && coachingSession.coachingRelationship.clientId !== session.user.id
        ) {
          return NextResponse.json({ error: 'You do not have access to update this session' }, { status: 403 });
        }
        
        // Update the session
        const updatedSession = await prisma.coachingSession.update({
          where: {
            id: sessionId,
          },
          data: {
            status: status || undefined,
            notes: notes || undefined,
          },
        });
        
        return NextResponse.json({ success: true, session: updatedSession });
      }
      
      case 'cancel': {
        // Cancel a coaching session
        const { sessionId, reason } = body;
        
        if (!sessionId) {
          return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
        }
        
        // Find the session
        const coachingSession = await prisma.coachingSession.findUnique({
          where: {
            id: sessionId,
          },
          include: {
            coachingRelationship: true,
          },
        });
        
        if (!coachingSession) {
          return NextResponse.json({ error: 'Session not found' }, { status: 404 });
        }
        
        // Check if the user has access to cancel this session
        if (
          userRole === 'trainer' && coachingSession.coachingRelationship.trainerId !== session.user.id ||
          userRole === 'client' && coachingSession.coachingRelationship.clientId !== session.user.id
        ) {
          return NextResponse.json({ error: 'You do not have access to cancel this session' }, { status: 403 });
        }
        
        // Update the session
        const updatedSession = await prisma.coachingSession.update({
          where: {
            id: sessionId,
          },
          data: {
            status: 'cancelled',
            notes: reason ? `Cancelled: ${reason}` : 'Cancelled by user',
          },
        });
        
        return NextResponse.json({ success: true, session: updatedSession });
      }
      
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in coaching sessions API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
