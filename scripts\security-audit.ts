#!/usr/bin/env ts-node

/**
 * Security Audit Script
 * 
 * This script runs a security audit on the application and generates a report.
 * 
 * Usage:
 *   npm run security:audit
 * 
 * Or directly:
 *   ts-node scripts/security-audit.ts
 */

import { runSecurityAudit, printSecurityAuditResults } from '../lib/security/audit';
import fs from 'fs';
import path from 'path';

// Main function
async function main() {
  console.log('Starting security audit...');
  
  try {
    // Run the security audit
    const auditResults = await runSecurityAudit();
    
    // Print the results to console
    printSecurityAuditResults(auditResults);
    
    // Save the results to a file
    const outputDir = path.join(process.cwd(), 'reports');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const filename = `security-audit-${new Date().toISOString().replace(/:/g, '-')}.json`;
    const outputPath = path.join(outputDir, filename);
    
    fs.writeFileSync(
      outputPath,
      JSON.stringify(auditResults, null, 2)
    );
    
    console.log(`\nAudit report saved to: ${outputPath}`);
    
    // Exit with appropriate code based on audit results
    process.exit(auditResults.overallStatus === 'fail' ? 1 : 0);
  } catch (error) {
    console.error('Error running security audit:', error);
    process.exit(1);
  }
}

// Run the main function
main(); 