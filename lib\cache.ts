/**
 * Simple in-memory cache implementation for Prisma queries
 * This helps reduce database load for frequently accessed data
 */

import { unstable_cache } from 'next/cache'

// Cache durations in seconds
export const CACHE_DURATIONS = {
  SHORT: 60, // 1 minute
  MEDIUM: 300, // 5 minutes
  LONG: 3600, // 1 hour
  VERY_LONG: 86400, // 24 hours
} as const

// Create a cached function with proper typing
export function createCache<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  keyParts: string[],
  options: {
    revalidate?: number
    tags?: string[]
  } = {}
) {
  return unstable_cache(
    fn,
    keyParts,
    {
      revalidate: options.revalidate || CACHE_DURATIONS.MEDIUM,
      tags: options.tags || [],
    }
  )
}

// In-memory cache for quick lookups (for dev environment)
const memoryCache = new Map<string, { data: any; expiry: number }>()

export function getFromMemoryCache<T>(key: string): T | null {
  const cached = memoryCache.get(key)
  if (!cached || Date.now() > cached.expiry) {
    memoryCache.delete(key)
    return null
  }
  return cached.data
}

export function setToMemoryCache<T>(key: string, data: T, ttlSeconds: number = 300): void {
  const expiry = Date.now() + (ttlSeconds * 1000)
  memoryCache.set(key, { data, expiry })
}

// Clean up expired entries periodically
setInterval(() => {
  const now = Date.now()
  for (const [key, value] of memoryCache.entries()) {
    if (now > value.expiry) {
      memoryCache.delete(key)
    }
  }
}, 60000) // Clean up every minute
