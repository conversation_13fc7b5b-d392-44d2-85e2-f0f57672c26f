import { NextResponse } from "next/server"
import { BaseApiHandler } from "./base-api-handler"
import { ClientService } from "../services/client-service"
import { prisma } from "@/lib/prisma"

export class ClientHandler extends BaseApiHandler {
  /**
   * Get all clients for a trainer
   */
  protected async get(req: Request, userId: string): Promise<NextResponse> {
    const clients = await ClientService.findByTrainerId(userId)
    return NextResponse.json(clients)
  }

  /**
   * Create a new client
   */
  protected async post(req: Request, userId: string): Promise<NextResponse> {
    const data = await req.json()

    // Check if the user exists
    if (!data.userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 })
    }

    const client = await ClientService.create({
      user: {
        connect: {
          id: data.userId
        }
      },
      assignedTrainer: {
        connect: {
          userId
        }
      },
      height: data.height,
      weight: data.weight,
      goals: data.goals,
      fitnessLevel: data.fitnessLevel,
      medicalConditions: data.medicalConditions
    })

    return NextResponse.json(client)
  }

  /**
   * Update a client (not implemented in main route)
   */
  protected async put(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete a client (not implemented in main route)
   */
  protected async delete(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}

export class ClientByIdHandler extends BaseApiHandler {
  /**
   * Get a client by ID
   */
  protected async get(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    try {
      console.log(`ClientByIdHandler.get: Fetching client with ID ${params.id} for user ${userId}`);

      // Get the user to check their role
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, role: true }
      });

      if (!user) {
        console.error(`User not found with ID ${userId}`);
        return NextResponse.json({ error: "User not found" }, { status: 404 })
      }

      console.log(`User found with role: ${user.role}`);

      const client = await ClientService.findByIdWithDetails(params.id)

      if (!client) {
        console.error(`Client not found with ID ${params.id}`);
        return NextResponse.json({ error: "Client not found" }, { status: 404 })
      }

      console.log(`Client found: ${client.name}`);

      // Allow access if:
      // 1. The user is the client themselves (userId matches client's userId)
      // 2. The user is the client's assigned trainer
      // 3. The user is an admin
      // 4. The user is a trainer and the client has a subscription with them
      const isClient = client.user?.id === userId || client.userId === userId || client.id === userId;

      // Check for assigned trainer in all possible locations
      const isAssignedTrainer =
        client.assignedTrainer?.userId === userId ||
        client.assignedTrainer?.user?.id === userId ||
        client.clientProfile?.assignedTrainer?.userId === userId ||
        client.clientProfile?.assignedTrainer?.user?.id === userId;

      // Check if the client has a subscription with this trainer
      const hasSubscriptionWithTrainer = client.subscriptions?.some(
        sub => sub.trainer?.id === userId || sub.trainerId === userId
      );

      const isAdmin = user.role === "admin";
      const isTrainer = user.role === "trainer";

      console.log('Access check:', {
        userId,
        clientId: client.id,
        clientUserId: client.user?.id || client.userId,
        trainerUserId: client.assignedTrainer?.userId || client.assignedTrainer?.user?.id ||
                      client.clientProfile?.assignedTrainer?.userId ||
                      client.clientProfile?.assignedTrainer?.user?.id,
        userRole: user.role,
        isClient,
        isAssignedTrainer,
        hasSubscriptionWithTrainer,
        isAdmin,
        isTrainer
      });

      // Allow access if any of the conditions are met
      if (!isClient && !isAssignedTrainer && !isAdmin && !(isTrainer && hasSubscriptionWithTrainer)) {
        // For debugging, log more details about the client's subscriptions
        console.log('Client subscriptions:', client.subscriptions?.map(sub => ({
          id: sub.id,
          trainerId: sub.trainerId,
          trainerName: sub.trainer?.name,
          status: sub.status
        })));

        // TEMPORARY FIX: Allow trainers to access any client data
        // This is a fallback to ensure trainers can always access client data
        // Remove this once the subscription system is fully implemented
        if (isTrainer) {
          console.log('FALLBACK: Allowing trainer access to client data');
          // Continue execution and return the client data
        } else {
          return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
        }
      }

      return NextResponse.json(client);
    } catch (error) {
      console.error(`Error in ClientByIdHandler.get:`, error);
      return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }

  }

  /**
   * Create a client (not implemented in [id] route)
   */
  protected async post(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Update a client
   */
  protected async put(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    const data = await req.json()

    // Check if the client exists
    const client = await ClientService.findById(params.id)

    if (!client) {
      return NextResponse.json({ error: "Client not found" }, { status: 404 })
    }

    // Check if the client is assigned to this trainer
    if (client.assignedTrainerId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    const updatedClient = await ClientService.update(params.id, data)

    return NextResponse.json(updatedClient)
  }

  /**
   * Delete a client
   */
  protected async delete(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    // Check if the client exists
    const client = await ClientService.findById(params.id)

    if (!client) {
      return NextResponse.json({ error: "Client not found" }, { status: 404 })
    }

    // Check if the client is assigned to this trainer
    if (client.assignedTrainerId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    await ClientService.delete(params.id)

    return NextResponse.json({ success: true })
  }
}

export class ClientTrainingPlanHandler extends BaseApiHandler {
  /**
   * Get training plans for a client
   */
  protected async get(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    console.log(`GET /api/clients/${params.id}/training-plan - Request received from trainer ${userId}`)

    // Check if there's a specific plan ID requested
    const url = new URL(req.url)
    const planId = url.searchParams.get('planId')

    if (planId) {
      console.log(`Specific plan requested: ${planId}`)
      // Fetch a specific training plan
      const plan = await prisma.trainingPlanTemplate.findUnique({
        where: {
          id: planId,
          clientId: params.id,
          type: 'personalized'
        }
      })

      if (!plan) {
        return NextResponse.json({ error: "Training plan not found" }, { status: 404 })
      }

      return NextResponse.json(plan)
    }

    // Get client details with training plans
    const client = await ClientService.findByIdWithDetails(params.id)

    if (!client) {
      return NextResponse.json({ error: "Client not found" }, { status: 404 })
    }

    // Check if the client is assigned to this trainer
    // For user-based clients, check subscriptions
    const isAuthorized = client.assignedTrainer?.id === userId ||
                        (client.subscriptions && client.subscriptions.some(sub => sub.athleteId === userId))

    if (!isAuthorized) {
      console.log(`Unauthorized: Trainer ${userId} is not assigned to client ${params.id}`)
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    console.log(`Returning training plan for client ${params.id}:`,
                client.trainingPlans ? `Plan ID: ${client.trainingPlans.id}` : 'No plan')
    return NextResponse.json(client.trainingPlans)
  }

  /**
   * Assign a training plan to a client
   */
  protected async post(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    try {
      const { trainingPlanId, customName } = await req.json()
      console.log('Received request to assign plan:', { trainingPlanId, customName, clientId: params.id })

      if (!trainingPlanId) {
        return NextResponse.json({ error: "Training plan ID is required" }, { status: 400 })
      }

      // Get the original template to copy its data
      const originalTemplate = await prisma.trainingPlanTemplate.findUnique({
        where: { id: trainingPlanId }
      })

      if (!originalTemplate) {
        return NextResponse.json({ error: "Training plan not found" }, { status: 404 })
      }

      console.log('Found original template:', {
        id: originalTemplate.id,
        title: originalTemplate.title,
        weeksType: typeof originalTemplate.weeks
      })

      // Parse weeks data if needed
      let weeksData = originalTemplate.weeks;
      if (typeof weeksData === 'string') {
        try {
          weeksData = JSON.parse(weeksData);
          console.log('Successfully parsed weeks data from string');
        } catch (e) {
          console.error('Error parsing weeks data:', e);
          weeksData = {};
        }
      }

      // Try to find the client profile in multiple ways
      let clientProfile = null;

      // First try by user ID
      clientProfile = await prisma.clientProfile.findUnique({
        where: { userId: params.id }
      });

      // If not found, try by client profile ID
      if (!clientProfile) {
        clientProfile = await prisma.clientProfile.findUnique({
          where: { id: params.id }
        });
        console.log(`Tried to find client profile by ID: ${params.id}, found: ${!!clientProfile}`);
      }

      // If still not found, try to find any client profile assigned to this trainer
      if (!clientProfile) {
        // Get the trainer profile
        const trainerProfile = await prisma.trainerProfile.findUnique({
          where: { userId: userId }
        });

        if (trainerProfile) {
          // Find all clients assigned to this trainer
          const clients = await prisma.clientProfile.findMany({
            where: { assignedTrainerId: trainerProfile.id }
          });

          console.log(`Found ${clients.length} clients assigned to trainer ${trainerProfile.id}`);

          // For debugging, log all client profiles
          const allClients = await prisma.clientProfile.findMany();
          console.log(`Total client profiles in database: ${allClients.length}`);
          console.log('All client profiles:', allClients.map(c => ({ id: c.id, userId: c.userId, trainerId: c.assignedTrainerId })));
        }
      }

      // If client profile doesn't exist, create one
      if (!clientProfile) {
        console.log(`Client profile not found for user ${params.id}, creating one...`);

        // First, check if the user exists
        const user = await prisma.user.findUnique({
          where: { id: params.id }
        });

        if (!user) {
          console.log(`User not found with ID ${params.id}, creating a mock user...`);

          // Create a mock user if it doesn't exist
          try {
            const mockUser = await prisma.user.create({
              data: {
                id: params.id,
                email: `client-${params.id}@example.com`,
                name: `Client ${params.id.substring(0, 5)}`,
                role: 'client'
              }
            });

            console.log(`Created mock user:`, {
              id: mockUser.id,
              email: mockUser.email,
              name: mockUser.name
            });
          } catch (error) {
            console.error('Error creating mock user:', error);
            return NextResponse.json({ error: "Failed to create user" }, { status: 500 });
          }
        }

        // Get the trainer profile for the current user (trainer)
        const trainerProfile = await prisma.trainerProfile.findUnique({
          where: { userId: userId }
        });

        if (!trainerProfile) {
          console.log(`Creating trainer profile for user ${userId}...`);
          // Create trainer profile if it doesn't exist
          const newTrainerProfile = await prisma.trainerProfile.create({
            data: {
              userId: userId
            }
          });
          console.log(`Created trainer profile:`, newTrainerProfile);
        }

        // Get the trainer profile again (in case it was just created)
        const updatedTrainerProfile = await prisma.trainerProfile.findUnique({
          where: { userId: userId }
        });

        if (!updatedTrainerProfile) {
          return NextResponse.json({ error: "Failed to create trainer profile" }, { status: 500 });
        }

        // Create the client profile
        clientProfile = await prisma.clientProfile.create({
          data: {
            userId: params.id,
            assignedTrainerId: updatedTrainerProfile.id
          }
        });

        console.log(`Created client profile:`, {
          id: clientProfile.id,
          userId: clientProfile.userId,
          assignedTrainerId: clientProfile.assignedTrainerId
        });
      }

      console.log('Found client profile:', {
        clientProfileId: clientProfile.id,
        userId: clientProfile.userId,
        assignedTrainerId: clientProfile.assignedTrainerId
      });

      let result;

      // Use the client ID directly from the User table
      console.log(`Looking for user with ID ${params.id}`);

      // Find the user
      const user = await prisma.user.findUnique({
        where: { id: params.id }
      });

      if (!user) {
        console.error(`User not found with ID ${params.id}`);
        return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      console.log(`Using user for direct plan creation:`, {
        userId: user.id,
        name: user.name,
        email: user.email
      });

      // Create a personalized training plan directly
      result = await prisma.trainingPlanTemplate.create({
        data: {
          title: customName || `${originalTemplate.title} (Personalized)`,
          description: originalTemplate.description,
          difficulty: originalTemplate.difficulty,
          type: "personalized",
          trainerId: userId,
          clientId: user.id, // Use the user ID as the client ID
          weeks: weeksData
        }
      });

      console.log(`Created plan directly:`, {
        id: result.id,
        title: result.title,
        clientId: result.clientId,
        trainerId: result.trainerId
      });

      // Return the assigned plan with success status and detailed information
      return NextResponse.json({
        success: true,
        message: "Plan assigned successfully",
        plan: {
          id: result.id,
          title: result.title,
          description: result.description,
          type: result.type,
          clientId: result.clientId,
          trainerId: result.trainerId,
          difficulty: result.difficulty,
          createdAt: result.createdAt
        }
      })
    } catch (error) {
      console.error('Error assigning training plan:', error)
      return NextResponse.json({
        error: error instanceof Error ? error.message : "Unknown error occurred"
      }, { status: 400 })
    }
  }

  /**
   * Update a client's training plan
   */
  protected async put(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    try {
      console.log('Updating plan for client:', params.id);

      // Get the plan data from the request body
      const planData = await req.json();
      console.log('Plan data to update:', planData);

      // Find the user
      const user = await prisma.user.findUnique({
        where: { id: params.id }
      });

      if (!user) {
        console.error(`User not found with ID ${params.id}`);
        return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      console.log('Found user:', {
        id: user.id,
        name: user.name,
        email: user.email
      });

      // Find the existing plan
      const existingPlan = await prisma.trainingPlanTemplate.findFirst({
        where: {
          clientId: user.id,
          type: 'personalized'
        }
      });

      if (!existingPlan) {
        console.error(`No personalized plan found for user ${user.id}`);
        return NextResponse.json({ error: "Plan not found" }, { status: 404 });
      }

      console.log('Found existing plan:', {
        id: existingPlan.id,
        title: existingPlan.title,
        clientId: existingPlan.clientId
      });

      // Update the plan
      const updatedPlan = await prisma.trainingPlanTemplate.update({
        where: { id: existingPlan.id },
        data: {
          weeks: planData.weeks
        }
      });

      console.log('Plan updated successfully:', {
        id: updatedPlan.id,
        title: updatedPlan.title
      });

      return NextResponse.json({
        success: true,
        message: "Plan updated successfully",
        plan: updatedPlan
      });
    } catch (error) {
      console.error('Error updating plan:', error);
      return NextResponse.json({
        error: error instanceof Error ? error.message : "Unknown error occurred"
      }, { status: 500 });
    }
  }

  /**
   * Remove a training plan from a client
   */
  protected async delete(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    try {
      console.log('Disassociating plan from client:', params.id);

      // Get the URL parameters
      const url = new URL(req.url);
      const planId = url.searchParams.get('planId');
      const planTitle = url.searchParams.get('title');

      console.log('Plan to disassociate:', { planId, planTitle });

      // Find the user directly
      const user = await prisma.user.findUnique({
        where: { id: params.id }
      });

      if (!user) {
        console.error(`User not found with ID ${params.id}`);
        return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      console.log('Found user:', {
        id: user.id,
        name: user.name,
        email: user.email
      });

      // Check if the plan ID starts with "local-", which means it's a local plan not in the database
      if (planId && planId.startsWith('local-')) {
        console.log('Plan is local only, no need to delete from database');

        // Try to find any plans in the database that might be associated with this user
        const userPlans = await prisma.trainingPlanTemplate.findMany({
          where: {
            clientId: user.id,
            type: 'personalized'
          }
        });

        console.log(`Found ${userPlans.length} plans in database for user ${user.id}`);

        // Delete all personalized plans for this user
        if (userPlans.length > 0) {
          for (const plan of userPlans) {
            console.log(`Deleting plan ${plan.id} from database`);
            await prisma.trainingPlanTemplate.delete({
              where: { id: plan.id }
            });
          }
        }

        return NextResponse.json({
          success: true,
          message: "Local plan disassociated successfully",
          plan: { id: planId }
        });
      }

      // Find the training plan in the database
      let trainingPlan;
      if (planId) {
        trainingPlan = await prisma.trainingPlanTemplate.findUnique({
          where: { id: planId }
        });
      } else if (planTitle) {
        trainingPlan = await prisma.trainingPlanTemplate.findFirst({
          where: {
            title: planTitle,
            clientId: user.id
          }
        });
      }

      // If no plan found in database but we have a plan ID or title, consider it a success
      // This handles the case where the plan exists only in localStorage
      if (!trainingPlan) {
        console.log('Training plan not found in database, considering it already disassociated');
        return NextResponse.json({
          success: true,
          message: "Plan already disassociated",
          plan: { id: planId || 'unknown', title: planTitle || 'Unknown Plan' }
        });
      }

      console.log('Found training plan to disassociate:', trainingPlan.id);

      // Delete the training plan
      const result = await prisma.trainingPlanTemplate.delete({
        where: { id: trainingPlan.id }
      });

      return NextResponse.json({
        success: true,
        message: "Plan disassociated successfully",
        plan: result
      });
    } catch (error) {
      console.error('Error disassociating plan:', error);
      return NextResponse.json({
        error: error instanceof Error ? error.message : "Unknown error occurred"
      }, { status: 500 });
    }
  }
}

export class ClientCountHandler extends BaseApiHandler {
  /**
   * Get client counts for a trainer
   */
  protected async get(req: Request, userId: string): Promise<NextResponse> {
    const counts = await ClientService.getClientCounts(userId)
    return NextResponse.json(counts)
  }

  /**
   * Create client counts (not implemented)
   */
  protected async post(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Update client counts (not implemented)
   */
  protected async put(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete client counts (not implemented)
   */
  protected async delete(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}
