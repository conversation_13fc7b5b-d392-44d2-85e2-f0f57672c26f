'use client';

import { useState } from "react";
import Link from "next/link";
import { Calendar, ChevronDown, ChevronUp, BarChart, Lock } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

interface ProgramProgressCardProps {
  programName: string;
  trainerName: string;
  progress: number;
  completedWorkouts: number;
  totalWorkouts: number;
  isPremium: boolean;
  weekTitle: string;
  workoutId?: string;
}

export function ProgramProgressCard({
  programName,
  trainerName,
  progress,
  completedWorkouts,
  totalWorkouts,
  isPremium,
  weekTitle,
  workoutId = "current"
}: ProgramProgressCardProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Card className="shadow-md hover:shadow-lg transition-all border-primary/10 overflow-hidden group">
      <div className="bg-gradient-to-r from-primary/10 to-primary/5 px-4 py-3 border-b border-primary/10 group-hover:from-primary/15 group-hover:to-primary/10 transition-all">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4 text-primary" />
            Current Program
          </CardTitle>
          <Badge variant="outline" className="text-xs bg-background/80 border-primary/20 text-primary font-medium">
            {progress}% Complete
          </Badge>
        </div>
      </div>

      <CardContent className="p-5">
        <div className="space-y-5">
          <div>
            <h3 className="font-semibold text-xl text-foreground/90">{programName}</h3>
            <div className="flex items-center mt-1">
              <span className="text-sm text-muted-foreground">Coach: </span>
              <span className="text-sm font-medium ml-1">{trainerName}</span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">Progress</span>
              <span className="text-primary font-medium">{progress}%</span>
            </div>
            <Progress value={progress} className="h-2.5 bg-primary/10" />
          </div>

          <div className="flex justify-between items-center bg-muted/30 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                <Calendar className="h-4 w-4 text-primary" />
              </div>
              <div>
                <div className="text-sm font-medium">{completedWorkouts} of {totalWorkouts} workouts</div>
                <div className="text-xs text-muted-foreground">This week</div>
              </div>
            </div>
            <Badge variant={completedWorkouts === totalWorkouts ? "default" : "outline"} className="text-xs">
              {completedWorkouts === totalWorkouts ? "Completed" : "In Progress"}
            </Badge>
          </div>

          <Button asChild className="w-full bg-primary hover:bg-primary/90 text-white h-11 text-base">
            <Link href={`/dashboard/workouts/${workoutId}`}>
              <BarChart className="mr-2 h-5 w-5" />
              Continue My Program
            </Link>
          </Button>

          <Collapsible open={isOpen} onOpenChange={setIsOpen} className="w-full">
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="w-full border border-border/40 mt-2">
                {isOpen ? (
                  <>
                    <ChevronUp className="h-4 w-4 mr-2" />
                    Hide Previous Week
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4 mr-2" />
                    View Previous Week
                  </>
                )}
              </Button>
            </CollapsibleTrigger>

            <CollapsibleContent className="mt-4 space-y-4 relative">
              {!isPremium && (
                <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10 rounded-md">
                  <div className="text-center p-4">
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-2">
                      <Lock className="h-5 w-5 text-primary" />
                    </div>
                    <p className="text-sm font-medium mb-2">Unlock your full program history</p>
                    <Button size="sm" asChild>
                      <Link href="/dashboard/upgrade">Upgrade to Premium</Link>
                    </Button>
                  </div>
                </div>
              )}

              <div className="bg-muted/30 p-3 rounded-md">
                <h4 className="font-medium text-sm mb-2">Previous: {weekTitle}</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Workout A - Push Day</span>
                    <Badge variant="outline" className="text-xs">Completed</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Workout B - Pull Day</span>
                    <Badge variant="outline" className="text-xs">Completed</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Workout C - Leg Day</span>
                    <Badge variant="outline" className="text-xs">Missed</Badge>
                  </div>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      </CardContent>
    </Card>
  );
}
