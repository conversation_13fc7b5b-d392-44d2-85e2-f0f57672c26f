import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface ChatMessageProps {
  message: any
  isCurrentUser: boolean
  athleteInfo?: {
    name: string
    avatarUrl: string
  }
}

export function ChatMessage({ message, isCurrentUser, athleteInfo }: ChatMessageProps) {
  return (
    <div className={`flex gap-2 ${isCurrentUser ? "flex-row-reverse" : ""}`}>
      <Avatar className="h-8 w-8">
        <AvatarImage
          src={
            isCurrentUser
              ? message.users.avatar_url || "/placeholder.svg?height=32&width=32"
              : athleteInfo?.avatarUrl || "/placeholder.svg?height=32&width=32"
          }
          alt={isCurrentUser ? message.users.full_name : athleteInfo?.name || "Coach"}
        />
        <AvatarFallback>
          {isCurrentUser ? message.users.full_name.charAt(0) : athleteInfo?.name.charAt(0) || "C"}
        </AvatarFallback>
      </Avatar>
      <div
        className={`max-w-[70%] ${isCurrentUser ? "bg-primary text-primary-foreground" : "bg-muted"} p-3 rounded-lg`}
      >
        <p>{message.content}</p>
        <p className="text-xs opacity-70 mt-1">
          {new Date(message.created_at).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
        </p>
      </div>
    </div>
  )
}

