'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { UserCog } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function RoleSwitcher() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const switchRole = async (role: string) => {
    setIsLoading(true);
    try {
      // Redirect to the dev login endpoint with the selected role
      window.location.href = `/api/auth/dev-login?role=${role}&returnTo=${window.location.pathname}`;
    } catch (error) {
      console.error('Error switching role:', error);
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="bg-background">
            <UserCog className="h-4 w-4 mr-2" />
            Switch Role
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Development Roles</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => switchRole('admin')}>
            Admin
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => switchRole('trainer')}>
            Trainer
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => switchRole('client')}>
            Client
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => window.location.href = `/dashboard/coaching-chat`}>
            Go to Coaching Chat
          </DropdownMenuItem>
          <DropdownMenuItem onClick={async () => {
            try {
              const response = await fetch('/api/direct-setup');
              const result = await response.json();
              if (result.success) {
                window.location.href = '/api/auth/dev-login?role=client&userId=dev-user-id&returnTo=/dashboard/coaching-chat';
              } else {
                alert('Failed to set up test chat: ' + (result.error || 'Unknown error'));
              }
            } catch (error) {
              alert('Error setting up test chat: ' + error);
            }
          }}>
            Set Up Test Chat
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => window.location.href = `/api/auth/dev-login?clear=true&returnTo=${window.location.pathname}`}>
            Clear Role Override
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
