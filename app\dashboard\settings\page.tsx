import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { SettingsForm } from "@/components/settings/settings-form"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { Button } from "@/components/ui/button"

export default async function SettingsPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect("/login")
  }

  const user = await prisma.user.findUnique({
    where: {
      id: session.user.id,
    },
    select: {
      id: true,
      name: true,
      email: true,
      bio: true,
      role: true,
      avatarUrl: true,
      socialLinks: true,
    },
  })

  if (!user) {
    redirect("/login")
  }

  const settingsFormData = {
    id: user.id,
    fullName: user.name || '',
    email: user.email || '',
    bio: user.bio,
    role: user.role,
    avatarUrl: user.avatarUrl,
    socialLinks: typeof user.socialLinks === 'string' ? JSON.parse(user.socialLinks) : user.socialLinks
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Settings</h1>
        <SettingsForm user={settingsFormData} />
      </div>
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Notification Settings</CardTitle>
            <CardDescription>
              Manage your notification preferences.
            </CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4">
            <div className="grid grid-cols-[25px_1fr] items-start pb-4 last:mb-0 last:pb-0">
              <span className="flex h-2 w-2 translate-y-1 rounded-full bg-sky-500" />
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">
                  Push Notifications
                </p>
                <p className="text-sm text-muted-foreground">
                  Receive alerts for new messages, workout reminders, and check-ins.
                </p>
              </div>
            </div>
            <div className="grid grid-cols-[25px_1fr] items-start pb-4 last:mb-0 last:pb-0">
              <span className="flex h-2 w-2 translate-y-1 rounded-full bg-sky-500" />
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">
                  Email Updates
                </p>
                <p className="text-sm text-muted-foreground">
                  Get weekly progress summaries and important account updates.
                </p>
              </div>
            </div>
            <div className="grid grid-cols-[25px_1fr] items-start pb-4 last:mb-0 last:pb-0">
              <span className="flex h-2 w-2 translate-y-1 rounded-full bg-sky-500" />
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">
                  Promotional Offers
                </p>
                <p className="text-sm text-muted-foreground">
                  Receive news about new programs, products, and special offers.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Privacy Settings</CardTitle>
            <CardDescription>
              Manage how your profile information is displayed.
            </CardDescription>
          </CardHeader>
          <CardContent className="grid gap-6">
            <div className="flex items-center justify-between space-x-4">
              <Label htmlFor="public-profile" className="flex flex-col space-y-1">
                <span>Public Profile</span>
                <span className="font-normal leading-snug text-muted-foreground">
                  Allow other users to view your basic profile information.
                </span>
              </Label>
              <Switch id="public-profile" aria-label="Public profile" />
            </div>
            <div className="flex items-center justify-between space-x-4">
              <Label htmlFor="activity-sharing" className="flex flex-col space-y-1">
                <span>Activity Sharing</span>
                <span className="font-normal leading-snug text-muted-foreground">
                  Share your workout completions and achievements with your coach/friends.
                </span>
              </Label>
              <Switch id="activity-sharing" aria-label="Activity sharing" />
            </div>
            <div className="flex items-center justify-between space-x-4">
              <Label htmlFor="progress-visibility" className="flex flex-col space-y-1">
                <span>Progress Visibility</span>
                <span className="font-normal leading-snug text-muted-foreground">
                  Control who can see your detailed progress logs and charts.
                </span>
              </Label>
              <Select defaultValue="coach">
                <SelectTrigger
                  id="progress-visibility"
                  className="w-[180px]"
                  aria-label="Select progress visibility"
                >
                  <SelectValue placeholder="Select visibility" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="private">Private (Only You)</SelectItem>
                  <SelectItem value="coach">Coach Only</SelectItem>
                  <SelectItem value="friends">Friends Only</SelectItem> { /* If applicable */}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Account Management</CardTitle>
            <CardDescription>
              Manage your account settings and data.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
             <Button variant="outline">Change Password</Button>
             <Button variant="outline">Download Your Data</Button>
             <Button variant="destructive">Delete Account</Button>
             <p className="text-xs text-muted-foreground pt-2">
                Note: Deleting your account is permanent and cannot be undone. All your data, including training history and purchases, will be removed.
             </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 