'use client';

import { useState, useRef } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Send, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

// Mock data
const MOCK_TRAINER = {
  id: 'trainer-1',
  name: '<PERSON>',
  avatarUrl: null,
};

const MOCK_CLIENT = {
  id: 'client-1',
  name: '<PERSON> Client',
  avatarUrl: null,
};

const INITIAL_MESSAGES = [
  {
    id: 'msg-1',
    content: 'Hello! Welcome to your coaching chat. How can I help you today?',
    senderId: MOCK_TRAINER.id,
    createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
  },
];

export default function MockChatPage() {
  const router = useRouter();
  const [messages, setMessages] = useState(INITIAL_MESSAGES);
  const [newMessage, setNewMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newMessage.trim()) return;
    
    // Add client message
    const clientMessage = {
      id: `msg-${Date.now()}-client`,
      content: newMessage,
      senderId: MOCK_CLIENT.id,
      createdAt: new Date().toISOString(),
    };
    
    setMessages((prev) => [...prev, clientMessage]);
    setNewMessage('');
    
    // Simulate trainer response after a delay
    setTimeout(() => {
      const trainerResponse = {
        id: `msg-${Date.now()}-trainer`,
        content: getTrainerResponse(newMessage),
        senderId: MOCK_TRAINER.id,
        createdAt: new Date().toISOString(),
      };
      
      setMessages((prev) => [...prev, trainerResponse]);
      scrollToBottom();
    }, 1000);
  };

  const getTrainerResponse = (message: string): string => {
    // Simple response logic
    if (message.toLowerCase().includes('hello') || message.toLowerCase().includes('hi')) {
      return 'Hi there! How are you doing today?';
    }
    
    if (message.toLowerCase().includes('workout') || message.toLowerCase().includes('exercise')) {
      return 'Great question about your workout! I recommend focusing on proper form and gradually increasing intensity. How has your progress been so far?';
    }
    
    if (message.toLowerCase().includes('diet') || message.toLowerCase().includes('nutrition') || message.toLowerCase().includes('food')) {
      return 'Nutrition is key to your success! Remember to focus on whole foods, adequate protein, and staying hydrated. Have you been tracking your meals?';
    }
    
    if (message.toLowerCase().includes('progress') || message.toLowerCase().includes('results')) {
      return 'Your progress has been impressive! Remember that consistency is key, and results take time. Keep up the great work!';
    }
    
    // Default response
    return "That's a great point! Let's discuss this further in our next session. Is there anything specific you'd like to focus on?";
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="container py-6 space-y-6">
      <Button variant="outline" onClick={() => router.back()}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back
      </Button>
      
      <h1 className="text-2xl font-bold">Mock Coaching Chat</h1>
      <p className="text-muted-foreground">
        This is a mock chat interface for testing. It simulates a conversation between a trainer and a client.
      </p>
      
      <Card className="flex flex-col h-[600px] border-primary/20">
        <CardHeader className="border-b pb-3">
          <div className="flex items-center gap-3">
            <Avatar>
              <AvatarImage src={MOCK_TRAINER.avatarUrl || undefined} alt={MOCK_TRAINER.name} />
              <AvatarFallback>{getInitials(MOCK_TRAINER.name)}</AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">{MOCK_TRAINER.name}</CardTitle>
              <p className="text-sm text-muted-foreground">1:1 Coaching Chat</p>
            </div>
          </div>
        </CardHeader>
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {messages.map((message) => {
              const isTrainer = message.senderId === MOCK_TRAINER.id;
              const messageUser = isTrainer ? MOCK_TRAINER : MOCK_CLIENT;
              
              return (
                <div key={message.id} className={`flex ${isTrainer ? 'justify-start' : 'justify-end'}`}>
                  <div className="flex items-start gap-2 max-w-[80%]">
                    {isTrainer && (
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={messageUser.avatarUrl || undefined} alt={messageUser.name} />
                        <AvatarFallback>{getInitials(messageUser.name)}</AvatarFallback>
                      </Avatar>
                    )}
                    <div>
                      <div className={`p-3 rounded-lg ${isTrainer ? 'bg-muted' : 'bg-primary text-primary-foreground'}`}>
                        <p className="text-sm">{message.content}</p>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">{formatTime(message.createdAt)}</p>
                    </div>
                    {!isTrainer && (
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={messageUser.avatarUrl || undefined} alt={messageUser.name} />
                        <AvatarFallback>{getInitials(messageUser.name)}</AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                </div>
              );
            })}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
        <CardFooter className="border-t p-3">
          <form onSubmit={handleSendMessage} className="flex w-full gap-2">
            <Input
              ref={inputRef}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type your message..."
              className="flex-1"
            />
            <Button type="submit" disabled={!newMessage.trim()}>
              <Send className="h-4 w-4" />
            </Button>
          </form>
        </CardFooter>
      </Card>
      
      <div className="bg-muted p-4 rounded-lg">
        <h2 className="font-medium mb-2">Test Prompts</h2>
        <div className="grid grid-cols-2 gap-2">
          <Button 
            variant="outline" 
            onClick={() => {
              setNewMessage("Hi, how are you today?");
              inputRef.current?.focus();
            }}
          >
            Greeting
          </Button>
          <Button 
            variant="outline" 
            onClick={() => {
              setNewMessage("I need help with my workout routine");
              inputRef.current?.focus();
            }}
          >
            Workout Help
          </Button>
          <Button 
            variant="outline" 
            onClick={() => {
              setNewMessage("What should I eat before my workout?");
              inputRef.current?.focus();
            }}
          >
            Nutrition Question
          </Button>
          <Button 
            variant="outline" 
            onClick={() => {
              setNewMessage("I'm not seeing much progress yet");
              inputRef.current?.focus();
            }}
          >
            Progress Concern
          </Button>
        </div>
      </div>
    </div>
  );
}
