import { PrismaClient } from "@prisma/client"
import bcrypt from "bcryptjs"
import { UserRole } from "../lib/auth"

const prisma = new PrismaClient()

async function resetUsers() {
  try {
    // Delete all users
    await prisma.user.deleteMany()
    console.log("All users deleted successfully")

    // Create admin user
    const adminPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD || "admin123", 10)
    const admin = await prisma.user.create({
      data: {
        email: process.env.ADMIN_EMAIL || "<EMAIL>",
        password: adminPassword,
        fullName: process.env.ADMIN_NAME || "Admin User",
        role: "admin" as UserRole,
      },
    })
    console.log("Admin user created:", admin.email)

    // Create test user
    const testPassword = await bcrypt.hash(process.env.TEST_USER_PASSWORD || "test123", 10)
    const testUser = await prisma.user.create({
      data: {
        email: process.env.TEST_USER_EMAIL || "<EMAIL>",
        password: testPassword,
        fullName: process.env.TEST_USER_NAME || "Test User",
        role: "client" as UserRole,
      },
    })
    console.log("Test user created:", testUser.email)

    console.log("User reset completed successfully")
  } catch (error) {
    console.error("Error resetting users:", error)
  } finally {
    await prisma.$disconnect()
  }
}

resetUsers() 