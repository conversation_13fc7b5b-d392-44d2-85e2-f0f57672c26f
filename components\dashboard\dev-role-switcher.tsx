"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { useRout<PERSON> } from "next/navigation"
import { useState, useEffect } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { cn } from "@/lib/utils"

export function DevRoleSwitcher() {
  const router = useRouter()
  const [currentRole, setCurrentRole] = useState<string>("")
  const [isPremium, setIsPremium] = useState<boolean>(false)
  const [isClient, setIsClient] = useState(false)
  const [isChanging, setIsChanging] = useState(false)
  const [testClientId, setTestClientId] = useState<string>("") // Store the test client ID

  // Fetch the test client ID
  const fetchTestClient = async () => {
    try {
      const response = await fetch('/api/dev/test-clients')

      if (response.ok) {
        const clients = await response.json()
        // Find the test client (assuming it has "test" in the name or email)
        const testClient = clients.find(client =>
          (client.user?.name?.toLowerCase().includes('test') ||
           client.user?.email?.toLowerCase().includes('test'))
        )

        if (testClient?.user?.id) {
          console.log('[DevRoleSwitcher] Found test client:', testClient.user.id)
          setTestClientId(testClient.user.id)
        }
      }
    } catch (error) {
      console.error('[DevRoleSwitcher] Error fetching test client:', error)
    }
  }

  // Only run on client side
  useEffect(() => {
    setIsClient(true)

    // Get the role from the cookie
    const cookies = document.cookie.split(';')
    const roleCookie = cookies.find(cookie => cookie.trim().startsWith('dev_override_role='))
    if (roleCookie) {
      const role = roleCookie.split('=')[1]
      setCurrentRole(role)
      console.log('[DevRoleSwitcher] Found role cookie:', role)
    } else {
      console.log('[DevRoleSwitcher] No role cookie found')
    }

    // Get premium status from cookie
    const premiumCookie = cookies.find(cookie => cookie.trim().startsWith('dev_premium_status='))
    if (premiumCookie) {
      const isPremiumValue = premiumCookie.split('=')[1] === 'true'
      setIsPremium(isPremiumValue)
      console.log('[DevRoleSwitcher] Found premium cookie:', isPremiumValue)
    } else {
      console.log('[DevRoleSwitcher] No premium cookie found')
    }

    // Log all cookies for debugging
    console.log('[DevRoleSwitcher] All cookies:', document.cookie)

    // Fetch the test client ID
    fetchTestClient()
  }, [])

  // Only show in development environment
  if (process.env.NODE_ENV !== 'development' || !isClient) {
    return null
  }

  const setRoleCookie = async (role: string) => {
    // Handle special test client case
    if (role === 'test-client') {
      switchToTestClient()
      return
    }

    // Don't do anything if we're already changing or if the role is the same
    if (isChanging || role === currentRole) {
      console.log(`[DevRoleSwitcher] Ignoring role change: isChanging=${isChanging}, role=${role}, currentRole=${currentRole}`)
      return
    }

    console.log(`[DevRoleSwitcher] Starting role change to ${role}`)
    setIsChanging(true)

    try {
      console.log(`[DevRoleSwitcher] Setting role to ${role}, current premium status: ${isPremium}`)

      // Set cookie for 12 hours
      document.cookie = `dev_override_role=${role};path=/;max-age=${60 * 60 * 12}`
      setCurrentRole(role)

      // All clients are now premium by default - no special handling needed

      // Log cookies after setting
      console.log('[DevRoleSwitcher] Cookies after setting:', document.cookie)

      // Direct navigation to the dev-login endpoint with explicit flag to apply role override
      console.log(`[DevRoleSwitcher] Redirecting to dev-login with role=${role}`)
      // Use different returnTo URLs based on role
      const returnTo = role === 'trainer' ? '/dashboard/trainer-profile' : '/dashboard'
      window.location.href = `/api/auth/dev-login?role=${role}&premium=true&returnTo=${returnTo}&apply_role_override=true`
    } catch (error) {
      console.error('Error setting role:', error)
      // Show an alert to the user
      alert(`Failed to switch role: ${error.message || 'Unknown error'}. Please try again.`)
      setIsChanging(false)
    }
  }

  const togglePremiumStatus = async () => {
    if (isChanging) return

    setIsChanging(true)
    try {
      const newStatus = !isPremium
      console.log(`[DevRoleSwitcher] Toggling premium status from ${isPremium} to ${newStatus}`);

      // Set premium status cookie with a long expiration
      document.cookie = `dev_premium_status=${newStatus};path=/;max-age=${60 * 60 * 24 * 30}`
      setIsPremium(newStatus)

      // Determine the role to use
      let roleToUse = currentRole

      // All clients are premium now - no role switching needed

      // Direct navigation to the dev-login endpoint
      if (roleToUse) {
        console.log(`[DevRoleSwitcher] Redirecting to dev-login with role=${roleToUse} and premium=${newStatus}`);
        // Use different returnTo URLs based on role
        const returnTo = roleToUse === 'trainer' ? '/dashboard/trainer-profile' : '/dashboard'
        window.location.href = `/api/auth/dev-login?role=${roleToUse}&premium=${newStatus}&returnTo=${returnTo}&apply_role_override=true`
      } else {
        console.error('No role available for redirection');
        throw new Error('No role available for redirection');
      }
    } catch (error) {
      console.error('Error toggling premium status:', error)
      // Show an alert to the user
      alert(`Failed to toggle premium status: ${error.message || 'Unknown error'}. Please try again.`)
      setIsChanging(false)
    }
  }

  const clearRoleCookie = async () => {
    if (isChanging) return

    setIsChanging(true)
    try {
      // Clear the cookies
      document.cookie = "dev_override_role=;path=/;max-age=0"
      document.cookie = "dev_premium_status=;path=/;max-age=0"
      setCurrentRole("")
      setIsPremium(false)

      // Direct navigation to clear the session
      console.log('[DevRoleSwitcher] Redirecting to clear role override');
      window.location.href = '/api/auth/dev-login?clear=true&returnTo=/dashboard&apply_role_override=true'
    } catch (error) {
      console.error('Error clearing role:', error)
      // Show an alert to the user
      alert(`Failed to clear role: ${error.message || 'Unknown error'}. Please try again.`)
      setIsChanging(false)
    }
  }

  // Switch to the test client
  const switchToTestClient = async () => {
    if (isChanging || !testClientId) return

    setIsChanging(true)
    try {
      console.log(`[DevRoleSwitcher] Switching to test client: ${testClientId}`)

      // Set cookie for client role since all clients are premium now
      document.cookie = `dev_override_role=client;path=/;max-age=${60 * 60 * 12}`
      setCurrentRole('client')

      // Set premium status to true for test client with assigned training plan
      document.cookie = `dev_premium_status=true;path=/;max-age=${60 * 60 * 12}`
      setIsPremium(true)

      // Navigate to the dev-login endpoint with the test client's user ID
      window.location.href = `/api/auth/dev-login?role=client&userId=${testClientId}&premium=true&returnTo=/dashboard&apply_role_override=true`
    } catch (error) {
      console.error('Error switching to test client:', error)
      alert(`Failed to switch to test client: ${error.message || 'Unknown error'}. Please try again.`)
      setIsChanging(false)
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "admin":
        return <Shield className="h-4 w-4 mr-2" />
      case "trainer":
        return <Dumbbell className="h-4 w-4 mr-2" />
      case "client":
        return <User className="h-4 w-4 mr-2" />
      case "premiumClient":
        return <Sparkles className="h-4 w-4 mr-2" />
      default:
        return null
    }
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-background border p-3 rounded-lg shadow-lg">
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Dev Role Override</span>
          {currentRole && (
            <Badge className={cn(
              "ml-2 capitalize",
              isPremium && "bg-primary/10 text-primary border-primary/20"
            )}>
              {getRoleIcon(currentRole)}
              {currentRole}
              {isPremium && (
                <Sparkles className="ml-1 h-3 w-3 text-primary" />
              )}
            </Badge>
          )}
        </div>

        {isChanging ? (
          <div className="flex items-center justify-center p-2">
            <RotateCw className="h-4 w-4 mr-2 animate-spin" />
            <span className="text-sm">Applying changes...</span>
          </div>
        ) : (
          <div className="flex flex-col gap-2">
            <Select value={currentRole || ""} onValueChange={setRoleCookie}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Switch role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="trainer">Trainer</SelectItem>
                <SelectItem value="client">Client</SelectItem>
                {testClientId && (
                  <SelectItem value="test-client" className="bg-blue-50 text-blue-700 font-medium">
                    <div className="flex items-center">
                      <Sparkles className="h-3 w-3 mr-1" />
                      Premium Test Client
                    </div>
                  </SelectItem>
                )}
              </SelectContent>
            </Select>

            <div className="flex items-center justify-between">
              <span className="text-xs flex items-center">
                Premium Status
                {isPremium && <Sparkles className="ml-1 h-3 w-3 text-primary" />}
              </span>
              <Switch
                checked={isPremium}
                onCheckedChange={togglePremiumStatus}
                className={cn("ml-auto", isPremium && "bg-primary")}
              />
            </div>

            <div className={cn(
              "text-xs px-2 py-1 mt-1 rounded border",
              isPremium
                ? "bg-primary/10 text-primary border-primary/20"
                : "bg-muted-foreground/10 text-muted-foreground border-muted-foreground/20"
            )}>
              Current: {currentRole || "Default"} {isPremium && "(Premium)"}
            </div>

            {testClientId && (
              <Button
                onClick={switchToTestClient}
                className="mt-2 bg-blue-600 hover:bg-blue-700 text-white"
                size="sm"
                disabled={isChanging}
              >
                <Sparkles className="h-3 w-3 mr-1" />
                Switch to Premium Test Client
              </Button>
            )}

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" size="sm" disabled={!currentRole && !isPremium}>
                  Reset All
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Reset role and premium status?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will clear all role and premium overrides and revert to your actual account settings.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={clearRoleCookie}>Reset</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )}
      </div>
    </div>
  )
}