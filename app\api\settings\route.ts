import { Prisma } from "@prisma/client"
import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { z } from "zod"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

const urlRegex = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/

const socialLinksSchema = z.object({
  twitter: z.string()
    .regex(/^@?(\w){1,15}$/, "Invalid Twitter username")
    .or(z.string().regex(urlRegex, "Invalid URL"))
    .or(z.string().max(0))
    .optional(),
  instagram: z.string()
    .regex(/^@?([A-Za-z0-9._]){1,30}$/, "Invalid Instagram username")
    .or(z.string().regex(urlRegex, "Invalid URL"))
    .or(z.string().max(0))
    .optional(),
  youtube: z.string()
    .regex(urlRegex, "Invalid YouTube URL")
    .or(z.string().max(0))
    .optional(),
  linkedin: z.string()
    .regex(urlRegex, "Invalid LinkedIn URL")
    .or(z.string().max(0))
    .optional(),
  website: z.string()
    .regex(urlRegex, "Invalid website URL")
    .or(z.string().max(0))
    .optional(),
})

const settingsSchema = z.object({
  fullName: z.string().min(1, "Full name is required"),
  bio: z.string().optional(),
  avatarUrl: z.string().optional(),
  socialLinks: socialLinksSchema.optional(),
})

export async function PATCH(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const json = await request.json()
    const body = settingsSchema.parse(json)

    // Format social links
    const formattedSocialLinks = body.socialLinks ? {
      ...body.socialLinks,
      ...Object.fromEntries(
        Object.entries(body.socialLinks)
          .filter(([_, value]) => value && typeof value === "string")
          .map(([key, value]) => [
            key,
            !value.startsWith("@") && !value.startsWith("http")
              ? `https://${value}`
              : value
          ])
      )
    } : null

    const user = await prisma.user.update({
      where: {
        id: session.user.id,
      },
      data: {
        fullName: body.fullName,
        bio: body.bio || null,
        avatarUrl: body.avatarUrl || null,
        socialLinks: formattedSocialLinks as Prisma.InputJsonValue,
      },
    })

    return NextResponse.json(user)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return new NextResponse(JSON.stringify(error.errors), { status: 400 })
    }

    return new NextResponse("Internal error", { status: 500 })
  }
} 