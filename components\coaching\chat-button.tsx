'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button, ButtonProps } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { MessageSquare, Loader2 } from 'lucide-react';

interface ChatButtonProps extends ButtonProps {
  trainerId: string;
  clientId: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

export function ChatButton({
  trainerId,
  clientId,
  variant = 'default',
  size = 'default',
  className,
  ...props
}: ChatButtonProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    try {
      setIsLoading(true);
      
      // Find or create a conversation between the trainer and client
      const response = await fetch('/api/coaching/conversations');
      
      if (!response.ok) {
        throw new Error('Failed to fetch conversations');
      }
      
      const conversations = await response.json();
      
      // Find the conversation with this trainer/client
      const conversation = conversations.find(
        (c: any) => 
          (c.user.id === trainerId || c.user.id === clientId)
      );
      
      if (conversation) {
        // Navigate to the chat page with the conversation ID
        router.push(`/dashboard/coaching-chat?id=${conversation.id}`);
      } else {
        // If no conversation exists, just navigate to the chat page
        // The page will create a conversation if needed
        router.push('/dashboard/coaching-chat');
      }
    } catch (error) {
      console.error('Error navigating to chat:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to open chat. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={isLoading}
      className={className}
      {...props}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        <MessageSquare className="h-4 w-4 mr-2" />
      )}
      Message
    </Button>
  );
}
