"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Shield,
  Calendar,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Edit,
  ExternalLink,
  MessageSquare,
  Plus
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { EditTrainerProfileDialog } from "@/components/profile/edit-trainer-profile-dialog"
import { ConnectionsTab } from "@/components/profile/connections-tab"
import {
  ResponsiveContainer,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarC<PERSON>,
  Bar,
  Pie<PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from "recharts"

interface TrainerData {
  id: string
  name: string
  email: string
  bio: string | null
  avatarUrl: string | null
  role: string
  createdAt: string
  updatedAt: string
  stats: {
    trainingPlans: number
    dietPlans: number
    clients: number
    revenue: {
      current: number
      previous: number
      percentChange: number
    }
  }
  plans: {
    id: string
    title: string
    description: string
    difficulty: string
    type: string
  }[]
  clients: {
    id: string
    name: string
    email: string
    avatarUrl: string | null
    startDate: string
    monthlyFee: number
  }[]
}

interface MockData {
  socialLinks: {
    instagram: string
    twitter: string
    youtube: string
    website: string
  }
  stats: {
    activeClients: number
    totalClients: number
    clientsLastMonth: number
    revenue: {
      current: number
      previous: number
      percentChange: number
    }
    messages: number
    sessions: number
  }
  revenueData: {
    month: string
    revenue: number
  }[]
  clientsData: {
    month: string
    clients: number
  }[]
  revenueBreakdown: {
    name: string
    value: number
  }[]
}

interface TrainerProfileClientProps {
  trainer: TrainerData
  mockData: MockData
}

export function TrainerProfileClient({ trainer, mockData }: TrainerProfileClientProps) {
  const router = useRouter()
  const [editDialogOpen, setEditDialogOpen] = useState(false)

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042']

  const publicProfileUrl = `/${trainer.name.toLowerCase().replace(/\\s+/g, "-")}`

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Trainer Profile</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => window.open(publicProfileUrl, '_blank')}>
            <ExternalLink className="mr-2 h-4 w-4" />
            View Public Profile
          </Button>
          <Button onClick={() => setEditDialogOpen(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Profile
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="plans">Training Plans</TabsTrigger>
          <TabsTrigger value="clients">Clients</TabsTrigger>
          <TabsTrigger value="connections">Connections</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Profile Card */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-6 items-center md:items-start">
                <Avatar className="h-24 w-24 border-2 border-primary">
                  <AvatarImage
                    src={trainer.avatarUrl || ""}
                    alt={trainer.name || "Trainer"}
                  />
                  <AvatarFallback className="text-2xl">
                    {trainer.name?.[0]?.toUpperCase() || "T"}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 text-center md:text-left">
                  <h2 className="text-2xl font-bold">{trainer.name}</h2>
                  <p className="text-muted-foreground">{trainer.email}</p>

                  <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                    <Badge variant="outline">Trainer</Badge>
                    {trainer.stats.clients > 20 && (
                      <Badge variant="secondary">Top Trainer</Badge>
                    )}
                  </div>

                  <p className="text-sm mt-2">{trainer.bio || "No bio available. Click 'Edit Profile' to add your bio."}</p>

                  {mockData.socialLinks && (
                    <div className="flex gap-3 justify-center md:justify-start mt-2">
                      {mockData.socialLinks.instagram && (
                        <a href={`https://instagram.com/${mockData.socialLinks.instagram}`} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary">
                          Instagram
                        </a>
                      )}
                      {mockData.socialLinks.twitter && (
                        <a href={`https://twitter.com/${mockData.socialLinks.twitter}`} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary">
                          Twitter
                        </a>
                      )}
                      {mockData.socialLinks.website && (
                        <a href={`https://${mockData.socialLinks.website}`} target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary">
                          Website
                        </a>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="shadow-sm hover:shadow-md transition-all">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-primary/10 rounded-xl">
                    <Users className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Active Clients</p>
                    <h3 className="text-2xl font-bold">{trainer.stats.clients}</h3>
                    <p className="text-xs text-muted-foreground">Premium coaching</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm hover:shadow-md transition-all">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-primary/10 rounded-xl">
                    <DollarSign className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Monthly Revenue</p>
                    <div className="flex items-center gap-2">
                      <h3 className="text-2xl font-bold">${trainer.stats.revenue.current.toFixed(2)}</h3>
                      {trainer.stats.revenue.current === 0 && trainer.stats.revenue.previous === 0 ? (
                        <span className="text-xs text-muted-foreground">No revenue yet</span>
                      ) : (
                        trainer.stats.revenue.percentChange > 0 ? (
                          <span className="text-xs text-green-500 flex items-center">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            {trainer.stats.revenue.percentChange.toFixed(2)}%
                          </span>
                        ) : (
                          <span className="text-xs text-red-500 flex items-center">
                            <TrendingDown className="h-3 w-3 mr-1" />
                            {Math.abs(trainer.stats.revenue.percentChange).toFixed(2)}%
                          </span>
                        )
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">vs. last month</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm hover:shadow-md transition-all">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-primary/10 rounded-xl">
                    <Dumbbell className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Training Plans</p>
                    <h3 className="text-2xl font-bold">{trainer.stats.trainingPlans}</h3>
                    <p className="text-xs text-muted-foreground">Active templates</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm hover:shadow-md transition-all">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-primary/10 rounded-xl">
                    <Calendar className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Diet Plans</p>
                    <h3 className="text-2xl font-bold">{trainer.stats.dietPlans}</h3>
                    <p className="text-xs text-muted-foreground">Active plans</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
                <CardDescription>Monthly revenue over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={mockData.revenueData}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <defs>
                        <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                          <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                        </linearGradient>
                      </defs>
                      <XAxis dataKey="month" />
                      <YAxis domain={[0, 'auto']} />
                      <CartesianGrid strokeDasharray="3 3" />
                      <Tooltip formatter={(value) => [`$${value}`, 'Revenue']} />
                      <Area
                        type="monotone"
                        dataKey="revenue"
                        stroke="#8884d8"
                        fillOpacity={1}
                        fill="url(#colorRevenue)"
                        connectNulls={false}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue Breakdown</CardTitle>
                <CardDescription>Revenue by client</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={trainer.clients.map(client => ({
                          name: client.name,
                          value: client.monthlyFee
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {trainer.clients.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`$${value}`, 'Monthly Fee']} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Clients */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Recent Clients</CardTitle>
                <Button variant="outline" size="sm" onClick={() => router.push('/dashboard/clients')}>
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {trainer.clients.length > 0 ? (
                  trainer.clients.map((client) => (
                    <div key={client.id} className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={client.avatarUrl || ""} alt={client.name} />
                          <AvatarFallback>{client.name?.[0]?.toUpperCase() || "C"}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{client.name}</p>
                          <p className="text-xs text-muted-foreground">
                            Joined {new Date(client.startDate).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm" onClick={() => router.push(`/dashboard/clients/${client.id}`)}>
                          View Profile
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => router.push(`/dashboard/messages?client=${client.id}`)}>
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground">No clients yet</p>
                    <Button className="mt-2" variant="outline" size="sm" onClick={() => router.push('/dashboard/clients/new')}>
                      Add Your First Client
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recent Training Plans */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Recent Training Plans</CardTitle>
                <Button variant="outline" size="sm" onClick={() => router.push('/dashboard/training-plans')}>
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {trainer.plans.length > 0 ? (
                  trainer.plans.map((plan) => (
                    <div key={plan.id} className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <Dumbbell className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <p className="font-medium">{plan.title}</p>
                          <p className="text-xs text-muted-foreground">
                            {plan.difficulty} • {plan.type}
                          </p>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm" onClick={() => router.push(`/dashboard/training-plans/preview/${plan.id}`)}>
                        View
                      </Button>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground">No training plans yet</p>
                    <Button className="mt-2" variant="outline" size="sm" onClick={() => router.push('/dashboard/training-plans/new')}>
                      Create Your First Plan
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {/* Client Growth Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Client Growth & Revenue</CardTitle>
              <CardDescription>Monthly client acquisition and revenue trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={mockData.revenueData}
                    margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="revenue"
                      stroke="#8884d8"
                      fill="#8884d8"
                      name="Revenue ($)"
                    />
                    <Area
                      yAxisId="right"
                      type="monotone"
                      dataKey="clients"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      name="Active Clients"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Client Retention Card */}
            <Card>
              <CardHeader>
                <CardTitle>Client Retention</CardTitle>
                <CardDescription>Client retention metrics and trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'Active Clients', value: trainer.stats.clients },
                          { name: 'Churned', value: Math.max(0, mockData.stats.totalClients - trainer.stats.clients) }
                        ]}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={80}
                        fill="#8884d8"
                        paddingAngle={5}
                        dataKey="value"
                      >
                        <Cell fill="#4ade80" />
                        <Cell fill="#ef4444" />
                      </Pie>
                      <Tooltip formatter={(value, name) => [`${value} clients`, name]} />
                    </PieChart>
                  </ResponsiveContainer>
                  <div className="text-center mt-4">
                    <div className="text-2xl font-bold text-primary">
                      {Math.min(100, Math.round((trainer.stats.clients / Math.max(1, mockData.stats.totalClients)) * 100))}%
                    </div>
                    <p className="text-sm text-muted-foreground">Overall Retention Rate</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {trainer.stats.clients} active out of {mockData.stats.totalClients} total clients
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Revenue Breakdown Card */}
            <Card>
              <CardHeader>
                <CardTitle>Revenue Analysis</CardTitle>
                <CardDescription>Monthly revenue breakdown and trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={mockData.revenueData.slice(-6)}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${value}`, 'Revenue']} />
                      <Bar dataKey="revenue" fill="#8884d8">
                        {mockData.revenueData.slice(-6).map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={entry.revenue > mockData.revenueData.slice(-6)[index - 1]?.revenue
                              ? '#4ade80'
                              : '#ef4444'}
                          />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Key Metrics Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Key Performance Metrics</CardTitle>
              <CardDescription>Overview of important business metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-primary/5 rounded-lg">
                  <h4 className="text-sm font-medium text-muted-foreground">Average Revenue per Client</h4>
                  <div className="mt-2 flex items-center">
                    <DollarSign className="h-4 w-4 text-primary mr-1" />
                    <span className="text-2xl font-bold">
                      {trainer.clients.length > 0
                        ? (trainer.clients.reduce((acc, client) => acc + client.monthlyFee, 0) / trainer.clients.length).toFixed(2)
                        : '0.00'}
                    </span>
                  </div>
                </div>

                <div className="p-4 bg-primary/5 rounded-lg">
                  <h4 className="text-sm font-medium text-muted-foreground">Client Growth Rate</h4>
                  <div className="mt-2 flex items-center">
                    <TrendingUp className="h-4 w-4 text-primary mr-1" />
                    <span className="text-2xl font-bold">
                      {((trainer.stats.clients / (trainer.stats.trainingPlans || 1)) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>

                <div className="p-4 bg-primary/5 rounded-lg">
                  <h4 className="text-sm font-medium text-muted-foreground">Active Training Plans</h4>
                  <div className="mt-2 flex items-center">
                    <Dumbbell className="h-4 w-4 text-primary mr-1" />
                    <span className="text-2xl font-bold">{trainer.stats.trainingPlans}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Client Retention Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Client Retention Analysis</CardTitle>
              <CardDescription>Detailed metrics about client retention and churn</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Active vs Inactive Clients */}
                <div className="flex flex-col justify-between h-full">
                  <div className="h-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={[
                            { name: 'Active Clients', value: mockData.stats.activeClients },
                            { name: 'Inactive Clients', value: mockData.stats.totalClients - mockData.stats.activeClients }
                          ]}
                          cx="50%"
                          cy="50%"
                          innerRadius={50}
                          outerRadius={80}
                          fill="#8884d8"
                          paddingAngle={5}
                          dataKey="value"
                        >
                          <Cell fill="#4ade80" />
                          <Cell fill="#ef4444" />
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="mt-6 grid grid-cols-2 gap-4 text-center">
                    <div className="p-3 bg-primary/5 rounded-lg">
                      <div className="text-2xl font-bold text-green-500">{mockData.stats.activeClients}</div>
                      <div className="text-sm text-muted-foreground">Active Clients</div>
                    </div>
                    <div className="p-3 bg-primary/5 rounded-lg">
                      <div className="text-2xl font-bold text-red-500">
                        {mockData.stats.totalClients - mockData.stats.activeClients}
                      </div>
                      <div className="text-sm text-muted-foreground">Inactive Clients</div>
                    </div>
                  </div>
                </div>

                {/* Retention Metrics */}
                <div className="space-y-8">
                  <div>
                    <h4 className="text-sm font-medium mb-3">Monthly Retention Rate</h4>
                    <div className="bg-secondary h-4 rounded-full overflow-hidden">
                      <div 
                        className="bg-primary h-full transition-all"
                        style={{ 
                          width: `${(mockData.stats.activeClients / mockData.stats.totalClients) * 100}%` 
                        }}
                      />
                    </div>
                    <div className="mt-2 flex justify-between text-sm">
                      <span className="text-muted-foreground">
                        {((mockData.stats.activeClients / mockData.stats.totalClients) * 100).toFixed(1)}% retained
                      </span>
                      <span className="text-muted-foreground">
                        {((mockData.stats.totalClients - mockData.stats.activeClients) / mockData.stats.totalClients * 100).toFixed(1)}% churned
                      </span>
                    </div>
                  </div>

                  <div className="space-y-3 bg-primary/5 p-4 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">New Clients (This Month)</span>
                      <span className="font-medium">{mockData.stats.clientsLastMonth}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Average Client Lifetime</span>
                      <span className="font-medium">4.2 months</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Client Satisfaction</span>
                      <span className="font-medium">92%</span>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <h4 className="text-sm font-medium mb-2">Retention Trend</h4>
                    <div className="flex items-center gap-2 bg-primary/5 p-3 rounded-lg">
                      {mockData.stats.activeClients > mockData.stats.clientsLastMonth ? (
                        <>
                          <TrendingUp className="h-4 w-4 text-green-500" />
                          <span className="text-sm text-green-500">
                            +{((mockData.stats.activeClients - mockData.stats.clientsLastMonth) / mockData.stats.clientsLastMonth * 100).toFixed(2)}% vs last month
                          </span>
                        </>
                      ) : (
                        <>
                          <TrendingDown className="h-4 w-4 text-red-500" />
                          <span className="text-sm text-red-500">
                            {((mockData.stats.activeClients - mockData.stats.clientsLastMonth) / mockData.stats.clientsLastMonth * 100).toFixed(2)}% vs last month
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="plans" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>All Training Plans</CardTitle>
                <Button onClick={() => router.push('/dashboard/training-plans/new')}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create New Plan
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {trainer.plans.length > 0 ? (
                  trainer.plans.map((plan) => (
                    <div key={plan.id} className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <Dumbbell className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <p className="font-medium">{plan.title}</p>
                          <p className="text-xs text-muted-foreground">
                            {plan.difficulty} • {plan.type}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={() => router.push(`/dashboard/training-plans/${plan.id}/edit`)}>
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => router.push(`/dashboard/training-plans/preview/${plan.id}`)}>
                          View
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">You haven't created any training plans yet.</p>
                    <Button className="mt-4" onClick={() => router.push('/dashboard/training-plans/new')}>
                      Create Your First Plan
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
            {trainer.plans.length > 0 && (
              <CardFooter>
                <Button variant="outline" className="w-full" onClick={() => router.push('/dashboard/training-plans')}>
                  View All Training Plans
                </Button>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="clients" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>All Clients</CardTitle>
                <Button onClick={() => router.push('/dashboard/clients/new')}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Client
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {trainer.clients.length > 0 ? (
                  trainer.clients.map((client) => (
                    <div key={client.id} className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={client.avatarUrl || ""} alt={client.name} />
                          <AvatarFallback>{client.name?.[0]?.toUpperCase() || "C"}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{client.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {client.email} • Joined {new Date(client.startDate).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={() => router.push(`/dashboard/clients/${client.id}/edit`)}>
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => router.push(`/dashboard/clients/${client.id}`)}>
                          View
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground">No clients yet</p>
                    <Button className="mt-2" variant="outline" size="sm" onClick={() => router.push('/dashboard/clients/new')}>
                      Add Your First Client
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="connections" className="space-y-4">
          <ConnectionsTab />
        </TabsContent>
      </Tabs>

      {/* Edit Profile Dialog */}
      {editDialogOpen && (
        <EditTrainerProfileDialog
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          trainer={{
            id: trainer.id,
            name: trainer.name,
            email: trainer.email,
            bio: trainer.bio || "",
            avatarUrl: trainer.avatarUrl || "",
            socialLinks: mockData.socialLinks,
            role: trainer.role
          }}
          onSave={async (updatedProfile) => {
            try {
              // Call the API to update the profile
              const response = await fetch('/api/trainer-profile', {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedProfile),
              });

              if (!response.ok) {
                throw new Error('Failed to update profile');
              }

              console.log("Profile updated:", updatedProfile);

              // Reload the page to show the updated profile
              window.location.reload();
            } catch (error) {
              console.error("Error updating profile:", error);
              // You could show a toast notification here
            }
          }}
        />
      )}
    </div>
  )
}
