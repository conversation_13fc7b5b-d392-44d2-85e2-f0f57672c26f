/**
 * Utility functions for checking tier-based access to features
 */

import { prisma } from '@/lib/prisma';

export type SubscriptionTier = 'basic' | 'mid' | 'premium' | 'coaching';

// Define feature access by tier
export const tierFeatures = {
  // Basic tier features
  basic: [
    'basic_workouts',
    'view_workouts',
    'basic_tracking',
    'limited_history',
  ],
  
  // Mid tier features (includes all basic features)
  mid: [
    'all_workouts',
    'workout_logs',
    'training_volume',
    'performance_metrics',
    'habit_tracking',
    'extended_history',
  ],
  
  // Premium tier features (includes all mid features)
  premium: [
    'advanced_analytics',
    'nutrition_tracking',
    'recovery_tools',
    'unlimited_history',
  ],
  
  // Coaching tier features (includes all premium features)
  coaching: [
    'private_chat',
    'weekly_checkins',
    'personalized_feedback',
    'custom_programming',
  ],
};

// Get all features available to a specific tier
export function getTierFeatures(tier: SubscriptionTier): string[] {
  switch (tier) {
    case 'coaching':
      return [
        ...tierFeatures.basic,
        ...tierFeatures.mid,
        ...tierFeatures.premium,
        ...tierFeatures.coaching,
      ];
    case 'premium':
      return [
        ...tierFeatures.basic,
        ...tierFeatures.mid,
        ...tierFeatures.premium,
      ];
    case 'mid':
      return [
        ...tierFeatures.basic,
        ...tierFeatures.mid,
      ];
    case 'basic':
    default:
      return [...tierFeatures.basic];
  }
}

// Check if a user has access to a specific feature based on their tier
export function hasFeatureAccess(
  userTier: SubscriptionTier | null | undefined,
  feature: string
): boolean {
  // Default to basic tier if no tier is provided
  const tier = userTier || 'basic';
  
  // Get all features available to the user's tier
  const availableFeatures = getTierFeatures(tier);
  
  // Check if the feature is available
  return availableFeatures.includes(feature);
}

// Get the history limit based on tier
export function getHistoryLimit(tier: SubscriptionTier | null | undefined): number {
  switch (tier) {
    case 'premium':
    case 'coaching':
      return -1; // Unlimited
    case 'mid':
      return 30; // 30 days
    case 'basic':
    default:
      return 7; // 7 days
  }
}

// Check if a user can access detailed workout tracking
export function canAccessDetailedTracking(tier: SubscriptionTier | null | undefined): boolean {
  return hasFeatureAccess(tier, 'training_volume') || 
         hasFeatureAccess(tier, 'performance_metrics');
}

// Check if a user can access unlimited workout history
export function canAccessUnlimitedHistory(tier: SubscriptionTier | null | undefined): boolean {
  return hasFeatureAccess(tier, 'unlimited_history');
}

// Check if a user can access nutrition tracking
export function canAccessNutritionTracking(tier: SubscriptionTier | null | undefined): boolean {
  return hasFeatureAccess(tier, 'nutrition_tracking');
}

// Check if a user can access recovery tools
export function canAccessRecoveryTools(tier: SubscriptionTier | null | undefined): boolean {
  return hasFeatureAccess(tier, 'recovery_tools');
}

// Check if a user can access advanced analytics
export function canAccessAdvancedAnalytics(tier: SubscriptionTier | null | undefined): boolean {
  return hasFeatureAccess(tier, 'advanced_analytics');
}

/**
 * Check if a client has access to a specific training plan
 * based on subscription status, tier, completion, and time
 */
export async function checkTrainingPlanAccess(
  clientId: string,
  trainingPlanId: string
): Promise<{ hasAccess: boolean; reason?: string; unlockWeek?: number; currentWeek?: number }> {
  try {
    const subscription = await prisma.subscription.findFirst({
      where: {
        clientId,
        status: 'active',
      },
      include: {
        tier: {
          include: {
            tierTrainingPlans: {
              include: {
                trainingPlan: true,
              },
            },
          },
        },
      },
    });
    
    if (!subscription) {
      return { hasAccess: false, reason: 'no-subscription' };
    }
    
    // Check if the plan is included in the subscription tier
    const tierPlan = subscription.tier.tierTrainingPlans.find(
      tp => tp.trainingPlanId === trainingPlanId
    );
    
    if (!tierPlan) {
      return { hasAccess: false, reason: 'not-in-tier' };
    }
    
    const subscriptionStartDate = new Date(subscription.startDate);
    const currentDate = new Date();
    const weeksSinceStart = Math.floor(
      (currentDate.getTime() - subscriptionStartDate.getTime()) / 
      (7 * 24 * 60 * 60 * 1000)
    );
    
    if (weeksSinceStart < tierPlan.weekOffset) {
      return { 
        hasAccess: false, 
        reason: 'time-locked', 
        unlockWeek: tierPlan.weekOffset,
        currentWeek: weeksSinceStart,
      };
    }
    
    return { hasAccess: true };
  } catch (error) {
    console.error('Error checking training plan access:', error);
    // Default to no access on error
    return { hasAccess: false, reason: 'error' };
  }
}

/**
 * Update client's progress in a subscription
 */
export async function updateSubscriptionProgress(
  subscriptionId: string,
  progress: Record<string, any>
): Promise<boolean> {
  try {
    await prisma.subscription.update({
      where: { id: subscriptionId },
      data: {
        progress,
        lastAccessDate: new Date(),
      },
    });
    
    return true;
  } catch (error) {
    console.error('Error updating subscription progress:', error);
    return false;
  }
}
