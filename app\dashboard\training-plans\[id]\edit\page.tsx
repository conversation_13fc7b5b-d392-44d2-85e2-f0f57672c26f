import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { redirect } from "next/navigation"
import { TrainingPlanEditor } from "@/components/training-plan/TrainingPlanEditor"

interface EditTrainingPlanPageProps {
  params: {
    id: string
  }
}

export default async function EditTrainingPlanPage({ params }: EditTrainingPlanPageProps) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect("/auth/signin")
  }

  // Fetch the training plan
  const trainingPlan = await prisma.trainingPlanTemplate.findFirst({
    where: {
      id: params.id,
      trainerId: session.user.id
    }
  })

  if (!trainingPlan) {
    redirect("/dashboard/training-plans")
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Edit Training Plan</h1>
      <TrainingPlanEditor 
        initialData={trainingPlan}
        mode="edit"
      />
    </div>
  )
} 