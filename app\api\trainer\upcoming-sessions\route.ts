import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    console.log('Upcoming sessions API called');

    const session = await getServerSession(authOptions)

    if (!session?.user) {
      console.log('No session found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const trainerId = session.user.id
    console.log('Trainer ID:', trainerId);

    // Fetch upcoming coaching sessions for the trainer
    console.log('Querying database for upcoming sessions...');

    try {
      const upcomingSessions = await prisma.coachingSession.findMany({
        where: {
          coachingRelationship: {
            trainerId: trainerId,
          },
          scheduledDate: {
            gte: new Date(), // Only future sessions
          },
          status: {
            in: ['scheduled', 'rescheduled'], // Only active sessions
          },
        },
        include: {
          coachingRelationship: {
            include: {
              client: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatarUrl: true,
                },
              },
            },
          },
        },
        orderBy: {
          scheduledDate: 'asc', // Earliest sessions first
        },
        take: 10, // Limit to next 10 sessions
      })

      console.log('Found sessions:', upcomingSessions.length);

      // Format the sessions for the frontend
      const formattedSessions = upcomingSessions.map((session) => ({
        id: session.id,
        title: session.title,
        description: session.description,
        scheduledDate: session.scheduledDate,
        duration: session.duration,
        status: session.status,
        type: session.type,
        location: session.location,
        videoConferenceUrl: session.videoConferenceUrl,
        calendlyEventId: session.calendlyEventId,
        calendlyEventUri: session.calendlyEventUri,
        client: {
          id: session.coachingRelationship.client.id,
          name: session.coachingRelationship.client.name,
          email: session.coachingRelationship.client.email,
          avatarUrl: session.coachingRelationship.client.avatarUrl,
        },
        // Calculate time until session
        timeUntil: getTimeUntilSession(session.scheduledDate),
        // Format date for display
        formattedDate: formatSessionDate(session.scheduledDate),
        formattedTime: formatSessionTime(session.scheduledDate),
      }))

      return NextResponse.json({
        sessions: formattedSessions,
        total: formattedSessions.length,
      })

    } catch (dbError) {
      console.error('Database error:', dbError);
      // If database fails, return empty array instead of crashing
      return NextResponse.json({
        sessions: [],
        total: 0,
        error: 'Database connection issue'
      })
    }


  } catch (error) {
    console.error('Error fetching upcoming sessions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch upcoming sessions' },
      { status: 500 }
    )
  }
}


