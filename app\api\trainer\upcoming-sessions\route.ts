import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const trainerId = session.user.id

    // Fetch upcoming coaching sessions for the trainer
    const upcomingSessions = await prisma.coachingSession.findMany({
      where: {
        coachingRelationship: {
          trainerId: trainerId,
        },
        scheduledDate: {
          gte: new Date(), // Only future sessions
        },
        status: {
          in: ['scheduled', 'rescheduled'], // Only active sessions
        },
      },
      include: {
        coachingRelationship: {
          include: {
            client: {
              select: {
                id: true,
                name: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
        },
      },
      orderBy: {
        scheduledDate: 'asc', // Earliest sessions first
      },
      take: 10, // Limit to next 10 sessions
    })

    // Format the sessions for the frontend
    const formattedSessions = upcomingSessions.map((session) => ({
      id: session.id,
      title: session.title,
      description: session.description,
      scheduledDate: session.scheduledDate,
      duration: session.duration,
      status: session.status,
      type: session.type,
      location: session.location,
      videoConferenceUrl: session.videoConferenceUrl,
      calendlyEventId: session.calendlyEventId,
      calendlyEventUri: session.calendlyEventUri,
      client: {
        id: session.coachingRelationship.client.id,
        name: session.coachingRelationship.client.name,
        email: session.coachingRelationship.client.email,
        avatarUrl: session.coachingRelationship.client.avatarUrl,
      },
      // Calculate time until session
      timeUntil: getTimeUntilSession(session.scheduledDate),
      // Format date for display
      formattedDate: formatSessionDate(session.scheduledDate),
      formattedTime: formatSessionTime(session.scheduledDate),
    }))

    return NextResponse.json({
      sessions: formattedSessions,
      total: formattedSessions.length,
    })
  } catch (error) {
    console.error('Error fetching upcoming sessions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch upcoming sessions' },
      { status: 500 }
    )
  }
}

// Helper function to calculate time until session
function getTimeUntilSession(scheduledDate: Date): string {
  const now = new Date()
  const sessionDate = new Date(scheduledDate)
  const diffMs = sessionDate.getTime() - now.getTime()
  
  if (diffMs < 0) {
    return 'Past'
  }
  
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''}`
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''}`
  } else {
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`
  }
}

// Helper function to format session date
function formatSessionDate(scheduledDate: Date): string {
  const sessionDate = new Date(scheduledDate)
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  if (sessionDate.toDateString() === today.toDateString()) {
    return 'Today'
  } else if (sessionDate.toDateString() === tomorrow.toDateString()) {
    return 'Tomorrow'
  } else {
    return sessionDate.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    })
  }
}

// Helper function to format session time
function formatSessionTime(scheduledDate: Date): string {
  return new Date(scheduledDate).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  })
}
