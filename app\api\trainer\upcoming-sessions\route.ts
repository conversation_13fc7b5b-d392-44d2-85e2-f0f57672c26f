import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    console.log('Upcoming sessions API called');

    const session = await getServerSession(authOptions)

    if (!session?.user) {
      console.log('No session found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const trainerId = session.user.id
    console.log('Trainer ID:', trainerId);

    // First, try to get sessions from the database (webhook-created sessions)
    console.log('Querying database for upcoming sessions...');

    try {
      const upcomingSessions = await prisma.coachingSession.findMany({
        where: {
          coachingRelationship: {
            trainerId: trainerId,
          },
          scheduledDate: {
            gte: new Date(), // Only future sessions
          },
          status: {
            in: ['scheduled', 'rescheduled'], // Only active sessions
          },
        },
        include: {
          coachingRelationship: {
            include: {
              client: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatarUrl: true,
                },
              },
            },
          },
        },
        orderBy: {
          scheduledDate: 'asc', // Earliest sessions first
        },
        take: 10, // Limit to next 10 sessions
      })

      console.log('Found sessions in database:', upcomingSessions.length);

      if (upcomingSessions.length > 0) {
        // Format the sessions for the frontend
        const formattedSessions = upcomingSessions.map((session) => ({
          id: session.id,
          title: session.title,
          description: session.description,
          scheduledDate: session.scheduledDate,
          duration: session.duration,
          status: session.status,
          type: session.type,
          location: session.location,
          videoConferenceUrl: session.videoConferenceUrl,
          calendlyEventId: session.calendlyEventId,
          calendlyEventUri: session.calendlyEventUri,
          client: {
            id: session.coachingRelationship.client.id,
            name: session.coachingRelationship.client.name,
            email: session.coachingRelationship.client.email,
            avatarUrl: session.coachingRelationship.client.avatarUrl,
          },
          // Calculate time until session
          timeUntil: getTimeUntilSession(session.scheduledDate),
          // Format date for display
          formattedDate: formatSessionDate(session.scheduledDate),
          formattedTime: formatSessionTime(session.scheduledDate),
        }))

        return NextResponse.json({
          sessions: formattedSessions,
          total: formattedSessions.length,
        })
      }

      // If no sessions in database, try to fetch from Calendly API
      const calendlyApiToken = process.env.CALENDLY_API_TOKEN;

      if (!calendlyApiToken || calendlyApiToken === 'your_calendly_api_token_here') {
        console.log('No sessions in database and no Calendly API token configured');
        return NextResponse.json({
          sessions: [],
          total: 0,
          message: 'No upcoming sessions found. To see your Calendly sessions, please configure your Calendly API token in the environment variables.',
          needsCalendlyApi: true
        })
      }

      // Get trainer's Calendly user ID
      const trainer = await prisma.user.findUnique({
        where: { id: trainerId },
        select: { calendlyUserId: true }
      })

      if (!trainer?.calendlyUserId) {
        console.log('No Calendly user ID found for trainer');
        return NextResponse.json({
          sessions: [],
          total: 0,
          message: 'Calendly integration not configured. Please set up your Calendly connection in your profile.',
          needsCalendlyApi: false
        })
      }

      console.log('Fetching sessions from Calendly API...');
      console.log('Calendly User ID:', trainer.calendlyUserId);

      // Fetch events directly from Calendly API
      try {
        // Get the user's organization URI first
        const userResponse = await fetch(`https://api.calendly.com/users/${trainer.calendlyUserId}`, {
          headers: {
            'Authorization': `Bearer ${calendlyApiToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (!userResponse.ok) {
          console.error('Failed to fetch Calendly user:', userResponse.status, await userResponse.text());
          return NextResponse.json({
            sessions: [],
            total: 0,
            message: 'Failed to connect to Calendly. Please check your API token and user ID.',
            needsCalendlyApi: false
          })
        }

        const userData = await userResponse.json();
        const organizationUri = userData.resource.current_organization;

        // Fetch scheduled events from Calendly
        const now = new Date();
        const futureDate = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000)); // 30 days from now

        const eventsResponse = await fetch(
          `https://api.calendly.com/scheduled_events?organization=${encodeURIComponent(organizationUri)}&min_start_time=${now.toISOString()}&max_start_time=${futureDate.toISOString()}&status=active&sort=start_time:asc`,
          {
            headers: {
              'Authorization': `Bearer ${calendlyApiToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (!eventsResponse.ok) {
          console.error('Failed to fetch Calendly events:', eventsResponse.status, await eventsResponse.text());
          return NextResponse.json({
            sessions: [],
            total: 0,
            message: 'Failed to fetch events from Calendly API.',
            needsCalendlyApi: false
          })
        }

        const eventsData = await eventsResponse.json();
        console.log('Found Calendly events:', eventsData.collection?.length || 0);

        // Format the sessions for the frontend
        const formattedSessions = await Promise.all(
          (eventsData.collection || []).map(async (event: any) => {
            // Fetch invitee details for each event
            let invitee = null;
            try {
              const inviteesResponse = await fetch(
                `https://api.calendly.com/scheduled_events/${event.uri.split('/').pop()}/invitees`,
                {
                  headers: {
                    'Authorization': `Bearer ${calendlyApiToken}`,
                    'Content-Type': 'application/json'
                  }
                }
              );

              if (inviteesResponse.ok) {
                const inviteesData = await inviteesResponse.json();
                invitee = inviteesData.collection?.[0]; // Get first invitee
              }
            } catch (error) {
              console.error('Error fetching invitee:', error);
            }

            return {
              id: event.uri.split('/').pop(),
              title: event.name,
              description: event.event_type?.description || '',
              scheduledDate: event.start_time,
              duration: Math.round((new Date(event.end_time).getTime() - new Date(event.start_time).getTime()) / 60000),
              status: 'scheduled',
              type: 'video',
              location: event.location?.type || '',
              videoConferenceUrl: event.location?.join_url || '',
              calendlyEventId: event.uri.split('/').pop(),
              calendlyEventUri: event.uri,
              client: {
                id: invitee?.uri?.split('/').pop() || 'unknown',
                name: invitee?.name || 'Unknown Client',
                email: invitee?.email || '',
                avatarUrl: null,
              },
              // Calculate time until session
              timeUntil: getTimeUntilSession(new Date(event.start_time)),
              // Format date for display
              formattedDate: formatSessionDate(new Date(event.start_time)),
              formattedTime: formatSessionTime(new Date(event.start_time)),
            }
          })
        );

        return NextResponse.json({
          sessions: formattedSessions,
          total: formattedSessions.length,
          source: 'calendly_api'
        })

      } catch (calendlyError) {
        console.error('Error fetching from Calendly API:', calendlyError);
        return NextResponse.json({
          sessions: [],
          total: 0,
          message: 'Error connecting to Calendly API. Please check your configuration.',
          needsCalendlyApi: false
        })
      }

    } catch (error) {
      console.error('Error fetching upcoming sessions:', error);
      return NextResponse.json({
        sessions: [],
        total: 0,
        error: 'Failed to fetch upcoming sessions'
      })
    }


  } catch (error) {
    console.error('Error fetching upcoming sessions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch upcoming sessions' },
      { status: 500 }
    )
  }
}

// Helper function to calculate time until session
function getTimeUntilSession(scheduledDate: Date): string {
  const now = new Date()
  const sessionDate = new Date(scheduledDate)
  const diffMs = sessionDate.getTime() - now.getTime()

  if (diffMs < 0) {
    return 'Past'
  }

  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)

  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''}`
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''}`
  } else {
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`
  }
}

// Helper function to format session date
function formatSessionDate(scheduledDate: Date): string {
  const sessionDate = new Date(scheduledDate)
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  if (sessionDate.toDateString() === today.toDateString()) {
    return 'Today'
  } else if (sessionDate.toDateString() === tomorrow.toDateString()) {
    return 'Tomorrow'
  } else {
    return sessionDate.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    })
  }
}

// Helper function to format session time
function formatSessionTime(scheduledDate: Date): string {
  return new Date(scheduledDate).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  })
}


