import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET() {
  try {
    console.log('Upcoming sessions API called');

    const session = await getServerSession(authOptions)

    if (!session?.user) {
      console.log('No session found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const trainerId = session.user.id
    console.log('Trainer ID:', trainerId);

    // For now, return mock data to demonstrate the widget functionality
    // TODO: Replace with real database query once Prisma connection is stable
    console.log('Returning mock data for demonstration');

    const mockSessions = [
      {
        id: 'mock-1',
        title: 'Form Check & Progress Review',
        description: 'Review your form and progress on key exercises',
        scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
        duration: 45,
        status: 'scheduled',
        type: 'video',
        location: null,
        videoConferenceUrl: 'https://meet.google.com/abc-defg-hij',
        calendlyEventId: 'mock-event-1',
        calendlyEventUri: null,
        client: {
          id: 'mock-client-1',
          name: '<PERSON>e',
          email: '<EMAIL>',
          avatarUrl: null,
        },
        timeUntil: '2 days',
        formattedDate: 'Tomorrow',
        formattedTime: '2:00 PM',
      },
      {
        id: 'mock-2',
        title: 'Nutrition Consultation',
        description: 'Discuss your nutrition plan and meal prep strategies',
        scheduledDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        duration: 30,
        status: 'scheduled',
        type: 'video',
        location: null,
        videoConferenceUrl: 'https://zoom.us/j/123456789',
        calendlyEventId: 'mock-event-2',
        calendlyEventUri: null,
        client: {
          id: 'mock-client-2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          avatarUrl: null,
        },
        timeUntil: '5 days',
        formattedDate: 'Mon, Mar 25',
        formattedTime: '10:30 AM',
      },
      {
        id: 'mock-3',
        title: 'Weekly Check-in',
        description: 'Review progress and adjust training plan',
        scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        duration: 60,
        status: 'scheduled',
        type: 'video',
        location: null,
        videoConferenceUrl: 'https://teams.microsoft.com/l/meetup-join/xyz',
        calendlyEventId: 'mock-event-3',
        calendlyEventUri: null,
        client: {
          id: 'mock-client-3',
          name: 'Mike Rodriguez',
          email: '<EMAIL>',
          avatarUrl: null,
        },
        timeUntil: '1 week',
        formattedDate: 'Wed, Mar 27',
        formattedTime: '4:00 PM',
      }
    ];

    return NextResponse.json({
      sessions: mockSessions,
      total: mockSessions.length,
    })


  } catch (error) {
    console.error('Error fetching upcoming sessions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch upcoming sessions' },
      { status: 500 }
    )
  }
}


