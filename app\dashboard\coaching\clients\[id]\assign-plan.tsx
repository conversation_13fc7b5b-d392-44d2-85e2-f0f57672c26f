"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Check, Dumbbell } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"

interface AssignPlanProps {
  clientId: string
  onAssign: (plan: Plan) => void
}

interface Exercise {
  id: string
  name: string
  sets: number
  reps: number
  weight?: number
  category?: string
  iconName?: string
  video?: string
  description?: string
  targetMuscles?: string[]
  difficulty?: "Beginner" | "Intermediate" | "Advanced"
  duration?: string
}

interface Week {
  id: string
  weekNumber: number
  dailyWorkouts: DailyWorkout[]
}

interface DailyWorkout {
  id: string
  day: string
  exercises: Exercise[]
}

interface Plan {
  id: string
  title: string
  description: string
  type: "personalized" | "template"
  isPublic?: boolean
  trainerNotes?: string
  targetLevel?: "Beginner" | "Intermediate" | "Advanced"
  estimatedDuration?: string
  category?: string
  price?: number
  weeks?: {
    id: string
    weekNumber?: number
    dailyWorkouts: {
      id: string
      day: string
      exercises: Exercise[]
    }[]
  }[]
  exercises?: Exercise[]
  createdAt: string
}

export function AssignPlan({ clientId, onAssign }: AssignPlanProps) {
  const [open, setOpen] = useState(false)
  const [renameDialogOpen, setRenameDialogOpen] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null)
  const [customPlanName, setCustomPlanName] = useState("")
  const [plans, setPlans] = useState<Plan[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    // Fetch plans from the database when dialog opens
    if (open) {
      setIsLoading(true)
      fetch('/api/trainer/plans')
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to fetch plans')
          }
          return response.json()
        })
        .then(data => {
          setPlans(data)
        })
        .catch(error => {
          console.error("Error loading plans:", error)
          toast({
            title: "Error",
            description: "Failed to load training plans. Please try again.",
            variant: "destructive",
          })
          setPlans([])
        })
        .finally(() => {
          setIsLoading(false)
        })
    }
  }, [open, toast])

  const handleAssignPlan = async (plan: Plan) => {
    setSelectedPlan(plan)
    setCustomPlanName(plan.title)
    setRenameDialogOpen(true)
  }

  const handleConfirmAssign = async () => {
    if (!selectedPlan) return

    try {
      console.log('Selected plan for assignment:', selectedPlan);

      // Send the trainingPlanId and custom name to the API
      const response = await fetch(`/api/clients/${clientId}/training-plan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          trainingPlanId: selectedPlan.id,
          customName: customPlanName
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to assign plan');
      }

      const assignedPlan = await response.json();
      console.log('Plan assigned successfully:', assignedPlan);

      // Call the onAssign callback with the personalized plan
      onAssign({
        ...selectedPlan,
        id: assignedPlan.id,
        title: customPlanName,
        type: "personalized"
      });

      toast({
        title: "Plan Assigned",
        description: "The training plan has been successfully assigned to the client.",
      });

      setOpen(false);
      setRenameDialogOpen(false);
      setSelectedPlan(null);
      setCustomPlanName("");
    } catch (error) {
      console.error('Error assigning plan:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to assign the plan. Please try again.",
        variant: "destructive",
      });
    }
  }

  const renderPlanContent = (plan: Plan) => {
    console.log('Rendering plan content:', plan);

    if (plan.weeks && Array.isArray(plan.weeks)) {
      // Count total exercises across all weeks and days
      let totalExercises = 0;
      plan.weeks.forEach(week => {
        if (week.dailyWorkouts && Array.isArray(week.dailyWorkouts)) {
          week.dailyWorkouts.forEach(day => {
            if (day.exercises && Array.isArray(day.exercises)) {
              totalExercises += day.exercises.length;
            }
          });
        }
      });

      return (
        <div className="space-y-2">
          <div className="text-sm text-muted-foreground mb-2">
            Total: {totalExercises} exercises across {plan.weeks.length} weeks
          </div>
          {plan.weeks.map((week, weekIndex) => (
            <div key={week.id || `week-${weekIndex}`} className="text-sm">
              <div className="font-medium mb-1">Week {weekIndex + 1}</div>
              {week.dailyWorkouts && Array.isArray(week.dailyWorkouts) ? (
                <div className="space-y-1 text-muted-foreground">
                  {week.dailyWorkouts.map((day) => (
                    <div key={day.id || `day-${day.day}`}>
                      {day.day}: {day.exercises?.length || 0} exercises
                      {day.exercises && day.exercises.length > 0 && (
                        <ul className="ml-4 mt-1 text-xs">
                          {day.exercises.slice(0, 3).map((exercise, idx) => (
                            <li key={exercise.id || `ex-${idx}`} className="list-disc list-inside">
                              {exercise.name}
                            </li>
                          ))}
                          {day.exercises.length > 3 && (
                            <li className="text-muted-foreground">+{day.exercises.length - 3} more</li>
                          )}
                        </ul>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-muted-foreground">No workouts scheduled</div>
              )}
            </div>
          ))}
        </div>
      )
    } else if (plan.exercises && Array.isArray(plan.exercises)) {
      return (
        <div className="space-y-2">
          <div className="text-sm text-muted-foreground">
            {plan.exercises.length} exercises
          </div>
          {plan.exercises.length > 0 && (
            <ul className="ml-4 text-xs">
              {plan.exercises.slice(0, 5).map((exercise, idx) => (
                <li key={exercise.id || `ex-${idx}`} className="list-disc list-inside">
                  {exercise.name}
                </li>
              ))}
              {plan.exercises.length > 5 && (
                <li className="text-muted-foreground">+{plan.exercises.length - 5} more</li>
              )}
            </ul>
          )}
        </div>
      )
    }
    return (
      <div className="text-sm text-muted-foreground">
        No exercises defined
      </div>
    )
  }

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button>
            <ClipboardList className="mr-2 h-4 w-4" />
            Assign Plan
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Assign Training Plan</DialogTitle>
            <DialogDescription>
              Select a training plan to assign to your client
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="h-[500px] pr-4">
            <div className="grid gap-4">
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading training plans...</p>
                </div>
              ) : plans.length === 0 ? (
                <div className="text-center py-8">
                  <Dumbbell className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground mb-4">
                    No training plans found. Create a plan in the Training Plan Builder first.
                  </p>
                  <Button asChild>
                    <a href="/dashboard/training-plans/create">Create New Plan</a>
                  </Button>
                </div>
              ) : (
                plans.map((plan) => (
                  <Card key={plan.id} className="cursor-pointer hover:bg-muted/50 transition-colors">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle>{plan.title}</CardTitle>
                          <CardDescription>{plan.description}</CardDescription>
                          {plan.targetLevel && (
                            <div className="mt-1">
                              <span className="text-xs text-muted-foreground">
                                Level: {plan.targetLevel}
                              </span>
                            </div>
                          )}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAssignPlan(plan)}
                        >
                          <Check className="mr-2 h-4 w-4" />
                          Assign
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {renderPlanContent(plan)}
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>

      <Dialog open={renameDialogOpen} onOpenChange={setRenameDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Customize Plan Name</DialogTitle>
            <DialogDescription>
              Enter a custom name for this training plan
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="planName">Plan Name</Label>
              <Input
                id="planName"
                value={customPlanName}
                onChange={(e) => setCustomPlanName(e.target.value)}
                placeholder="Enter plan name"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setRenameDialogOpen(false)
                  setSelectedPlan(null)
                  setCustomPlanName("")
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  handleConfirmAssign()
                  setRenameDialogOpen(false)
                }}
              >
                Assign Plan
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}