// Mock implementation of useSafeQuery and useSafeMutation hooks for testing

export type QueryStatus = 'idle' | 'loading' | 'success' | 'error';

export interface ApiError extends Error {
  status?: number;
  data?: any;
}

export interface UseSafeQueryOptions<T> {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: any;
  headers?: HeadersInit;
  skip?: boolean;
  sanitize?: boolean;
  retry?: boolean;
  retryCount?: number;
  retryDelay?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: ApiError) => void;
  cacheKey?: string;
  dedupingInterval?: number;
  dependencies?: any[];
  cache?: boolean;
  cacheTTL?: number;
}

// Mock data for testing
const mockData = {
  success: { message: 'Success' },
  error: new Error('Mock error')
};

// Mock implementation of useSafeQuery
export function useSafeQuery<T = any>(options: UseSafeQueryOptions<T>) {
  // Return mock data based on the URL for testing
  const isError = options.url.includes('error');
  
  return {
    data: isError ? null : (mockData.success as T),
    error: isError ? mockData.error as ApiError : null,
    status: isError ? 'error' : 'success',
    isLoading: false,
    isSuccess: !isError,
    isError: isError,
    refetch: jest.fn().mockResolvedValue(mockData.success),
    updateData: jest.fn(),
    clearCache: jest.fn(),
    reset: jest.fn(),
  };
}

// Mock implementation of useSafeMutation
export function useSafeMutation<TData = any, TVariables = any>(
  options: Omit<UseSafeQueryOptions<TData>, 'body' | 'skip'>
) {
  // Return mock mutation function and state
  const isError = options.url.includes('error');
  
  return {
    mutate: jest.fn().mockResolvedValue(mockData.success),
    data: isError ? null : (mockData.success as TData),
    error: isError ? mockData.error as ApiError : null,
    status: isError ? 'error' : 'success',
    isLoading: false,
    isSuccess: !isError,
    isError: isError,
    reset: jest.fn(),
  };
}
