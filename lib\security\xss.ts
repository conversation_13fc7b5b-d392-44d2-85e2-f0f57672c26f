/**
 * XSS (Cross-Site Scripting) Protection Utility
 * 
 * This utility provides functions for sanitizing user input to prevent XSS attacks.
 */

// Regular expressions for identifying potentially dangerous patterns
const SCRIPT_PATTERN = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
const EVENT_HANDLER_PATTERN = /\bon\w+\s*=/gi;
const DATA_URI_PATTERN = /data:[^;]*;base64/gi;
const JAVASCRIPT_URI_PATTERN = /javascript:/gi;
const DANGEROUS_HTML_PATTERN = /<iframe|<object|<embed|<frame|<img[^>]+onerror/gi;

/**
 * Sanitize a string to prevent XSS attacks
 * @param input The string to sanitize
 * @returns The sanitized string
 */
export function sanitizeAgainstXSS(input: string): string {
  if (!input) return '';
  
  // Convert to string if not already
  let result = String(input);
  
  // Remove script tags
  result = result.replace(SCRIPT_PATTERN, '');
  
  // Remove event handlers (onclick, onload, etc.)
  result = result.replace(EVENT_HANDLER_PATTERN, ' disabled_');
  
  // Remove data URIs that could contain executable code
  result = result.replace(DATA_URI_PATTERN, 'data:invalid');
  
  // Remove javascript: URIs
  result = result.replace(JAVASCRIPT_URI_PATTERN, 'invalid:');
  
  // Remove other dangerous HTML elements
  result = result.replace(DANGEROUS_HTML_PATTERN, '<span');
  
  // Encode HTML special characters
  result = encodeHTMLEntities(result);
  
  return result;
}

/**
 * Encode HTML special characters to prevent XSS attacks
 * @param input The string to encode
 * @returns The encoded string
 */
export function encodeHTMLEntities(input: string): string {
  if (!input) return '';
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

/**
 * Safely parse JSON with XSS protection
 * @param jsonString The JSON string to parse
 * @returns The parsed object or null if invalid
 */
export function safeJSONParse<T>(jsonString: string): T | null {
  try {
    // First, try to parse the JSON
    const parsed = JSON.parse(jsonString) as T;
    
    // Then, recursively sanitize all string values
    return recursiveSanitize(parsed);
  } catch (error) {
    // If parsing fails, return null
    console.error('Error parsing JSON:', error);
    return null;
  }
}

/**
 * Recursively sanitize all string values in an object
 * @param obj The object to sanitize
 * @returns The sanitized object
 */
export function recursiveSanitize<T>(obj: T): T {
  // Base case: if obj is null, undefined, or not an object, return it as is
  if (obj === null || obj === undefined || typeof obj !== 'object') {
    // If it's a string, sanitize it
    if (typeof obj === 'string') {
      return sanitizeAgainstXSS(obj) as unknown as T;
    }
    return obj;
  }
  
  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => recursiveSanitize(item)) as unknown as T;
  }
  
  // Handle objects
  const result = {} as Record<string, unknown>;
  
  for (const [key, value] of Object.entries(obj)) {
    // Sanitize the key as well, in case it's used in dynamic property access
    const sanitizedKey = typeof key === 'string' ? sanitizeAgainstXSS(key) : key;
    
    // Recursively sanitize the value
    result[sanitizedKey] = recursiveSanitize(value);
  }
  
  return result as unknown as T;
}

/**
 * Create a middleware for Express.js to sanitize request bodies against XSS
 * @returns Middleware function
 */
export function createXSSMiddleware() {
  return (req: any, _res: any, next: () => void) => {
    if (req.body && typeof req.body === 'object') {
      req.body = recursiveSanitize(req.body);
    }
    if (req.query && typeof req.query === 'object') {
      req.query = recursiveSanitize(req.query);
    }
    if (req.params && typeof req.params === 'object') {
      req.params = recursiveSanitize(req.params);
    }
    next();
  };
}

/**
 * Sanitize URL parameters to prevent XSS
 * @param url The URL to sanitize
 * @returns The sanitized URL
 */
export function sanitizeURL(url: string): string {
  if (!url) return '';
  
  try {
    const parsedUrl = new URL(url);
    
    // Sanitize each search parameter
    const sanitizedParams = new URLSearchParams();
    parsedUrl.searchParams.forEach((value, key) => {
      sanitizedParams.append(
        sanitizeAgainstXSS(key),
        sanitizeAgainstXSS(value)
      );
    });
    
    // Reconstruct the URL with sanitized parameters
    parsedUrl.search = sanitizedParams.toString();
    
    // Ensure the protocol is safe
    if (parsedUrl.protocol === 'javascript:') {
      parsedUrl.protocol = 'https:';
    }
    
    return parsedUrl.toString();
  } catch (error) {
    // If URL parsing fails, do basic sanitization
    return sanitizeAgainstXSS(url);
  }
} 