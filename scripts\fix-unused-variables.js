const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Function to fix unused variables by prefixing them with underscore
function fixUnusedVariables(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Get all variable declarations
    const lines = content.split('\n');
    let modified = false;
    
    // Process each line
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Check for common patterns of unused variables
      if (line.includes(' is assigned a value but never used') || 
          line.includes(' is defined but never used')) {
        
        // Extract the variable name from the warning
        const match = line.match(/'([^']+)'/);
        if (match && match[1]) {
          const varName = match[1];
          
          // Skip if already prefixed with underscore
          if (varName.startsWith('_')) continue;
          
          // Find the line with the variable declaration
          for (let j = Math.max(0, i - 20); j < i; j++) {
            if (lines[j].includes(varName) && 
                (lines[j].includes('const ') || 
                 lines[j].includes('let ') || 
                 lines[j].includes('function ') ||
                 lines[j].includes('(') && lines[j].includes(')') && !lines[j].includes('//'))) {
              
              // Replace the variable name with prefixed version
              lines[j] = lines[j].replace(
                new RegExp(`\\b${varName}\\b`), 
                `_${varName}`
              );
              modified = true;
              break;
            }
          }
        }
      }
    }
    
    // Save the file if modified
    if (modified) {
      fs.writeFileSync(filePath, lines.join('\n'), 'utf8');
      console.log(`Fixed unused variables in ${filePath}`);
    }
    
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

// Find all TypeScript files
const tsFiles = glob.sync('**/*.{ts,tsx}', {
  ignore: ['node_modules/**', '.next/**', 'out/**', 'dist/**']
});

// Process each file
tsFiles.forEach(fixUnusedVariables);

console.log('Finished fixing unused variables');
