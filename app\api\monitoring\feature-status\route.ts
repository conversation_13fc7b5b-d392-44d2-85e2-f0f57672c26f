import { NextResponse } from "next/server";
import { getServerAuthSession } from "@/lib/auth";
import logger from "@/lib/logger";
import logService from "@/lib/log-service";
import { isFeatureEnabled } from "@/config/features";
import { 
  initializeFeatureStore, 
  getFeatureStatus, 
  updateFeatureStatus,
  addFeatureLog,
  incrementFeatureIncidents
} from "@/lib/monitoring-store";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export const dynamic = 'force-dynamic'

// Get feature status
export async function GET(request: Request) {
  try {
    const session = await getServerAuthSession();
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Initialize statuses if not already done
    initializeFeatureStore();

    // Get feature name from query parameters
    const url = new URL(request.url);
    const featureName = url.searchParams.get('name');

    if (!featureName) {
      return NextResponse.json({ error: "Feature name required" }, { status: 400 });
    }

    // Get feature status
    const status = getFeatureStatus(featureName);

    // For development, check if the feature is enabled in config
    if (process.env.NODE_ENV === 'development') {
      try {
        // @ts-ignore - isFeatureEnabled is expecting a CoreFeature type, but we're passing a string
        const isEnabled = isFeatureEnabled(featureName);
        if (!isEnabled && status.status === 'online') {
          updateFeatureStatus(featureName, { status: 'offline' });
          addFeatureLog(featureName, `Feature disabled in configuration at ${new Date().toLocaleString()}`);
          incrementFeatureIncidents(featureName);
          
          // Log to log service
          logService.warn(`Feature ${featureName} is disabled in config but was online`, {
            feature: featureName,
            userId: session.user.id
          });
        } else if (isEnabled && status.status === 'offline') {
          updateFeatureStatus(featureName, { status: 'online' });
          addFeatureLog(featureName, `Feature enabled in configuration at ${new Date().toLocaleString()}`);
          
          // Log to log service
          logService.info(`Feature ${featureName} is enabled in config and was offline`, {
            feature: featureName,
            userId: session.user.id
          });
        }
      } catch (error) {
        // Just log the error, don't fail the request
        logger.error({ error }, `Error checking feature status for ${featureName}`);
        logService.error(`Error checking feature status for ${featureName}`, { error });
      }
    }

    // Get updated status after changes
    const updatedStatus = getFeatureStatus(featureName);

    // Log the status check
    logService.debug(`Feature status check for ${featureName}`, {
      status: updatedStatus.status,
      userId: session.user.id
    });

    return NextResponse.json({
      name: featureName,
      status: updatedStatus.status,
      logs: updatedStatus.logs.slice(0, 20), // Return only the last 20 logs
      incidents: updatedStatus.incidents,
      lastCheck: updatedStatus.lastCheck
    });
  } catch (error) {
    logger.error({ error }, "Error fetching feature status");
    logService.error("Error fetching feature status", { error });
    return new NextResponse("Internal error", { status: 500 });
  }
} 