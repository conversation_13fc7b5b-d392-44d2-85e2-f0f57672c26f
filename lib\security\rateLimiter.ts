/**
 * Rate limiter utility for API routes
 *
 * This module provides rate limiting functionality to protect API routes
 * from abuse and DoS attacks.
 */

// In-memory cache for rate limiting
const ipRequests = new Map<string, { count: number; timestamp: number }>();

// Configure rate limits
const DEFAULT_REQUESTS_PER_MINUTE = 60;
const SENSITIVE_REQUESTS_PER_MINUTE = 10;
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute in ms

/**
 * Check if a request should be rate limited
 * @param ip The IP address of the requester
 * @param limit The maximum number of requests allowed in the time window
 * @returns True if the request is allowed, false if it should be rate limited
 */
export const rateLimiter = {
  check: (ip: string, limit: number = DEFAULT_REQUESTS_PER_MINUTE, path?: string): boolean => {
    // Special cases for development mode
    if (process.env.NODE_ENV === 'development') {
      // Always allow dev-login and auth requests in development
      if (path && (path.includes('/dev-login') || path.includes('/api/auth'))) {
        return true;
      }

      // Use a much higher limit in development mode
      limit = limit * 10;
    }

    const now = Date.now();
    const record = ipRequests.get(ip);

    // Check if we have a recent record
    if (record) {
      // Remove expired entries
      if (now - record.timestamp > RATE_LIMIT_WINDOW) {
        ipRequests.set(ip, { count: 1, timestamp: now });
        return true;
      }

      // Check if we're over the limit
      if (record.count >= limit) {
        return false;
      }

      // Increment the counter
      ipRequests.set(ip, {
        count: record.count + 1,
        timestamp: record.timestamp
      });
      return true;
    }

    // First request from this IP
    ipRequests.set(ip, { count: 1, timestamp: now });

    // Clean up old entries every few minutes
    if (Math.random() < 0.001) { // 0.1% chance of cleanup on each request
      const cutoff = now - RATE_LIMIT_WINDOW;
      for (const [ip, data] of ipRequests.entries()) {
        if (data.timestamp < cutoff) {
          ipRequests.delete(ip);
        }
      }
    }

    return true;
  },

  // Reset the rate limiter (useful for testing)
  reset: () => {
    ipRequests.clear();
  }
};
