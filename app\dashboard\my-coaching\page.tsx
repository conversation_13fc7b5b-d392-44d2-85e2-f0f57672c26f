"use client"

import { format } from "date-fns"
import { CalendarD<PERSON>, <PERSON>lipboard<PERSON>heck, Clock, ArrowRight, AlertCircle } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { toast } from "sonner"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"

interface CoachingApplication {
  id: string
  status: "pending" | "accepted" | "rejected"
  submittedDate: string
  trainerName: string
  trainerId: string
  notes?: string
}

interface Session {
  id: string
  title: string
  scheduledDate: string
  type: string
  duration: number
}

interface ActiveCoaching {
  relationshipId: string
  startDate: string
  trainerName: string
  trainerId: string
  nextSessionDate: string | null
  plan: string
  nextBillingDate: string | null
}

export default function MyCoachingPage() {
  const [loading, setLoading] = useState(true)
  const [applications, setApplications] = useState<CoachingApplication[]>([])
  const [activeCoaching, setActiveCoaching] = useState<ActiveCoaching | null>(null)
  const [sessions, setSessions] = useState<Session[]>([])
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false)
  const [withdrawingApplication, setWithdrawingApplication] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    // Check for coaching requests in localStorage (for development)
    let mockApplications: CoachingApplication[] = []

    if (process.env.NODE_ENV === 'development') {
      const storedRequests = localStorage.getItem('coachingRequests')
      if (storedRequests) {
        try {
          const requests = JSON.parse(storedRequests)

          // Convert stored requests to application format
          const newApplications = requests.map((req: any, index: number) => ({
            id: `app-${index}`,
            status: "pending",
            submittedDate: req.timestamp || new Date().toISOString(),
            trainerName: req.trainerName || "Requested Coach",
            trainerId: req.trainerId || `trainer-${index}`,
            notes: `Goals: ${req.goals}\nExperience: ${req.experience}${req.message ? `\nMessage: ${req.message}` : ''}`
          }))

          mockApplications = [...newApplications]
        } catch (error) {
          console.error('Error parsing coaching requests:', error)
        }
      }
    }

    // Add default application if none exist
    if (mockApplications.length === 0) {
      mockApplications = [
        {
          id: "app1",
          status: "pending",
          submittedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          trainerName: "Sarah Johnson",
          trainerId: "1",
          notes: "I'm interested in your 1:1 coaching for strength training."
        }
      ]
    }

    const mockActiveCoaching: ActiveCoaching | null = {
      relationshipId: "rel123",
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      trainerName: "Emma Wilson",
      trainerId: "3",
      plan: "Executive Coaching",
      nextSessionDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      nextBillingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString()
    }

    const mockSessions: Session[] = [
      {
        id: "sess1",
        title: "Initial Assessment & Goal Setting",
        scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
        type: "VIDEO",
        duration: 60
      }
    ]

    // Simulate loading
    const timer = setTimeout(() => {
      setApplications(mockApplications)
      setActiveCoaching(mockActiveCoaching)
      setSessions(mockSessions)
      setLoading(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  const handleCancelCoaching = () => {
    setLoading(true)

    // Simulate API call delay
    setTimeout(() => {
      setActiveCoaching(null)
      setSessions([])
      setLoading(false)
      setIsCancelDialogOpen(false)

      toast.success("Coaching relationship cancelled", {
        description: "Your coaching has been cancelled successfully. You won't be billed in the next cycle."
      })
    }, 1000)
  }

  const handleJoinSession = (sessionId: string) => {
    // In a real app, this would join a video call or redirect to a meeting link
    toast.info("Joining session...", {
      description: "You'll be connected to your coaching session shortly."
    })

    // Simulate session joining with a delay
    setTimeout(() => {
      toast.success("Session joined successfully", {
        description: "You're now connected to your coaching session."
      })

      // In real app, this would redirect to a video call page
      router.push(`/dashboard/sessions/${sessionId}/active`)
    }, 1500)
  }

  const handleWithdrawApplication = (applicationId: string) => {
    setWithdrawingApplication(null);

    // Add loading state
    setLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      setApplications(prev => prev.filter(a => a.id !== applicationId));
      setLoading(false);

      toast.success("Application withdrawn successfully", {
        description: "Your coaching application has been withdrawn."
      });
    }, 800);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">1:1 Coaching</h1>
          <p className="text-muted-foreground">
            Direct coaching with professional trainers
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button asChild variant="outline" size="sm">
            <Link href="/dashboard/trainers">
              Find Trainers
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="coaching" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="coaching">Active Coaching</TabsTrigger>
          <TabsTrigger value="applications">Applications {applications.length > 0 && `(${applications.length})`}</TabsTrigger>
        </TabsList>

        <TabsContent value="coaching" className="space-y-6">
          {activeCoaching ? (
            <>
              {/* Active Coaching Card */}
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>Your 1:1 Coach</CardTitle>
                      <CardDescription>Personal coaching program</CardDescription>
                    </div>
                    <Badge>Active</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col md:flex-row md:justify-between gap-6">
                    <div>
                      <h3 className="font-bold text-xl">{activeCoaching.trainerName}</h3>
                      <p className="text-muted-foreground mt-1">
                        {activeCoaching.plan}
                      </p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Coaching since {format(new Date(activeCoaching.startDate), 'MMMM d, yyyy')}
                      </p>
                    </div>

                    <div className="space-y-2">
                      {activeCoaching.nextSessionDate && (
                        <div className="flex items-center text-sm">
                          <CalendarDays className="w-4 h-4 mr-2 text-muted-foreground" />
                          <span>Next session: {format(new Date(activeCoaching.nextSessionDate), 'EEEE, MMMM d, yyyy')}</span>
                        </div>
                      )}

                      {activeCoaching.nextBillingDate && (
                        <div className="flex items-center text-sm">
                          <Clock className="w-4 h-4 mr-2 text-muted-foreground" />
                          <span>Next billing: {format(new Date(activeCoaching.nextBillingDate), 'MMMM d, yyyy')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="bg-muted/50 flex flex-col sm:flex-row justify-between gap-4">
                  <Button asChild>
                    <Link href="/dashboard/sessions">
                      View Upcoming Sessions
                    </Link>
                  </Button>
                  <AlertDialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline">
                        Cancel Coaching
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure you want to cancel coaching?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will end your coaching relationship with {activeCoaching.trainerName}.
                          Any scheduled sessions will be cancelled and you won't be billed in the next cycle.
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Keep Coaching</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleCancelCoaching}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Cancel Coaching
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </CardFooter>
              </Card>

              {/* Upcoming Sessions Card */}
              {sessions.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Upcoming Sessions</CardTitle>
                    <CardDescription>Scheduled coaching sessions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {sessions.map(session => (
                        <div key={session.id} className="border rounded-lg p-4">
                          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                            <div>
                              <h4 className="font-medium">{session.title}</h4>
                              <div className="flex flex-wrap gap-x-4 gap-y-1 mt-1 text-sm">
                                <div className="flex items-center text-muted-foreground">
                                  <CalendarDays className="h-3.5 w-3.5 mr-1" />
                                  {format(new Date(session.scheduledDate), 'EEEE, MMMM d, yyyy')}
                                </div>
                                <div className="flex items-center text-muted-foreground">
                                  <Clock className="h-3.5 w-3.5 mr-1" />
                                  {format(new Date(session.scheduledDate), 'h:mm a')} ({session.duration} min)
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{session.type}</Badge>
                              <Button
                                size="sm"
                                onClick={() => handleJoinSession(session.id)}
                              >
                                Join Session
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          ) : (
            <Card>
              <CardContent className="pt-6 flex flex-col items-center text-center py-10">
                <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-xl font-medium mb-2">No active coaching</h3>
                <p className="text-muted-foreground mb-6 max-w-md">
                  You don't have any active 1:1 coaching relationships. Browse trainers and apply for coaching to get started.
                </p>
                <Button asChild>
                  <Link href="/dashboard/trainers">
                    Find Trainers
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="applications" className="space-y-6">
          <div className="space-y-4">
            {applications.length > 0 ? (
              applications.map(application => (
                <Card key={application.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>Application for {application.trainerName}</CardTitle>
                        <CardDescription>
                          Submitted on {format(new Date(application.submittedDate), 'MMMM d, yyyy')}
                        </CardDescription>
                      </div>
                      <Badge variant={
                        application.status === "accepted" ? "default" :
                        application.status === "rejected" ? "destructive" :
                        "secondary"
                      }>
                        {application.status === "pending" ? "Pending Review" :
                          application.status === "accepted" ? "Accepted" :
                          "Rejected"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {application.notes && (
                        <div>
                          <h4 className="text-sm font-medium mb-1">Your Notes</h4>
                          <p className="text-sm text-muted-foreground">{application.notes}</p>
                        </div>
                      )}

                      <div className="flex items-center text-sm text-muted-foreground">
                        <ClipboardCheck className="h-4 w-4 mr-2" />
                        {application.status === "pending" ? (
                          <span>Your application is being reviewed by the trainer.</span>
                        ) : application.status === "accepted" ? (
                          <span>Your application has been accepted! The trainer will contact you soon.</span>
                        ) : (
                          <span>Your application has been rejected. This may be due to the trainer's capacity or coaching fit.</span>
                        )}
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="bg-muted/50 flex justify-between">
                    <Button
                      asChild
                      variant="outline"
                      size="sm"
                    >
                      <Link href={`/dashboard/trainers/${application.trainerId}`}>
                        View Trainer
                      </Link>
                    </Button>

                    {application.status === "pending" && (
                      <AlertDialog
                        open={withdrawingApplication === application.id}
                        onOpenChange={(isOpen) => !isOpen && setWithdrawingApplication(null)}
                      >
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setWithdrawingApplication(application.id)}
                          >
                            Withdraw Application
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Withdraw Application?</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to withdraw your coaching application to {application.trainerName}?
                              This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleWithdrawApplication(application.id)}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                              Withdraw Application
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                  </CardFooter>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="pt-6 flex flex-col items-center text-center py-10">
                  <ClipboardCheck className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-medium mb-2">No applications</h3>
                  <p className="text-muted-foreground mb-6 max-w-md">
                    You haven't submitted any applications for 1:1 coaching yet.
                  </p>
                  <Button asChild>
                    <Link href="/dashboard/trainers">
                      Browse Trainers
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}