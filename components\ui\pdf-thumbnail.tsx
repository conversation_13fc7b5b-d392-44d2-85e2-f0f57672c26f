"use client"

import { useState, useEffect } from "react"
import { FileText } from "lucide-react"
import Image from "next/image"
import { ClientPdfThumbnail } from "./client-pdf-thumbnail"

interface PdfThumbnailProps {
  pdfUrl?: string
  thumbnailUrl?: string
  title: string
  className?: string
}

export function PdfThumbnail({ pdfUrl, thumbnailUrl, title, className = "" }: PdfThumbnailProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(false)

  // If we have a thumbnail URL, use it directly
  if (thumbnailUrl) {
    return (
      <div className={`relative w-full h-full ${className}`}>
        <Image
          src={thumbnailUrl}
          alt={`Thumbnail for ${title}`}
          fill
          sizes="(max-width: 768px) 100vw, 33vw"
          className="object-cover rounded-md"
          onLoad={() => setIsLoading(false)}
          onError={() => setError(true)}
        />
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted">
            <div className="animate-pulse">
              <FileText className="h-10 w-10 text-muted-foreground" />
            </div>
          </div>
        )}
        <span className="absolute bottom-2 right-2 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100 text-xs px-2 py-1 rounded-md">
          PDF
        </span>
      </div>
    )
  }

  // If we don't have a thumbnail but have a PDF URL, try to generate one client-side
  if (pdfUrl) {
    return <ClientPdfThumbnail pdfUrl={pdfUrl} title={title} className={className} />
  }

  // If we don't have either, show a generic placeholder
  return (
    <div className={`flex items-center justify-center h-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/20 dark:to-gray-800/20 rounded-md ${className}`}>
      <FileText className="h-16 w-16 text-gray-400" />
    </div>
  )
}
