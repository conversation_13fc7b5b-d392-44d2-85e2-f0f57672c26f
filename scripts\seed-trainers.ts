import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('Seeding demo trainers...')

  // Create "Alin Lifts" trainer
  const alinLiftsTheme = {
    primaryColor: "#ff4d4d",
    secondaryColor: "#262626",
    logoUrl: "https://images.unsplash.com/photo-1517836357463-d25dfeac3438?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    bannerUrl: "https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
    fontFamily: "'Montserrat', sans-serif"
  }

  const alinSocialLinks = {
    instagram: "https://instagram.com/alinlifts",
    youtube: "https://youtube.com/alinlifts",
    twitter: "https://twitter.com/alinlifts",
    website: "https://alinlifts.com"
  }

  const alinPassword = await hash('password123', 10)

  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Alin Lifts',
      password: alinPassword,
      fullName: 'Alin Strongman',
      role: 'trainer',
      bio: "Powerlifting champion with 15+ years of experience. Specializing in strength training, muscle building, and competition prep. I have helped hundreds of clients reach their strength goals and break personal records.",
      avatarUrl: 'https://images.unsplash.com/photo-1531384441138-2736e62e0919?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      socialLinks: alinSocialLinks,
      slug: 'alinlifts',
      themeSettings: JSON.stringify(alinLiftsTheme)
    }
  })

  console.log('Created Alin Lifts trainer profile')

  // Create "Fitness with Maya" trainer
  const mayaTheme = {
    primaryColor: "#4A90E2",
    secondaryColor: "#50E3C2",
    logoUrl: "https://images.unsplash.com/photo-1518310383802-640c2de311b2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    bannerUrl: "https://images.unsplash.com/photo-1518611012118-696072aa579a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
    fontFamily: "'Poppins', sans-serif"
  }

  const mayaSocialLinks = {
    instagram: "https://instagram.com/fitnesswithmaya",
    youtube: "https://youtube.com/fitnesswithmaya",
    linkedin: "https://linkedin.com/in/mayafitness"
  }

  const mayaPassword = await hash('password123', 10)

  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Fitness with Maya',
      password: mayaPassword,
      fullName: 'Maya Johnson',
      role: 'trainer',
      bio: 'Certified personal trainer and nutritionist with a focus on holistic wellness and sustainable fitness. My approach combines effective workouts with mindful nutrition to help you achieve lasting results and feel your best every day.',
      avatarUrl: 'https://images.unsplash.com/photo-1579047440583-43a690fe2243?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      socialLinks: mayaSocialLinks,
      slug: 'fitnesswithmaya',
      themeSettings: JSON.stringify(mayaTheme)
    }
  })

  console.log('Created Fitness with Maya trainer profile')

  // Add some subscription tiers for demo purposes
  await createDemoSubscriptionPlans()

  console.log('Seeding completed successfully!')
}

async function createDemoSubscriptionPlans() {
  // Add some basic subscription plans
  // In a real app, these would be linked to the trainers
  console.log('Creating demo subscription plans...')

  // Note: In a production app, we'd need to adjust the schema to properly link
  // subscription tiers to specific trainers. For now, we're just creating sample data.
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  }) 