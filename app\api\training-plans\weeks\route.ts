import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions, getAuthSession } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: Request) {
  try {
    const session = await getAuthSession()
    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access this resource" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { trainingPlanId, weekNumber } = body

    // Validation
    if (!trainingPlanId) {
      return NextResponse.json(
        { error: "Training plan ID is required" },
        { status: 400 }
      )
    }

    if (!weekNumber) {
      return NextResponse.json(
        { error: "Week number is required" },
        { status: 400 }
      )
    }

    // Log request information for debugging
    console.log("Creating week with params:", {
      trainingPlanId,
      weekNumber,
      userId: session.user.id,
      userRole: session.user.role
    });

    // Handle development mode with dev-bypass-user
    const isDevelopment = process.env.NODE_ENV === "development";
    const isDevBypassUser = session.user.id === "dev-bypass-user";
    
    // Check if this is a mock training plan ID from development mode
    const isMockTrainingPlan = trainingPlanId.startsWith('mock-');
    
    if ((isDevelopment && isDevBypassUser) || isMockTrainingPlan) {
      // In development mode with dev bypass, create a mock response
      console.log("Development mode: Creating mock week");
      const mockWeek = {
        id: `mock-week-${Date.now()}`,
        weekNumber: Number(weekNumber),
        trainingPlanId,
        order: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
        workouts: []
      };
      
      return NextResponse.json(mockWeek);
    }

    // Production mode - continue with database operations
    try {
      // Check if user owns the training plan
      const trainingPlan = await prisma.trainingPlan.findUnique({
        where: {
          id: trainingPlanId,
        },
      })

      if (!trainingPlan) {
        return NextResponse.json(
          { error: "Training plan not found" },
          { status: 404 }
        )
      }

      console.log("Training plan found:", {
        id: trainingPlan.id,
        title: trainingPlan.title,
        athleteId: trainingPlan.athleteId,
        requestUserId: session.user.id
      });

      if (trainingPlan.athleteId !== session.user.id && session.user.role !== "admin") {
        return NextResponse.json(
          { error: "You do not have permission to edit this training plan" },
          { status: 403 }
        )
      }

      // Get highest order in the plan
      const highestOrderWeek = await prisma.week.findFirst({
        where: {
          trainingPlanId,
        },
        orderBy: {
          order: "desc",
        },
      })

      const newOrder = highestOrderWeek ? highestOrderWeek.order + 1 : 0

      // Create the week
      const week = await prisma.week.create({
        data: {
          weekNumber,
          trainingPlanId,
          order: newOrder,
        },
      })

      return NextResponse.json(week)
    } catch (error) {
      console.error("[WEEK_CREATE]", error)
      return NextResponse.json(
        { error: "An error occurred while creating the week" },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("[WEEK_CREATE]", error)
    return NextResponse.json(
      { error: "An error occurred while creating the week" },
      { status: 500 }
    )
  }
}

export async function PATCH(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access this resource" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { id, weekNumber, order } = body

    // Validation
    if (!id) {
      return NextResponse.json(
        { error: "Week ID is required" },
        { status: 400 }
      )
    }

    // Check if user owns the week
    const week = await prisma.week.findUnique({
      where: {
        id,
      },
      include: {
        trainingPlan: true,
      },
    })

    if (!week) {
      return NextResponse.json(
        { error: "Week not found" },
        { status: 404 }
      )
    }

    if (week.trainingPlan.athleteId !== session.user.id && session.user.role !== "admin") {
      return NextResponse.json(
        { error: "You do not have permission to edit this week" },
        { status: 403 }
      )
    }

    // Update the week
    const updatedWeek = await prisma.week.update({
      where: {
        id,
      },
      data: {
        weekNumber: weekNumber !== undefined ? weekNumber : week.weekNumber,
        order: order !== undefined ? order : week.order,
      },
    })

    return NextResponse.json(updatedWeek)
  } catch (error) {
    console.error("[WEEK_UPDATE]", error)
    return NextResponse.json(
      { error: "An error occurred while updating the week" },
      { status: 500 }
    )
  }
}

export async function DELETE(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access this resource" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json(
        { error: "Week ID is required" },
        { status: 400 }
      )
    }

    // Check if user owns the week
    const week = await prisma.week.findUnique({
      where: {
        id,
      },
      include: {
        trainingPlan: true,
      },
    })

    if (!week) {
      return NextResponse.json(
        { error: "Week not found" },
        { status: 404 }
      )
    }

    if (week.trainingPlan.athleteId !== session.user.id && session.user.role !== "admin") {
      return NextResponse.json(
        { error: "You do not have permission to delete this week" },
        { status: 403 }
      )
    }

    // Delete the week
    await prisma.week.delete({
      where: {
        id,
      },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("[WEEK_DELETE]", error)
    return NextResponse.json(
      { error: "An error occurred while deleting the week" },
      { status: 500 }
    )
  }
} 