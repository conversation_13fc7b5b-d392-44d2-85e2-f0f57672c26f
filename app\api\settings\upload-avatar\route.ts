import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import sharp from "sharp"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get("file") as File
    
    if (!file) {
      return new NextResponse("No file provided", { status: 400 })
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      return new NextResponse("Invalid file type", { status: 400 })
    }

    // Read file buffer
    const buffer = Buffer.from(await file.arrayBuffer())

    // Optimize image
    const optimizedImageBuffer = await sharp(buffer)
      .resize(400, 400, {
        fit: "cover",
        position: "center"
      })
      .webp({ quality: 80 })
      .toBuffer()

    // In a real application, you would upload this to a cloud storage service
    // For now, we'll convert to base64 and store in the database
    const base64Image = `data:image/webp;base64,${optimizedImageBuffer.toString("base64")}`

    // Update user's avatar URL
    const user = await prisma.user.update({
      where: {
        id: session.user.id
      },
      data: {
        avatarUrl: base64Image
      }
    })

    return NextResponse.json({ url: base64Image })
  } catch (error) {
    console.error("[AVATAR_UPLOAD]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 