import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`

    // Get system status
    const status = {
      database: "healthy",
      api: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    }

    return NextResponse.json(status)
  } catch (error) {
    console.error("[HEALTH_CHECK]", error)
    return new NextResponse(
      JSON.stringify({
        database: "error",
        api: "healthy",
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      }),
      { status: 500 }
    )
  }
} 