import { ReactNode } from 'react'

export interface DietPlanFormProps {
  onSubmit?: (data: DietPlanFormData) => void
  initialData?: DietPlanFormData
}

export interface DietPlanFormData {
  title: string
  description?: string
  meals: Array<{
    name: string
    calories: number
    protein: number
    carbs: number
    fats: number
  }>
}

export function DietPlanForm(props: DietPlanFormProps): ReactNode 