import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import { v4 as uuidv4 } from 'uuid'

// Import the prisma client from the app
import { prisma } from '../../lib/prisma'

/**
 * This test file tests the complete workflow for a trainer:
 * 1. Creating and managing training plans
 * 2. Viewing and managing clients
 * 3. Assigning training plans to clients
 * 4. Creating subscription tiers
 * 5. Managing subscriptions
 */
describe('Trainer Complete Workflow', () => {
  // Test data
  let trainerUser: any
  let trainerProfile: any
  let clientUser: any
  let clientProfile: any
  let trainingPlan: any
  let week: any
  let workout: any
  let exercise: any
  let subscriptionTier: any
  let subscription: any

  // Setup: Create test users and profiles
  beforeAll(async () => {
    // Create trainer user
    const trainerPassword = await bcrypt.hash('password123', 10)
    trainerUser = await prisma.user.create({
      data: {
        id: uuidv4(),
        name: 'Test Trainer',
        email: `test-trainer-${Date.now()}@example.com`,
        password: trainerPassword,
        role: 'trainer',
        emailVerified: new Date()
      }
    })

    // Create trainer profile
    trainerProfile = await prisma.trainerProfile.create({
      data: {
        userId: trainerUser.id
      }
    })

    // Create client user
    const clientPassword = await bcrypt.hash('password123', 10)
    clientUser = await prisma.user.create({
      data: {
        id: uuidv4(),
        name: 'Test Client',
        email: `test-client-${Date.now()}@example.com`,
        password: clientPassword,
        role: 'client',
        emailVerified: new Date()
      }
    })

    // Create client profile
    clientProfile = await prisma.clientProfile.create({
      data: {
        userId: clientUser.id,
        assignedTrainerId: trainerProfile.id
      }
    })
  })

  // Cleanup: Remove test data
  afterAll(async () => {
    // Clean up in reverse order of creation
    if (subscription) {
      await prisma.subscription.delete({
        where: { id: subscription.id }
      })
    }

    if (subscriptionTier) {
      await prisma.subscriptionTier.delete({
        where: { id: subscriptionTier.id }
      })
    }

    if (exercise) {
      await prisma.exercise.delete({
        where: { id: exercise.id }
      })
    }

    if (workout) {
      await prisma.workout.delete({
        where: { id: workout.id }
      })
    }

    if (week) {
      await prisma.week.delete({
        where: { id: week.id }
      })
    }

    if (trainingPlan) {
      await prisma.trainingPlan.delete({
        where: { id: trainingPlan.id }
      })
    }

    // Delete client profile
    await prisma.clientProfile.delete({
      where: { id: clientProfile.id }
    })

    // Delete client user
    await prisma.user.delete({
      where: { id: clientUser.id }
    })

    // Delete trainer profile
    await prisma.trainerProfile.delete({
      where: { id: trainerProfile.id }
    })

    // Delete trainer user
    await prisma.user.delete({
      where: { id: trainerUser.id }
    })

    // Disconnect from the database
    await prisma.$disconnect()
  })

  // Test: Create and manage training plans
  describe('Training Plan Management', () => {
    it('should create a training plan', async () => {
      trainingPlan = await prisma.trainingPlan.create({
        data: {
          title: 'Test Training Plan',
          description: 'A test training plan for automated testing',
          type: 'strength',
          difficulty: 'intermediate',
          trainerId: trainerProfile.id
        }
      })

      expect(trainingPlan).toBeDefined()
      expect(trainingPlan.title).toBe('Test Training Plan')
      expect(trainingPlan.trainerId).toBe(trainerProfile.id)
    })

    it('should add a week to the training plan', async () => {
      week = await prisma.week.create({
        data: {
          number: 1,
          trainingPlanId: trainingPlan.id
        }
      })

      expect(week).toBeDefined()
      expect(week.number).toBe(1)
      expect(week.trainingPlanId).toBe(trainingPlan.id)
    })

    it('should add a workout to the week', async () => {
      workout = await prisma.workout.create({
        data: {
          name: 'Test Workout',
          description: 'A test workout for automated testing',
          day: 1,
          weekId: week.id,
          trainerId: trainerProfile.id
        }
      })

      expect(workout).toBeDefined()
      expect(workout.name).toBe('Test Workout')
      expect(workout.weekId).toBe(week.id)
      expect(workout.trainerId).toBe(trainerProfile.id)
    })

    it('should add an exercise to the workout', async () => {
      exercise = await prisma.exercise.create({
        data: {
          name: 'Push-ups',
          description: 'Standard push-ups',
          sets: 3,
          reps: 10,
          restTime: 60,
          order: 1,
          workoutId: workout.id,
          createdById: trainerUser.id
        }
      })

      expect(exercise).toBeDefined()
      expect(exercise.name).toBe('Push-ups')
      expect(exercise.workoutId).toBe(workout.id)
    })

    it('should update the training plan', async () => {
      const updatedTrainingPlan = await prisma.trainingPlan.update({
        where: { id: trainingPlan.id },
        data: {
          title: 'Updated Training Plan',
          description: 'An updated test training plan'
        }
      })

      expect(updatedTrainingPlan).toBeDefined()
      expect(updatedTrainingPlan.title).toBe('Updated Training Plan')
      expect(updatedTrainingPlan.description).toBe('An updated test training plan')

      // Update our reference
      trainingPlan = updatedTrainingPlan
    })
  })

  // Test: View and manage clients
  describe('Client Management', () => {
    it('should retrieve the trainer\'s clients', async () => {
      const clients = await prisma.clientProfile.findMany({
        where: { assignedTrainerId: trainerProfile.id },
        include: { user: true }
      })

      expect(clients).toBeDefined()
      expect(clients.length).toBeGreaterThan(0)
      expect(clients.some(client => client.userId === clientUser.id)).toBe(true)
    })

    it('should retrieve a specific client\'s details', async () => {
      const client = await prisma.clientProfile.findUnique({
        where: { id: clientProfile.id },
        include: { user: true }
      })

      expect(client).toBeDefined()
      expect(client?.userId).toBe(clientUser.id)
      expect(client?.assignedTrainerId).toBe(trainerProfile.id)
    })

    it('should assign a training plan to a client', async () => {
      const clientPlan = await prisma.clientPlan.create({
        data: {
          clientProfileId: clientProfile.id,
          trainingPlanId: trainingPlan.id,
          assignedAt: new Date()
        }
      })

      expect(clientPlan).toBeDefined()
      expect(clientPlan.clientProfileId).toBe(clientProfile.id)
      expect(clientPlan.trainingPlanId).toBe(trainingPlan.id)

      // Clean up
      await prisma.clientPlan.delete({
        where: { id: clientPlan.id }
      })
    })
  })

  // Test: Create and manage subscription tiers
  describe('Subscription Management', () => {
    it('should create a subscription tier', async () => {
      subscriptionTier = await prisma.subscriptionTier.create({
        data: {
          name: 'Test Tier',
          price: 29.99,
          description: 'A test subscription tier',
          features: ['Feature 1', 'Feature 2', 'Feature 3'],
          trainerId: trainerProfile.id
        }
      })

      expect(subscriptionTier).toBeDefined()
      expect(subscriptionTier.name).toBe('Test Tier')
      expect(subscriptionTier.price).toBe(29.99)
      expect(subscriptionTier.trainerId).toBe(trainerProfile.id)
    })

    it('should update a subscription tier', async () => {
      const updatedTier = await prisma.subscriptionTier.update({
        where: { id: subscriptionTier.id },
        data: {
          price: 39.99,
          features: ['Feature 1', 'Feature 2', 'Feature 3', 'Feature 4']
        }
      })

      expect(updatedTier).toBeDefined()
      expect(updatedTier.price).toBe(39.99)
      expect(updatedTier.features).toHaveLength(4)

      // Update our reference
      subscriptionTier = updatedTier
    })

    it('should create a subscription for a client', async () => {
      subscription = await prisma.subscription.create({
        data: {
          clientProfileId: clientProfile.id,
          tierId: subscriptionTier.id,
          status: 'active',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
        }
      })

      expect(subscription).toBeDefined()
      expect(subscription.clientProfileId).toBe(clientProfile.id)
      expect(subscription.tierId).toBe(subscriptionTier.id)
      expect(subscription.status).toBe('active')
    })

    it('should retrieve active subscriptions', async () => {
      const activeSubscriptions = await prisma.subscription.findMany({
        where: {
          tierId: subscriptionTier.id,
          status: 'active'
        },
        include: {
          clientProfile: {
            include: {
              user: true
            }
          }
        }
      })

      expect(activeSubscriptions).toBeDefined()
      expect(activeSubscriptions.length).toBeGreaterThan(0)
      expect(activeSubscriptions.some(sub => sub.clientProfileId === clientProfile.id)).toBe(true)
    })
  })

  // Test: Complete workflow
  describe('Complete Trainer Workflow', () => {
    it('should verify the complete workflow is functional', async () => {
      // 1. Verify trainer can see their clients
      const clients = await prisma.clientProfile.findMany({
        where: { assignedTrainerId: trainerProfile.id }
      })
      expect(clients.length).toBeGreaterThan(0)

      // 2. Verify trainer can see their training plans
      const trainingPlans = await prisma.trainingPlan.findMany({
        where: { trainerId: trainerProfile.id }
      })
      expect(trainingPlans.length).toBeGreaterThan(0)

      // 3. Verify trainer can see their subscription tiers
      const subscriptionTiers = await prisma.subscriptionTier.findMany({
        where: { trainerId: trainerProfile.id }
      })
      expect(subscriptionTiers.length).toBeGreaterThan(0)

      // 4. Verify trainer can see active subscriptions
      const activeSubscriptions = await prisma.subscription.findMany({
        where: {
          tierId: subscriptionTier.id,
          status: 'active'
        }
      })
      expect(activeSubscriptions.length).toBeGreaterThan(0)

      // 5. Assign training plan to client
      const clientPlan = await prisma.clientPlan.create({
        data: {
          clientProfileId: clientProfile.id,
          trainingPlanId: trainingPlan.id,
          assignedAt: new Date()
        }
      })
      expect(clientPlan).toBeDefined()

      // 6. Verify client has the training plan assigned
      const clientWithPlans = await prisma.clientProfile.findUnique({
        where: { id: clientProfile.id },
        include: {
          trainingPlans: {
            include: {
              trainingPlan: true
            }
          }
        }
      })
      expect(clientWithPlans?.trainingPlans.length).toBeGreaterThan(0)
      expect(clientWithPlans?.trainingPlans.some(plan => plan.trainingPlanId === trainingPlan.id)).toBe(true)

      // Clean up
      await prisma.clientPlan.delete({
        where: { id: clientPlan.id }
      })
    })
  })
})
