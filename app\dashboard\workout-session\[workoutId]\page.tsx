"use client"

import {
  <PERSON>Left,
  CheckCircle,
  Timer,
  SkipForward,
  ChevronRight,
  ChevronLeft,
  PlayCircle,
  PauseCircle,
  RotateCcw
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import React, { useState, useEffect } from "react"
import { toast } from "sonner"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { VideoPlayer } from "@/components/video/video-player"
import { BottomNav } from "@/components/ui/mobile/bottom-nav"
import { SwipeContainer } from "@/components/ui/mobile/swipe-container"
import { 
  timerCompleteHaptic, 
  exerciseCompleteHaptic, 
  navigationHaptic 
} from "@/lib/utils/haptic"

interface WorkoutSessionProps {
  params: Promise<{
    workoutId: string
  }> | {
    workoutId: string
  }
}

export default function WorkoutSession({ params }: WorkoutSessionProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [workout, setWorkout] = useState<any>(null)
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0)
  const [timerActive, setTimerActive] = useState(false)
  const [timerCount, setTimerCount] = useState(0)
  const [completedExercises, setCompletedExercises] = useState<string[]>([])

  // Unwrap params with React.use() if it's a Promise
  const unwrappedParams = params instanceof Promise ? React.use(params) : params

  useEffect(() => {
    const fetchWorkoutData = async () => {
      try {
        setIsLoading(true)

        // Check if this is a product ID (from digital products)
        const isProductWorkout = unwrappedParams.workoutId.startsWith('prod_')

        if (isProductWorkout) {
          // Fetch product workout data
          const response = await fetch(`/api/user/purchases?productId=${unwrappedParams.workoutId}`)

          if (response.ok) {
            const data = await response.json()
            const product = data.products?.[0]

            if (product && product.productType === 'program') {
              // Create workout from product
              const workoutData = {
                id: product.id,
                title: product.title,
                description: product.description,
                planId: 'digital-product',
                exercises: [
                  {
                    id: "ex1",
                    name: "Warm-up",
                    sets: 1,
                    reps: "5 minutes",
                    rest: 60,
                    notes: "Light cardio and dynamic stretching"
                  },
                  {
                    id: "ex2",
                    name: "Squats",
                    sets: 3,
                    reps: "12-15",
                    rest: 90,
                    notes: "Focus on form and depth"
                  },
                  {
                    id: "ex3",
                    name: "Push-ups",
                    sets: 3,
                    reps: "10-12",
                    rest: 60,
                    notes: "Keep core tight throughout movement"
                  },
                  {
                    id: "ex4",
                    name: "Plank",
                    sets: 3,
                    reps: "30-45 seconds",
                    rest: 45,
                    notes: "Maintain neutral spine position"
                  },
                  {
                    id: "ex5",
                    name: "Lunges",
                    sets: 3,
                    reps: "10 each leg",
                    rest: 60,
                    notes: "Step forward with control"
                  },
                  {
                    id: "ex6",
                    name: "Cool Down",
                    sets: 1,
                    reps: "5 minutes",
                    rest: 0,
                    notes: "Static stretching for major muscle groups"
                  }
                ]
              }

              setWorkout(workoutData)
              setIsLoading(false)
              return
            }
          }
        }

        // If not a product or product fetch failed, try regular workout
        // Fetch the training plan template
        try {
          const response = await fetch(`/api/training-plan-templates/${unwrappedParams.workoutId}`)

          if (response.ok) {
            const planData = await response.json()
            console.log('Fetched training plan:', planData)

            if (planData) {
              // Extract the first day's exercises from the first week
              let exercises = []

              if (planData.weeks) {
                // Handle different weeks data structures
                const weeksData = typeof planData.weeks === 'string'
                  ? JSON.parse(planData.weeks)
                  : planData.weeks

                if (Array.isArray(weeksData) && weeksData.length > 0) {
                  // New format with array of weeks
                  const firstWeek = weeksData[0]
                  if (firstWeek.dailyWorkouts && firstWeek.dailyWorkouts.length > 0) {
                    exercises = firstWeek.dailyWorkouts[0].exercises || []
                  }
                } else if (weeksData.week1) {
                  // Old format with week1, week2, etc.
                  const firstWeek = weeksData.week1
                  if (firstWeek.workouts && firstWeek.workouts.length > 0) {
                    exercises = firstWeek.workouts[0].exercises || []
                  }
                }
              }

              // Create workout object
              const workoutData = {
                id: planData.id,
                title: planData.title,
                description: planData.description || 'Your personalized workout',
                planId: planData.id,
                exercises: exercises.map((ex: any, index: number) => ({
                  id: ex.id || `ex-${index}`,
                  name: ex.name,
                  sets: ex.sets || 3,
                  reps: ex.reps || '10',
                  rest: ex.restTime || 60,
                  notes: ex.description || ''
                }))
              }

              // If no exercises were found, add some default ones
              if (workoutData.exercises.length === 0) {
                workoutData.exercises = [
                  { id: "ex1", name: "Warm-up", sets: 1, reps: "5 minutes", rest: 60, notes: "Light cardio and dynamic stretching" },
                  { id: "ex2", name: "Squats", sets: 3, reps: "12-15", rest: 90, notes: "Focus on form and depth" },
                  { id: "ex3", name: "Push-ups", sets: 3, reps: "10-12", rest: 60, notes: "Keep core tight throughout movement" },
                  { id: "ex4", name: "Plank", sets: 3, reps: "30-45 seconds", rest: 45, notes: "Maintain neutral spine position" },
                  { id: "ex5", name: "Cool Down", sets: 1, reps: "5 minutes", rest: 0, notes: "Static stretching for major muscle groups" }
                ]
              }

              setWorkout(workoutData)
            }
          } else {
            console.error('Failed to fetch training plan:', response.statusText)
          }
        } catch (error) {
          console.error('Error fetching training plan:', error)
        }
      } catch (error) {
        console.error('Error fetching workout:', error);
        toast('Failed to load workout details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchWorkoutData();
  }, [unwrappedParams.workoutId]);

  // Timer effect for rest periods
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (timerActive && timerCount > 0) {
      interval = setInterval(() => {
        setTimerCount(prev => prev - 1);
      }, 1000);
    } else if (timerCount === 0) {
      setTimerActive(false);
      if (completedExercises.length > 0) {
        toast.success("Rest complete! Continue to next exercise.");
        timerCompleteHaptic(); // Provide haptic feedback when timer completes
      }
    }

    return () => clearInterval(interval);
  }, [timerActive, timerCount, completedExercises.length]);

  const startRestTimer = (seconds: number) => {
    setTimerCount(seconds);
    setTimerActive(true);
    toast.info(`Rest timer started for ${seconds} seconds`);
  };

  const toggleTimer = () => {
    setTimerActive(prev => !prev);
  };

  const resetTimer = () => {
    const currentExercise = workout?.exercises[currentExerciseIndex];
    if (currentExercise) {
      setTimerCount(currentExercise.rest);
      setTimerActive(false);
    }
  };

  const markExerciseComplete = async (exerciseId: string) => {
    if (!workout) return;

    if (!completedExercises.includes(exerciseId)) {
      setCompletedExercises(prev => [...prev, exerciseId]);

      exerciseCompleteHaptic();

      // If there's a next exercise, start the rest timer
      const currentExercise = workout.exercises[currentExerciseIndex];
      if (currentExercise && currentExercise.rest) {
        startRestTimer(currentExercise.rest);
      }

      // Check if this is a product workout
      const isProductWorkout = unwrappedParams.workoutId.startsWith('prod_');

      if (isProductWorkout) {
        // Calculate new progress percentage
        const newProgressPercentage = Math.round(
          ((completedExercises.length + 1) / workout.exercises.length) * 100
        );

        try {
          // Update progress in the database for product workouts
          await fetch('/api/user/product-progress', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              productId: workout.id,
              progressPercentage: newProgressPercentage,
              completedSections: {
                completedExercises: [...completedExercises, exerciseId],
                lastCompletedIndex: currentExerciseIndex
              }
            }),
          });
        } catch (error) {
          console.error('Error updating progress:', error);
        }
      }
    } else {
      setCompletedExercises(prev => prev.filter(id => id !== exerciseId));
    }
  };

  const goToNextExercise = () => {
    if (currentExerciseIndex < (workout?.exercises.length || 0) - 1) {
      setCurrentExerciseIndex(prev => prev + 1);
      setTimerActive(false); // Stop any active timer
      navigationHaptic(); // Provide haptic feedback for navigation
    }
  };

  const goToPreviousExercise = () => {
    if (currentExerciseIndex > 0) {
      setCurrentExerciseIndex(prev => prev - 1);
      setTimerActive(false); // Stop any active timer
      navigationHaptic(); // Provide haptic feedback for navigation
    }
  };

  const completeWorkout = async () => {
    try {
      toast.success("Great job! Workout completed successfully.");

      // Check if this is a product workout
      const isProductWorkout = unwrappedParams.workoutId.startsWith('prod_');

      if (isProductWorkout && workout) {
        // Update progress in the database for product workouts
        await fetch('/api/user/product-progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            productId: workout.id,
            progressPercentage: 100,
            completedSections: {
              completedExercises: workout.exercises.map((ex: { id: string }) => ex.id),
              completed: true,
              completedAt: new Date().toISOString()
            }
          }),
        });

        // Redirect to dashboard for product workouts
        router.push('/dashboard/dashboard');
      } else {
        // For regular workouts, redirect to the training plan
        router.push(`/dashboard/training/${workout?.planId}`);
      }
    } catch (error) {
      console.error("Error completing workout:", error);
      toast.error("Failed to save workout completion");
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  if (isLoading) {
    return (
      <div className="container py-10 flex justify-center">
        <div className="animate-spin h-8 w-8 border-t-2 border-primary rounded-full"></div>
      </div>
    );
  }

  if (!workout) {
    return (
      <div className="container py-10">
        <Button variant="outline" asChild className="mb-8">
          <Link href="/dashboard/workouts/current">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to My Workouts
          </Link>
        </Button>

        <Card className="mx-auto max-w-md text-center p-6">
          <CardContent className="pt-6">
            <h2 className="text-2xl font-bold mb-2">Workout Not Found</h2>
            <p className="text-muted-foreground mb-6">
              The workout you&apos;re looking for doesn&apos;t exist or you don&apos;t have access to it.
            </p>
            <Button asChild>
              <Link href="/dashboard/workouts/current">
                View My Workouts
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentExercise = workout.exercises[currentExerciseIndex];
  const workoutProgress = Math.round((completedExercises.length / workout.exercises.length) * 100);

  return (
    <SwipeContainer
      onSwipeLeft={currentExerciseIndex < workout.exercises.length - 1 ? goToNextExercise : undefined}
      onSwipeRight={currentExerciseIndex > 0 ? goToPreviousExercise : undefined}
      className="container max-w-4xl py-10 pb-20" // Added bottom padding for mobile nav
    >
      {/* Header Section */}
      <div className="flex justify-between items-center mb-8">
        <Button variant="outline" asChild>
          <Link href={`/dashboard/training/${workout.planId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Cancel Workout
          </Link>
        </Button>

        {completedExercises.length === workout.exercises.length ? (
          <Button className="btn-success" onClick={completeWorkout}>
            <CheckCircle className="mr-2 h-4 w-4" />
            Finish Workout
          </Button>
        ) : (
          <div className="text-sm text-muted-foreground">
            {completedExercises.length} of {workout.exercises.length} exercises completed
          </div>
        )}
      </div>

      {/* Workout Progress Card */}
      <Card className="card-hover bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 mb-8">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">{workout.title}</CardTitle>
              <CardDescription>Workout progress</CardDescription>
            </div>
            <Badge className="bg-gradient-primary text-white">
              {workoutProgress}%
            </Badge>
          </div>
          <Progress value={workoutProgress} className="progress-bar" />
        </CardHeader>
      </Card>

      {/* Current Exercise Card */}
      <Card className="card-hover mb-8">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl">
                Exercise {currentExerciseIndex + 1} of {workout.exercises.length}
              </CardTitle>
              <CardDescription>Current exercise details</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="icon"
                onClick={goToPreviousExercise}
                disabled={currentExerciseIndex === 0}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={goToNextExercise}
                disabled={currentExerciseIndex === workout.exercises.length - 1}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-4">
            <div>
              <h3 className="text-xl font-bold">{currentExercise.name}</h3>
              <div className="grid grid-cols-3 gap-2 mt-3">
                <div className="bg-muted rounded-md p-3 text-center">
                  <div className="text-lg font-bold">{currentExercise.sets}</div>
                  <div className="text-xs text-muted-foreground">Sets</div>
                </div>
                <div className="bg-muted rounded-md p-3 text-center">
                  <div className="text-lg font-bold">{currentExercise.reps}</div>
                  <div className="text-xs text-muted-foreground">Reps</div>
                </div>
                <div className="bg-muted rounded-md p-3 text-center">
                  <div className="text-lg font-bold">{currentExercise.rest}s</div>
                  <div className="text-xs text-muted-foreground">Rest</div>
                </div>
              </div>
            </div>

            <div className="flex justify-center">
              <Button
                className="w-full btn-primary"
                onClick={() => markExerciseComplete(currentExercise.id)}
                variant={completedExercises.includes(currentExercise.id) ? "outline" : "default"}
              >
                {completedExercises.includes(currentExercise.id) ? (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Completed
                  </>
                ) : (
                  "Mark as Completed"
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rest Timer Card */}
      <Card className={`card-hover ${timerActive || timerCount > 0 ? 'border-primary' : ''}`}>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center">
            <Timer className="mr-2 h-5 w-5" />
            Rest Timer
          </CardTitle>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="text-center my-4">
            <div className="text-4xl font-bold mb-2">{formatTime(timerCount)}</div>
            <div className="text-sm text-muted-foreground">
              {timerActive ? "Rest in progress" : "Timer paused"}
            </div>
          </div>

          <div className="flex justify-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={toggleTimer}
              disabled={timerCount === 0}
            >
              {timerActive ? (
                <PauseCircle className="h-5 w-5" />
              ) : (
                <PlayCircle className="h-5 w-5" />
              )}
            </Button>

            <Button
              variant="outline"
              size="icon"
              onClick={resetTimer}
              disabled={!currentExercise?.rest}
            >
              <RotateCcw className="h-5 w-5" />
            </Button>

            <Button
              variant="outline"
              onClick={() => startRestTimer(currentExercise.rest)}
              disabled={!currentExercise?.rest}
            >
              Start {currentExercise?.rest}s Rest
            </Button>

            <Button
              variant="outline"
              onClick={goToNextExercise}
              disabled={currentExerciseIndex === workout.exercises.length - 1}
            >
              <SkipForward className="mr-2 h-4 w-4" />
              Skip Rest
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Navigation Footer */}
      <div className="flex justify-between mt-8">
        <Button
          variant="outline"
          onClick={goToPreviousExercise}
          disabled={currentExerciseIndex === 0}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous Exercise
        </Button>

        {currentExerciseIndex < workout.exercises.length - 1 ? (
          <Button
            className="btn-primary"
            onClick={goToNextExercise}
            disabled={currentExerciseIndex === workout.exercises.length - 1}
          >
            Next Exercise
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        ) : (
          <Button className="btn-success" onClick={completeWorkout}>
            <CheckCircle className="mr-2 h-4 w-4" />
            Finish Workout
          </Button>
        )}
      </div>
      
      {/* Mobile Bottom Navigation */}
      <BottomNav 
        showWorkoutControls={true}
        onPrevious={currentExerciseIndex > 0 ? goToPreviousExercise : undefined}
        onNext={currentExerciseIndex < workout.exercises.length - 1 ? goToNextExercise : undefined}
        onComplete={completedExercises.length === workout.exercises.length ? completeWorkout : undefined}
        isLastExercise={currentExerciseIndex === workout.exercises.length - 1}
      />
    </SwipeContainer>
  )
}
