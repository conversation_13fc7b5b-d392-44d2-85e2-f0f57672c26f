"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON><PERSON>, Book<PERSON>pen, FileText, Play } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"

interface PurchasedProduct {
  id: string
  title: string
  description: string
  thumbnailUrl?: string
  fileUrl?: string
  productType: string
  purchaseDate: string
  progress?: number
}

export function PurchasedProducts() {
  const [products, setProducts] = useState<PurchasedProduct[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    const fetchPurchasedProducts = async () => {
      try {
        setIsLoading(true)

        // Try to fetch from API first
        const response = await fetch('/api/user/purchases')

        if (response.ok) {
          const data = await response.json()
          const apiProducts = data.products || []

          if (apiProducts.length > 0) {
            setProducts(apiProducts)
            // Save to localStorage for offline access
            localStorage.setItem('purchases', JSON.stringify(apiProducts))
            return
          }
        }

        // If API fails or returns no products, check localStorage
        const savedProducts = localStorage.getItem('purchases')

        if (savedProducts) {
          try {
            const parsedProducts = JSON.parse(savedProducts)
            setProducts(parsedProducts)
            return
          } catch (parseError) {
            console.error('Error parsing saved products:', parseError)
            // Continue to fallback if parsing fails
          }
        }

        // Fallback for development: create sample products
        if (process.env.NODE_ENV === 'development') {
          const devProducts = [
            {
              id: 'prod_dev_1',
              title: 'Strength Training Fundamentals',
              description: 'A comprehensive guide to strength training basics',
              thumbnailUrl: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438',
              fileUrl: '/sample/strength-guide.pdf',
              productType: 'ebook',
              purchaseDate: new Date().toISOString(),
              progress: 25
            },
            {
              id: 'prod_dev_2',
              title: '12-Week Muscle Building Program',
              description: 'Complete workout program for muscle growth',
              thumbnailUrl: 'https://images.unsplash.com/photo-1583454110551-21f2fa2afe61',
              fileUrl: '/sample/muscle-program.pdf',
              productType: 'program',
              purchaseDate: new Date().toISOString(),
              progress: 10
            }
          ]
          setProducts(devProducts)

          // Save to localStorage for persistence
          localStorage.setItem('purchases', JSON.stringify(devProducts))
        }
      } catch (error) {
        console.error('Error fetching purchased products:', error)
        toast({
          title: 'Error',
          description: 'Failed to load your purchased products',
          variant: 'destructive'
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchPurchasedProducts()
  }, [toast])

  const startProgram = async (productId: string) => {
    try {
      // Show loading state
      toast({
        title: 'Starting program...',
        description: 'Preparing your workout session',
      })

      // Find the product
      const product = products.find(p => p.id === productId)
      if (!product) return

      // Update progress in the database
      const response = await fetch('/api/user/product-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          progressPercentage: Math.min((product.progress || 0) + 5, 100),
          completedSections: { lastSection: 'introduction' }
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update progress')
      }

      // Update progress in state
      const updatedProducts = products.map(product =>
        product.id === productId
          ? { ...product, progress: Math.min((product.progress || 0) + 5, 100) }
          : product
      )

      setProducts(updatedProducts)

      // Navigate to the workout session
      window.location.href = `/dashboard/workout-session/${productId}`
    } catch (error) {
      console.error('Error starting program:', error)
      toast({
        title: 'Error',
        description: 'Failed to start the program',
        variant: 'destructive'
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center p-6">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-medium mb-2">No purchased products</h3>
          <p className="text-muted-foreground text-center mb-4">
            You haven't purchased any digital products yet.
          </p>
          <Button asChild>
            <Link href="/dashboard/shop">
              Browse Products
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {products.map((product) => (
          <Card key={product.id} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>{product.title}</CardTitle>
                  <CardDescription className="mt-1">
                    {product.productType === 'program' ? 'Training Program' : 'Digital Product'}
                  </CardDescription>
                </div>
                {product.thumbnailUrl && (
                  <div className="w-16 h-16 rounded-md overflow-hidden">
                    <img
                      src={product.thumbnailUrl}
                      alt={product.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                {product.description}
              </p>

              {product.productType === 'program' && (
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Progress</span>
                    <span className="font-medium">{product.progress || 0}%</span>
                  </div>
                  <Progress value={product.progress || 0} className="h-2" />
                </div>
              )}
            </CardContent>
            <CardFooter className="bg-muted/50 flex gap-2 border-t">
              <Button variant="outline" className="flex-1" asChild>
                <Link href={product.fileUrl || '#'}>
                  <BookOpen className="mr-2 h-4 w-4" />
                  View Content
                </Link>
              </Button>

              {product.productType === 'program' && (
                <Button className="flex-1" onClick={() => startProgram(product.id)}>
                  <Play className="mr-2 h-4 w-4" />
                  Start Workout
                </Button>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
