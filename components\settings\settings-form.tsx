"use client"

import { Upload, <PERSON><PERSON>2, <PERSON>, Trash2, ExternalLink, AlertCircle } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

interface SocialLinks {
  twitter?: string
  instagram?: string
  youtube?: string
  linkedin?: string
  website?: string
}

interface User {
  id: string
  fullName: string
  email: string
  bio: string | null
  role: string
  avatarUrl: string | null
  socialLinks: SocialLinks | null
}

interface SettingsFormProps {
  user: User
}

interface FormErrors {
  fullName?: string
  bio?: string
  socialLinks?: {
    [key: string]: string
  }
}

export function SettingsForm({ user }: SettingsFormProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [errors, setErrors] = useState<FormErrors>({})

  const [formData, setFormData] = useState({
    fullName: user.fullName,
    bio: user.bio || "",
    avatarUrl: user.avatarUrl || "",
    socialLinks: user.socialLinks || {
      twitter: "",
      instagram: "",
      youtube: "",
      linkedin: "",
      website: ""
    }
  })

  // Validate social media links
  const validateSocialLinks = (links: SocialLinks) => {
    const errors: { [key: string]: string } = {}
    const urlRegex = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/
    const twitterRegex = /^@?(\w){1,15}$/
    const instagramRegex = /^@?([A-Za-z0-9._]){1,30}$/

    Object.entries(links).forEach(([key, value]) => {
      if (value) {
        if (key === "twitter" && !value.startsWith("http")) {
          if (!twitterRegex.test(value)) {
            errors[key] = "Invalid Twitter username"
          }
        } else if (key === "instagram" && !value.startsWith("http")) {
          if (!instagramRegex.test(value)) {
            errors[key] = "Invalid Instagram username"
          }
        } else if (!urlRegex.test(value)) {
          errors[key] = "Invalid URL format"
        }
      }
    })

    return errors
  }

  // Validate form data
  const validateForm = () => {
    const newErrors: FormErrors = {}

    if (!formData.fullName.trim()) {
      newErrors.fullName = "Full name is required"
    }

    if (formData.bio && formData.bio.length > 500) {
      newErrors.bio = "Bio must be less than 500 characters"
    }

    const socialLinksErrors = validateSocialLinks(formData.socialLinks)
    if (Object.keys(socialLinksErrors).length > 0) {
      newErrors.socialLinks = socialLinksErrors
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.[0]) return

    setIsUploading(true)
    const file = e.target.files[0]

    try {
      const formData = new FormData()
      formData.append("file", file)

      const response = await fetch("/api/settings/upload-avatar", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Failed to upload image")
      }

      const data = await response.json()
      setFormData(prev => ({ ...prev, avatarUrl: data.url }))
      
      toast({
        title: "Success",
        description: "Profile picture updated successfully",
      })
    } catch (error) {
      console.error("Error uploading image:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to upload profile picture",
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fix the errors in the form",
      })
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch("/api/settings", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const data = await response.json()
        if (data.errors) {
          setErrors(data.errors)
          throw new Error("Validation failed")
        }
        throw new Error("Failed to update settings")
      }

      toast({
        title: "Success",
        description: "Settings updated successfully",
      })
      router.refresh()
    } catch (error) {
      console.error("Error updating settings:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update settings",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-8 max-w-4xl mx-auto">
      {Object.keys(errors).length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Please fix the following errors:
            <ul className="list-disc list-inside mt-2">
              {errors.fullName && (
                <li>{errors.fullName}</li>
              )}
              {errors.bio && (
                <li>{errors.bio}</li>
              )}
              {errors.socialLinks && Object.entries(errors.socialLinks).map(([key, error]) => (
                <li key={key}>{`${key.charAt(0).toUpperCase() + key.slice(1)}: ${error}`}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      <Card className="border-none shadow-lg">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Profile Settings</CardTitle>
          <CardDescription className="text-base">
            Customize your profile information and appearance.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="space-y-4">
              <Label className="text-base font-semibold">Profile Picture</Label>
              <div className="flex items-center space-x-6">
                <div className="relative group">
                  <Avatar className="h-24 w-24 ring-2 ring-offset-2 ring-offset-background transition-all duration-300 group-hover:ring-primary">
                    <AvatarImage src={formData.avatarUrl || ""} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/30">
                      {user.fullName[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute inset-0 flex items-center justify-center bg-black/60 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                    <Camera className="h-6 w-6" />
                  </div>
                </div>
                <div className="flex flex-col space-y-3">
                  <Label htmlFor="avatar" className="cursor-pointer">
                    <div className={cn(
                      "flex items-center space-x-2 rounded-md border-2 border-dashed px-4 py-2 transition-colors",
                      "hover:border-primary hover:bg-primary/5",
                      isUploading && "opacity-50 cursor-not-allowed"
                    )}>
                      {isUploading ? (
                        <Loader2 className="h-5 w-5 animate-spin" />
                      ) : (
                        <Upload className="h-5 w-5" />
                      )}
                      <span className="font-medium">
                        {isUploading ? "Uploading..." : "Upload Picture"}
                      </span>
                    </div>
                    <Input
                      id="avatar"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleImageUpload}
                      disabled={isUploading}
                    />
                  </Label>
                  {formData.avatarUrl && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setFormData(prev => ({ ...prev, avatarUrl: "" }))}
                      className="text-destructive hover:text-destructive hover:bg-destructive/10 border-none"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Remove Picture
                    </Button>
                  )}
                </div>
              </div>
            </div>

            <div className="grid gap-6 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="fullName" className="text-base font-semibold">
                  Full Name
                  <span className="text-destructive ml-1">*</span>
                </Label>
                <Input
                  id="fullName"
                  value={formData.fullName}
                  onChange={(e) => {
                    setFormData({ ...formData, fullName: e.target.value })
                    setErrors({ ...errors, fullName: undefined })
                  }}
                  className={cn(
                    "transition-all duration-300 focus:ring-2 focus:ring-primary",
                    errors.fullName && "border-destructive"
                  )}
                  required
                />
                {errors.fullName && (
                  <p className="text-sm text-destructive">{errors.fullName}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-base font-semibold">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={user.email}
                  disabled
                  className="bg-muted/50"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="bio" className="text-base font-semibold">Bio</Label>
              <Textarea
                id="bio"
                value={formData.bio}
                onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                placeholder="Tell us about yourself..."
                className="min-h-[100px] transition-all duration-300 focus:ring-2 focus:ring-primary"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-base font-semibold">Account Type</Label>
              <Input
                value={user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                disabled
                className="bg-muted/50"
              />
            </div>

            <Button 
              type="submit" 
              disabled={isLoading}
              className="w-full sm:w-auto"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving Changes...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card className="border-none shadow-lg">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Social Media Links</CardTitle>
          <CardDescription className="text-base">
            Connect your social profiles to expand your reach.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid gap-6 sm:grid-cols-2">
              {Object.entries({
                twitter: "Twitter",
                instagram: "Instagram",
                youtube: "YouTube",
                linkedin: "LinkedIn"
              }).map(([key, label]) => (
                <div key={key} className="space-y-2">
                  <Label htmlFor={key} className="text-base font-semibold inline-flex items-center">
                    {label}
                    <ExternalLink className="ml-1 h-4 w-4 text-muted-foreground" />
                  </Label>
                  <Input
                    id={key}
                    placeholder="@username or profile URL"
                    value={formData.socialLinks[key as keyof typeof formData.socialLinks]}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        socialLinks: {
                          ...formData.socialLinks,
                          [key]: e.target.value,
                        },
                      })
                    }
                    className="transition-all duration-300 focus:ring-2 focus:ring-primary"
                  />
                </div>
              ))}
            </div>

            <div className="space-y-2">
              <Label htmlFor="website" className="text-base font-semibold inline-flex items-center">
                Personal Website
                <ExternalLink className="ml-1 h-4 w-4 text-muted-foreground" />
              </Label>
              <Input
                id="website"
                placeholder="https://"
                value={formData.socialLinks.website}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    socialLinks: {
                      ...formData.socialLinks,
                      website: e.target.value,
                    },
                  })
                }
                className="transition-all duration-300 focus:ring-2 focus:ring-primary"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-none shadow-lg bg-destructive/5">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-destructive">Danger Zone</CardTitle>
          <CardDescription className="text-base">
            Permanent account deletion and data removal.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            variant="destructive"
            onClick={() => router.push("/dashboard/settings/delete-account")}
            className="w-full sm:w-auto hover:bg-destructive/90"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete Account
          </Button>
        </CardContent>
      </Card>
    </div>
  )
} 