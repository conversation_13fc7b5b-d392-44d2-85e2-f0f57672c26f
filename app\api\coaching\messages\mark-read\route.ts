import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }
    
    const userId = session.user.id
    const body = await request.json()
    const { conversationId } = body
    
    if (!conversationId) {
      return new NextResponse("Conversation ID is required", { status: 400 })
    }
    
    // Verify the user is part of this conversation
    const conversation = await prisma.conversation.findUnique({
      where: {
        id: conversationId,
      },
    })
    
    if (!conversation) {
      return new NextResponse("Conversation not found", { status: 404 })
    }
    
    if (conversation.user1Id !== userId && conversation.user2Id !== userId) {
      return new NextResponse("Unauthorized", { status: 403 })
    }
    
    // Mark all messages in this conversation as read
    await prisma.message.updateMany({
      where: {
        conversationId,
        receiverId: userId,
        read: false,
      },
      data: {
        read: true,
      },
    })
    
    // Also mark any related notifications as read
    await prisma.notification.updateMany({
      where: {
        userId,
        type: "message",
        sourceType: "message",
        read: false,
        actionLink: {
          contains: `conversationId=${conversationId}`,
        },
      },
      data: {
        read: true,
      },
    })
    
    return NextResponse.json({ success: true, message: "Messages marked as read" })
  } catch (error) {
    console.error("[COACHING_MESSAGES_MARK_READ_POST]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
