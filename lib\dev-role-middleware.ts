import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

/**
 * Middleware to apply dev role override to the session
 * This is only used in development mode and only when explicitly requested
 */
export async function applyDevRoleOverride(req: NextRequest, res: NextResponse) {
  // Only run in development mode
  if (process.env.NODE_ENV !== 'development') {
    return res;
  }

  try {
    // Check if role override is explicitly requested
    // This can be triggered by specific routes or a query parameter
    const url = new URL(req.url);
    const applyOverride = url.searchParams.get('apply_role_override') === 'true' ||
                          url.pathname.includes('/dev-login') ||
                          url.pathname.includes('/switch-role');

    // Get the role override from cookies
    const cookies = req.cookies;
    const roleOverride = cookies.get('dev_override_role')?.value;
    const premiumStatus = cookies.get('dev_premium_status')?.value;

    // If no role override or not explicitly requested, return the original response
    if (!roleOverride || !applyOverride) {
      return res;
    }

    // Get the session token
    const token = await getToken({
      req,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, return the original response
    if (!token) {
      return res;
    }

    // Log the role override
    console.log(`[Dev] Applying role override: ${roleOverride}`);

    // Add the role override to the request headers
    // This will be used by the NextAuth handler
    const newHeaders = new Headers(req.headers);
    newHeaders.set('x-dev-override-role', roleOverride);

    if (premiumStatus) {
      newHeaders.set('x-dev-premium-status', premiumStatus);
    }

    // Create a new request with the updated headers
    const newReq = new NextRequest(req.url, {
      headers: newHeaders,
      method: req.method,
      body: req.body,
      cache: req.cache,
      credentials: req.credentials,
      integrity: req.integrity,
      keepalive: req.keepalive,
      mode: req.mode,
      redirect: req.redirect,
      referrer: req.referrer,
      referrerPolicy: req.referrerPolicy,
    });

    return { req: newReq, res };
  } catch (error) {
    console.error('[Dev] Error applying role override:', error);
    return res;
  }
}
