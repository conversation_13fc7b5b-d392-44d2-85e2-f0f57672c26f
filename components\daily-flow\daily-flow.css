/* Daily Flow Styles */

/* Streak colors */
.streak-sleep {
  --streak-color: 238, 130, 238; /* violet */
  --streak-bg: 238, 130, 238, 0.1;
  --streak-border: 238, 130, 238, 0.2;
}

.streak-stress {
  --streak-color: 255, 165, 0; /* orange */
  --streak-bg: 255, 165, 0, 0.1;
  --streak-border: 255, 165, 0, 0.2;
}

.streak-coffee {
  --streak-color: 139, 69, 19; /* brown */
  --streak-bg: 139, 69, 19, 0.1;
  --streak-border: 139, 69, 19, 0.2;
}

.streak-nutrition {
  --streak-color: 0, 128, 0; /* green */
  --streak-bg: 0, 128, 0, 0.1;
  --streak-border: 0, 128, 0, 0.2;
}

.streak-measurement {
  --streak-color: 0, 0, 255; /* blue */
  --streak-bg: 0, 0, 255, 0.1;
  --streak-border: 0, 0, 255, 0.2;
}

/* Achievement badge colors */
.achievement-bronze {
  --badge-color: 205, 127, 50; /* bronze */
  --badge-bg: 205, 127, 50, 0.1;
  --badge-border: 205, 127, 50, 0.2;
}

.achievement-silver {
  --badge-color: 192, 192, 192; /* silver */
  --badge-bg: 192, 192, 192, 0.1;
  --badge-border: 192, 192, 192, 0.2;
}

.achievement-gold {
  --badge-color: 255, 215, 0; /* gold */
  --badge-bg: 255, 215, 0, 0.1;
  --badge-border: 255, 215, 0, 0.2;
}

/* Animations */
@keyframes pulse-streak {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.streak-pulse {
  animation: pulse-streak 2s infinite;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-in-out;
}

@keyframes bounce-in {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-bounce-in {
  animation: bounce-in 0.5s ease-in-out;
}

/* Logged indicator styles */
.logged-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #10b981;
  border: 2px solid white;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

@keyframes shine {
  0% {
    background-position: -100px;
  }
  40%, 100% {
    background-position: 300px;
  }
}

.achievement-shine {
  position: relative;
  overflow: hidden;
}

.achievement-shine::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.2) 50%,
    rgba(255,255,255,0) 100%
  );
  background-size: 200px 100%;
  background-repeat: no-repeat;
  background-position: -100px;
  animation: shine 3s infinite;
  pointer-events: none;
}

/* Metric cards */
.metric-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Slider customizations */
.metric-slider {
  height: 8px;
  border-radius: 4px;
}

.metric-slider-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: white;
  border: 2px solid var(--streak-color, hsl(var(--primary)));
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Progress component customizations */
.progress {
  position: relative;
  width: 100%;
  height: 0.5rem;
  background-color: hsl(var(--muted));
  border-radius: 9999px;
  overflow: hidden;
}

.progress-indicator {
  height: 100%;
  width: 0;
  background-color: hsl(var(--primary));
  transition: width 0.3s ease;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .daily-flow-grid {
    grid-template-columns: 1fr;
  }

  .metric-card {
    margin-bottom: 1rem;
  }
}
