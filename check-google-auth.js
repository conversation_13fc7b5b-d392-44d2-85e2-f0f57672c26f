// Simple script to check Google OAuth configuration
require('dotenv').config();

function checkGoogleAuth() {
  console.log('----- Google OAuth Configuration Check -----');

  // Check GOOGLE_CLIENT_ID
  if (!process.env.GOOGLE_CLIENT_ID) {
    console.error('❌ GOOGLE_CLIENT_ID is missing in your environment variables');
  } else {
    const clientId = process.env.GOOGLE_CLIENT_ID;
    console.log(`✅ GOOGLE_CLIENT_ID found: ${clientId.substring(0, 6)}...${clientId.substring(clientId.length - 4)}`);
  }

  // Check GOOGLE_CLIENT_SECRET
  if (!process.env.GOOGLE_CLIENT_SECRET) {
    console.error('❌ GOOGLE_CLIENT_SECRET is missing in your environment variables');
  } else {
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
    console.log(`✅ GOOGLE_CLIENT_SECRET found: ${clientSecret.substring(0, 6)}...`);
  }

  // Check NEXTAUTH_URL
  if (!process.env.NEXTAUTH_URL) {
    console.warn('⚠️ NEXTAUTH_URL is not set. This might be fine for development but is required for production.');
  } else {
    console.log(`✅ NEXTAUTH_URL found: ${process.env.NEXTAUTH_URL}`);
  }

  // Reminder about redirect URI
  console.log('\n----- Redirect URI Check -----');
  console.log('Make sure you have the following redirect URI configured in Google Cloud Console:');
  console.log(`- ${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/auth/callback/google`);
  
  console.log('\n----- Final Steps -----');
  console.log('1. Ensure your redirect URI is exactly as shown above in Google Cloud Console');
  console.log('2. Check that your app is not in testing mode if you\'re adding external users');
  console.log('3. Verify that scopes include email and profile at minimum');
}

checkGoogleAuth(); 