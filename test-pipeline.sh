#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

echo "===== Testing GitHub Actions Pipeline ====="
echo ""

echo "===== Step 1: Install dependencies ====="
npm ci --legacy-peer-deps

echo ""
echo "===== Step 2: Install Playwright browsers ====="
npx playwright install --with-deps chromium

echo ""
echo "===== Step 3: Run linting ====="
npm run lint

echo ""
echo "===== Step 4: Run unit tests ====="
npm test

echo ""
echo "===== Step 5: Start application in background ====="
npm run dev &
APP_PID=$!
echo "App started with PID: $APP_PID"
echo "Waiting for app to start..."
sleep 10  # Give the app time to start

echo ""
echo "===== Step 6: Run client journey E2E test ====="
npm run test:e2e:client

echo ""
echo "===== Step 7: Run premium client journey E2E test ====="
npm run test:e2e:premium

echo ""
echo "===== Step 8: Run client upgrade journey E2E test ====="
npm run test:e2e:upgrade

echo ""
echo "===== Step 9: Run trainer journey E2E test ====="
npm run test:e2e:trainer

echo ""
echo "===== Step 10: Run coaching journey E2E test ====="
npm run test:e2e:coaching

echo ""
echo "===== Step 11: Clean up ====="
kill $APP_PID
echo "App process terminated"

echo ""
echo "===== Pipeline test completed successfully! ====="
