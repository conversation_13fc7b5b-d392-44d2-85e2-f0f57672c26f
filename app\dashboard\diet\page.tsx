import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { DietPlanList } from "@/components/nutrition/diet-plan-list"
import { MealTracker } from "@/components/nutrition/meal-tracker"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export default async function DietPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user?.email) {
    redirect("/login")
  }

  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    include: {
      dietPlans: {
        include: {
          meals: true,
        },
      },
      progress: {
        orderBy: {
          createdAt: "desc",
        },
        take: 1,
      },
    },
  })

  if (!user) {
    redirect("/login")
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Nutrition</h1>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h2 className="text-2xl font-semibold mb-4">Diet Plans</h2>
          <DietPlanList plans={user.dietPlans} />
        </div>
        <div>
          <h2 className="text-2xl font-semibold mb-4">Meal Tracker</h2>
          <MealTracker userId={user.id} />
        </div>
      </div>
    </div>
  )
} 