import { <PERSON><PERSON><PERSON>, <PERSON>, Star } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { authOptions } from "@/lib/auth"

export default async function HireCoachPage() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    redirect("/login")
  }

  return (
    <div className="container py-6 space-y-8 max-w-5xl mx-auto">
      <div>
        <h1 className="text-3xl font-bold">Find Your Premium Coach</h1>
        <p className="text-muted-foreground">
          Connect with professional coaches for personalized training
        </p>
      </div>

      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search coaches..."
            className="pl-9"
          />
        </div>
        <Button variant="outline" rounded="lg">
          <Dumbbell className="mr-2 h-4 w-4" />
          Filter
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Example coach cards - replace with real data */}
        <Card className="overflow-hidden hover:shadow-lg hover:-translate-y-1 transition-all">
          <div className="relative h-32 w-full">
            <Image
              src="/placeholder.svg"
              alt="Coach background"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/70"></div>
            <div className="absolute bottom-4 left-4 right-4 flex items-center gap-3">
              <div className="relative h-16 w-16 rounded-full overflow-hidden avatar-ring">
                <Image
                  src="/placeholder.svg"
                  alt="Coach"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="text-white">
                <h3 className="font-bold text-lg">John Smith</h3>
                <p className="text-xs text-white/80">Strength & Conditioning</p>
              </div>
            </div>
          </div>
          <CardContent className="p-4">
            <div className="space-y-4">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm">4.9 (120 reviews)</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Certified personal trainer with 8+ years of experience. Specialized in strength training and athletic performance.
              </p>
              <div className="flex justify-between items-center">
                <span className="font-medium">$150/week</span>
                <Button size="sm" variant="premium" rounded="lg" asChild>
                  <Link href="/dashboard/coaching/1">View Profile</Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden hover:shadow-lg hover:-translate-y-1 transition-all">
          <div className="relative h-32 w-full">
            <Image
              src="/placeholder.svg"
              alt="Coach background"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/70"></div>
            <div className="absolute bottom-4 left-4 right-4 flex items-center gap-3">
              <div className="relative h-16 w-16 rounded-full overflow-hidden avatar-ring">
                <Image
                  src="/placeholder.svg"
                  alt="Coach"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="text-white">
                <h3 className="font-bold text-lg">Sarah Johnson</h3>
                <p className="text-xs text-white/80">CrossFit & HIIT</p>
              </div>
            </div>
          </div>
          <CardContent className="p-4">
            <div className="space-y-4">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm">4.8 (85 reviews)</span>
              </div>
              <p className="text-sm text-muted-foreground">
                CrossFit Level 3 trainer specializing in high-intensity interval training and functional fitness.
              </p>
              <div className="flex justify-between items-center">
                <span className="font-medium">$180/week</span>
                <Button size="sm" variant="premium" rounded="lg" asChild>
                  <Link href="/dashboard/coaching/2">View Profile</Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden hover:shadow-lg hover:-translate-y-1 transition-all">
          <div className="relative h-32 w-full">
            <Image
              src="/placeholder.svg"
              alt="Coach background"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/70"></div>
            <div className="absolute bottom-4 left-4 right-4 flex items-center gap-3">
              <div className="relative h-16 w-16 rounded-full overflow-hidden avatar-ring">
                <Image
                  src="/placeholder.svg"
                  alt="Coach"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="text-white">
                <h3 className="font-bold text-lg">Mike Wilson</h3>
                <p className="text-xs text-white/80">Bodybuilding & Nutrition</p>
              </div>
            </div>
          </div>
          <CardContent className="p-4">
            <div className="space-y-4">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm">4.9 (95 reviews)</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Former competitive bodybuilder with expertise in muscle building and nutrition planning.
              </p>
              <div className="flex justify-between items-center">
                <span className="font-medium">$200/week</span>
                <Button size="sm" variant="premium" rounded="lg" asChild>
                  <Link href="/dashboard/coaching/3">View Profile</Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 