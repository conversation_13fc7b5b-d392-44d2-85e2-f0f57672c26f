// API route handler for user purchases
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET(req: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Check if this is a dev premium user
    const cookies = req.headers.get('cookie') || '';
    const isPremiumDev = process.env.NODE_ENV === 'development' &&
                        cookies.includes('dev_premium_status=true');

    // For dev premium users, add some sample products
    if (isPremiumDev) {
      const sampleProducts = [
        {
          id: 'prod_dev_1',
          title: 'Strength Training Fundamentals',
          description: 'A comprehensive guide to strength training basics',
          thumbnailUrl: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438',
          fileUrl: '/sample/strength-guide.pdf',
          productType: 'ebook',
          purchaseDate: new Date().toISOString(),
          progress: 25
        },
        {
          id: 'prod_dev_2',
          title: '12-Week Muscle Building Program',
          description: 'Complete workout program for muscle growth',
          thumbnailUrl: 'https://images.unsplash.com/photo-1583454110551-21f2fa2afe61',
          fileUrl: '/sample/muscle-program.pdf',
          productType: 'program',
          purchaseDate: new Date().toISOString(),
          progress: 10
        }
      ];

      return NextResponse.json({
        products: sampleProducts
      });
    }

    // Return empty array for non-premium users
    return NextResponse.json({
      products: []
    });
  } catch (error) {
    console.error("[USER_PURCHASES_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
