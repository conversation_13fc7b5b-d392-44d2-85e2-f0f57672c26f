import { GET, DELETE } from '@/app/api/diet-plans/[id]/route'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { DietPlanService } from '@/lib/services/diet-plan-service'

// Mock next-auth
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    dietPlan: {
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
  },
}))

// Mock DietPlanService
jest.mock('@/lib/services/diet-plan-service', () => ({
  DietPlanService: {
    findById: jest.fn(),
    delete: jest.fn(),
  },
}))

describe('Diet Plan API', () => {
  const mockUser = {
    id: '1',
    role: 'admin',
  }

  beforeEach(() => {
    ;(getServerSession as jest.Mock).mockResolvedValue({ user: mockUser })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('GET', () => {
    it('returns 401 when user is not authenticated', async () => {
      ;(getServerSession as jest.Mock).mockResolvedValue(null)

      const request = new Request('http://localhost:3000/api/diet-plans/1', {
        method: 'GET',
      })
      const response = await GET(request, { params: { id: '1' } })
      expect(response?.status).toBe(401)
    })

    it('returns 401 when user is not the owner of the plan', async () => {
      const plan = {
        id: '1',
        athleteId: '2', // Different from mockUser.id
      }

      ;(DietPlanService.findById as jest.Mock).mockResolvedValue(plan)

      const request = new Request('http://localhost:3000/api/diet-plans/1', {
        method: 'GET',
      })
      const response = await GET(request, { params: { id: '1' } })
      expect(response?.status).toBe(401)
    })

    it('successfully retrieves a diet plan', async () => {
      const plan = {
        id: '1',
        athleteId: '1', // Same as mockUser.id
        title: 'Test Plan',
        description: 'Test Description',
        meals: [],
      }

      ;(DietPlanService.findById as jest.Mock).mockResolvedValue(plan)

      const request = new Request('http://localhost:3000/api/diet-plans/1', {
        method: 'GET',
      })
      const response = await GET(request, { params: { id: '1' } })
      expect(response?.status).toBe(200)
      expect(await response?.json()).toEqual(plan)
    })

    it('returns 404 when diet plan is not found', async () => {
      ;(DietPlanService.findById as jest.Mock).mockResolvedValue(null)

      const request = new Request('http://localhost:3000/api/diet-plans/1', {
        method: 'GET',
      })
      const response = await GET(request, { params: { id: '1' } })
      expect(response?.status).toBe(404)
    })

    it('handles database errors', async () => {
      ;(DietPlanService.findById as jest.Mock).mockRejectedValue(new Error('Database error'))

      const request = new Request('http://localhost:3000/api/diet-plans/1', {
        method: 'GET',
      })
      const response = await GET(request, { params: { id: '1' } })
      expect(response?.status).toBe(500)
    })
  })

  describe('DELETE', () => {
    it('returns 401 when user is not authenticated', async () => {
      ;(getServerSession as jest.Mock).mockResolvedValue(null)

      const request = new Request('http://localhost:3000/api/diet-plans/1', {
        method: 'DELETE',
      })
      const response = await DELETE(request, { params: { id: '1' } })
      expect(response?.status).toBe(401)
    })

    it('returns 401 when user is not the owner of the plan', async () => {
      const plan = {
        id: '1',
        athleteId: '2', // Different from mockUser.id
      }

      ;(DietPlanService.findById as jest.Mock).mockResolvedValue(plan)

      const request = new Request('http://localhost:3000/api/diet-plans/1', {
        method: 'DELETE',
      })
      const response = await DELETE(request, { params: { id: '1' } })
      expect(response?.status).toBe(401)
    })

    it('successfully deletes a diet plan', async () => {
      const plan = {
        id: '1',
        athleteId: '1', // Same as mockUser.id
      }

      ;(DietPlanService.findById as jest.Mock).mockResolvedValue(plan)
      ;(DietPlanService.delete as jest.Mock).mockResolvedValue(plan)

      const request = new Request('http://localhost:3000/api/diet-plans/1', {
        method: 'DELETE',
      })
      const response = await DELETE(request, { params: { id: '1' } })
      expect(response?.status).toBe(204)
    })

    it('returns 404 when diet plan is not found', async () => {
      ;(DietPlanService.findById as jest.Mock).mockResolvedValue(null)

      const request = new Request('http://localhost:3000/api/diet-plans/1', {
        method: 'DELETE',
      })
      const response = await DELETE(request, { params: { id: '1' } })
      expect(response?.status).toBe(404)
    })

    it('handles database errors', async () => {
      const plan = {
        id: '1',
        athleteId: '1',
      }

      ;(DietPlanService.findById as jest.Mock).mockResolvedValue(plan)
      ;(DietPlanService.delete as jest.Mock).mockRejectedValue(new Error('Database error'))

      const request = new Request('http://localhost:3000/api/diet-plans/1', {
        method: 'DELETE',
      })
      const response = await DELETE(request, { params: { id: '1' } })
      expect(response?.status).toBe(500)
    })
  })
})