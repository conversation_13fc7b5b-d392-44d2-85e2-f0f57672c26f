'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { CalendlySettings } from '@/components/calendly/calendly-settings';
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, Calendar, Check, Info } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface CalendlySettingsData {
  calendlyUrl?: string;
  calendlyUserId?: string;
}

export default function TrainerCalendlyPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [settings, setSettings] = useState<CalendlySettingsData>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/calendly?action=my-settings');
        
        if (!response.ok) {
          throw new Error('Failed to fetch Calendly settings');
        }
        
        const data = await response.json();
        
        if (data.user?.calendlyUserId) {
          setSettings({
            calendlyUserId: data.user.calendlyUserId,
            calendlyUrl: `https://calendly.com/${data.user.calendlyUserId}`,
          });
        }
      } catch (error) {
        console.error('Error fetching Calendly settings:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load your Calendly settings. Please try again later.',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, [toast]);

  const handleSaveSettings = async (data: CalendlySettingsData) => {
    try {
      const response = await fetch('/api/calendly?action=update-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save Calendly settings');
      }
      
      const responseData = await response.json();
      
      setSettings({
        calendlyUserId: responseData.user.calendlyUserId,
        calendlyUrl: data.calendlyUrl,
      });
      
      toast({
        title: 'Settings saved',
        description: 'Your Calendly integration has been set up successfully.',
      });
    } catch (error) {
      console.error('Error saving Calendly settings:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save your Calendly settings. Please try again later.',
      });
      throw error; // Re-throw to be caught by the component
    }
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      </div>
      
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Calendly Integration</h1>
        <p className="text-muted-foreground">
          Connect your Calendly account to allow clients to schedule 1:1 sessions with you
        </p>
      </div>
      
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Important</AlertTitle>
        <AlertDescription>
          Connecting your Calendly account allows clients to schedule 1:1 coaching sessions with you based on your availability.
          Make sure to set up your Calendly account with the correct availability and session types.
        </AlertDescription>
      </Alert>
      
      <Tabs defaultValue="settings" className="space-y-6">
        <TabsList>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="instructions">Instructions</TabsTrigger>
        </TabsList>
        
        <TabsContent value="settings" className="space-y-6">
          <CalendlySettings
            initialData={settings}
            onSave={handleSaveSettings}
          />
          
          {settings.calendlyUserId && (
            <Card className="bg-muted/50">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-green-500" />
                  Calendly Integration Active
                </CardTitle>
                <CardDescription>
                  Your Calendly integration is set up and ready to use.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  Clients can now schedule 1:1 coaching sessions with you based on your Calendly availability.
                  You can view and manage your scheduled sessions in the Sessions tab.
                </p>
                <div className="mt-4">
                  <Button onClick={() => router.push('/dashboard/trainer/sessions')}>
                    <Calendar className="mr-2 h-4 w-4" />
                    View Scheduled Sessions
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="instructions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Setting Up Your Calendly Account</CardTitle>
              <CardDescription>
                Follow these steps to set up your Calendly account for coaching sessions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">1. Create a Calendly Account</h3>
                <p className="text-sm text-muted-foreground">
                  If you don't already have a Calendly account, sign up at{' '}
                  <a
                    href="https://calendly.com/signup"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline"
                  >
                    calendly.com
                  </a>
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">2. Set Up Your Availability</h3>
                <p className="text-sm text-muted-foreground">
                  Configure your available hours for coaching sessions in your Calendly settings.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">3. Create Event Types</h3>
                <p className="text-sm text-muted-foreground">
                  Create different event types for different types of coaching sessions (e.g., Initial Consultation, Follow-up Session).
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">4. Connect Your Calendar</h3>
                <p className="text-sm text-muted-foreground">
                  Connect your Google Calendar, Outlook, or other calendar to avoid double-bookings.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">5. Copy Your Calendly URL</h3>
                <p className="text-sm text-muted-foreground">
                  Copy your Calendly URL (e.g., https://calendly.com/yourusername) and paste it in the settings tab.
                </p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Best Practices</CardTitle>
              <CardDescription>
                Tips for effectively managing your coaching sessions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Set Buffer Time</h3>
                <p className="text-sm text-muted-foreground">
                  Add buffer time between sessions to give yourself breaks and preparation time.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Use Confirmation Emails</h3>
                <p className="text-sm text-muted-foreground">
                  Customize confirmation emails to include important information for clients.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Set Up Reminders</h3>
                <p className="text-sm text-muted-foreground">
                  Configure reminders to reduce no-shows and help clients prepare for sessions.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Collect Information</h3>
                <p className="text-sm text-muted-foreground">
                  Use Calendly's form fields to collect relevant information from clients before sessions.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
