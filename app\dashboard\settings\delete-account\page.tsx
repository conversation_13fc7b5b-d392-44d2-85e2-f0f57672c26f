import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { DeleteAccountForm } from "@/components/settings/delete-account-form"
import { authOptions } from "@/lib/auth"

export default async function DeleteAccountPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect("/login")
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Delete Account</h1>
        <DeleteAccountForm />
      </div>
    </div>
  )
} 