"use client"

import { X } from "lucide-react"
import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { MainNav, filterNavByFeatureFlags } from "@/components/dashboard/main-nav"
import { Button } from "@/components/ui/button"
import { ThemeToggle } from "@/components/theme-toggle"
import { NotificationBell } from "@/components/notifications/notification-bell"
import { defaultFeatureFlags } from "@/lib/feature-flags"
import Image from "next/image"
import Link from "next/link"

interface MobileNavProps {
  isOpen: boolean
  onClose: () => void
  userRole: string | null
}

export function MobileNav({ isOpen, onClose, userRole }: MobileNavProps) {
  const { data: session } = useSession()

  // Prevent scrolling when mobile nav is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }

    return () => {
      document.body.style.overflow = "auto"
    }
  }, [isOpen])

  if (!isOpen) {
    return null
  }

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden">
      <div className="fixed inset-y-0 left-0 z-50 h-full w-3/4 max-w-sm border-r bg-background p-6 shadow-lg">
        <div className="flex items-center justify-between pb-4">
          <Link href="/dashboard" className="flex items-center">
            <Image
              src="/logo/brand-logo.png"
              alt="Clear-Coach"
              width={140}
              height={50}
              className="h-10 w-auto"
            />
          </Link>
          <div className="flex items-center gap-2">
            {session?.user?.id && <NotificationBell userId={session.user.id} />}
            <ThemeToggle />
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-5 w-5" />
              <span className="sr-only">Close menu</span>
            </Button>
          </div>
        </div>
        <MainNav userRole={userRole} />
      </div>
      <div
        className="fixed inset-0 z-40 bg-black/20"
        onClick={onClose}
        aria-hidden="true"
      />
    </div>
  )
}