'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { CalendlyModal } from '@/components/calendly/calendly-modal';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calendar, Clock, User, AlertCircle, CheckCircle } from 'lucide-react';

interface FollowUpTabProps {
  clientId: string;
  clientName: string;
  clientEmail: string;
  calendlySettings: {
    calendlyUserId: string | null;
    calendlyUrl: string | null;
  };
}

export function FollowUpTab({ clientId, clientName, clientEmail, calendlySettings }: FollowUpTabProps) {
  const { data: session } = useSession();



  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Schedule 1:1 Session
          </CardTitle>
          <CardDescription>
            Schedule a personalized coaching session with {clientName}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {calendlySettings.calendlyUrl ? (
            <>
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Your Calendly integration is active. You can schedule sessions with this client.
                </AlertDescription>
              </Alert>
              
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 rounded-lg border bg-muted/50">
                  <div className="p-2 rounded-full bg-primary/10">
                    <User className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{clientName}</h4>
                    <p className="text-sm text-muted-foreground">{clientEmail}</p>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>30-60 min session</span>
                  </div>
                </div>

                <CalendlyModal
                  calendlyUrl={calendlySettings.calendlyUrl}
                  clientName={clientName}
                  clientEmail={clientEmail}
                  trainerName={session?.user?.name || 'Trainer'}
                  trigger={
                    <Button size="lg" className="w-full">
                      <Calendar className="mr-2 h-5 w-5" />
                      Schedule Meeting
                    </Button>
                  }
                />
              </div>

              <div className="text-center">
                <p className="text-sm text-muted-foreground">
                  This client's information will be automatically filled in the scheduling form.
                </p>
              </div>
            </>
          ) : (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                You need to set up your Calendly integration before you can schedule sessions.
                <Button variant="link" className="p-0 h-auto ml-1" asChild>
                  <a href="/dashboard/profile">Configure in Connections tab</a>
                </Button>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Session Information</CardTitle>
          <CardDescription>What to expect during your 1:1 coaching session</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-medium">Session Format</h4>
              <p className="text-sm text-muted-foreground">Video call via your preferred platform</p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Duration</h4>
              <p className="text-sm text-muted-foreground">30-60 minutes depending on needs</p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Preparation</h4>
              <p className="text-sm text-muted-foreground">Review client's progress and goals beforehand</p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Follow-up</h4>
              <p className="text-sm text-muted-foreground">Session notes and action items will be shared</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
