"use client"

import { Loader2 } from "lucide-react"
import { useState } from "react"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogT<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

interface RequestCoachingButtonProps {
  trainerId: string
}

export function RequestCoachingButton({ trainerId }: RequestCoachingButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const [formData, setFormData] = useState({
    goals: "",
    experience: "",
    availability: "",
    message: ""
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      setIsLoading(true)
      
      // In a real application, this would call an API endpoint to handle the coaching request
      // For now, we'll simulate a successful request with a timeout
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Show success message
      toast.success(
        "Coaching request sent", 
        { 
          description: "Your request has been sent. The coach will respond within 24 hours."
        }
      )
      
      // Close the dialog
      setOpen(false)
      
      // Reset form
      setFormData({
        goals: "",
        experience: "",
        availability: "",
        message: ""
      })
    } catch (error) {
      console.error("Coaching request error:", error)
      toast.error(
        "Request failed", 
        { description: "There was an error sending your request. Please try again." }
      )
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="w-full text-sm mt-1" size="sm">
          Request Coaching
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Request 1:1 Coaching</DialogTitle>
            <DialogDescription>
              Fill out this form to request personalized coaching. The trainer will review your
              request and respond within 24 hours.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="goals">Your fitness goals</Label>
              <Textarea
                id="goals"
                name="goals"
                value={formData.goals}
                onChange={handleChange}
                placeholder="What are you trying to achieve?"
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="experience">Fitness experience</Label>
              <Input
                id="experience"
                name="experience"
                value={formData.experience}
                onChange={handleChange}
                placeholder="e.g. Beginner, 3 years training, etc."
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="availability">Availability for training</Label>
              <Input
                id="availability"
                name="availability"
                value={formData.availability}
                onChange={handleChange}
                placeholder="e.g. 3 days/week, mornings only, etc."
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="message">Additional information</Label>
              <Textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                placeholder="Anything else you'd like the coach to know"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending request...
                </>
              ) : (
                "Send request"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 