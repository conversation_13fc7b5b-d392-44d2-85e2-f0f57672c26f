import { MessageSquare, LineChart, Utensils, CreditCard, ShoppingBag, Users } from "lucide-react"

export function FeaturesSection() {
  const features = [
    {
      icon: <MessageSquare className="h-10 w-10" />,
      title: "1:1 Chat System",
      description: "Direct communication with athletes for personalized coaching and feedback.",
    },
    {
      icon: <LineChart className="h-10 w-10" />,
      title: "Progress Tracking",
      description: "Track your workouts, performance metrics, and see your improvement over time.",
    },
    {
      icon: <Utensils className="h-10 w-10" />,
      title: "Diet & Training Plans",
      description: "Access custom diet and training programs designed by professional athletes.",
    },
    {
      icon: <CreditCard className="h-10 w-10" />,
      title: "Subscription System",
      description: "Choose from different subscription tiers for access to premium features.",
    },
    {
      icon: <ShoppingBag className="h-10 w-10" />,
      title: "Product Shop",
      description: "Purchase digital training programs and resources from your favorite athletes.",
    },
    {
      icon: <Users className="h-10 w-10" />,
      title: "Community Support",
      description: "Join a community of like-minded individuals on their fitness journey.",
    },
  ]

  return (
    <section id="features" className="w-full py-12 md:py-24 lg:py-32 bg-muted/50">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">
              Powerful Features for Athletes and Clients
            </h2>
            <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Our platform provides everything you need to connect with athletes, follow personalized plans, and achieve
              your fitness goals.
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <div key={index} className="flex flex-col items-center space-y-4 rounded-lg border p-6 shadow-sm">
              <div className="text-primary">{feature.icon}</div>
              <h3 className="text-xl font-bold">{feature.title}</h3>
              <p className="text-muted-foreground text-center">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

