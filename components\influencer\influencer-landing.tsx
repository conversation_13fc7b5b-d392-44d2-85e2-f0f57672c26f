"use client"

import { CheckCircle2, ShoppingCart, Mail, Instagram, Twitter, Youtube, Facebook, Linkedin, ArrowRight, CheckCheck, Calendar, Star } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"

// Define theme interface
interface trainerTheme {
  primaryColor: string
  secondaryColor: string
  logoUrl: string | null
  bannerUrl: string | null
  fontFamily: string
}

// Define subscription tier
interface SubscriptionTier {
  id: string
  name: string
  description: string
  price: number
  features: string[]
}

// Define digital product
interface DigitalProduct {
  id: string
  title: string
  description: string
  price: number
  thumbnailUrl: string
}

// Define trainer
interface Trainer {
  id: string
  name: string | null
  email: string | null
  bio: string | null
  avatarUrl: string | null
  socialLinks: any
  // We'll make themeSettings optional since it may not exist in schema yet
  themeSettings?: any
  featuredProducts?: DigitalProduct[]
  testimonials?: { quote: string; clientName: string }[]
}

// Define props for the landing page
interface trainerLandingProps {
  trainer: Trainer
  theme: trainerTheme
  subscriptionTiers: SubscriptionTier[]
  digitalProducts: DigitalProduct[]
}

export function trainerLanding({ 
  trainer, 
  theme, 
  subscriptionTiers,
  digitalProducts 
}: trainerLandingProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [contactName, setContactName] = useState("")
  const [contactEmail, setContactEmail] = useState("")
  const [contactMessage, setContactMessage] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  // Parse social links
  let socialLinks = {}
  try {
    socialLinks = typeof trainer.socialLinks === 'string' 
      ? JSON.parse(trainer.socialLinks) 
      : trainer.socialLinks || {}
  } catch (e) {
    console.error("Error parsing social links:", e)
  }
  
  const handleSubscribe = (tierId: string) => {
    // In a real app, this would redirect to a checkout page or authentication
    // For now, just show a toast and log
    toast({
      title: "Subscription Selected",
      description: `You selected the ${subscriptionTiers.find(tier => tier.id === tierId)?.name} plan`,
    })
    
    // Redirect to login or checkout
    router.push(`/checkout?tier=${tierId}`)
  }
  
  const handleProductPurchase = (productId: string) => {
    // In a real app, this would add to cart or direct to checkout
    // For now, just show a toast and log
    toast({
      title: "Product Added to Cart",
      description: `Added ${digitalProducts.find(product => product.id === productId)?.title} to cart`,
    })
    
    // Redirect to cart
    router.push(`/dashboard/shop/cart?added=${productId}`)
  }
  
  const handleContactSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    try {
      // In a real app, this would send to an API endpoint
      // For now, just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Message Sent",
        description: `Your message has been sent to ${trainer.name}`,
      })
      
      // Reset form
      setContactName("")
      setContactEmail("")
      setContactMessage("")
    } catch (error) {
      toast({
        title: "Error",
        description: "There was an error sending your message. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // Determine if we have a banner image
  const hasBanner = !!theme.bannerUrl
  
  // Apply custom CSS variables for theme
  const customStyles = {
    "--theme-primary": theme.primaryColor,
    "--theme-secondary": theme.secondaryColor,
    "--theme-font": theme.fontFamily,
  } as React.CSSProperties
  
  // Social media icon mapping
  const socialIcons: Record<string, JSX.Element> = {
    instagram: <Instagram className="h-5 w-5" />,
    twitter: <Twitter className="h-5 w-5" />,
    youtube: <Youtube className="h-5 w-5" />,
    facebook: <Facebook className="h-5 w-5" />,
    linkedin: <Linkedin className="h-5 w-5" />,
  }
  
  return (
    <div className="flex flex-col min-h-screen" style={customStyles}>
      {/* Custom CSS */}
      <style jsx global>{`
        :root {
          --primary: ${theme.primaryColor};
          --primary-foreground: white;
          --secondary: ${theme.secondaryColor};
        }
        body {
          font-family: ${theme.fontFamily}, system-ui, sans-serif;
        }
        .custom-gradient {
          background: linear-gradient(135deg, ${theme.primaryColor}, ${theme.secondaryColor});
        }
        .custom-button {
          background-color: ${theme.primaryColor};
          color: white;
          transition: all 0.2s ease;
        }
        .custom-button:hover {
          background-color: ${theme.secondaryColor};
          transform: translateY(-2px);
        }
      `}</style>
      
      {/* Header/Banner */}
      <header className={`relative ${hasBanner ? 'h-[50vh] min-h-[400px]' : 'py-20 custom-gradient'}`}>
        {hasBanner ? (
          <div className="absolute inset-0 z-0">
            <Image 
              src={theme.bannerUrl as string} 
              alt={`${trainer.name}'s banner`}
              fill
              style={{ objectFit: 'cover' }}
              priority
            />
            <div className="absolute inset-0 bg-black/40 backdrop-blur-sm" />
          </div>
        ) : null}
        
        <div className="container relative z-10 mx-auto px-4 h-full flex flex-col justify-center">
          <div className="flex flex-col items-center text-center">
            <div className="mb-6">
              {theme.logoUrl ? (
                <div className="h-24 w-24 relative mb-4 rounded-full overflow-hidden border-4 border-white shadow-lg">
                  <Image 
                    src={theme.logoUrl} 
                    alt={`${trainer.name}'s logo`}
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                </div>
              ) : (
                <Avatar className="h-24 w-24 border-4 border-white shadow-lg">
                  <AvatarImage src={trainer.avatarUrl || ""} />
                  <AvatarFallback className="text-2xl">
                    {trainer.name?.[0]?.toUpperCase() || "A"}
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
            
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              {trainer.name || "Athlete Trainer"}
            </h1>
            
            <p className="text-xl text-white/90 max-w-2xl">
              {trainer.bio || "Professional trainer providing personalized fitness plans and nutrition guidance."}
            </p>
            
            <div className="flex mt-8 space-x-4">
              {Object.entries(socialLinks).map(([platform, url]) => 
                url && socialIcons[platform] ? (
                  <a 
                    key={platform}
                    href={url as string}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white/10 hover:bg-white/20 p-3 rounded-full transition-all"
                  >
                    {socialIcons[platform]}
                  </a>
                ) : null
              )}
            </div>
          </div>
        </div>
      </header>
      
      {/* Main Content */}
      <main className="flex-grow bg-background">
        <div className="container mx-auto px-4 py-12">
          <Tabs defaultValue="subscriptions" className="w-full">
            <TabsList className="grid w-full max-w-md mx-auto grid-cols-3 mb-8">
              <TabsTrigger value="subscriptions">Memberships</TabsTrigger>
              <TabsTrigger value="products">Programs</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
            </TabsList>
            
            {/* Subscription Tiers */}
            <TabsContent value="subscriptions">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4">Training Memberships</h2>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  Choose a membership plan that fits your goals and commitment level.
                  All plans include direct access to personalized training.
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                {subscriptionTiers.map((tier) => (
                  <Card 
                    key={tier.id} 
                    className={`overflow-hidden flex flex-col transition-all hover:shadow-lg ${
                      tier.name.includes("Pro") ? "border-primary shadow-md" : ""
                    }`}
                  >
                    {tier.name.includes("Pro") && (
                      <div className="py-1.5 px-3 bg-primary text-white text-center text-sm font-medium">
                        MOST POPULAR
                      </div>
                    )}
                    
                    <CardHeader>
                      <CardTitle className="text-xl">{tier.name}</CardTitle>
                      <CardDescription>{tier.description}</CardDescription>
                      <div className="mt-4">
                        <span className="text-3xl font-bold">${tier.price}</span>
                        <span className="text-muted-foreground">/month</span>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="flex-grow">
                      <ul className="space-y-3">
                        {tier.features.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <CheckCircle2 className="h-5 w-5 text-primary shrink-0 mr-2" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                    
                    <CardFooter>
                      <Button 
                        className="w-full custom-button"
                        onClick={() => handleSubscribe(tier.id)}
                      >
                        Choose Plan
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            {/* Digital Products */}
            <TabsContent value="products">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4">Training Programs</h2>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  Explore specialized training programs and resources to elevate your fitness journey.
                  These digital products can be purchased individually.
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-2">
                {digitalProducts.map((product) => (
                  <Card key={product.id} className="overflow-hidden flex flex-col md:flex-row">
                    <div className="relative h-48 md:h-auto md:w-1/3">
                      <Image 
                        src={product.thumbnailUrl}
                        alt={product.title}
                        fill
                        style={{ objectFit: 'cover' }}
                      />
                    </div>
                    
                    <div className="flex flex-col md:w-2/3">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle>{product.title}</CardTitle>
                            <CardDescription className="mt-2">{product.description}</CardDescription>
                          </div>
                          <Badge className="bg-primary ml-2">${product.price}</Badge>
                        </div>
                      </CardHeader>
                      
                      <CardContent className="flex-grow">
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="outline" className="flex items-center">
                            <CheckCheck className="mr-1 h-3 w-3" />
                            Instant Access
                          </Badge>
                          <Badge variant="outline" className="flex items-center">
                            <Calendar className="mr-1 h-3 w-3" />
                            Lifetime Updates
                          </Badge>
                          <Badge variant="outline" className="flex items-center">
                            <Star className="mr-1 h-3 w-3" />
                            Premium Content
                          </Badge>
                        </div>
                      </CardContent>
                      
                      <CardFooter>
                        <Button
                          className="custom-button"
                          onClick={() => handleProductPurchase(product.id)}
                        >
                          <ShoppingCart className="mr-2 h-4 w-4" />
                          Add to Cart
                        </Button>
                      </CardFooter>
                    </div>
                  </Card>
                ))}
              </div>
              
              <div className="mt-8 text-center">
                <Link href="/dashboard/shop" className="inline-flex items-center text-primary hover:text-primary/80">
                  Browse all programs <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </TabsContent>
            
            {/* Contact Form */}
            <TabsContent value="contact">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4">Get in Touch</h2>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  Have questions about my training programs or need personalized advice?
                  Send me a message and I'll get back to you as soon as possible.
                </p>
              </div>
              
              <div className="max-w-md mx-auto">
                <form onSubmit={handleContactSubmit} className="space-y-4">
                  <div>
                    <Input
                      placeholder="Your Name"
                      value={contactName}
                      onChange={(e) => setContactName(e.target.value)}
                      required
                    />
                  </div>
                  
                  <div>
                    <Input
                      type="email"
                      placeholder="Your Email"
                      value={contactEmail}
                      onChange={(e) => setContactEmail(e.target.value)}
                      required
                    />
                  </div>
                  
                  <div>
                    <Textarea
                      placeholder="Your Message"
                      rows={5}
                      value={contactMessage}
                      onChange={(e) => setContactMessage(e.target.value)}
                      required
                    />
                  </div>
                  
                  <Button 
                    type="submit" 
                    className="w-full custom-button"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Sending..." : "Send Message"}
                    {!isSubmitting && <Mail className="ml-2 h-4 w-4" />}
                  </Button>
                </form>
                
                <div className="mt-8 text-center text-sm text-muted-foreground">
                  Or reach out directly via email at{" "}
                  <a href={`mailto:${trainer.email}`} className="text-primary hover:underline">
                    {trainer.email}
                  </a>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      
      {/* Footer */}
      <footer className="bg-muted py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <Avatar className="h-8 w-8 mr-2">
                <AvatarImage src={trainer.avatarUrl || ""} />
                <AvatarFallback>
                  {trainer.name?.[0]?.toUpperCase() || "A"}
                </AvatarFallback>
              </Avatar>
              <span className="font-semibold">{trainer.name}</span>
            </div>
            
            <div className="flex space-x-4 mb-4 md:mb-0">
              {Object.entries(socialLinks).map(([platform, url]) => 
                url && socialIcons[platform] ? (
                  <a 
                    key={platform}
                    href={url as string}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary"
                  >
                    {socialIcons[platform]}
                  </a>
                ) : null
              )}
            </div>
            
            <div className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} {trainer.name} | Powered by <Link href="/" className="text-primary hover:underline">FitPro</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
} 