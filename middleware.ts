import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { Ratelimit } from '@upstash/ratelimit'
import { Redis } from '@upstash/redis'
import { handleCorsOptions, applyCorsHeaders } from './lib/security/cors'
import { applyDevRoleOverride } from './lib/dev-role-middleware'
import { rateLimiter } from './lib/security/rateLimiter'

/**
 * Security middleware for Next.js
 * - Adds security headers
 * - Applies rate limiting for API routes
 * - Sanitizes input against XSS attacks
 */

// Configure rate limits
const REQUESTS_PER_MINUTE = 60
const REQUESTS_PER_10_SECONDS = 20
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute in ms

// Initialize rate limiter with Redis if available, otherwise use memory
let ratelimit: Ratelimit | null = null

// Try to initialize Redis-based rate limiter if environment variables are present
if (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN) {
  try {
    const redis = new Redis({
      url: process.env.UPSTASH_REDIS_REST_URL,
      token: process.env.UPSTASH_REDIS_REST_TOKEN,
    })

    ratelimit = new Ratelimit({
      redis,
      limiter: Ratelimit.slidingWindow(REQUESTS_PER_MINUTE, '1m'),
      analytics: true,
    })
  } catch (error) {
    console.error('Failed to initialize Redis rate limiter:', error)
  }
}

// Security headers for all responses
const securityHeaders = {
  'X-DNS-Prefetch-Control': 'on',
  'Strict-Transport-Security': 'max-age=63072000; includeSubDomains; preload',
  'X-XSS-Protection': '1; mode=block',
  'X-Frame-Options': 'SAMEORIGIN',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), interest-cohort=()',
}

// Cache control headers for static assets
const cacheControlHeaders = {
  'Cache-Control': 'public, max-age=31536000, immutable',
}

// Routes that require authentication
const authRoutes = [
  '/dashboard',
  '/profile',
  '/settings',
  '/workout',
  '/training',
  '/coaching',
  '/subscription',
  '/cart',
  '/api/protected',
  '/api/user',
  '/api/workout',
  '/api/training',
  '/api/coaching',
]

// Routes that require admin access
const adminRoutes = [
  '/admin',
  '/api/admin',
  '/api/users',
]

// Routes that should never be cached
const noCacheRoutes = [
  '/api/auth',
  '/api/cart',
  '/api/checkout',
  '/api/webhook',
  '/api/user',
  '/api/workout',
  '/api/training',
  '/api/coaching',
]

// Public routes that don't need authentication
const publicRoutes = [
  '/',
  '/login',
  '/register',
  '/api/public',
  '/terms',
  '/privacy',
  '/about',
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/callback',
]

// Helper function to get the client IP address
function getClientIp(request: NextRequest): string {
  // Check for Cloudflare connecting IP
  const cfConnectingIp = request.headers.get('cf-connecting-ip')
  if (cfConnectingIp) {
    return cfConnectingIp
  }

  // Check X-Forwarded-For header
  const forwardedFor = request.headers.get('x-forwarded-for')
  if (forwardedFor) {
    // X-Forwarded-For can contain multiple IPs, the client IP is the first one
    return forwardedFor.split(',')[0].trim()
  }

  // Use the remote address as fallback
  const remoteAddr = request.headers.get('x-real-ip')
  if (remoteAddr) {
    return remoteAddr
  }

  // If we can't determine the IP, use a default value
  return 'unknown'
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const clientIp = getClientIp(request)

  // Special handling for specific auth routes in development mode
  // Only apply role override for specific routes or when explicitly requested
  if (process.env.NODE_ENV === 'development' &&
      (pathname.startsWith('/api/auth/dev-login') ||
       pathname.startsWith('/api/auth/session') ||
       pathname.startsWith('/api/dev/switch-role') ||
       request.nextUrl.searchParams.get('apply_role_override') === 'true')) {

    // Apply dev role override to the request
    const response = NextResponse.next()
    const { req: modifiedRequest, res: modifiedResponse } = await applyDevRoleOverride(request, response)

    // If the request was modified, use it
    if (modifiedRequest) {
      return NextResponse.next({
        request: modifiedRequest,
      })
    }
  }

  // Redirect (main) routes to modern dashboard routes
  if (pathname.startsWith('/(main)/dashboard/') || pathname === '/(main)/dashboard') {
    const newPath = pathname.replace('/(main)/dashboard', '/dashboard')
    return NextResponse.redirect(new URL(newPath, request.url))
  }

  if (pathname.startsWith('/(main)/cart/') || pathname === '/(main)/cart') {
    const newPath = pathname.replace('/(main)/cart', '/dashboard/cart')
    return NextResponse.redirect(new URL(newPath, request.url))
  }

  // Apply rate limiting to all requests except auth routes in development
  const isAuthRoute = pathname.startsWith('/api/auth/');
  const skipRateLimit = process.env.NODE_ENV === 'development' && isAuthRoute;

  if (!skipRateLimit) {
    const isRateLimited = await checkRateLimit(clientIp, request)
    if (isRateLimited) {
      return new NextResponse(JSON.stringify({ error: 'Too many requests' }), {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': '60',
          ...securityHeaders,
        },
      })
    }
  }

  // Get session token for auth checks
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  })

  // Check if this is a static asset request
  const isStaticAsset = /\.(jpg|jpeg|gif|png|webp|svg|css|js|woff|woff2|ttf)$/i.test(pathname)

  // Handle CORS preflight requests for API routes
  if (pathname.startsWith('/api/')) {
    const corsOptionsResponse = handleCorsOptions(request);
    if (corsOptionsResponse) {
      return corsOptionsResponse;
    }
  }

  // Set correct response headers based on route type
  const response = NextResponse.next()

  // Always apply security headers
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })

  // Apply CORS headers for API routes
  if (pathname.startsWith('/api/')) {
    // Mark API routes as dynamic to prevent static generation warnings
    response.headers.set('x-nextjs-route-type', 'dynamic');
    return applyCorsHeaders(request, response);
  }

  // Apply cache control for static assets
  if (isStaticAsset) {
    Object.entries(cacheControlHeaders).forEach(([key, value]) => {
      response.headers.set(key, value)
    })
    return response
  }

  // Prevent caching of sensitive routes
  if (noCacheRoutes.some(route => pathname.startsWith(route))) {
    response.headers.set('Cache-Control', 'no-store, max-age=0')
  }

  // Check for authentication on protected routes
  if (authRoutes.some(route => pathname.startsWith(route))) {
    if (!token) {
      const url = new URL('/login', request.url)
      url.searchParams.set('from', pathname)
      url.searchParams.set('callbackUrl', encodeURIComponent(request.url))
      return NextResponse.redirect(url)
    }

    // Add role-based access control for specific routes
    if (pathname.startsWith('/api/workout') && token.role !== 'trainer' && token.role !== 'admin') {
      return new NextResponse('Unauthorized', { status: 403, headers: securityHeaders })
    }

    if (pathname.startsWith('/api/coaching') && token.role !== 'trainer' && token.role !== 'admin') {
      return new NextResponse('Unauthorized', { status: 403, headers: securityHeaders })
    }
  }

  // Check for admin permissions on admin routes
  if (adminRoutes.some(route => pathname.startsWith(route))) {
    if (!token || token.role !== 'admin') {
      return new NextResponse('Unauthorized', { status: 403, headers: securityHeaders })
    }
  }

  return response
}

// Check for rate limiting
async function checkRateLimit(ip: string, request: NextRequest): Promise<boolean> {
  // Skip rate limiting for static assets
  const { pathname } = request.nextUrl
  if (/\.(jpg|jpeg|gif|png|webp|svg|css|js|woff|woff2|ttf)$/i.test(pathname)) {
    return false
  }

  // If we have Redis-based rate limiter, use it
  if (ratelimit) {
    try {
      const { success } = await ratelimit.limit(ip)
      return !success
    } catch (error) {
      console.error('Rate limiting error:', error)
      // Fall back to memory-based limiting on Redis failure
    }
  }

  // Use our rate limiter module
  // Determine the appropriate rate limit based on the route
  let limit = REQUESTS_PER_MINUTE

  // Apply stricter limits for sensitive routes
  // Skip strict limits for dev-login in development mode
  if ((pathname.startsWith('/api/auth') && !pathname.includes('/dev-login')) ||
      pathname.startsWith('/api/checkout')) {
    limit = 10 // Stricter limit for auth and checkout routes
  }

  // Completely bypass rate limiting for auth-related routes in development
  if (process.env.NODE_ENV === 'development' &&
      (pathname.includes('/dev-login') || pathname.startsWith('/api/auth'))) {
    return false // Allow all auth-related requests in development
  }

  // Check if the request is allowed
  return !rateLimiter.check(ip, limit, pathname)
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images/ (public images)
     * - public/ (public files)
     */
    '/((?!_next/static|_next/image|favicon.ico|images/|public/).*)',

    // Add redirects for (main) routes to direct users to the modern dashboard layout
    '/(main)/dashboard/:path*',
    '/(main)/cart/:path*',

    // Include API routes for CORS and security headers
    '/api/:path*',
  ],
}

// The middleware can be extended with more security features as needed

