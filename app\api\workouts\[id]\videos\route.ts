import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// Remove custom type if direct Prisma types suffice or adjust includes
// type WorkoutWithRelations = Prisma.WorkoutGetPayload<{ ... }>

export async function POST(
  request: Request,
  context: any // Use 'any' workaround
) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return new NextResponse("Unauthorized", { status: 401 })
  }

  try {
    // Safely access params
    const workoutId = context?.params?.id; // Assuming param name is 'id'
    if (!workoutId) {
        return new NextResponse("Workout ID missing in URL", { status: 400 });
    }

    // Fetch workout for ownership check
    const workout = await prisma.workout.findUnique({
      where: {
        id: workoutId,
      },
      include: {
        // Include only fields needed for ownership check
        trainingPlan: { select: { athleteId: true } },
      },
    })

    if (!workout) {
      return new NextResponse("Workout not found", { status: 404 })
    }

    // Only allow the owner (athlete) or admin to add videos
    if (workout.trainingPlan.athleteId !== session.user.id && session.user.role !== "admin") {
      return new NextResponse("Forbidden: You cannot add videos to this workout", { status: 403 })
    }

    const body = await request.json()
    const { url } = body // Assuming you have a WorkoutVideo model

    if (!url || typeof url !== 'string') {
      return new NextResponse("Video URL is required and must be a string", { status: 400 })
    }

    // Check if WorkoutVideo model exists in your schema
    // If not, maybe videos are stored directly on Exercise?
    // Assuming WorkoutVideo model exists:
    /*
    const video = await prisma.workoutVideo.create({
      data: {
        url,
        workoutId: workoutId,
      },
    })
    return NextResponse.json(video)
    */

    // If WorkoutVideo does NOT exist, return an error or handle differently
     return new NextResponse("WorkoutVideo model not implemented in schema", { status: 501 });

  } catch (error) {
    console.error("[WORKOUT_VIDEO_CREATE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function GET(
  request: Request,
  context: any // Use 'any' workaround
) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return new NextResponse("Unauthorized", { status: 401 })
  }

  try {
    // Safely access params
    const workoutId = context?.params?.id; // Assuming param name is 'id'
    if (!workoutId) {
        return new NextResponse("Workout ID missing in URL", { status: 400 });
    }

    // Fetch workout for authorization check
    const workout = await prisma.workout.findUnique({
      where: {
        id: workoutId,
      },
      include: {
        trainingPlan: {
          select: {
            trainerId: true,
            trainer: {
               select: { trainerSubscriptions: { where: { status: 'active' }}}
            }
          }
        },
        // Include the actual relation name for videos from your schema
        // workoutVideos: true // Assuming this is the relation name
        exercises: { include: { /* videoUrl field? */ } } // Or maybe videos are on exercises?
      }
    });

    if (!workout) {
      return new NextResponse("Workout not found", { status: 404 })
    }

    // Authorization check (owner, admin, or subscribed client)
    const isOwner = workout.trainingPlan.athleteId === session.user.id;
    const isAdmin = session.user.role === "admin";
    const isSubscribedClient = session.user.role === "client" &&
                              workout.trainingPlan.athlete?.athleteSubscriptions.some(sub => sub.clientId === session.user.id);

    if (!isOwner && !isAdmin && !isSubscribedClient) {
      return new NextResponse("Forbidden", { status: 403 })
    }

    // Return the relevant video data based on your schema structure
    // If WorkoutVideo model exists:
    // const videos = await prisma.workoutVideo.findMany({ where: { workoutId: workoutId } });
    // return NextResponse.json(videos);

    // If videos are on exercises:
    const videoUrls = workout.exercises.map(ex => ex.videoUrl).filter(Boolean); // Example
    return NextResponse.json(videoUrls);

    // If WorkoutVideo model doesn't exist and not on exercise:
    // return new NextResponse("Video data structure not implemented", { status: 501 });

  } catch (error) {
    console.error("[WORKOUT_VIDEOS_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}