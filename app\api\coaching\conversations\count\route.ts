import { NextResponse } from "next/server"
import { getAuthSession } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  try {
    const session = await getAuthSession()

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const userId = session.user.id
    const userRole = session.user.role

    // Get all conversations for this user
    const conversations = await prisma.conversation.findMany({
      where: {
        OR: [
          { user1Id: userId },
          { user2Id: userId }
        ]
      },
      include: {
        relationship: true
      }
    })

    // For each conversation, get the unread count
    const conversationsWithUnreadCount = await Promise.all(
      conversations.map(async (conversation) => {
        // Count unread messages for this conversation
        const unreadCount = await prisma.message.count({
          where: {
            conversationId: conversation.id,
            receiverId: userId,
            read: false,
          },
        })

        return {
          id: conversation.id,
          unreadCount
        }
      })
    )

    return NextResponse.json(conversationsWithUnreadCount)
  } catch (error) {
    console.error("[CONVERSATIONS_COUNT_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
