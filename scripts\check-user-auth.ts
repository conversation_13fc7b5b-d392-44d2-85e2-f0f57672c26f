/**
 * User Authentication Diagnostic Script
 * 
 * This script helps diagnose issues with user authentication and database records.
 * Run it with:
 *   npx ts-node scripts/check-user-auth.ts
 */

import { prisma } from '../lib/prisma';

async function diagnoseAuthIssues() {
  console.log('🔍 Starting authentication diagnosis...');
  
  try {
    // 1. Check if the authentication system is working
    console.log('\n1. Checking authentication system...');
    try {
      // This won't work in a script context, but keep it for reference
      // const session = await getServerSession(authOptions);
      console.log('Note: Cannot check session directly in script context.');
      console.log('Please run this check in an API route.');
    } catch (error) {
      console.error('Auth system error:', error);
    }
    
    // 2. Check for users in the database
    console.log('\n2. Checking for users in the database...');
    const userCount = await prisma.user.count();
    console.log(`Found ${userCount} users in the database.`);
    
    if (userCount > 0) {
      const sampleUsers = await prisma.user.findMany({
        take: 3,
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          createdAt: true
        }
      });
      
      console.log('Sample users:');
      sampleUsers.forEach(user => {
        console.log(`- ID: ${user.id} | Email: ${user.email} | Name: ${user.name} | Role: ${user.role}`);
      });
    } else {
      console.log('⚠️ No users found in the database. This is likely the source of the problem.');
    }
    
    // 3. Check table structure to confirm athleteId requirements
    console.log('\n3. Checking database table structure...');
    // Using raw query to get information about the Product table
    const tableInfo = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'Product'
      ORDER BY ordinal_position;
    `;
    
    console.log('Product table columns:');
    console.log(tableInfo);
    
    // 4. Check for constraints
    console.log('\n4. Checking table constraints...');
    const constraints = await prisma.$queryRaw`
      SELECT conname, contype, pg_get_constraintdef(oid)
      FROM pg_constraint
      WHERE conrelid = 'Product'::regclass::oid;
    `;
    
    console.log('Product table constraints:');
    console.log(constraints);
    
    // 5. Provide summary and next steps
    console.log('\n✅ Diagnosis complete. Recommendations:');
    console.log('- If no users were found, you need to create users first');
    console.log('- Check that the athleteId in the session matches a user ID in the database');
    console.log('- Ensure the user has the correct role to create products');
    console.log('- Try logging out and logging back in to refresh the session');
    
  } catch (error) {
    console.error('Diagnostic error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

diagnoseAuthIssues()
  .then(() => console.log('Diagnosis completed'))
  .catch(err => console.error('Fatal error:', err)); 