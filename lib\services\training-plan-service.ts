import { TrainingPlanTemplate } from "@prisma/client"
import { ITrainingPlanRepository, TrainingPlanRepository } from "../repositories/training-plan-repository"

export interface TrainingPlanData {
  title: string
  description?: string
  difficulty?: string
  weeks: any
  type?: string
}

export class TrainingPlanService {
  private static repository: ITrainingPlanRepository = new TrainingPlanRepository()

  /**
   * Set a custom repository implementation (useful for testing)
   */
  static setRepository(repository: ITrainingPlanRepository) {
    this.repository = repository
  }

  /**
   * Create a new training plan template
   */
  static async createTemplate(trainerId: string, planData: TrainingPlanData): Promise<TrainingPlanTemplate> {
    return this.repository.createTemplate(trainerId, planData)
  }

  /**
   * Get all training plan templates for a trainer
   */
  static async getTemplatesForTrainer(trainerId: string): Promise<any[]> {
    const templates = await this.repository.getTemplatesForTrainer(trainerId)
    return templates
  }

  /**
   * Get a specific training plan template
   */
  static async getTemplateById(id: string, trainerId: string): Promise<TrainingPlanTemplate | null> {
    return this.repository.getTemplateById(id, trainerId)
  }

  /**
   * Update a training plan template
   */
  static async updateTemplate(
    id: string,
    trainerId: string,
    data: Partial<TrainingPlanData>
  ): Promise<TrainingPlanTemplate> {
    return this.repository.updateTemplate(id, trainerId, data)
  }

  /**
   * Delete a training plan template
   */
  static async deleteTemplate(id: string, trainerId: string): Promise<TrainingPlanTemplate> {
    return this.repository.deleteTemplate(id, trainerId)
  }

  /**
   * Assign a training plan to a client
   */
  static async assignPlanToClient(
    trainerId: string,
    clientId: string,
    planData: TrainingPlanData
  ): Promise<TrainingPlanTemplate> {
    return this.repository.assignPlanToClient(trainerId, clientId, planData)
  }
}
