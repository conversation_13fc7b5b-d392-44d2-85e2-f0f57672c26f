/* Custom styles for react-chat-elements */

/* ChatList styles */
.rce-container-clist {
  background-color: #f9f9f9;
  border-radius: 0;
  height: 100%;
}

.rce-citem {
  background-color: transparent;
  border-bottom: 1px solid #eaeaea;
  padding: 10px 15px;
  transition: background-color 0.2s;
}

.rce-citem:hover {
  background-color: #f0f0f0;
}

.rce-citem-body {
  padding-bottom: 0;
}

.rce-citem-body--top {
  margin-bottom: 5px;
}

.rce-citem-body--top-title {
  font-weight: 500;
  font-size: 15px;
  color: #333;
}

.rce-citem-body--top-time {
  font-size: 12px;
  color: #999;
}

.rce-citem-body--bottom {
  margin-top: 0;
}

.rce-citem-body--bottom-title {
  font-size: 13px;
  color: #666;
}

.rce-citem-body--bottom-status {
  font-size: 12px;
  color: #999;
}

.rce-citem-avatar {
  width: 48px !important;
  height: 48px !important;
  border-radius: 50%;
  margin-right: 12px;
  border: 1px solid #eaeaea;
}

.rce-citem-status {
  margin: 0 0 0 10px;
}

.rce-citem-status > span {
  background-color: #f06292 !important;
  width: 18px;
  height: 18px;
  font-size: 11px;
  font-weight: 500;
}

/* MessageList styles */
.rce-container-mlist {
  background-color: #f9f9f9;
  padding: 10px;
}

.rce-mlist {
  padding: 0;
}

.rce-mlist-down-button {
  background-color: #f06292;
  box-shadow: 0 2px 10px rgba(240, 98, 146, 0.3);
}

/* MessageBox styles */
.rce-container-mbox {
  margin: 8px 0;
}

.rce-mbox {
  background-color: #f0f0f0;
  border-radius: 16px;
  max-width: 70%;
  padding: 10px 15px;
  box-shadow: none;
}

.rce-mbox-right {
  background-color: #f06292;
  color: white;
}

.rce-mbox-right .rce-mbox-text {
  color: white;
}

.rce-mbox-left {
  background-color: #f0f0f0;
  color: #333;
}

.rce-mbox-left .rce-mbox-text {
  color: #333;
}

.rce-mbox-text {
  font-size: 14px;
  line-height: 1.5;
}

.rce-mbox-time {
  font-size: 11px;
  color: #999;
  bottom: -20px;
}

.rce-mbox-right .rce-mbox-time {
  color: #999;
}

.rce-mbox-left .rce-mbox-time {
  color: #999;
}

.rce-mbox-right-notch {
  fill: #f06292;
}

.rce-mbox-left-notch {
  fill: #f0f0f0;
}

.rce-mbox-avatar {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50%;
  margin-right: 5px;
  border: 1px solid #eaeaea;
}

/* Input styles */
.rce-container-input {
  background-color: white;
  border-top: 1px solid #eaeaea;
  padding: 10px 15px;
}

.rce-input {
  background-color: #f9f9f9;
  border-radius: 20px;
  border: 1px solid #eaeaea;
  padding: 10px 15px;
  font-size: 14px;
  color: #333;
}

.rce-input:focus {
  outline: none;
  border-color: #f06292;
}

.rce-input-buttons {
  margin-left: 10px;
}

.rce-input-buttons .rce-button {
  background-color: #f06292;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rce-input-buttons .rce-button svg {
  fill: white;
  width: 20px;
  height: 20px;
}

/* Button styles */
.rce-button {
  background-color: #f06292;
  color: white;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
}

.rce-button:hover {
  background-color: #e91e63;
}

/* System message styles */
.rce-mbox-system {
  background-color: rgba(0, 0, 0, 0.05);
  color: #666;
  font-size: 12px;
  border-radius: 10px;
  padding: 8px 12px;
  margin: 10px auto;
  max-width: 80%;
  text-align: center;
}

/* Typing indicator styles */
.rce-container-typing {
  margin: 5px 0;
}

.rce-typing {
  background-color: #f0f0f0;
  border-radius: 16px;
  padding: 8px 15px;
  display: inline-block;
}

.rce-typing-dot {
  background-color: #999;
}

/* Custom styles for our chat UI */
.chat-container {
  display: flex;
  height: calc(100vh - 120px);
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #eaeaea;
}

.chat-sidebar {
  width: 320px;
  border-right: 1px solid #eaeaea;
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
}

.chat-sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-sidebar-tabs {
  display: flex;
  border-bottom: 1px solid #eaeaea;
}

.chat-sidebar-tab {
  flex: 1;
  text-align: center;
  padding: 12px;
  cursor: pointer;
  font-weight: 500;
  color: #666;
  position: relative;
}

.chat-sidebar-tab.active {
  color: #f06292;
  border-bottom: 2px solid #f06292;
}

.chat-sidebar-tab .badge {
  background-color: #f06292;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 12px;
  margin-left: 5px;
}

.chat-search {
  padding: 12px;
  position: relative;
}

.chat-search input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border-radius: 20px;
  border: 1px solid #eaeaea;
  background-color: #fff;
  font-size: 14px;
}

.chat-search svg {
  position: absolute;
  left: 24px;
  top: 22px;
  color: #999;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.chat-header {
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
  text-align: center;
  padding: 0 32px;
}

.chat-empty svg {
  color: #ccc;
  margin-bottom: 16px;
  width: 64px;
  height: 64px;
}

.chat-empty h3 {
  margin-bottom: 8px;
  font-weight: 500;
}

.chat-empty p {
  color: #999;
  margin-bottom: 16px;
}

.chat-empty button {
  background-color: #f06292;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
}
