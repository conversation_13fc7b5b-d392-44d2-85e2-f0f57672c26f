import { <PERSON><PERSON>, <PERSON>rism<PERSON> } from "@prisma/client"
import { IUserRepository, UserRepository } from "../repositories/user-repository"
import { hash } from "bcryptjs"

export class UserService {
  private static repository: IUserRepository = new UserRepository()

  /**
   * Set a custom repository implementation (useful for testing)
   */
  static setRepository(repository: IUserRepository) {
    this.repository = repository
  }

  /**
   * Find a user by ID
   */
  static async findById(id: string): Promise<User | null> {
    return this.repository.findById(id)
  }

  /**
   * Find a user by email
   */
  static async findByEmail(email: string): Promise<User | null> {
    return this.repository.findByEmail(email)
  }

  /**
   * Create a new user
   */
  static async create(data: {
    email: string
    name?: string
    password: string
    role?: string
  }): Promise<User> {
    const hashedPassword = await hash(data.password, 10)
    
    return this.repository.create({
      email: data.email,
      name: data.name,
      password: hashedPassword,
      role: data.role || "user"
    })
  }

  /**
   * Update a user
   */
  static async update(id: string, data: Partial<User>): Promise<User> {
    // If password is being updated, hash it
    if (data.password) {
      data.password = await hash(data.password, 10)
    }
    
    return this.repository.update(id, data)
  }

  /**
   * Delete a user
   */
  static async delete(id: string): Promise<User> {
    return this.repository.delete(id)
  }

  /**
   * Find multiple users
   */
  static async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.UserWhereInput
    orderBy?: Prisma.UserOrderByWithRelationInput
  }): Promise<User[]> {
    return this.repository.findMany(params)
  }

  /**
   * Count users
   */
  static async count(where?: Prisma.UserWhereInput): Promise<number> {
    return this.repository.count(where)
  }

  /**
   * Search users by name
   */
  static async searchByName(query: string, role?: string): Promise<User[]> {
    return this.repository.searchByName(query, role)
  }

  /**
   * Update user role
   */
  static async updateRole(id: string, role: string): Promise<User> {
    return this.repository.update(id, { role })
  }
}
