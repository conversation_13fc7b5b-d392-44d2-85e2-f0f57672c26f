'use client';

import React from 'react';
import { ChatAttachment, Attachment } from './chat-attachment';

interface ChatMessageAttachmentsProps {
  attachments: Attachment[];
  className?: string;
}

export function ChatMessageAttachments({
  attachments,
  className = '',
}: ChatMessageAttachmentsProps) {
  if (!attachments || attachments.length === 0) return null;

  // Group attachments by type
  const imageAttachments = attachments.filter((att) => att.fileType === 'image');
  const documentAttachments = attachments.filter((att) => att.fileType !== 'image');

  return (
    <div style={{ background: 'transparent', border: 'none', padding: 0, margin: 0 }} className={className}>
      {/* Image Gallery */}
      {imageAttachments.length > 0 && (
        <div style={{ background: 'transparent', border: 'none', padding: 0, margin: 0 }}>
          {imageAttachments.length === 1 ? (
            <ChatAttachment attachment={imageAttachments[0]} />
          ) : (
            <div className="grid grid-cols-2 gap-2">
              {imageAttachments.map((attachment) => (
                <ChatAttachment
                  key={attachment.id}
                  attachment={attachment}
                  className="h-full"
                />
              ))}
            </div>
          )}
        </div>
      )}

      {/* Document Attachments */}
      {documentAttachments.length > 0 && (
        <div className="document-attachments space-y-2">
          {documentAttachments.map((attachment) => (
            <ChatAttachment key={attachment.id} attachment={attachment} />
          ))}
        </div>
      )}
    </div>
  );
}
