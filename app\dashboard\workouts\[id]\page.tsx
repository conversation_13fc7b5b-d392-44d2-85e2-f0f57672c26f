import { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { notFound, redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { <PERSON>L<PERSON><PERSON>, Edit, Play, Clock, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Info } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { VideoManager } from "@/components/video/video-manager"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { canAccessDetailedTracking } from "@/lib/utils/tier-access"

interface WorkoutPageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({
  params,
}: WorkoutPageProps): Promise<Metadata> {
  const workout = await prisma.workout.findUnique({
    where: { id: params.id },
    select: { title: true, description: true }
  });

  if (!workout) {
    return {
      title: "Workout Not Found",
    }
  }

  return {
    title: workout.title,
    description: workout.description || "Workout details and exercises",
  }
}

export default async function WorkoutPage({ params }: WorkoutPageProps) {
  const session = await getServerSession(authOptions)
  if (!session?.user) {
    return notFound()
  }

  // Get user information including subscription tier
  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: {
      id: true,
      name: true,
      subscriptionTier: true
    }
  })

  const userTier = user?.subscriptionTier as 'basic' | 'mid' | 'premium' | 'coaching' || 'basic'
  const canTrackDetails = canAccessDetailedTracking(userTier)

  // Fetch the workout from the database
  const workout = await prisma.workout.findUnique({
    where: { id: params.id },
    include: {
      exercises: {
        orderBy: { order: 'asc' }
      },
      videos: true,
      week: {
        select: {
          weekNumber: true,
          trainingPlan: {
            select: {
              title: true,
              athleteId: true
            }
          }
        }
      }
    }
  })

  if (!workout) {
    return notFound()
  }

  // Check if user has permission to view this workout
  const isOwner = workout.week?.trainingPlan?.athleteId === session.user.id
  const isAdmin = session.user.role === "admin"
  const isTrainer = session.user.role === "trainer"

  if (!isOwner && !isAdmin && !isTrainer) {
    return notFound()
  }

  // Check if user can edit the workout
  const isEditable = isAdmin || isTrainer

  // Fetch workout logs to see if this workout has been completed
  const workoutLogs = await prisma.workoutLog.findMany({
    where: {
      clientId: session.user.id,
      workoutId: workout.id
    },
    orderBy: {
      date: 'desc'
    },
    take: 1
  })

  const hasBeenCompleted = workoutLogs.length > 0
  const lastCompletedDate = hasBeenCompleted ? workoutLogs[0].date : null

  return (
    <div className="container mx-auto py-6 max-w-5xl">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button variant="ghost" size="sm" className="p-0" asChild>
              <Link href="/dashboard/workouts">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back
              </Link>
            </Button>
            <Badge variant="outline" className="capitalize">
              {workout.type || 'General'}
            </Badge>
            {hasBeenCompleted && (
              <Badge className="bg-green-500 text-white">
                Completed
              </Badge>
            )}
            {userTier !== 'basic' && (
              <Badge variant="outline" className="bg-primary/5 border-primary/20">
                {userTier === 'mid' ? 'Mid Tier' : userTier === 'premium' ? 'Premium' : 'Coaching'}
              </Badge>
            )}
          </div>
          <h1 className="text-3xl font-bold">{workout.title}</h1>
          {workout.week && (
            <p className="text-muted-foreground mt-1">
              {workout.week.trainingPlan.title} - Week {workout.week.weekNumber}
            </p>
          )}
        </div>
        <div className="flex gap-2 self-end md:self-auto">
          {isEditable && (
            <Button variant="outline" asChild>
              <Link href={`/dashboard/workouts/${workout.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            </Button>
          )}
          <Button asChild>
            <Link href={`/dashboard/workouts/${workout.id}/track`}>
              <Play className="mr-2 h-4 w-4" />
              {userTier === 'basic' ? 'Basic Tracking' : canTrackDetails ? 'Advanced Tracking' : 'Start Tracking'}
            </Link>
          </Button>
        </div>
      </div>

      {/* Tier-specific information */}
      {userTier === 'basic' && (
        <Alert className="mb-6 bg-muted/30 border-muted">
          <Info className="h-4 w-4" />
          <AlertTitle>Basic Tier Access</AlertTitle>
          <AlertDescription>
            You have access to basic workout tracking.
            <Link href="/dashboard/upgrade" className="text-primary hover:underline ml-1">
              Upgrade to Mid Tier or Premium
            </Link> for advanced tracking features like RPE, RIR, and detailed performance metrics.
          </AlertDescription>
        </Alert>
      )}

      {workout.description && (
        <Card className="mb-8">
          <CardContent className="p-6">
            <p className="text-sm">{workout.description}</p>
          </CardContent>
        </Card>
      )}

      <div className="space-y-8">
        {/* Video Section */}
        <section>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Workout Videos</h2>
          </div>
          <VideoManager workoutId={workout.id} isEditable={isEditable} />
        </section>

        <Separator />

        {/* Exercises Section */}
        <section>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Exercises</h2>
            {hasBeenCompleted && lastCompletedDate && (
              <div className="flex items-center text-sm text-muted-foreground">
                <Clock className="h-4 w-4 mr-1" />
                Last completed: {new Date(lastCompletedDate).toLocaleDateString()}
              </div>
            )}
          </div>

          {workout.exercises.length > 0 ? (
            <div className="space-y-4">
              {workout.exercises.map((exercise, index) => (
                <Card key={exercise.id} className="overflow-hidden hover:shadow-sm transition-all">
                  <CardHeader className="p-4 pb-2 bg-muted/20">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="bg-primary/10 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <CardTitle className="text-lg">{exercise.name}</CardTitle>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4 pt-3">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {exercise.sets && (
                        <div className="flex items-center gap-2 bg-muted/20 p-2 rounded">
                          <BarChart className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="text-xs text-muted-foreground">Sets</div>
                            <div className="font-medium">{exercise.sets}</div>
                          </div>
                        </div>
                      )}
                      {exercise.reps && (
                        <div className="flex items-center gap-2 bg-muted/20 p-2 rounded">
                          <Dumbbell className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="text-xs text-muted-foreground">Reps</div>
                            <div className="font-medium">{exercise.reps}</div>
                          </div>
                        </div>
                      )}
                      {exercise.weight && (
                        <div className="flex items-center gap-2 bg-muted/20 p-2 rounded">
                          <Weight className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="text-xs text-muted-foreground">Weight</div>
                            <div className="font-medium">{exercise.weight} lbs</div>
                          </div>
                        </div>
                      )}
                      {exercise.restTime && (
                        <div className="flex items-center gap-2 bg-muted/20 p-2 rounded">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="text-xs text-muted-foreground">Rest</div>
                            <div className="font-medium">{exercise.restTime} sec</div>
                          </div>
                        </div>
                      )}
                      {exercise.duration && (
                        <div className="flex items-center gap-2 bg-muted/20 p-2 rounded">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="text-xs text-muted-foreground">Duration</div>
                            <div className="font-medium">{exercise.duration} sec</div>
                          </div>
                        </div>
                      )}
                    </div>

                    {exercise.description && (
                      <div className="mt-3 text-sm text-muted-foreground bg-muted/10 p-3 rounded">
                        {exercise.description}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center text-muted-foreground">
                No exercises found for this workout.
              </CardContent>
            </Card>
          )}
        </section>
      </div>
    </div>
  )
}