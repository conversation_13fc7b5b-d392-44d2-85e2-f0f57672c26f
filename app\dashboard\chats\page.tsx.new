"use client"

import { useState, useEffect, useRef, use<PERSON>allback } from "react"
import { useSession } from "next-auth/react"
import { useRouter, useSearchParams } from "next/navigation"
import { MessageSquare, Search, User, Send, ArrowLeft, MoreVertical, PlusCircle, UserPlus, Image as ImageIcon, RefreshCw } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { formatDistanceToNow } from "date-fns"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Badge } from "@/components/ui/badge"
import { useSocket } from "@/components/socket-provider"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

// Import custom styles
import "./chat.css"

interface Client {
  id: string
  name: string
  email: string
  avatarUrl?: string
  clientSubscriptions?: any[]
}

interface Conversation {
  id: string
  user: {
    id: string
    name: string
    avatarUrl?: string
  }
  lastMessage?: {
    content: string
    createdAt: string
  }
  unreadCount: number
}

interface Message {
  id: string
  content: string
  senderId: string
  conversationId: string
  createdAt: string
  read: boolean
  sender: {
    id: string
    name: string
    avatarUrl?: string
  }
}

export default function ChatsPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const { isConnected, joinConversation, leaveConversation, sendMessage: socketSendMessage } = useSocket()

  const [conversations, setConversations] = useState<Conversation[]>([])
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState("")
  const [loading, setLoading] = useState(true)
  const [sendingMessage, setSendingMessage] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [updatingMessages, setUpdatingMessages] = useState(false)

  // New state variables for client management
  const [clients, setClients] = useState<Client[]>([])
  const [loadingClients, setLoadingClients] = useState(false)
  const [clientSearchQuery, setClientSearchQuery] = useState("")
  const [isNewChatDialogOpen, setIsNewChatDialogOpen] = useState(false)
  const [creatingConversation, setCreatingConversation] = useState(false)

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Filter conversations based on search query
  const filteredConversations = conversations.filter(
    (conv) => conv.user.name?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Separate unread conversations
  const unreadConversations = filteredConversations.filter((conv) => conv.unreadCount > 0)
  const readConversations = filteredConversations.filter((conv) => conv.unreadCount === 0)

  // Filter clients based on search query
  const filteredClients = clients.filter(
    (client) =>
      client.name?.toLowerCase().includes(clientSearchQuery.toLowerCase()) ||
      client.email?.toLowerCase().includes(clientSearchQuery.toLowerCase())
  )

  // Scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messagesEndRef])

  // Mark messages as read
  const markMessagesAsRead = async (conversationId: string) => {
    try {
      await fetch(`/api/coaching/messages/mark-read`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ conversationId }),
      })

      // Update unread count in conversations
      setConversations((prev) =>
        prev.map((conv) =>
          conv.id === conversationId ? { ...conv, unreadCount: 0 } : conv
        )
      )
    } catch (error) {
      console.error("Error marking messages as read:", error)
    }
  }

  // Fetch messages for a conversation with useCallback
  const fetchMessages = useCallback(async (conversationId: string, showLoading = true) => {
    if (!session?.user?.id) return;

    // Always set updating messages to false when this function completes
    if (showLoading) {
      setUpdatingMessages(true);
    }

    try {
      // Add timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/coaching/messages?conversationId=${conversationId}&t=${timestamp}`, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch messages: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // If data is empty or not an array, set empty messages and return
      if (!data || !Array.isArray(data)) {
        console.warn("Received invalid data format for messages:", data);
        setMessages([]);
        return;
      }

      setMessages(data);

      // Mark messages as read
      markMessagesAsRead(conversationId);

      // Scroll to bottom with animation
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    } catch (error) {
      console.error("Error fetching messages:", error);
      // Set empty messages to avoid showing loading forever
      if (showLoading) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load messages. Please try refreshing the page.",
        });
        setMessages([]);
      }
    } finally {
      // Always turn off loading state
      if (showLoading) {
        setUpdatingMessages(false);
      }
    }
  }, [session?.user?.id, markMessagesAsRead, scrollToBottom, toast])

  // Fetch conversations with useCallback to avoid recreating the function on each render
  const fetchConversations = useCallback(async (isInitialLoad = false) => {
    if (!session?.user?.id) return;

    // Always set loading to false when this function completes, even if there's an error
    if (isInitialLoad) {
      setLoading(true);
    }

    try {
      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/coaching/conversations?t=${timestamp}`, {
        // Add cache control to prevent frequent requests
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch conversations: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // If data is empty or not an array, set empty conversations and return
      if (!data || !Array.isArray(data)) {
        console.warn("Received invalid data format for conversations:", data);
        setConversations([]);
        return;
      }

      // Transform the data to match our expected format
      const formattedConversations = data.map((conv: any) => ({
        id: conv.id,
        user: conv.user || {
          id: conv.relationship?.clientId || conv.relationship?.trainerId,
          name: conv.user?.name || "User",
          avatarUrl: conv.user?.avatarUrl
        },
        lastMessage: conv.lastMessage || {
          content: "Start a conversation",
          createdAt: new Date().toISOString()
        },
        unreadCount: conv.unreadCount || 0
      }));

      setConversations(formattedConversations);

      // Check if there's a conversationId in the URL (only on initial load)
      if (isInitialLoad && searchParams) {
        const conversationId = searchParams.get("conversationId");
        if (conversationId) {
          const conversation = formattedConversations.find((c: any) => c.id === conversationId);
          if (conversation) {
            setActiveConversation(conversation);
            fetchMessages(conversationId);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching conversations:", error);
      // Only show error toast on initial load
      if (isInitialLoad) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load conversations. Please try refreshing the page.",
        });

        // Set empty conversations to avoid showing loading forever
        setConversations([]);
      }
    } finally {
      // Always turn off loading state
      if (isInitialLoad) {
        setLoading(false);
      }
    }
  }, [session?.user?.id, searchParams, toast, fetchMessages])

  // Fetch all clients with useCallback
  const fetchClients = useCallback(async () => {
    if (!session?.user?.id || session.user.role !== "trainer") return

    try {
      setLoadingClients(true)
      const response = await fetch("/api/trainer/clients", {
        // Add cache control to prevent frequent requests
        cache: "no-cache",
        headers: {
          "Cache-Control": "no-cache"
        }
      })

      if (response.ok) {
        const data = await response.json()
        setClients(data)
      } else {
        console.error("Error fetching clients:", response.statusText)
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load clients",
        })
      }
    } catch (error) {
      console.error("Error fetching clients:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load clients",
      })
    } finally {
      setLoadingClients(false)
    }
  }, [session?.user?.id, session?.user?.role, toast])
