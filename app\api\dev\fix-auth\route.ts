import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// Development-only API endpoint to fix authentication issues
// This creates a user record matching the current session ID
export async function GET() {
  // Safety check - only run in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: "This endpoint is only available in development mode" }, { status: 403 })
  }
  
  try {
    // Get current session
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ 
        error: "You must be logged in to use this endpoint",
        suggestion: "Please log in first, then try again" 
      }, { status: 401 })
    }
    
    // Check if user already exists
    const existingUser = await prisma.$queryRaw`
      SELECT "id", "email", "name", "role" FROM "User" 
      WHERE "id" = ${session.user.id} OR "email" = ${session.user.email}
      LIMIT 1;
    `;
    
    if (Array.isArray(existingUser) && existingUser.length > 0) {
      // User exists - return details
      return NextResponse.json({
        message: "User already exists in database",
        user: existingUser[0],
        action: "none"
      })
    }
    
    // Create a new user matching the session
    await prisma.$executeRaw`
      INSERT INTO "User" (
        "id", "email", "name", "password", "role", "createdAt", "updatedAt"
      ) VALUES (
        ${session.user.id}, 
        ${session.user.email || '<EMAIL>'}, 
        ${session.user.name || 'Dev User'}, 
        'dev-password', 
        'athlete', 
        NOW(), 
        NOW()
      )
      ON CONFLICT ("id", "email") DO NOTHING;
    `;
    
    // Get the newly created user
    const newUser = await prisma.$queryRaw`
      SELECT "id", "email", "name", "role" FROM "User" 
      WHERE "id" = ${session.user.id}
      LIMIT 1;
    `;
    
    return NextResponse.json({
      message: "Successfully created user record for your session",
      user: Array.isArray(newUser) && newUser.length > 0 ? newUser[0] : null,
      action: "created"
    })
  } catch (error) {
    console.error("[DEV_FIX_AUTH_ERROR]", error)
    return NextResponse.json({
      error: "Failed to fix authentication",
      details: error instanceof Error ? error.message : String(error),
      suggestion: "Try logging out and back in, or check database connection"
    }, { status: 500 })
  }
} 