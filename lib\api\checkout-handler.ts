import { NextResponse } from "next/server"
import { BaseApiHandler } from "./base-api-handler"
import { CheckoutService } from "../services/checkout-service"
import { CartService } from "../services/cart-service"

export class CheckoutHandler extends BaseApiHandler {
  /**
   * Get checkout information
   */
  protected async get(req: Request, userId: string): Promise<NextResponse> {
    const cartItems = await CartService.getCart(userId)
    const formattedCart = CartService.formatCartData(cartItems)

    return NextResponse.json({
      cart: formattedCart
    })
  }

  /**
   * Create a new order
   */
  protected async post(req: Request, userId: string): Promise<NextResponse> {
    try {
      // Get the user's cart
      const cartItems = await CartService.getCart(userId)

      console.log(`[CheckoutHandler] Processing checkout for user ${userId} with ${cartItems.length} cart items`)

      if (cartItems.length === 0) {
        return NextResponse.json({ error: "Cart is empty" }, { status: 400 })
      }

      // Create an order from the cart items
      const items = cartItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity
      }))

      // In development mode with premium status, allow direct checkout
      const isDev = process.env.NODE_ENV === 'development'

      // Create the order
      const order = await CheckoutService.createOrder(userId, items)

      console.log(`[CheckoutHandler] Order created successfully: ${order.id}`)

      return NextResponse.json({
        message: "Order created successfully",
        orderId: order.id
      })
    } catch (error) {
      console.error('[CheckoutHandler_POST_ERROR]', error)
      return NextResponse.json({
        error: "Failed to process checkout",
        details: error instanceof Error ? error.message : "Unknown error"
      }, { status: 500 })
    }
  }

  /**
   * Update checkout information (not implemented)
   */
  protected async put(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete checkout information (not implemented)
   */
  protected async delete(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}

export class OrderHandler extends BaseApiHandler {
  /**
   * Get all orders for a user
   */
  protected async get(req: Request, userId: string): Promise<NextResponse> {
    const orders = await CheckoutService.getOrdersByUserId(userId)

    return NextResponse.json(orders)
  }

  /**
   * Create a new order (not implemented in orders route)
   */
  protected async post(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Update an order (not implemented)
   */
  protected async put(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete an order (not implemented)
   */
  protected async delete(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}

export class OrderByIdHandler extends BaseApiHandler {
  /**
   * Get an order by ID
   */
  protected async get(req: Request, userId: string, params: { orderId: string }): Promise<NextResponse> {
    const order = await CheckoutService.getOrderById(params.orderId)

    if (!order) {
      return NextResponse.json({ error: "Order not found" }, { status: 404 })
    }

    // Check if the order belongs to the user
    if (order.userId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    return NextResponse.json(order)
  }

  /**
   * Create an order (not implemented in [id] route)
   */
  protected async post(req: Request, userId: string, params: { orderId: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Update an order
   */
  protected async put(req: Request, userId: string, params: { orderId: string }): Promise<NextResponse> {
    const { status } = await req.json()

    if (!status) {
      return NextResponse.json({ error: "Status is required" }, { status: 400 })
    }

    // Check if the order exists
    const order = await CheckoutService.getOrderById(params.orderId)

    if (!order) {
      return NextResponse.json({ error: "Order not found" }, { status: 404 })
    }

    // Check if the order belongs to the user
    if (order.userId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    const updatedOrder = await CheckoutService.updateOrderStatus(params.orderId, status)

    return NextResponse.json(updatedOrder)
  }

  /**
   * Delete an order (not implemented)
   */
  protected async delete(req: Request, userId: string, params: { orderId: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}
