"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { createClient } from "@/app/actions/coaching"

interface AddClientModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: (client: Client) => void
}

interface Client {
  id: string
  name: string
  email: string
  status: string
  joinedDate: string
  nextSession: string | null
  unreadMessages: number
  plan: string
  relationshipId: string
  monthlyFee: number
}

interface CreateClientData {
  firstName: string
  lastName: string
  email: string
  password: string
  monthlyFee: number
  subscriptionDuration: number
}

// Utility function to generate a random password
function generateRandomPassword(length = 12) {
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
  let password = ""
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length)
    password += charset[randomIndex]
  }
  return password
}

export function AddClientModal({ isOpen, onClose, onSuccess }: AddClientModalProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<CreateClientData>({
    firstName: "",
    lastName: "",
    email: "",
    password: generateRandomPassword(),
    monthlyFee: 0,
    subscriptionDuration: 1
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validate form data
      if (!formData.firstName || !formData.lastName || !formData.email || !formData.password || !formData.monthlyFee) {
        throw new Error("All fields are required")
      }

      // Create client
      const result = await createClient({
        ...formData,
        monthlyFee: parseFloat(formData.monthlyFee.toString()),
      })

      // Format the client data to match the Client interface
      const newClient: Client = {
        id: result.client.id,
        name: result.client.name || `${formData.firstName} ${formData.lastName}`,
        email: result.client.email,
        status: "active",
        joinedDate: result.relationship.startDate.toISOString(),
        nextSession: null,
        unreadMessages: 0,
        plan: "Premium Coaching",
        relationshipId: result.relationship.id,
        monthlyFee: parseFloat(formData.monthlyFee.toString()),
      }

      toast.success("Client added successfully")
      onSuccess?.(newClient)
      onClose()
      router.refresh()
    } catch (error) {
      console.error("Error adding client:", error)
      toast.error(error instanceof Error ? error.message : "Failed to add client")
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add New Client</DialogTitle>
          <DialogDescription>
            Create a new client and establish a coaching relationship.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                name="firstName"
                placeholder="John"
                value={formData.firstName}
                onChange={handleChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                name="lastName"
                placeholder="Doe"
                value={formData.lastName}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              name="password"
              type="password"
              placeholder="••••••••"
              value={formData.password}
              onChange={handleChange}
              required
            />
          </div>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="monthlyFee" className="text-right">
                Monthly Fee
              </Label>
              <Input
                id="monthlyFee"
                type="number"
                value={formData.monthlyFee}
                onChange={(e) => setFormData({ ...formData, monthlyFee: parseFloat(e.target.value) })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="subscriptionDuration" className="text-right">
                Duration (months)
              </Label>
              <Input
                id="subscriptionDuration"
                type="number"
                min="1"
                max="12"
                value={formData.subscriptionDuration}
                onChange={(e) => setFormData({ ...formData, subscriptionDuration: parseInt(e.target.value) })}
                className="col-span-3"
              />
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" loading={loading}>
              Add Client
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 