import { NextResponse } from "next/server"
import { getAuthSession } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: Request) {
  try {
    const session = await getAuthSession()
    
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }
    
    const userId = session.user.id
    const body = await request.json()
    const { id, all = false } = body
    
    // Mark all notifications as read
    if (all) {
      await prisma.notification.updateMany({
        where: {
          userId,
          read: false,
        },
        data: {
          read: true,
        },
      })
      
      return NextResponse.json({ success: true, message: "All notifications marked as read" })
    }
    
    // Mark a specific notification as read
    if (!id) {
      return new NextResponse("Notification ID is required", { status: 400 })
    }
    
    const notification = await prisma.notification.findUnique({
      where: {
        id,
      },
    })
    
    if (!notification) {
      return new NextResponse("Notification not found", { status: 404 })
    }
    
    if (notification.userId !== userId) {
      return new NextResponse("Unauthorized", { status: 403 })
    }
    
    await prisma.notification.update({
      where: {
        id,
      },
      data: {
        read: true,
      },
    })
    
    return NextResponse.json({ success: true, message: "Notification marked as read" })
  } catch (error) {
    console.error("[NOTIFICATIONS_MARK_READ_POST]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
