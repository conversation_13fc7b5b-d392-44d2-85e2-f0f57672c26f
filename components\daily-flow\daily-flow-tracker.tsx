"use client"

import { useState, useEffect } from "react"
import { format, subDays } from "date-fns"
import { 
  Moon, 
  Coffee, 
  Zap, 
  Activity, 
  Award, 
  Flame, 
  Plus, 
  ChevronRight, 
  Sparkles,
  Utensils,
  Scale,
  Ruler
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface Streak {
  id: string
  type: string
  currentCount: number
  longestCount: number
  lastLoggedAt: string | null
}

interface Achievement {
  id: string
  type: string
  name: string
  description: string
  earnedAt: string
  iconName: string | null
  level: number
}

export function DailyFlowTracker() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("today")
  const [isLoading, setIsLoading] = useState(true)
  const [streaks, setStreaks] = useState<Streak[]>([])
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [showLogDialog, setShowLogDialog] = useState(false)
  const [logType, setLogType] = useState<string>("")
  
  // Form values
  const [sleepHours, setSleepHours] = useState<number>(7)
  const [stressLevel, setStressLevel] = useState<number>(5)
  const [coffeeCount, setCoffeeCount] = useState<number>(2)
  
  // Fetch streaks and achievements
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // Fetch streaks
        const streaksResponse = await fetch("/api/streaks")
        if (streaksResponse.ok) {
          const streaksData = await streaksResponse.json()
          setStreaks(streaksData)
        }
        
        // Fetch achievements
        const achievementsResponse = await fetch("/api/achievements")
        if (achievementsResponse.ok) {
          const achievementsData = await achievementsResponse.json()
          setAchievements(achievementsData)
        }
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchData()
  }, [])
  
  // Handle logging metrics
  const handleLogMetrics = async () => {
    try {
      setIsLoading(true)
      
      // Create the measurement with the new metrics
      const response = await fetch("/api/progress", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          weight: 0, // Required by the API but we'll update it later
          sleepHours,
          stressLevel,
          coffeeCount,
          notes: `Daily metrics logged for ${format(new Date(), 'PPP')}`,
        }),
      })
      
      if (!response.ok) {
        throw new Error("Failed to log metrics")
      }
      
      // Refresh streaks
      const streaksResponse = await fetch("/api/streaks")
      if (streaksResponse.ok) {
        const streaksData = await streaksResponse.json()
        setStreaks(streaksData)
      }
      
      // Refresh achievements
      const achievementsResponse = await fetch("/api/achievements")
      if (achievementsResponse.ok) {
        const achievementsData = await achievementsResponse.json()
        setAchievements(achievementsData)
      }
      
      toast({
        title: "Success!",
        description: "Your daily metrics have been logged.",
        variant: "default",
      })
      
      setShowLogDialog(false)
    } catch (error) {
      console.error("Error logging metrics:", error)
      toast({
        title: "Error",
        description: "Failed to log your metrics. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  // Open log dialog for specific metric
  const openLogDialog = (type: string) => {
    setLogType(type)
    setShowLogDialog(true)
  }
  
  // Get streak for a specific type
  const getStreak = (type: string) => {
    return streaks.find(streak => streak.type === type) || { currentCount: 0, longestCount: 0 }
  }
  
  // Get icon for metric type
  const getMetricIcon = (type: string, className = "h-5 w-5") => {
    switch (type) {
      case "sleep":
        return <Moon className={className} />
      case "stress":
        return <Zap className={className} />
      case "coffee":
        return <Coffee className={className} />
      case "nutrition":
        return <Utensils className={className} />
      case "measurement":
        return <Scale className={className} />
      default:
        return <Activity className={className} />
    }
  }
  
  // Get color for metric type
  const getMetricColor = (type: string) => {
    switch (type) {
      case "sleep":
        return "bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-400 dark:border-indigo-800"
      case "stress":
        return "bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-800"
      case "coffee":
        return "bg-brown-100 text-brown-800 border-brown-200 dark:bg-brown-900/30 dark:text-brown-400 dark:border-brown-800"
      case "nutrition":
        return "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800"
      case "measurement":
        return "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-400 dark:border-gray-800"
    }
  }
  
  return (
    <div className="space-y-6">
      {/* Header with Tabs */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Daily Flow</h2>
          <p className="text-muted-foreground">Track your daily habits and build streaks</p>
        </div>
        <Tabs defaultValue="today" value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="today">Today</TabsTrigger>
            <TabsTrigger value="streaks">Streaks</TabsTrigger>
            <TabsTrigger value="achievements">Achievements</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {/* Today's Metrics Tab */}
      {activeTab === "today" && (
        <div className="grid gap-4 md:grid-cols-3">
          {/* Sleep Card */}
          <Card className="premium-card group">
            <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
            <CardHeader className="relative z-10 pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Moon className="h-5 w-5 text-indigo-500" />
                    <span>Sleep</span>
                  </CardTitle>
                  <CardDescription>Track your sleep quality</CardDescription>
                </div>
                <Badge variant="outline" className="bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-400 dark:border-indigo-800">
                  <Flame className="h-3 w-3 mr-1" />
                  {getStreak("sleep").currentCount} day streak
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="relative z-10 pt-2">
              <div className="flex flex-col items-center justify-center py-6">
                <div className="text-4xl font-bold text-indigo-500 mb-2">7-9</div>
                <div className="text-sm text-muted-foreground">Recommended hours</div>
              </div>
            </CardContent>
            <CardFooter className="relative z-10 pt-0">
              <Button 
                onClick={() => openLogDialog("sleep")} 
                className="w-full bg-indigo-500 hover:bg-indigo-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Log Sleep
              </Button>
            </CardFooter>
          </Card>
          
          {/* Stress Card */}
          <Card className="premium-card group">
            <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
            <CardHeader className="relative z-10 pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-amber-500" />
                    <span>Stress</span>
                  </CardTitle>
                  <CardDescription>Monitor your stress levels</CardDescription>
                </div>
                <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-800">
                  <Flame className="h-3 w-3 mr-1" />
                  {getStreak("stress").currentCount} day streak
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="relative z-10 pt-2">
              <div className="space-y-4">
                <div className="flex justify-between text-sm">
                  <span>Low</span>
                  <span>High</span>
                </div>
                <div className="h-2 bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 rounded-full"></div>
              </div>
            </CardContent>
            <CardFooter className="relative z-10 pt-0">
              <Button 
                onClick={() => openLogDialog("stress")} 
                className="w-full bg-amber-500 hover:bg-amber-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Log Stress
              </Button>
            </CardFooter>
          </Card>
          
          {/* Coffee Card */}
          <Card className="premium-card group">
            <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
            <CardHeader className="relative z-10 pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Coffee className="h-5 w-5 text-amber-700" />
                    <span>Coffee</span>
                  </CardTitle>
                  <CardDescription>Track your caffeine intake</CardDescription>
                </div>
                <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-800">
                  <Flame className="h-3 w-3 mr-1" />
                  {getStreak("coffee").currentCount} day streak
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="relative z-10 pt-2">
              <div className="flex items-center justify-center py-6">
                <div className="flex items-end">
                  <div className="text-4xl font-bold text-amber-700">2-3</div>
                  <div className="text-lg ml-2 mb-1 text-muted-foreground">cups/day</div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="relative z-10 pt-0">
              <Button 
                onClick={() => openLogDialog("coffee")} 
                className="w-full bg-amber-700 hover:bg-amber-800 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Log Coffee
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}
      
      {/* Streaks Tab */}
      {activeTab === "streaks" && (
        <div className="space-y-6">
          <Card className="premium-card">
            <div className="absolute inset-0 bg-grid-pattern opacity-5 transition-opacity"></div>
            <CardHeader className="relative z-10">
              <CardTitle className="flex items-center gap-2">
                <Flame className="h-5 w-5 text-orange-500" />
                <span>Your Active Streaks</span>
              </CardTitle>
              <CardDescription>Keep your streaks going to earn achievements</CardDescription>
            </CardHeader>
            <CardContent className="relative z-10">
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : streaks.length > 0 ? (
                <div className="space-y-4">
                  {streaks.map((streak) => (
                    <div key={streak.id} className="flex items-center justify-between p-4 rounded-lg border bg-background/60 hover:bg-background/80 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-full ${getMetricColor(streak.type)}`}>
                          {getMetricIcon(streak.type)}
                        </div>
                        <div>
                          <div className="font-medium capitalize">{streak.type}</div>
                          <div className="text-sm text-muted-foreground">
                            {streak.lastLoggedAt ? `Last logged: ${format(new Date(streak.lastLoggedAt), 'PPP')}` : 'Not started yet'}
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="flex items-center">
                          <Flame className="h-4 w-4 text-orange-500 mr-1" />
                          <span className="font-bold">{streak.currentCount}</span>
                          <span className="text-sm text-muted-foreground ml-1">days</span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Best: {streak.longestCount} days
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Start logging your daily metrics to build streaks!
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Achievements Tab */}
      {activeTab === "achievements" && (
        <div className="space-y-6">
          <Card className="premium-card">
            <div className="absolute inset-0 bg-grid-pattern opacity-5 transition-opacity"></div>
            <CardHeader className="relative z-10">
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-yellow-500" />
                <span>Your Achievements</span>
              </CardTitle>
              <CardDescription>Milestones you've reached on your journey</CardDescription>
            </CardHeader>
            <CardContent className="relative z-10">
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : achievements.length > 0 ? (
                <div className="space-y-4">
                  {achievements.map((achievement) => (
                    <div key={achievement.id} className="flex items-center justify-between p-4 rounded-lg border bg-background/60 hover:bg-background/80 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-full bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-800">
                          {achievement.iconName === "fire" ? (
                            <Flame className="h-5 w-5" />
                          ) : (
                            <Award className="h-5 w-5" />
                          )}
                        </div>
                        <div>
                          <div className="font-medium">{achievement.name}</div>
                          <div className="text-sm text-muted-foreground">{achievement.description}</div>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {format(new Date(achievement.earnedAt), 'PPP')}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Keep logging your metrics to earn achievements!
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Log Metrics Dialog */}
      <Dialog open={showLogDialog} onOpenChange={setShowLogDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {logType === "sleep" && <Moon className="h-5 w-5 text-indigo-500" />}
              {logType === "stress" && <Zap className="h-5 w-5 text-amber-500" />}
              {logType === "coffee" && <Coffee className="h-5 w-5 text-amber-700" />}
              <span>Log Your {logType ? logType.charAt(0).toUpperCase() + logType.slice(1) : "Metrics"}</span>
            </DialogTitle>
            <DialogDescription>
              Track your daily metrics to build streaks and earn achievements.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6 py-4">
            {/* Sleep Hours Input */}
            {(logType === "sleep" || !logType) && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="sleep-hours" className="flex items-center gap-2">
                    <Moon className="h-4 w-4 text-indigo-500" />
                    <span>Hours of Sleep</span>
                  </Label>
                  <span className="text-lg font-semibold">{sleepHours}</span>
                </div>
                <Slider
                  id="sleep-hours"
                  min={0}
                  max={12}
                  step={0.5}
                  value={[sleepHours]}
                  onValueChange={(value) => setSleepHours(value[0])}
                  className="[&>span:first-child]:bg-indigo-500"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>0h</span>
                  <span>6h</span>
                  <span>12h</span>
                </div>
              </div>
            )}
            
            {/* Stress Level Input */}
            {(logType === "stress" || !logType) && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="stress-level" className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-amber-500" />
                    <span>Stress Level</span>
                  </Label>
                  <span className="text-lg font-semibold">{stressLevel}/10</span>
                </div>
                <Slider
                  id="stress-level"
                  min={1}
                  max={10}
                  step={1}
                  value={[stressLevel]}
                  onValueChange={(value) => setStressLevel(value[0])}
                  className="[&>span:first-child]:bg-gradient-to-r [&>span:first-child]:from-green-500 [&>span:first-child]:via-yellow-500 [&>span:first-child]:to-red-500"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Low (1)</span>
                  <span>Medium (5)</span>
                  <span>High (10)</span>
                </div>
              </div>
            )}
            
            {/* Coffee Count Input */}
            {(logType === "coffee" || !logType) && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="coffee-count" className="flex items-center gap-2">
                    <Coffee className="h-4 w-4 text-amber-700" />
                    <span>Cups of Coffee</span>
                  </Label>
                  <span className="text-lg font-semibold">{coffeeCount}</span>
                </div>
                <div className="flex items-center justify-between gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setCoffeeCount(Math.max(0, coffeeCount - 1))}
                    disabled={coffeeCount <= 0}
                  >
                    -
                  </Button>
                  <div className="flex-1">
                    <Slider
                      id="coffee-count"
                      min={0}
                      max={10}
                      step={1}
                      value={[coffeeCount]}
                      onValueChange={(value) => setCoffeeCount(value[0])}
                      className="[&>span:first-child]:bg-amber-700"
                    />
                  </div>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setCoffeeCount(Math.min(10, coffeeCount + 1))}
                    disabled={coffeeCount >= 10}
                  >
                    +
                  </Button>
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>0 cups</span>
                  <span>5 cups</span>
                  <span>10 cups</span>
                </div>
              </div>
            )}
          </div>
          
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowLogDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleLogMetrics} disabled={isLoading}>
              {isLoading ? "Saving..." : "Save Metrics"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
