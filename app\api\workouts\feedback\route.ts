import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

/**
 * API route for saving workout feedback
 * POST /api/workouts/feedback
 */
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { workoutId, clientId, feedback } = await req.json();
    
    if (!workoutId || !clientId || !feedback) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    if (session.user.role !== 'trainer' && session.user.id !== clientId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }
    
    const updatedWorkout = await prisma.workoutLog.update({
      where: { id: workoutId },
      data: { notes: feedback },
    });
    
    return NextResponse.json(updatedWorkout);
  } catch (error) {
    console.error('Error saving workout feedback:', error);
    return NextResponse.json(
      { error: 'Failed to save feedback' },
      { status: 500 }
    );
  }
}
