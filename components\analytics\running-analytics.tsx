'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import {
  <PERSON><PERSON>hart, Line, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';

interface RunningData {
  date: string;
  distance: number;
  pace: string;
  duration: number;
  elevation: number;
  paceMinutes?: number;
}

export function RunningAnalytics() {
  // For now, we'll use mock data since there's no API endpoint for running metrics yet
  // In a real implementation, this would fetch from an API
  const [runningData, setRunningData] = useState<RunningData[]>([
    { date: "Mar 13", distance: 5.2, pace: "5:30", duration: 28.6, elevation: 120 },
    { date: "Mar 14", distance: 0, pace: "0:00", duration: 0, elevation: 0 },
    { date: "Mar 15", distance: 8.0, pace: "5:25", duration: 43.3, elevation: 210 },
    { date: "Mar 16", distance: 0, pace: "0:00", duration: 0, elevation: 0 },
    { date: "Mar 17", distance: 6.5, pace: "5:15", duration: 34.1, elevation: 150 },
    { date: "Mar 18", distance: 0, pace: "0:00", duration: 0, elevation: 0 },
    { date: "Mar 19", distance: 10.0, pace: "5:20", duration: 53.3, elevation: 320 },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Process data to convert pace strings to numbers for charting
  useEffect(() => {
    const processedData = runningData.map(run => {
      if (run.pace === "0:00") return { ...run, paceMinutes: 0 };
      
      const [minutes, seconds] = run.pace.split(':').map(Number);
      const paceMinutes = minutes + (seconds / 60);
      
      return {
        ...run,
        paceMinutes
      };
    });
    
    setRunningData(processedData);
  }, []);

  return (
    <div className="space-y-4">
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Running Distance</CardTitle>
          <CardDescription>
            Track your running distance over time
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={runningData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="distance"
                stroke="#00C49F"
                name="Distance (km)"
                activeDot={{ r: 8 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Running Pace</CardTitle>
            <CardDescription>
              Track your pace over time
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={runningData.filter(d => d.paceMinutes !== 0)}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis domain={['dataMin - 0.5', 'dataMax + 0.5']} />
                <Tooltip formatter={(value: any) => value === 0 ? ['N/A', 'Pace'] : [`${Math.floor(value)}:${Math.round((value % 1) * 60).toString().padStart(2, '0')}`, 'Pace (min/km)']} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="paceMinutes"
                  stroke="#FF8042"
                  name="Pace (min/km)"
                  activeDot={{ r: 8 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Running Duration</CardTitle>
            <CardDescription>
              Track your running time
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={runningData.filter(d => d.duration > 0)}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="duration" fill="#82ca9d" name="Duration (min)" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <div className="text-center text-sm text-muted-foreground mt-4">
        <p>Note: Connect your running app for real-time running data.</p>
      </div>
    </div>
  );
}
