"use client";

import { useState, useCallback, ChangeEvent, FormEvent } from 'react';
import { ZodType } from 'zod';
import { useToast } from '@/components/ui/use-toast';
import { validateForm, FormValidationError } from '@/lib/validation/formValidation';
import { sanitizeFormData } from '@/lib/validation/sanitization';

export interface UseSafeFormOptions<T> {
  initialValues: T;
  validationSchema?: ZodType<T>;
  onSubmit: (values: T) => void | Promise<void>;
  sanitize?: boolean;
}

export interface UseSafeFormReturn<T> {
  values: T;
  errors: FormValidationError[];
  isSubmitting: boolean;
  handleChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleSelectChange: (name: string, value: string) => void;
  setFieldValue: (name: string, value: any) => void;
  handleSubmit: (e: FormEvent<HTMLFormElement>) => Promise<void>;
  reset: () => void;
  getFieldError: (fieldName: string) => string | undefined;
}

/**
 * A custom hook for secure form handling with built-in validation and sanitization
 * @param options Configuration options for the form
 * @returns Form state and handlers
 */
export function useSafeForm<T extends Record<string, any>>({
  initialValues,
  validationSchema,
  onSubmit,
  sanitize = true
}: UseSafeFormOptions<T>): UseSafeFormReturn<T> {
  // State for form values, errors, and submission status
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<FormValidationError[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Access toast notifications
  const { toast } = useToast();
  
  // Handle input change
  const handleChange = useCallback((
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setValues(prevValues => ({ ...prevValues, [name]: value }));
    
    // Clear error for this field when user types
    setErrors(prev => prev.filter(error => error.field !== name));
  }, []);
  
  // Handle select change (for select components)
  const handleSelectChange = useCallback((name: string, value: string) => {
    setValues(prevValues => ({ ...prevValues, [name]: value }));
    setErrors(prev => prev.filter(error => error.field !== name));
  }, []);
  
  // Set a specific field value
  const setFieldValue = useCallback((name: string, value: any) => {
    setValues(prevValues => ({ ...prevValues, [name]: value }));
    setErrors(prev => prev.filter(error => error.field !== name));
  }, []);
  
  // Handle form submission
  const handleSubmit = useCallback(async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Sanitize form data if requested
      const sanitizedValues = sanitize ? sanitizeFormData(values) : values;
      
      // Validate form data if schema is provided
      if (validationSchema) {
        const validationErrors = validateForm(validationSchema, sanitizedValues);
        
        if (validationErrors.length > 0) {
          setErrors(validationErrors);
          toast({
            title: "Validation error",
            description: "Please check the form for errors",
            variant: "destructive"
          });
          setIsSubmitting(false);
          return;
        }
      }
      
      // Clear errors if validation passes
      setErrors([]);
      
      // Call the submit handler
      await onSubmit(sanitizedValues);
    } catch (error) {
      // Handle any errors that occur during submission
      console.error("Form submission error:", error);
      toast({
        title: "Submission error",
        description: error instanceof Error ? error.message : "An error occurred during form submission",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [values, validationSchema, onSubmit, sanitize, toast]);
  
  // Reset form to initial values
  const reset = useCallback(() => {
    setValues(initialValues);
    setErrors([]);
  }, [initialValues]);
  
  // Helper function to get an error message for a specific field
  const getFieldError = useCallback((fieldName: string): string | undefined => {
    const error = errors.find(err => err.field === fieldName);
    return error?.message;
  }, [errors]);
  
  return {
    values,
    errors,
    isSubmitting,
    handleChange,
    handleSelectChange,
    setFieldValue,
    handleSubmit,
    reset,
    getFieldError
  };
} 