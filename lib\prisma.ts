import { PrismaClient } from "@prisma/client"

// PrismaClient is attached to the `global` object in development to prevent
// exhausting your database connection limit.
// Learn more: https://pris.ly/d/help/next-js-best-practices

declare global {
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined
}

/**
 * Create a connection string with connection pooling parameters
 * This is the correct way to add connection pooling to Prisma with PostgreSQL
 */
function getDatabaseUrl() {
  const url = process.env.DATABASE_URL;

  // Only modify the URL if we're in a production or Docker environment
  if (!url || process.env.NODE_ENV === 'test') {
    return url;
  }

  try {
    // Parse the existing URL
    const parsedUrl = new URL(url);

    // Add connection pooling parameters if they don't exist
    if (!parsedUrl.searchParams.has('connection_limit')) {
      parsedUrl.searchParams.set('connection_limit', '10');
    }

    if (!parsedUrl.searchParams.has('pool_timeout')) {
      parsedUrl.searchParams.set('pool_timeout', '10');
    }

    if (!parsedUrl.searchParams.has('idle_timeout')) {
      parsedUrl.searchParams.set('idle_timeout', '30');
    }

    return parsedUrl.toString();
  } catch (error) {
    console.warn('Error parsing DATABASE_URL for connection pooling:', error);
    return url;
  }
}

// This ensures we always have a valid Prisma client instance with optimized settings
const getPrismaClient = () => {
  if (global.prisma) {
    return global.prisma;
  }

  // Create the Prisma client with the optimized connection URL
  const client = new PrismaClient({
    log: process.env.NODE_ENV === "development" ? ["query", "error", "warn"] : ["error"],
    datasources: {
      db: {
        url: getDatabaseUrl(),
      },
    },
  });

  if (process.env.NODE_ENV !== "production") {
    global.prisma = client;
  }

  return client;
};

export const prisma = getPrismaClient();