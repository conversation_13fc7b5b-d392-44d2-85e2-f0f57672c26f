'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquare, User, ArrowRight, RefreshCw } from 'lucide-react';
import Link from 'next/link';

export default function DirectTestPage() {
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<string>('');

  const runDirectSetup = async () => {
    try {
      setStatus('loading');
      setError('');
      
      console.log('Running direct setup...');
      const response = await fetch('/api/direct-setup');
      const result = await response.json();
      
      if (!response.ok || !result.success) {
        const errorMessage = result.error || 'Failed to run direct setup';
        console.error('Setup failed:', errorMessage);
        setError(errorMessage);
        setStatus('error');
        return;
      }
      
      console.log('Setup successful:', result);
      setData(result);
      setStatus('success');
    } catch (err) {
      console.error('Error running direct setup:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setStatus('error');
    }
  };

  useEffect(() => {
    // Auto-start setup when the page loads
    runDirectSetup();
  }, []);

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-8 text-center">Direct Chat Setup</h1>
      
      <div className="max-w-3xl mx-auto space-y-8">
        {/* Setup Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Direct Setup Status
            </CardTitle>
            <CardDescription>Status of the direct chat setup</CardDescription>
          </CardHeader>
          <CardContent>
            {status === 'idle' && (
              <div className="text-center p-6">
                <p>Click the button below to run the direct setup.</p>
              </div>
            )}
            
            {status === 'loading' && (
              <div className="flex items-center justify-center p-6">
                <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                <span className="ml-2">Running direct setup...</span>
              </div>
            )}
            
            {status === 'error' && (
              <div className="bg-destructive/10 text-destructive p-4 rounded-md space-y-2">
                <p className="font-medium">Failed to run direct setup</p>
                <p className="text-sm">{error}</p>
                <div className="flex flex-col space-y-2">
                  <Button 
                    variant="destructive" 
                    className="mt-2" 
                    onClick={runDirectSetup}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Try Again
                  </Button>
                  
                  <Link href="/mock-chat">
                    <Button variant="outline" className="w-full">
                      <MessageSquare className="mr-2 h-4 w-4" />
                      Use Mock Chat Instead
                    </Button>
                  </Link>
                </div>
              </div>
            )}
            
            {status === 'success' && data && (
              <div className="space-y-4">
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-md text-green-700 dark:text-green-300">
                  <p className="font-medium">Setup completed successfully!</p>
                  <p className="text-sm mt-1">You can now test the 1:1 chat functionality.</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-muted p-4 rounded-md">
                    <h3 className="font-medium mb-2">Trainer</h3>
                    <p><strong>Name:</strong> {data.trainer.name}</p>
                    <p><strong>Email:</strong> {data.trainer.email}</p>
                  </div>
                  
                  <div className="bg-muted p-4 rounded-md">
                    <h3 className="font-medium mb-2">Client</h3>
                    <p><strong>Name:</strong> {data.client.name}</p>
                    <p><strong>Email:</strong> {data.client.email}</p>
                  </div>
                </div>
                
                <div className="bg-primary/5 p-4 rounded-md border border-primary/20">
                  <h3 className="font-medium mb-2">Chat Details</h3>
                  <p><strong>Relationship ID:</strong> {data.coachingRelationship.id}</p>
                  <p><strong>Conversation ID:</strong> {data.conversation.id}</p>
                  <p><strong>Test Message:</strong> "{data.testMessage?.content || 'No test message'}"</p>
                </div>
              </div>
            )}
          </CardContent>
          {status === 'success' && data && (
            <CardFooter className="flex-col space-y-2">
              <div className="grid grid-cols-2 gap-4 w-full">
                <Link href={data.loginLinks.client} className="w-full">
                  <Button className="w-full">
                    <User className="mr-2 h-4 w-4" />
                    Login as Client
                  </Button>
                </Link>
                
                <Link href={data.loginLinks.trainer} className="w-full">
                  <Button variant="outline" className="w-full">
                    <User className="mr-2 h-4 w-4" />
                    Login as Trainer
                  </Button>
                </Link>
              </div>
              
              <Link href={data.chatLink} className="w-full">
                <Button variant="secondary" className="w-full mt-4">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Go to Chat
                </Button>
              </Link>
              
              <p className="text-xs text-center text-muted-foreground mt-2">
                Direct URL: <code className="bg-muted p-1 rounded">{data.chatLink}</code>
              </p>
            </CardFooter>
          )}
        </Card>
        
        {/* Instructions Card */}
        <Card>
          <CardHeader>
            <CardTitle>How to Test the Chat</CardTitle>
            <CardDescription>Follow these steps to test the 1:1 chat functionality</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h3 className="font-medium">1. Run the direct setup</h3>
              <p className="text-sm text-muted-foreground">
                The direct setup process creates test users, a coaching relationship, and a conversation with a test message.
              </p>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium">2. Log in as a client</h3>
              <p className="text-sm text-muted-foreground">
                Click the "Login as Client" button to log in as the test client user.
              </p>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium">3. Access the chat</h3>
              <p className="text-sm text-muted-foreground">
                Click the "Go to Chat" button to access the coaching chat page.
              </p>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium">4. Test both sides (optional)</h3>
              <p className="text-sm text-muted-foreground">
                Open a new browser window or incognito mode, log in as the trainer, and access the chat to test both sides of the conversation.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
