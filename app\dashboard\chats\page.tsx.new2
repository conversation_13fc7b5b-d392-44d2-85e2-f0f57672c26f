  // Initial data loading
  useEffect(() => {
    if (session?.user?.id) {
      // Only fetch data once when the component mounts
      fetchConversations(true);
      
      if (session.user.role === "trainer") {
        fetchClients();
      }
    }
  }, [session?.user?.id, fetchConversations, fetchClients]);

  // Load messages when active conversation changes - but only when it actually changes
  useEffect(() => {
    if (activeConversation?.id) {
      fetchMessages(activeConversation.id);
    }
  }, [activeConversation?.id]);

  // Handle joining/leaving conversations when the active conversation changes
  useEffect(() => {
    if (isConnected && activeConversation?.id) {
      joinConversation(activeConversation.id);
      
      // Clean up when component unmounts or conversation changes
      return () => {
        leaveConversation(activeConversation.id);
      };
    }
  }, [isConnected, activeConversation?.id, joinConversation, leaveConversation]);

  // Set up WebSocket event handlers for real-time updates ONLY when messages are received
  useEffect(() => {
    if (!session?.user?.id) return;
    
    console.log("Setting up WebSocket event handlers");
    
    // Set up a custom event listener for manually sent messages
    const handleMessageSent = () => {
      console.log('Message sent event received - UI already updated optimistically');
    };

    // Set up a custom event listener for messages received via WebSocket
    const handleMessageReceived = (event: Event) => {
      console.log('Chat page: Message received via WebSocket');

      try {
        // Get the event detail
        const detail = (event as CustomEvent).detail;
        
        // Only update if we have a valid message
        if (!detail.message || !detail.message.conversationId) {
          return;
        }

        // Update only the specific conversation that received a message
        if (activeConversation?.id && detail.message.conversationId === activeConversation.id) {
          console.log('Updating active conversation messages');

          // Add the message to the list if it's not already there
          const messageExists = messages.some(msg => msg.id === detail.message.id);

          if (!messageExists) {
            setMessages(prev => [...prev, detail.message]);
            scrollToBottom();
          }

          // Also update the active conversation's last message
          setActiveConversation(prev => {
            if (!prev) return prev;
            return {
              ...prev,
              lastMessage: {
                content: detail.message.content,
                createdAt: detail.message.createdAt
              },
              unreadCount: 0 // Reset unread count for active conversation
            } as Conversation;
          });
        }
        
        // Update the conversation in the list
        setConversations(prev => {
          // Check if the conversation exists in the list
          const conversationExists = prev.some(conv => conv.id === detail.message.conversationId);
          
          if (!conversationExists) {
            console.log('Conversation not found in list, but skipping fetch to prevent constant updates');
            return prev;
          }
          
          // Otherwise, just update the specific conversation
          return prev.map(conv =>
            conv.id === detail.message.conversationId
              ? {
                  ...conv,
                  lastMessage: {
                    content: detail.message.content,
                    createdAt: detail.message.createdAt
                  },
                  unreadCount: activeConversation?.id === conv.id ? 0 : conv.unreadCount + 1
                }
              : conv
          );
        });
      } catch (error) {
        console.error('Error handling message-received event:', error);
      }
    };

    // Set up a custom event listener for socket events
    const handleSocketEvent = (event: Event) => {
      const { detail } = event as CustomEvent;
      
      if (detail.event === 'new-message') {
        console.log('New message event received via socket event');

        // Only update if we have a valid payload
        if (!detail.payload || !detail.payload.conversationId) {
          return;
        }

        // Update only the specific conversation that received a message
        if (activeConversation?.id && detail.payload.conversationId === activeConversation.id) {
          console.log('Updating active conversation with new message');

          // Add the message to the list if it's not already there
          const messageExists = messages.some(msg => msg.id === detail.payload.id);

          if (!messageExists) {
            setMessages(prev => [...prev, detail.payload]);
            scrollToBottom();
          }

          // Also update the active conversation's last message
          setActiveConversation(prev => {
            if (!prev) return prev;
            return {
              ...prev,
              lastMessage: {
                content: detail.payload.content,
                createdAt: detail.payload.createdAt
              },
              unreadCount: 0 // Reset unread count for active conversation
            } as Conversation;
          });
        }
        
        // Update the conversation in the list
        setConversations(prev => {
          // Check if the conversation exists in the list
          const conversationExists = prev.some(conv => conv.id === detail.payload.conversationId);
          
          if (!conversationExists) {
            console.log('Conversation not found in list, but skipping fetch to prevent constant updates');
            return prev;
          }
          
          // Otherwise, just update the specific conversation
          return prev.map(conv =>
            conv.id === detail.payload.conversationId
              ? {
                  ...conv,
                  lastMessage: {
                    content: detail.payload.content,
                    createdAt: detail.payload.createdAt
                  },
                  unreadCount: activeConversation?.id === conv.id ? 0 : conv.unreadCount + 1
                }
              : conv
          );
        });
      }
    };

    // Add event listeners
    window.addEventListener('message-sent', handleMessageSent);
    window.addEventListener('message-received', handleMessageReceived);
    window.addEventListener('socket-event', handleSocketEvent);

    // Clean up
    return () => {
      console.log("Removing WebSocket event handlers");
      window.removeEventListener('message-sent', handleMessageSent);
      window.removeEventListener('message-received', handleMessageReceived);
      window.removeEventListener('socket-event', handleSocketEvent);
    };
  }, [session?.user?.id]);

  // Handle sending a new message
  const sendNewMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newMessage.trim() || !activeConversation?.id || sendingMessage) return;
    
    try {
      setSendingMessage(true);
      
      // Create a temporary message ID
      const tempId = `temp-${Date.now()}`;
      
      // Create a temporary message object for optimistic UI update
      const tempMessage: Message = {
        id: tempId,
        content: newMessage,
        senderId: session?.user?.id || "",
        conversationId: activeConversation.id,
        createdAt: new Date().toISOString(),
        read: false,
        sender: {
          id: session?.user?.id || "",
          name: session?.user?.name || "You",
          avatarUrl: session?.user?.image || undefined
        }
      };
      
      // Optimistically add the message to the UI
      setMessages(prev => [...prev, tempMessage]);
      
      // Clear the input field
      setNewMessage("");
      
      // Scroll to bottom
      scrollToBottom();
      
      // Update the conversation's last message
      setConversations(prev => 
        prev.map(conv => 
          conv.id === activeConversation.id 
            ? {
                ...conv,
                lastMessage: {
                  content: newMessage,
                  createdAt: new Date().toISOString()
                }
              }
            : conv
        )
      );
      
      // Send the message to the server
      const response = await fetch("/api/coaching/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: newMessage,
          conversationId: activeConversation.id,
        }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to send message");
      }
      
      const data = await response.json();
      
      // Replace the temporary message with the real one
      setMessages(prev => 
        prev.map(msg => 
          msg.id === tempId ? data : msg
        )
      );
      
      // Notify via WebSocket
      socketSendMessage({
        conversationId: activeConversation.id,
        message: data
      });
      
      // Dispatch a custom event to notify that a message was sent
      window.dispatchEvent(new CustomEvent('message-sent', {
        detail: {
          message: data
        }
      }));
      
    } catch (error) {
      console.error("Error sending message:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to send message. Please try again.",
      });
      
      // Remove the temporary message
      setMessages(prev => prev.filter(msg => msg.id !== `temp-${Date.now()}`));
      
      // Restore the message in the input field
      setNewMessage(newMessage);
    } finally {
      setSendingMessage(false);
      
      // Focus the input field
      inputRef.current?.focus();
    }
  };
