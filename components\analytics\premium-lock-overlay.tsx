'use client';

import { Lock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

interface PremiumLockOverlayProps {
  message?: string;
  showButton?: boolean;
}

export function PremiumLockOverlay({
  message = "Upgrade to unlock full analytics history",
  showButton = true
}: PremiumLockOverlayProps) {
  const router = useRouter();
  
  return (
    <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex flex-col items-center justify-center z-10 rounded-md">
      <div className="flex flex-col items-center text-center p-6 max-w-md">
        <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
          <Lock className="h-6 w-6 text-primary" />
        </div>
        <h3 className="text-xl font-semibold mb-2">Premium Feature</h3>
        <p className="text-muted-foreground mb-4">{message}</p>
        {showButton && (
          <Button 
            className="bg-primary hover:bg-primary/90" 
            onClick={() => router.push('/dashboard/upgrade')}
          >
            Upgrade Now
          </Button>
        )}
      </div>
    </div>
  );
}
