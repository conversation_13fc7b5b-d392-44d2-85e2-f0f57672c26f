'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';

interface MeasurementData {
  id: string;
  date: Date;
  measurements: {
    chest?: number;
    waist?: number;
    hips?: number;
    thighs?: number;
    arms?: number;
  } | null;
}

export function MeasurementsAnalytics() {
  const [measurementData, setMeasurementData] = useState<MeasurementData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Fetch measurement data
  useEffect(() => {
    const fetchMeasurementData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/progress');
        if (!response.ok) {
          throw new Error('Failed to fetch measurement data');
        }
        
        const data = await response.json();
        
        // Filter entries with measurements
        const measurementsData = data.filter((entry: any) => entry.measurements);
        
        // Sort by date
        const sortedData = [...measurementsData].sort((a, b) => 
          new Date(a.date).getTime() - new Date(b.date).getTime()
        );
        
        setMeasurementData(sortedData);
      } catch (error) {
        console.error('Error fetching measurement data:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load measurement data',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchMeasurementData();
  }, [toast]);

  // Format data for chart
  const chartData = measurementData.map(entry => ({
    date: format(new Date(entry.date), 'MMM dd'),
    chest: entry.measurements?.chest,
    waist: entry.measurements?.waist,
    hips: entry.measurements?.hips,
    thighs: entry.measurements?.thighs,
    arms: entry.measurements?.arms
  }));

  if (isLoading) {
    return <div className="text-center py-6">Loading measurement data...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Body Measurements</CardTitle>
        <CardDescription>
          Track changes in your body measurements
        </CardDescription>
      </CardHeader>
      <CardContent className="h-[300px]">
        {measurementData.length > 0 ? (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip formatter={(value) => [`${value} in`, '']} />
              <Legend />
              {chartData.some(data => data.chest) && (
                <Line type="monotone" dataKey="chest" stroke="#8884d8" name="Chest" />
              )}
              {chartData.some(data => data.waist) && (
                <Line type="monotone" dataKey="waist" stroke="#82ca9d" name="Waist" />
              )}
              {chartData.some(data => data.hips) && (
                <Line type="monotone" dataKey="hips" stroke="#ffc658" name="Hips" />
              )}
              {chartData.some(data => data.thighs) && (
                <Line type="monotone" dataKey="thighs" stroke="#ff8042" name="Thighs" />
              )}
              {chartData.some(data => data.arms) && (
                <Line type="monotone" dataKey="arms" stroke="#0088fe" name="Arms" />
              )}
            </LineChart>
          </ResponsiveContainer>
        ) : (
          <div className="flex h-full items-center justify-center">
            <p className="text-muted-foreground">No measurement data available. Start tracking your measurements to see progress.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
