import { NextResponse } from "next/server"
import crypto from "crypto"
import { prisma } from "@/lib/prisma"
import logger from "@/lib/logger"

// Mark this route as dynamic
export const dynamic = 'force-dynamic'

// --- Helper: Hash token for lookup ---
function hashToken(token: string) {
  return crypto.createHash("sha256").update(token).digest("hex")
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get("token")

    if (!token) {
      return NextResponse.json({ message: "Token is missing." }, { status: 400 })
    }

    const hashedToken = hashToken(token)

    // 1. Find the verification token (get full object)
    const verificationToken = await prisma.verificationToken.findUnique({
      where: { token: hashedToken },
    })

    if (!verificationToken) {
      logger.warn({ tokenReceived: token ? '******' : 'null' }, "Invalid verification token received.")
      return NextResponse.json({ message: "Invalid token." }, { status: 400 })
    }

    // 2. Check if token has expired
    const hasExpired = new Date(verificationToken.expires) < new Date()

    if (hasExpired) {
      logger.warn({ identifier: verificationToken.identifier }, "Expired verification token used.")
      await prisma.verificationToken.delete({
          where: {
            identifier_token: {
              identifier: verificationToken.identifier,
              token: hashedToken
            }
          }
      });
      return NextResponse.json({ message: "Token has expired." }, { status: 410 }) // 410 Gone
    }

    // 3. Find the user associated with the token
    const user = await prisma.user.findUnique({
      where: { email: verificationToken.identifier },
    })

    if (!user) {
      logger.error({ identifier: verificationToken.identifier }, "User not found for valid verification token.")
      return NextResponse.json({ message: "User not found." }, { status: 404 })
    }

    // 4. Update user's emailVerified status
    await prisma.user.update({
      where: { id: user.id },
      data: { emailVerified: new Date() },
    })

    // 5. Delete the verification token
    await prisma.verificationToken.delete({
       where: {
         identifier_token: {
           identifier: verificationToken.identifier,
           token: hashedToken
         }
       }
    })

    logger.info({ userId: user.id, email: user.email }, "Email verified successfully.")
    return NextResponse.json({ message: "Email verified successfully." }, { status: 200 })

  } catch (error) {
    logger.error({ error }, "Email verification process failed");
    return NextResponse.json(
      { message: "An internal server error occurred." },
      { status: 500 },
    )
  }
}