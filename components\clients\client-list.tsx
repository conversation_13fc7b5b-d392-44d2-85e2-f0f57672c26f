"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useEffect, useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface Client {
  id: string
  fullName: string
  email: string
  bio: string | null
  createdAt: string
  clientSubscriptions: {
    id: string
    status: string
    tier: {
      name: string
      price: number
    }
    athlete: {
      id: string
      fullName: string
      email: string
    }
  }[]
  progress: {
    id: string
    weight: number
    bodyFat: number | null
  }[]
  workoutLogs: {
    id: string
    date: string
  }[]
  orders: {
    id: string
    status: string
    total: number
  }[]
}

export function ClientList() {
  const router = useRouter()
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchClients = async () => {
      try {
        const response = await fetch("/api/clients")
        if (!response.ok) {
          throw new Error("Failed to fetch clients")
        }
        const data = await response.json()
        setClients(data)
      } catch (error) {
        console.error("Error fetching clients:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchClients()
  }, [])

  if (loading) {
    return <div>Loading clients...</div>
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {clients.map((client) => (
        <Card key={client.id}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {client.fullName}
            </CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => router.push(`/dashboard/clients/${client.id}`)}>
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push(`/dashboard/clients/${client.id}/messages`)}>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Messages
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push(`/dashboard/clients/${client.id}/training`)}>
                  <Dumbbell className="mr-2 h-4 w-4" />
                  Training
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <Avatar>
                <AvatarImage src={`https://avatar.vercel.sh/${client.email}`} />
                <AvatarFallback>{client.fullName[0]}</AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-1">
                <p className="text-sm font-medium leading-none">{client.email}</p>
                <p className="text-sm text-muted-foreground">
                  {client.clientSubscriptions[0]?.tier.name || "No active subscription"}
                </p>
              </div>
            </div>
            {client.bio && (
              <p className="mt-4 text-sm text-muted-foreground">{client.bio}</p>
            )}
            <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
              <div>
                <p className="font-medium">{client.progress.length}</p>
                <p className="text-muted-foreground">Progress Records</p>
              </div>
              <div>
                <p className="font-medium">{client.workoutLogs.length}</p>
                <p className="text-muted-foreground">Workouts</p>
              </div>
              <div>
                <p className="font-medium">{client.orders.length}</p>
                <p className="text-muted-foreground">Orders</p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

