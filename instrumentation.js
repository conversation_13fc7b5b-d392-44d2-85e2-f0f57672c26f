// This file is used to configure Next.js instrumentation
// It helps with handling dynamic routes and suppressing warnings

export function register() {
  // This function is called when the app starts
  // It can be used to register instrumentation hooks
  console.log('Registering instrumentation hooks for dynamic routes');
}

// Mark all API routes as dynamic
export const dynamic = 'force-dynamic';
