'use client';

import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Sparkles, Target, Rocket, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";

interface TierBadgeProps {
  tier: string;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showUpgradeButton?: boolean;
  className?: string;
}

export function TierBadge({
  tier,
  size = 'md',
  showIcon = true,
  showUpgradeButton = true,
  className
}: TierBadgeProps) {
  // Define tier-specific properties
  const tierConfig = {
    basic: {
      label: 'Free',
      icon: Rocket,
      className: 'bg-gradient-to-r from-slate-500 to-slate-400 text-white',
    },
    mid: {
      label: 'Mid-tier',
      icon: Rocket,
      className: 'bg-gradient-to-r from-blue-500 to-blue-400 text-white',
    },
    premium: {
      label: 'Premium',
      icon: Sparkles,
      className: 'bg-gradient-to-r from-amber-500 to-amber-300 text-amber-900',
    },
    coaching: {
      label: '1:1 Coaching',
      icon: Target,
      className: 'bg-gradient-to-r from-primary to-primary/80 text-white',
    },
  };

  // Default to basic if tier not found
  const config = tierConfig[tier as keyof typeof tierConfig] || tierConfig.basic;
  const Icon = config.icon;

  // Size classes
  const sizeClasses = {
    sm: 'text-xs px-1.5 py-0.5',
    md: 'text-xs px-2 py-1',
    lg: 'text-sm px-2.5 py-1',
  };

  // Determine if we should show the upgrade button
  const canUpgrade = showUpgradeButton && (tier === 'basic' || tier === 'mid');

  return (
    <div className="flex items-center gap-1.5">
      <Badge
        className={cn(
          "font-medium rounded-full",
          config.className,
          sizeClasses[size],
          className
        )}
      >
        {showIcon && <Icon className={cn("mr-1", size === 'sm' ? 'h-3 w-3' : 'h-4 w-4')} />}
        {config.label}
      </Badge>

      {canUpgrade && (
        <Button
          asChild
          variant="ghost"
          size="sm"
          className={cn(
            "h-6 px-2 text-xs hover:bg-primary/10 hover:text-primary",
            size === 'sm' ? 'h-5 px-1.5 text-[10px]' : '',
            size === 'lg' ? 'h-7 px-2.5 text-xs' : ''
          )}
        >
          <Link href="/dashboard/upgrade">
            <ChevronUp className={cn("mr-1", size === 'sm' ? 'h-2.5 w-2.5' : 'h-3 w-3')} />
            Upgrade
          </Link>
        </Button>
      )}
    </div>
  );
}
