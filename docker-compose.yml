version: '3.8'

services:
  app:
    build:
      context: .
      target: development
    container_name: clear-coach-app-dev
    ports:
      - "3000:3000"
      - "5555:5555"  # Expose Prisma Studio port
      - "3001:3001"  # Expose WebSocket port for real-time chat
    volumes:
      - .:/app
      - node_modules:/app/node_modules
    environment:
      NODE_ENV: development
      DATABASE_URL: ****************************************/devdb
      WATCHPACK_POLLING: "true"  # Enable polling for Next.js hot reload
      ENABLE_PRISMA_STUDIO: "true"  # Enable Prisma Studio
      HOSTNAME: "0.0.0.0"
      PORT: "3000"
      SOCKET_PORT: "3001"
    depends_on:
      - db
    networks:
      - app-network

  db:
    image: postgres:15-alpine
    container_name: trainer-db-dev
    environment:
      POSTGRES_USER: devuser
      POSTGRES_PASSWORD: devpassword
      POSTGRES_DB: devdb
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U devuser -d devdb -h localhost"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  node_modules:
    driver: local