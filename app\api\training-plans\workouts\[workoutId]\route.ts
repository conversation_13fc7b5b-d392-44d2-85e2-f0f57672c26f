"use server";

import { NextResponse } from "next/server";
import { getAuthSession } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function DELETE(
  request: Request,
  context: any
) {
  try {
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized: You must be logged in" },
        { status: 401 }
      );
    }

    const workoutId = context?.params?.workoutId;
    if (!workoutId) {
      return NextResponse.json(
        { error: "Workout ID is required in URL" },
        { status: 400 }
      );
    }

    console.log("Attempting to delete workout:", {
      workoutId: workoutId,
      userId: session.user.id,
      userRole: session.user.role
    });

    // Remove mock workout handling if not needed in production
    // if (workoutId.startsWith('mock-workout-')) { ... }

    // Fetch workout for ownership check
    const workout = await prisma.workout.findUnique({
      where: {
        id: workoutId,
      },
      include: {
        trainingPlan: { select: { athleteId: true } }
      },
    });

    if (!workout) {
      console.log(`Workout ${workoutId} not found for deletion.`);
      return new NextResponse(null, { status: 204 });
    }

    if (workout.trainingPlan.athleteId !== session.user.id && session.user.role !== "admin") {
      console.warn(`Forbidden attempt to delete workout ${workoutId} by user ${session.user.id}`);
      return NextResponse.json(
        { error: "Forbidden: You do not have permission to delete this workout" },
        { status: 403 }
      );
    }

    await prisma.workout.delete({
      where: {
        id: workoutId,
      },
    });

    console.log(`Workout ${workoutId} deleted successfully by user ${session.user.id}`);
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error(`[WORKOUT_DELETE] Error deleting workout ${context?.params?.workoutId}:`, error);
    return NextResponse.json(
      { error: "An internal server error occurred while deleting the workout" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: Request,
  context: any
) {
  try {
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const workoutId = context?.params?.workoutId;
    if (!workoutId) {
      return NextResponse.json({ error: "Workout ID required" }, { status: 400 });
    }

    const body = await request.json();
    const { title, description, type } = body;

    if (!title || typeof title !== 'string') {
      return NextResponse.json({ error: "Invalid title" }, { status: 400 });
    }

    const workout = await prisma.workout.findUnique({
      where: { id: workoutId },
      include: { trainingPlan: { select: { athleteId: true } } }
    });

    if (!workout) {
      return NextResponse.json({ error: "Workout not found" }, { status: 404 });
    }

    if (workout.trainingPlan.athleteId !== session.user.id && session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const updateData: Record<string, any> = {};
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (type !== undefined) updateData.type = type;

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json({ error: "No valid fields provided for update" }, { status: 400 });
    }

    const updatedWorkout = await prisma.workout.update({
      where: { id: workoutId },
      data: updateData
    });

    return NextResponse.json(updatedWorkout);
  } catch (error) {
    console.error(`[WORKOUT_PATCH] Error updating workout ${context?.params?.workoutId}:`, error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}

export async function GET(
  request: Request,
  context: any
) {
  try {
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const workoutId = context?.params?.workoutId;
    if (!workoutId) {
      return NextResponse.json({ error: "Workout ID required" }, { status: 400 });
    }

    const workout = await prisma.workout.findUnique({
      where: { id: workoutId },
      include: {
        trainingPlan: {
          select: {
            athleteId: true,
            athlete: {
              select: { athleteSubscriptions: { where: { status: 'active' }}}
            }
          }
        },
        exercises: { orderBy: { order: 'asc' } }
      }
    });

    if (!workout) {
      return NextResponse.json({ error: "Workout not found" }, { status: 404 });
    }

    const isOwner = workout.trainingPlan.athleteId === session.user.id;
    const isAdmin = session.user.role === "admin";
    const isSubscribedClient = session.user.role === "client" && 
                              workout.trainingPlan.athlete?.athleteSubscriptions.some(sub => sub.clientId === session.user.id);

    if (!isOwner && !isAdmin && !isSubscribedClient) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    return NextResponse.json(workout);
  } catch (error) {
    console.error(`[WORKOUT_GET] Error fetching workout ${context?.params?.workoutId}:`, error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
} 