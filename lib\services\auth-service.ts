import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { NextResponse } from "next/server"

export class AuthService {
  /**
   * Validate that a user is authenticated
   */
  static async validateAuthentication() {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return { 
        isAuthenticated: false, 
        error: NextResponse.json({ error: "Unauthorized" }, { status: 401 }) 
      }
    }

    const userId = session.user.id
    if (!userId) {
      return { 
        isAuthenticated: false, 
        error: NextResponse.json({ error: "User ID not found" }, { status: 401 }) 
      }
    }

    return { 
      isAuthenticated: true, 
      userId,
      user: session.user
    }
  }

  /**
   * Validate that a user has a specific role
   */
  static async validateRole(requiredRole: string | string[]) {
    const auth = await this.validateAuthentication()
    
    if (!auth.isAuthenticated) {
      return auth
    }

    const userRole = auth.user.role

    const hasRequiredRole = Array.isArray(requiredRole) 
      ? requiredRole.includes(userRole)
      : userRole === requiredRole

    if (!hasRequiredRole) {
      return { 
        isAuthenticated: true, 
        hasRole: false,
        error: NextResponse.json({ error: "Forbidden" }, { status: 403 }) 
      }
    }

    return { 
      ...auth,
      hasRole: true
    }
  }
}
