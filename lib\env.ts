import { z } from "zod"

/**
 * Environment variable schema with validation
 */
const envSchema = z.object({
  // Database
  DATABASE_URL: z.string().min(1),
  
  // Authentication
  NEXTAUTH_SECRET: z.string().min(1),
  NEXTAUTH_URL: z.string().url().optional(),
  
  // Next.js
  NODE_ENV: z.enum(["development", "production", "test"]).default("development"),
  
  // Stripe (optional)
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  STRIPE_PRODUCT_ID: z.string().optional(),
  
  // Features
  ENABLE_SUBSCRIPTIONS: z.coerce.boolean().default(true),
  ENABLE_DIGITAL_PRODUCTS: z.coerce.boolean().default(true),
})

/**
 * Parse environment variables and validate them
 * @throws {Error} if required environment variables are missing
 */
function safeParseEnvironment() {
  const parsed = envSchema.safeParse({
    // Database
    DATABASE_URL: process.env.DATABASE_URL,
    
    // Authentication
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    
    // Next.js
    NODE_ENV: process.env.NODE_ENV,
    
    // Stripe (optional)
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
    STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
    STRIPE_PRODUCT_ID: process.env.STRIPE_PRODUCT_ID,
    
    // Features
    ENABLE_SUBSCRIPTIONS: process.env.ENABLE_SUBSCRIPTIONS,
    ENABLE_DIGITAL_PRODUCTS: process.env.ENABLE_DIGITAL_PRODUCTS,
  })
  
  if (!parsed.success) {
    console.error(
      "❌ Invalid environment variables:",
      parsed.error.flatten().fieldErrors
    )
    
    throw new Error("Invalid environment variables")
  }
  
  return parsed.data
}

/**
 * Type-safe environment variables
 */
export const env = safeParseEnvironment() 