"use client"

import { Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { toast } from "sonner"
import { Button } from "@/components/ui/button"

interface SubscribeButtonProps {
  trainerId: string
  plan: "monthly" | "annual"
  price: number
}

export function SubscribeButton({ trainerId, plan, price }: SubscribeButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleSubscribe = async () => {
    try {
      setIsLoading(true)
      
      // In a real application, this would call an API endpoint to handle subscription
      // For now, we'll simulate a successful subscription with a timeout
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Redirect to a confirmation page or show success message
      toast.success(
        `Successfully subscribed to ${plan} plan`, 
        { description: `You have subscribed to the ${plan} plan for $${price}` }
      )
      
      // In a real app, you might redirect to a confirmation page
      // router.push(`/dashboard/subscription/confirm?trainerId=${trainerId}&plan=${plan}`)
      
      // For now, let's route to the workouts page to simulate completion
      router.push("/dashboard/workouts/current")
    } catch (error) {
      console.error("Subscription error:", error)
      toast.error(
        "Subscription failed", 
        { description: "There was an error processing your subscription. Please try again." }
      )
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button 
      size="sm" 
      onClick={handleSubscribe}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Processing...
        </>
      ) : (
        <>Subscribe</>
      )}
    </Button>
  )
} 