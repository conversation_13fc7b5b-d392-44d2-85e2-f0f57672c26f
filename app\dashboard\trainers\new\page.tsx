"use client";

import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useEffect } from "react";
import { AddTrainerForm } from "@/components/admin/AddTrainerForm";
import { useToast } from "@/components/ui/use-toast";

export default function AddTrainerPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    // Check if user is authenticated and has admin role
    if (status === "authenticated") {
      if (session?.user?.role !== "admin") {
        toast({
          title: "Access Denied",
          description: "You don't have permission to access this page.",
          variant: "destructive",
        });
        router.push("/dashboard");
      }
    } else if (status === "unauthenticated") {
      router.push("/login?callbackUrl=/dashboard/trainers/new");
    }
  }, [session, status, router, toast]);

  // Don't render anything while checking auth
  if (status === "loading" || (status === "authenticated" && session?.user?.role !== "admin")) {
    return null;
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold tracking-tight">Add New Trainer</h1>
      </div>
      <AddTrainerForm />
    </div>
  );
} 