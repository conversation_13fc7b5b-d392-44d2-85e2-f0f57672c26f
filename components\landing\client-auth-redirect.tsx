'use client'

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export function ClientAuthRedirect() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    // Only redirect if we have a confirmed session
    if (status === 'authenticated' && session?.user) {
      router.push('/dashboard/dashboard')
    }
  }, [session, status, router])

  // Don't render anything
  return null
} 