"use client"

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bar<PERSON>hart3,
  <PERSON>,
  Plus,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Scale,
  Target,
  Trash2,
  Edit,
} from "lucide-react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { useSession } from "next-auth/react"
import { useEffect, useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { EditPlan } from "./edit-plan"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { AssignPlan } from "./assign-plan"
import { EditExerciseDialog } from "./edit-exercise-dialog"
import { AddExerciseDialog } from "./add-exercise-dialog"
import { toast } from "@/components/ui/use-toast"
import { PremiumExerciseGrid } from "@/components/premium/premium-exercise-grid"
import { PremiumTrainingPlan } from "@/components/premium/premium-training-plan"

interface Exercise {
  id: string;
  name: string;
  sets: number;
  reps: number;
  notes?: string;
  weight?: number;
  category?: string;
  difficulty?: string;
  targetMuscles?: string[];
}

interface DailyWorkout {
  id: string;
  day: string;
  exercises: Exercise[];
}

interface Week {
  id: string;
  weekNumber: number;
  dailyWorkouts: DailyWorkout[];
}

interface ClientPlan {
  id: string;
  title: string;
  description: string;
  exercises: Exercise[];
  weeks?: Week[];
}

export default function ClientPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { data: session, status } = useSession()
  const [client, setClient] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedWeek, setSelectedWeek] = useState(1)
  const [selectedDay, setSelectedDay] = useState('Monday')
  const clientId = params.id

  useEffect(() => {
    const fetchClient = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/clients/${clientId}`)
        
        if (!response.ok) {
          throw new Error(`Failed to fetch client: ${response.status}`)
        }
        
        const data = await response.json()
        console.log('Fetched client data:', data)
        
        // Check if we have data from localStorage (for development)
        const localData = localStorage.getItem(`client_${clientId}`)
        if (localData) {
          const parsedLocalData = JSON.parse(localData)
          console.log('Found local data:', parsedLocalData)
          
          // Merge the API data with localStorage data
          setClient({
            ...data,
            ...parsedLocalData
          })
        } else {
          setClient(data)
        }
      } catch (error) {
        console.error('Error fetching client:', error)
        toast({
          title: "Error",
          description: `Failed to load client data: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    if (status === "authenticated") {
      fetchClient()
    }
  }, [clientId, status])

  const handlePlanAssign = (plan: ClientPlan) => {
    console.log('Assigning plan:', plan)
    
    setClient(prev => {
      if (!prev) return prev
      
      const updatedClient = {
        ...prev,
        plan
      }
      
      // Update localStorage
      localStorage.setItem(`client_${clientId}`, JSON.stringify(updatedClient))
      return updatedClient
    })
    
    toast({
      title: "Plan Assigned",
      description: `The training plan "${plan.title}" has been assigned to the client.`,
    })
  }

  const handleExerciseEdit = (weekIndex: number, dayId: string, exerciseId: string, updatedExercise: Exercise) => {
    console.log('Editing exercise:', exerciseId, 'with data:', updatedExercise)
    
    setClient(prev => {
      if (!prev) return prev
      
      // If there's no plan, just return the previous state
      if (!prev.plan) return prev
      
      const updatedPlan = {
        ...prev.plan,
        weeks: prev.plan.weeks?.map((week, index) => {
          // Try to match by week ID first
          if (week.id && week.id === `week-${weekIndex + 1}`) {
            console.log('Found week by ID:', week.id)
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: day.exercises.map(ex => 
                    ex.id === exerciseId ? updatedExercise : ex
                  )
                }
              })
            }
          }
          
          // If no match by ID, try by index
          if (index === weekIndex) {
            console.log('Found week by index:', index)
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: day.exercises.map(ex => 
                    ex.id === exerciseId ? updatedExercise : ex
                  )
                }
              })
            }
          }
          
          // If no match, return the week unchanged
          return week
        })
      }
      
      const updatedClient = { ...prev, plan: updatedPlan }
      
      // Update localStorage
      localStorage.setItem(`client_${clientId}`, JSON.stringify(updatedClient))
      
      // Update the plan in the database using PUT
      try {
        console.log('Sending plan update to server:', updatedPlan)
        fetch(`/api/clients/${clientId}/training-plan`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedPlan),
        })
        .then(response => {
          if (!response.ok) {
            return response.json().then(data => {
              console.error('Error updating plan:', data)
              throw new Error(data.error || 'Failed to update plan')
            })
          }
          return response.json()
        })
        .then(data => {
          console.log('Plan updated successfully:', data)
          
          // No need to reload, we've already updated the client state
          toast({
            title: "Success",
            description: "Exercise updated and saved to database",
          })
        })
        .catch(error => {
          console.error('Error updating plan:', error)
          toast({
            title: "Error",
            description: `Failed to save exercise to database: ${error.message}`,
            variant: "destructive",
          })
        })
      } catch (error) {
        console.error('Error updating plan:', error)
        toast({
          title: "Error",
          description: `Failed to save exercise to database: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      }
      
      return updatedClient
    })
  }

  const handleExerciseDelete = (weekIndex: number, dayId: string, exerciseId: string) => {
    console.log('Deleting exercise:', exerciseId, 'from week:', weekIndex, 'day:', dayId)
    
    setClient(prev => {
      if (!prev) return prev
      
      // If there's no plan, just return the previous state
      if (!prev.plan) return prev
      
      const updatedPlan = {
        ...prev.plan,
        weeks: prev.plan.weeks?.map((week, index) => {
          // Try to match by week ID first
          if (week.id && week.id === `week-${weekIndex + 1}`) {
            console.log('Found week by ID:', week.id)
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: day.exercises.filter(ex => ex.id !== exerciseId)
                }
              })
            }
          }
          
          // If no match by ID, try by index
          if (index === weekIndex) {
            console.log('Found week by index:', index)
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: day.exercises.filter(ex => ex.id !== exerciseId)
                }
              })
            }
          }
          
          // If no match, return the week unchanged
          return week
        })
      }
      
      const updatedClient = { ...prev, plan: updatedPlan }
      
      // Update localStorage
      localStorage.setItem(`client_${clientId}`, JSON.stringify(updatedClient))
      
      // Update the plan in the database using PUT
      try {
        console.log('Sending plan update to server:', updatedPlan)
        fetch(`/api/clients/${clientId}/training-plan`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedPlan),
        })
        .then(response => {
          if (!response.ok) {
            return response.json().then(data => {
              console.error('Error updating plan:', data)
              throw new Error(data.error || 'Failed to update plan')
            })
          }
          return response.json()
        })
        .then(data => {
          console.log('Plan updated successfully:', data)
          
          // No need to reload, we've already updated the client state
          toast({
            title: "Success",
            description: "Exercise deleted and saved to database",
          })
        })
        .catch(error => {
          console.error('Error updating plan:', error)
          toast({
            title: "Error",
            description: `Failed to save exercise to database: ${error.message}`,
            variant: "destructive",
          })
        })
      } catch (error) {
        console.error('Error updating plan:', error)
        toast({
          title: "Error",
          description: `Failed to save exercise to database: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      }
      
      return updatedClient
    })
  }

  const handleExerciseAdd = async (weekIndex: number, dayId: string, newExercise: Exercise) => {
    console.log('Adding exercise to week:', weekIndex, 'day:', dayId, 'exercise:', newExercise)
    
    setClient(prev => {
      if (!prev) return prev
      
      // If there's no plan, just return the previous state
      if (!prev.plan) return prev
      
      const updatedPlan = {
        ...prev.plan,
        weeks: prev.plan.weeks?.map((week, index) => {
          // Try to match by week ID first
          if (week.id && week.id === `week-${weekIndex + 1}`) {
            console.log('Found week by ID:', week.id)
            const isTargetWeek = true
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: [...day.exercises, newExercise]
                }
              })
            }
          }
          
          // If no match by ID, try by index
          if (index === weekIndex) {
            console.log('Found week by index:', index)
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: [...day.exercises, newExercise]
                }
              })
            }
          }
          
          // If no match, return the week unchanged
          return week
        })
      }
      
      const updatedClient = { ...prev, plan: updatedPlan }
      
      // Update localStorage
      localStorage.setItem(`client_${clientId}`, JSON.stringify(updatedClient))
      
      // Update the plan in the database using PUT
      try {
        console.log('Sending plan update to server:', updatedPlan)
        fetch(`/api/clients/${clientId}/training-plan`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedPlan),
        })
        .then(response => {
          if (!response.ok) {
            return response.json().then(data => {
              console.error('Error updating plan:', data)
              throw new Error(data.error || 'Failed to update plan')
            })
          }
          return response.json()
        })
        .then(data => {
          console.log('Plan updated successfully:', data)
          
          // No need to reload, we've already updated the client state
          toast({
            title: "Success",
            description: "Exercise added and saved to database",
          })
        })
        .catch(error => {
          console.error('Error updating plan:', error)
          toast({
            title: "Error",
            description: `Failed to save exercise to database: ${error.message}`,
            variant: "destructive",
          })
        })
      } catch (error) {
        console.error('Error updating plan:', error)
        toast({
          title: "Error",
          description: `Failed to save exercise to database: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      }
      
      return updatedClient
    })
  }

  const handlePlanDisassociate = async () => {
    try {
      // Get the plan ID or title for the API call
      const planId = client?.plan?.id
      const planTitle = client?.plan?.title
      
      if (!planId && !planTitle) {
        throw new Error('No plan ID or title found')
      }
      
      // Build the URL with query parameters
      let url = `/api/clients/${clientId}/training-plan`
      if (planId) {
        url += `?planId=${encodeURIComponent(planId)}`
      } else if (planTitle) {
        url += `?title=${encodeURIComponent(planTitle)}`
      }
      
      console.log('Attempting to delete plan with URL:', url)
      
      // Remove the plan from the database
      const response = await fetch(url, {
        method: 'DELETE',
      })
      
      console.log('Delete response status:', response.status)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Error response data:', errorData)
        throw new Error(`Failed to remove plan: ${response.status} ${response.statusText}`)
      }
      
      // Update the client state
      setClient(prev => {
        if (!prev) return null
        
        // Preserve the mock data
        const updatedClient = {
          ...prev,
          plan: null,
          workouts: []
        }
        
        // Update localStorage
        localStorage.setItem(`client_${clientId}`, JSON.stringify(updatedClient))
        return updatedClient
      })
      
      // Show success toast
      toast({
        title: "Plan Disassociated",
        description: "The training plan has been successfully removed from the client.",
      })
    } catch (error) {
      console.error('Error removing plan:', error)
      // Show error toast
      toast({
        title: "Error",
        description: `Failed to remove the plan: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      })
    }
  }

  // Add console log to see what's happening when selecting week/day
  useEffect(() => {
    if (client?.plan) {
      const selectedDayWorkouts = client.plan.weeks?.[selectedWeek - 1]?.dailyWorkouts
        ?.find((d: DailyWorkout) => d.day === selectedDay)?.exercises
      console.log('Selected week:', selectedWeek)
      console.log('Selected day:', selectedDay)
      console.log('Found exercises:', selectedDayWorkouts)
    }
  }, [selectedWeek, selectedDay, client?.plan])

  // Add debug logging for week selection
  useEffect(() => {
    if (client?.plan) {
      console.log('Current plan weeks:', client.plan.weeks)
      console.log('Selected week:', selectedWeek)
      const weekData = client.plan.weeks?.find(w => w.weekNumber === selectedWeek)
      console.log('Found week data:', weekData)
    }
  }, [client?.plan, selectedWeek])

  if (status === "loading" || isLoading || !client) {
    return <div>Loading...</div>
  }

  const userRole = session?.user?.role

  if (userRole !== "trainer" && userRole !== "admin") {
    router.push("/dashboard/dashboard")
    return null
  }

  return (
    <div className="container py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={client?.avatarUrl || `https://avatar.vercel.sh/${client?.name}`} />
            <AvatarFallback>{client?.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{client?.name}</h1>
            <p className="text-muted-foreground">{client?.email}</p>
            {client?.clientSubscriptions && client.clientSubscriptions.length > 0 ? (
              <p className="text-xs text-muted-foreground">
                {client.clientSubscriptions[0].tier?.name || 'Standard'} Plan
              </p>
            ) : client?.assignedTrainer && (
              <p className="text-xs text-muted-foreground">
                Assigned to {client.assignedTrainer.name}
              </p>
            )}
          </div>
        </div>
        <div className="flex gap-2 mt-4 md:mt-0">
          <Button variant="outline" asChild>
            <Link href={`/dashboard/messaging?client=${client.id}`}>
              <MessageSquare className="mr-2 h-4 w-4" />
              Message
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/dashboard/coaching/sessions/new?client=${client.id}`}>
              <Calendar className="mr-2 h-4 w-4" />
              Schedule Session
            </Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="workouts">
            <ClipboardList className="mr-2 h-4 w-4" />
            Workouts & Plans
          </TabsTrigger>
          <TabsTrigger value="nutrition">Nutrition</TabsTrigger>
          <TabsTrigger value="progress">
            <BarChart3 className="mr-2 h-4 w-4" />
            Progress Tracking
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Goals</CardTitle>
                <CardDescription>Client's current fitness goals</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {(client?.goals || []).map((goal: string, index: number) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="rounded-full h-5 w-5 bg-primary/10 text-primary flex items-center justify-center mt-0.5">
                        {index + 1}
                      </div>
                      <span>{goal}</span>
                    </li>
                  ))}
                </ul>
                <Button variant="outline" size="sm" className="mt-4">
                  <Pencil className="mr-2 h-3 w-3" />
                  Edit Goals
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Progress Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Weight Progress</span>
                      <span className="font-medium">
                        {client?.progress && client.progress.length > 0 ? client.progress[client.progress.length - 1].weight : 'N/A'} lbs
                      </span>
                    </div>
                    <Progress value={75} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Body Fat Progress</span>
                      <span className="font-medium">
                        {client?.progress && client.progress.length > 0 ? client.progress[client.progress.length - 1].bodyFat : 'N/A'}%
                      </span>
                    </div>
                    <Progress value={60} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="workouts" className="space-y-6">
          {client?.plan ? (
            <PremiumTrainingPlan
              plan={client.plan}
              onDisassociate={handlePlanDisassociate}
              onEdit={() => {
                // Use the existing EditPlan component
                const editButton = document.querySelector('[data-edit-plan-button]');
                if (editButton) {
                  (editButton as HTMLButtonElement).click();
                }
              }}
              onExerciseAdd={handleExerciseAdd}
              onExerciseEdit={handleExerciseEdit}
              onExerciseDelete={handleExerciseDelete}
            />
          ) : (
            <Card>
              <CardContent className="pt-6 pb-6">
                <div className="text-center py-12 bg-gradient-to-b from-muted/5 to-muted/10 rounded-lg border border-dashed">
                  <div className="max-w-md mx-auto space-y-4">
                    <div className="w-16 h-16 mx-auto rounded-full bg-primary/10 flex items-center justify-center">
                      <ClipboardList className="h-8 w-8 text-primary/70" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">No Training Plan</h3>
                      <p className="text-muted-foreground mt-1 mb-4">
                        This client doesn't have a training plan assigned yet.
                      </p>
                      <AssignPlan clientId={client.id} onAssign={handlePlanAssign} />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Hidden button for EditPlan to be triggered programmatically */}
          <div className="hidden">
            <EditPlan clientId={client.id} currentPlan={client?.plan} buttonProps={{ 'data-edit-plan-button': true }} />
          </div>
        </TabsContent>

        <TabsContent value="nutrition" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Nutrition Plan</CardTitle>
              <CardDescription>Client's current nutrition plan</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  No nutrition plan has been created for this client yet.
                </p>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Nutrition Plan
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Weight</CardTitle>
                <CardDescription>Client's weight over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={client?.progress || []}
                      margin={{ top: 5, right: 10, left: 10, bottom: 0 }}
                    >
                      <XAxis
                        dataKey="date"
                        tickFormatter={(date) => new Date(date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                      />
                      <YAxis />
                      <Tooltip
                        labelFormatter={(date) => new Date(date).toLocaleDateString()}
                        formatter={(value) => [`${value} lbs`, 'Weight']}
                      />
                      <Line
                        type="monotone"
                        dataKey="weight"
                        stroke="#8884d8"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
                <div className="flex justify-center mt-4">
                  <Button variant="outline" size="sm">
                    <Scale className="mr-2 h-4 w-4" />
                    Add Measurement
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Body Fat</CardTitle>
                <CardDescription>Client's body fat percentage</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={client?.progress || []}
                      margin={{ top: 5, right: 10, left: 10, bottom: 0 }}
                    >
                      <XAxis
                        dataKey="date"
                        tickFormatter={(date) => new Date(date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                      />
                      <YAxis />
                      <Tooltip
                        labelFormatter={(date) => new Date(date).toLocaleDateString()}
                        formatter={(value) => [`${value}%`, 'Body Fat']}
                      />
                      <Line
                        type="monotone"
                        dataKey="bodyFat"
                        stroke="#82ca9d"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
                <div className="flex justify-center mt-4">
                  <Button variant="outline" size="sm">
                    <Target className="mr-2 h-4 w-4" />
                    Set Goal
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Progress History</CardTitle>
                <CardDescription>Client's recorded measurements</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="grid grid-cols-3 p-4 font-medium border-b">
                    <div>Date</div>
                    <div>Weight (lbs)</div>
                    <div>Body Fat (%)</div>
                  </div>

                  {client?.progress.map((entry: any, index: number) => (
                    <div
                      key={index}
                      className="grid grid-cols-3 p-4 border-b last:border-0 hover:bg-muted/50"
                    >
                      <div>{new Date(entry.date).toLocaleDateString()}</div>
                      <div>{entry.weight}</div>
                      <div>{entry.bodyFat}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
