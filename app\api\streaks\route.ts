import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// Get user's streaks
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const userId = session.user.id

    // Get user's streaks
    const streaks = await prisma.streak.findMany({
      where: {
        userId: userId,
      },
    })

    // If no streaks found, initialize default streaks
    if (streaks.length === 0) {
      // Create default streaks for different tracking types
      const defaultStreakTypes = ["nutrition", "measurement", "sleep", "stress", "coffee"]
      
      const createdStreaks = await Promise.all(
        defaultStreakTypes.map(type => 
          prisma.streak.create({
            data: {
              userId,
              type,
              currentCount: 0,
              longestCount: 0,
            }
          })
        )
      )
      
      return NextResponse.json(createdStreaks)
    }

    return NextResponse.json(streaks)
  } catch (error) {
    console.error("[STREAKS_GET]", error)
    return new NextResponse("Internal Error", { status: 500 })
  }
}

// Update streak after logging
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const userId = session.user.id
    const body = await request.json()
    const { type } = body

    if (!type) {
      return new NextResponse("Type is required", { status: 400 })
    }

    // Get current streak
    let streak = await prisma.streak.findUnique({
      where: {
        userId_type: {
          userId,
          type,
        },
      },
    })

    // If no streak exists, create one
    if (!streak) {
      streak = await prisma.streak.create({
        data: {
          userId,
          type,
          currentCount: 1,
          longestCount: 1,
          lastLoggedAt: new Date(),
        },
      })
    } else {
      // Check if this is a continuation of the streak (within 24 hours of last log)
      const now = new Date()
      const lastLogged = streak.lastLoggedAt || new Date(0)
      const hoursSinceLastLog = (now.getTime() - lastLogged.getTime()) / (1000 * 60 * 60)
      
      // If logged within last 24 hours but not in the same day
      const isNewDay = now.getDate() !== lastLogged.getDate() || 
                       now.getMonth() !== lastLogged.getMonth() || 
                       now.getFullYear() !== lastLogged.getFullYear()
      
      if (hoursSinceLastLog <= 48 && isNewDay) {
        // Increment streak
        const newCount = streak.currentCount + 1
        streak = await prisma.streak.update({
          where: {
            id: streak.id,
          },
          data: {
            currentCount: newCount,
            longestCount: Math.max(newCount, streak.longestCount),
            lastLoggedAt: now,
          },
        })

        // Check for achievements
        if (newCount === 7 || newCount === 30 || newCount === 100) {
          await prisma.achievement.create({
            data: {
              userId,
              type: "streak",
              name: `${type.charAt(0).toUpperCase() + type.slice(1)} Streak`,
              description: `Logged ${type} for ${newCount} days in a row!`,
              iconName: "fire",
              level: newCount === 7 ? 1 : newCount === 30 ? 2 : 3,
            },
          })
        }
      } else if (hoursSinceLastLog > 48) {
        // Streak broken, reset to 1
        streak = await prisma.streak.update({
          where: {
            id: streak.id,
          },
          data: {
            currentCount: 1,
            lastLoggedAt: now,
          },
        })
      } else {
        // Already logged today, just update the timestamp
        streak = await prisma.streak.update({
          where: {
            id: streak.id,
          },
          data: {
            lastLoggedAt: now,
          },
        })
      }
    }

    return NextResponse.json(streak)
  } catch (error) {
    console.error("[STREAK_UPDATE]", error)
    return new NextResponse("Internal Error", { status: 500 })
  }
}
