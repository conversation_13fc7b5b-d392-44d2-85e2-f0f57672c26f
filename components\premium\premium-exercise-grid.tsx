'use client';

import { useState } from 'react';
import { Plus, Calendar, Clock, ChevronDown, ChevronUp, Layers } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { PremiumExerciseCard } from './premium-exercise-card';
import { AddExerciseDialog } from '@/app/dashboard/coaching/clients/[id]/add-exercise-dialog';
import { EditExerciseDialog } from '@/app/dashboard/coaching/clients/[id]/edit-exercise-dialog';

interface Exercise {
  id: string;
  name: string;
  sets: number;
  reps: number;
  notes?: string;
  weight?: number;
  category?: string;
  difficulty?: string;
  targetMuscles?: string[];
  videoUrl?: string;
  isAlternative?: boolean;
  parentExerciseId?: string | null;
}

interface DailyWorkout {
  id: string;
  day: string;
  exercises: Exercise[];
}

interface Week {
  id: string;
  weekNumber: number;
  dailyWorkouts: DailyWorkout[];
}

interface PremiumExerciseGridProps {
  selectedWeek: number;
  selectedDay: string;
  weekData: Week | null;
  dayData: DailyWorkout | null;
  onExerciseAdd: (weekIndex: number, dayId: string, exercise: Exercise) => void;
  onExerciseEdit: (weekIndex: number, dayId: string, exerciseId: string, updatedExercise: Exercise) => void;
  onExerciseDelete: (weekIndex: number, dayId: string, exerciseId: string) => void;
}

export function PremiumExerciseGrid({
  selectedWeek,
  selectedDay,
  weekData,
  dayData,
  onExerciseAdd,
  onExerciseEdit,
  onExerciseDelete
}: PremiumExerciseGridProps) {
  const [editingExercise, setEditingExercise] = useState<Exercise | null>(null);
  // Track expanded state for each exercise separately
  const [expandedExercises, setExpandedExercises] = useState<Record<string, boolean>>({});
  // Track which exercises have their alternatives shown
  const [showAlternativesFor, setShowAlternativesFor] = useState<Record<string, boolean>>({});

  if (!weekData) {
    return (
      <div className="text-center py-12 bg-gradient-to-b from-muted/5 to-muted/10 rounded-lg border border-dashed">
        <div className="max-w-md mx-auto space-y-4">
          <div className="w-16 h-16 mx-auto rounded-full bg-muted/20 flex items-center justify-center">
            <Calendar className="h-8 w-8 text-muted-foreground/50" />
          </div>
          <div>
            <h3 className="text-lg font-medium">No Week Data</h3>
            <p className="text-muted-foreground mt-1">
              No data available for Week {selectedWeek}. Please select a different week or add content to this week.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!dayData) {
    return (
      <div className="text-center py-12 bg-gradient-to-b from-muted/5 to-muted/10 rounded-lg border border-dashed">
        <div className="max-w-md mx-auto space-y-4">
          <div className="w-16 h-16 mx-auto rounded-full bg-muted/20 flex items-center justify-center">
            <Clock className="h-8 w-8 text-muted-foreground/50" />
          </div>
          <div>
            <h3 className="text-lg font-medium">No Day Data</h3>
            <p className="text-muted-foreground mt-1">
              No data available for {selectedDay}. Please select a different day or add content to this day.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!dayData.exercises?.length) {
    return (
      <div className="text-center py-12 bg-gradient-to-b from-muted/5 to-muted/10 rounded-lg border border-dashed">
        <div className="max-w-md mx-auto space-y-4">
          <div className="w-16 h-16 mx-auto rounded-full bg-primary/10 flex items-center justify-center">
            <Plus className="h-8 w-8 text-primary/70" />
          </div>
          <div>
            <h3 className="text-lg font-medium">No Exercises</h3>
            <p className="text-muted-foreground mt-1 mb-4">
              No exercises scheduled for {selectedDay}. Add your first exercise to get started.
            </p>
            <AddExerciseDialog
              onAdd={(newExercise) => {
                onExerciseAdd(selectedWeek - 1, dayData.id, newExercise);
              }}
              trigger={
                <Button className="px-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Exercise
                </Button>
              }
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {/* Count only principal exercises */}
          {(() => {
            const principalExercises = dayData.exercises.filter(ex => !ex.isAlternative);
            const alternativeCount = dayData.exercises.length - principalExercises.length;
            return (
              <h3 className="text-sm font-medium">
                {principalExercises.length} {principalExercises.length === 1 ? 'Exercise' : 'Exercises'}
                {alternativeCount > 0 && (
                  <span className="text-xs text-muted-foreground ml-1">
                    (+{alternativeCount} alternative{alternativeCount !== 1 ? 's' : ''})
                  </span>
                )}
              </h3>
            );
          })()}
          <div className="h-px w-12 bg-border"></div>
          <span className="text-xs text-muted-foreground">{selectedDay}, Week {selectedWeek}</span>
        </div>

        <AddExerciseDialog
          onAdd={(newExercise) => {
            onExerciseAdd(selectedWeek - 1, dayData.id, newExercise);
          }}
          trigger={
            <Button className="gap-1.5 bg-primary/90 hover:bg-primary shadow-sm">
              <Plus className="h-4 w-4" />
              Add Exercise
            </Button>
          }
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        {/* Filter to show only principal exercises (non-alternatives) */}
        {dayData.exercises
          .filter(exercise => !exercise.isAlternative)
          .map((exercise, index) => {
            // Find any alternative exercises for this principal exercise
            const alternatives = dayData.exercises.filter(
              alt => alt.isAlternative && alt.parentExerciseId === exercise.id
            );
            const hasAlternatives = alternatives.length > 0;

            return (
              <div key={exercise.id} className="space-y-3">
                <PremiumExerciseCard
                  exercise={exercise}
                  index={index}
                  expanded={!!expandedExercises[exercise.id]}
                  onToggleExpand={() => {
                    setExpandedExercises(prev => ({
                      ...prev,
                      [exercise.id]: !prev[exercise.id]
                    }));
                  }}
                  onEdit={() => setEditingExercise(exercise)}
                  onDelete={() => onExerciseDelete(selectedWeek - 1, dayData.id, exercise.id)}
                />

                {/* Show alternatives button if this exercise has alternatives */}
                {hasAlternatives && (
                  <div className="pl-8">
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs h-7 px-2 border-dashed border-primary/40 text-primary/80 hover:text-primary"
                      onClick={() => {
                        setShowAlternativesFor(prev => ({
                          ...prev,
                          [exercise.id]: !prev[exercise.id]
                        }));
                      }}
                    >
                      <Layers className="h-3 w-3 mr-1" />
                      {showAlternativesFor[exercise.id] ? 'Hide' : 'Show'} {alternatives.length} Alternative{alternatives.length !== 1 ? 's' : ''}
                      {showAlternativesFor[exercise.id] ? <ChevronUp className="h-3 w-3 ml-1" /> : <ChevronDown className="h-3 w-3 ml-1" />}
                    </Button>

                    {/* Show alternative exercises if expanded */}
                    {showAlternativesFor[exercise.id] && (
                      <div className="space-y-3 mt-3 border-l-2 border-primary/10 pl-4 rounded-bl-md">
                        {alternatives.map((altExercise, altIndex) => (
                          <div key={altExercise.id} className="relative">
                            {/* Alternative indicator */}
                            <div className="absolute -left-2.5 top-1/2 -translate-y-1/2 h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center">
                              <Layers className="h-3 w-3 text-primary" />
                            </div>
                            <PremiumExerciseCard
                              exercise={altExercise}
                              index={altIndex}
                              expanded={!!expandedExercises[altExercise.id]}
                              onToggleExpand={() => {
                                setExpandedExercises(prev => ({
                                  ...prev,
                                  [altExercise.id]: !prev[altExercise.id]
                                }));
                              }}
                              onEdit={() => setEditingExercise(altExercise)}
                              onDelete={() => onExerciseDelete(selectedWeek - 1, dayData.id, altExercise.id)}
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
      </div>

      {editingExercise && (
        <EditExerciseDialog
          exercise={editingExercise}
          onSave={(updatedExercise) => {
            onExerciseEdit(
              selectedWeek - 1,
              dayData.id,
              editingExercise.id,
              updatedExercise
            );
            setEditingExercise(null);
          }}
          open={!!editingExercise}
          onOpenChange={(open) => {
            if (!open) setEditingExercise(null);
          }}
        />
      )}
    </div>
  );
}
