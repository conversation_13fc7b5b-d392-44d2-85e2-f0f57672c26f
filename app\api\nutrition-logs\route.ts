import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET() {
  const session = await getServerSession(authOptions)

  if (!session?.user?.email) {
    return new NextResponse("Unauthorized", { status: 401 })
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return new NextResponse("User not found", { status: 404 })
    }

    try {
      const nutritionLogs = await prisma.nutritionLog.findMany({
        where: { clientId: user.id },
        orderBy: {
          date: "desc",
        },
      })
      return NextResponse.json(nutritionLogs)
    } catch (err) {
      console.error("[NUTRITION_LOGS_GET] Database error:", err)
      return NextResponse.json([])
    }
  } catch (error) {
    console.error("[NUTRITION_LOGS_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.email) {
    return new NextResponse("Unauthorized", { status: 401 })
  }

  try {
    const json = await request.json()
    const { date, mealType, name, calories, protein, carbs, fat, notes } = json

    // Get the user from the database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return new NextResponse("User not found", { status: 404 })
    }

    try {
      const nutritionLog = await prisma.nutritionLog.create({
        data: {
          clientId: user.id,
          date: new Date(date),
          mealType,
          name,
          calories: calories ? parseInt(calories.toString()) : null,
          protein: protein ? parseFloat(protein.toString()) : null,
          carbs: carbs ? parseFloat(carbs.toString()) : null,
          fat: fat ? parseFloat(fat.toString()) : null,
          notes: notes || null,
        },
      })
      return NextResponse.json(nutritionLog)
    } catch (err) {
      console.error("[NUTRITION_LOGS_POST] Database error:", err)
      return new NextResponse("Failed to create nutrition log", { status: 500 })
    }
  } catch (error) {
    console.error("[NUTRITION_LOGS_POST]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function DELETE(request: Request) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.email) {
    return new NextResponse("Unauthorized", { status: 401 })
  }

  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return new NextResponse("Missing log ID", { status: 400 })
    }

    // Get the user from the database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return new NextResponse("User not found", { status: 404 })
    }

    try {
      // First find the log to make sure it belongs to the user
      const log = await prisma.nutritionLog.findFirst({
        where: {
          id,
          clientId: user.id
        },
      })

      if (!log) {
        return new NextResponse("Log not found", { status: 404 })
      }

      // Then delete it
      const deletedLog = await prisma.nutritionLog.delete({
        where: {
          id
        },
      })

      return NextResponse.json({ success: true, deletedLog })
    } catch (err) {
      console.error("[NUTRITION_LOGS_DELETE] Database error:", err)
      return new NextResponse("Log not found or could not be deleted", { status: 404 })
    }
  } catch (error) {
    console.error("[NUTRITION_LOGS_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
