"use client"

import { Session<PERSON>rovider, useSession } from "next-auth/react"
import React, { useEffect } from "react"

// Debug component to monitor session status
function SessionMonitor() {
  const { data: session, status } = useSession()
  
  useEffect(() => {
    console.log(`[NextAuth] Session status: ${status}`, session)
  }, [session, status])
  
  // This component doesn't render anything
  return null
}

export function NextAuthProvider({
  children,
}: {
  children: React.ReactNode
}) {
  // Add error handling for NextAuth
  useEffect(() => {
    // Add global error handler for fetch errors
    const originalFetch = window.fetch
    window.fetch = async function(...args) {
      try {
        const response = await originalFetch(...args)
        // Check if this is a NextAuth fetch request
        if (args[0] && typeof args[0] === 'string' && args[0].includes('/api/auth')) {
          console.log(`[NextAuth] Fetch to ${args[0]}: status ${response.status}`)
          if (!response.ok) {
            console.error(`[NextAuth] Error in fetch to ${args[0]}: status ${response.status}`)
          }
        }
        return response
      } catch (error) {
        console.error('[NextAuth] Fetch error:', error)
        // Re-throw to let NextAuth handle it
        throw error
      }
    }
    
    return () => {
      // Restore original fetch when component unmounts
      window.fetch = originalFetch
    }
  }, [])
  
  return (
    <SessionProvider 
      refetchInterval={5 * 60} // Refetch session every 5 minutes
      refetchOnWindowFocus={true}
    >
      <SessionMonitor />
      {children}
    </SessionProvider>
  )
} 