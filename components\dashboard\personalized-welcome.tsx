'use client';

import { TierBadge } from "@/components/dashboard/tier-badge";
import { Card } from "@/components/ui/card";

interface PersonalizedWelcomeProps {
  userName: string;
  userTier: string;
  lastLogin?: Date;
}

export function PersonalizedWelcome({
  userName,
  userTier,
  lastLogin
}: PersonalizedWelcomeProps) {
  // Format the last login date if available
  const formattedLastLogin = lastLogin
    ? new Date(lastLogin).toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'short',
        day: 'numeric'
      })
    : null;

  return (
    <div className="relative overflow-hidden rounded-xl">
      {/* Background pattern/gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-background to-primary/10 opacity-70"></div>
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

      {/* Content */}
      <Card className="bg-transparent backdrop-blur-sm p-6 rounded-xl shadow-md border border-primary/10">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 relative z-10">
          <div>
            <div className="flex flex-wrap items-center gap-3 mb-3">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text">
                Welcome back, {userName}!
              </h1>
              <div className="flex items-center">
                <TierBadge
                  tier={userTier}
                  size="md"
                  showUpgradeButton={userTier !== "premium" && userTier !== "coaching"}
                />
              </div>
            </div>

            <p className="text-sm text-muted-foreground">
              {formattedLastLogin
                ? `Last login: ${formattedLastLogin}`
                : "Let's make progress on your fitness journey today!"}
            </p>

            <div className="mt-3 pt-3 border-t border-border/20 hidden md:block">
              <div className="text-sm font-medium">Today's Focus</div>
              <div className="flex gap-2 mt-1">
                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Upper Body</span>
                <span className="text-xs bg-blue-500/10 text-blue-500 px-2 py-1 rounded-full">Nutrition</span>
                <span className="text-xs bg-amber-500/10 text-amber-500 px-2 py-1 rounded-full">Recovery</span>
              </div>
            </div>
          </div>

          <div className="flex flex-col items-end gap-2">
            <div className="text-sm font-medium bg-background/80 px-4 py-2 rounded-full border border-border/40 shadow-sm">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'short',
                day: 'numeric'
              })}
            </div>

            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
              <span>Workout scheduled today</span>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
