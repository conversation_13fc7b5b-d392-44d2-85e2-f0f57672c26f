import { PrismaClient } from '@prisma/client'
import { v4 as uuidv4 } from 'uuid'
import bcrypt from 'bcryptjs'

// Use direct import instead of alias for testing
import { prisma as dbClient } from '../../lib/prisma'

// Create a separate Prisma client for testing
export const prisma = new PrismaClient()

// Helper function to create a test user
export async function createTestUser(role: 'admin' | 'trainer' | 'client' = 'trainer') {
  const userId = uuidv4()
  const email = `test-${role}-${userId}@example.com`
  const password = await bcrypt.hash('password123', 10)

  const user = await prisma.user.create({
    data: {
      id: userId,
      name: `Test ${role.charAt(0).toUpperCase() + role.slice(1)}`,
      email,
      password,
      role,
      emailVerified: new Date()
    }
  })

  // Create trainer profile if role is trainer
  if (role === 'trainer') {
    await prisma.trainerProfile.create({
      data: {
        userId: user.id
      }
    })

    await prisma.trainerSettings.create({
      data: {
        userId: user.id,
        serviceType: 'full-service',
        enableStore: true,
        enableSubscriptions: true,
        enablePremiumCoaching: true
      }
    })
  }

  // Create client profile if role is client
  if (role === 'client') {
    await prisma.clientProfile.create({
      data: {
        userId: user.id
      }
    })
  }

  return user
}

// Helper function to clean up test data
export async function cleanupTestData(userId: string) {
  // Delete all related data
  await prisma.$transaction([
    prisma.clientProfile.deleteMany({
      where: {
        userId
      }
    }),
    prisma.trainerProfile.deleteMany({
      where: {
        userId
      }
    }),
    prisma.trainerSettings.deleteMany({
      where: {
        userId
      }
    }),
    prisma.user.delete({
      where: {
        id: userId
      }
    })
  ])
}

// Helper function to create a test session
export function createTestSession(user: any) {
  return {
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  }
}
