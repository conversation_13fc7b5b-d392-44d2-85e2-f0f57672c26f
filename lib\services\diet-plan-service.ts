import { Diet<PERSON>lan, Prisma } from "@prisma/client"
import { IDietPlanRepository, DietPlanRepository } from "../repositories/diet-plan-repository"

export class DietPlanService {
  private static repository: IDietPlanRepository = new DietPlanRepository()

  /**
   * Set a custom repository implementation (useful for testing)
   */
  static setRepository(repository: IDietPlanRepository) {
    this.repository = repository
  }

  /**
   * Find a diet plan by ID
   */
  static async findById(id: string): Promise<DietPlan | null> {
    return this.repository.findById(id)
  }

  /**
   * Create a new diet plan
   */
  static async create(data: Prisma.DietPlanCreateInput): Promise<DietPlan> {
    return this.repository.create(data)
  }

  /**
   * Update a diet plan
   */
  static async update(id: string, data: Partial<DietPlan>): Promise<DietPlan> {
    return this.repository.update(id, data)
  }

  /**
   * Delete a diet plan
   */
  static async delete(id: string): Promise<DietPlan> {
    return this.repository.delete(id)
  }

  /**
   * Find multiple diet plans
   */
  static async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.DietPlanWhereInput
    orderBy?: Prisma.DietPlanOrderByWithRelationInput
  }): Promise<DietPlan[]> {
    return this.repository.findMany(params)
  }

  /**
   * Find diet plans by trainer ID
   */
  static async findByTrainerId(trainerId: string): Promise<DietPlan[]> {
    return this.repository.findByTrainerId(trainerId)
  }

  /**
   * Duplicate a diet plan
   */
  static async duplicate(id: string, trainerId: string, newTitle: string): Promise<DietPlan> {
    return this.repository.duplicate(id, trainerId, newTitle)
  }

  /**
   * Assign a diet plan to a client
   */
  static async assignToClient(dietPlanId: string, clientId: string, trainerId: string): Promise<any> {
    return this.repository.assignToClient(dietPlanId, clientId, trainerId)
  }
}
