"use client"

import { <PERSON>, <PERSON>R<PERSON> } from "lucide-react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { TabsContent } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { CoachingService } from "@/types/trainer"

interface CoachingSectionProps {
  coachingServices: CoachingService[]
  onSwitchToContact: () => void
}

export function CoachingSection({ 
  coachingServices, 
  onSwitchToContact 
}: CoachingSectionProps) {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)

  return (
    <TabsContent value="coaching" className="space-y-4">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-2">1:1 Premium Coaching</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Get personalized coaching, custom workout plans, and direct access to your coach
        </p>
      </div>

      {coachingServices.length > 0 ? (
        <div className="grid md:grid-cols-2 gap-6">
          {coachingServices.map((service) => (
            <Card key={service.id} className="overflow-hidden border-2 hover:border-primary/50 transition-all card-hover">
              <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 pb-4">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-xl font-bold">{service.name}</CardTitle>
                    <CardDescription className="mt-1">{service.description}</CardDescription>
                  </div>
                  <div className="text-right">
                    <span className="text-2xl font-bold">${service.price}</span>
                    {service.duration && (
                      <p className="text-sm text-muted-foreground">/{service.duration}</p>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-center mb-4">
                    {service.availableSlots && service.availableSlots.length < 5 && (
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-200 dark:border-yellow-800">
                        Limited Availability
                      </Badge>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    {service.features.map((feature, index) => (
                      <div key={index} className="flex items-start">
                        <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center mr-3 mt-0.5">
                          <span className="h-2 w-2 rounded-full bg-primary" />
                        </div>
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  {service.availableSlots && service.availableSlots.length > 0 && (
                    <div className="mt-6 pt-6 border-t border-border">
                      <h4 className="font-medium mb-2 text-sm uppercase text-muted-foreground">Available time slots:</h4>
                      <div className="space-y-3">
                        {service.availableSlots.slice(0, 3).map((slot, i) => (
                          <div key={i} className="flex items-start">
                            <div className="text-primary mr-3">
                              <Clock className="h-5 w-5" />
                            </div>
                            <span className="text-sm">
                              {slot.date}, {slot.startTime} - {slot.endTime}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="bg-muted/30 pt-4">
                <Button 
                  className="w-full" 
                  onClick={onSwitchToContact}
                >
                  Apply for Coaching
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center p-10 border rounded-lg bg-muted/40">
          <h3 className="text-xl font-medium mb-2">Premium Coaching Coming Soon</h3>
          <p className="text-muted-foreground mb-6">
            The trainer is not currently accepting new coaching clients. 
            Please check back later or contact for more information.
          </p>
          <Button onClick={onSwitchToContact}>
            Contact for Availability
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      )}
    </TabsContent>
  )
} 