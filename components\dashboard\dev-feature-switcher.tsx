"use client"

import { RotateCw, Store, ScrollText, Sparkles } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import {
  FeatureFlags,
  trainerServiceConfigs,
  trainerServiceType,
  getDevFeatureFlags,
  setDevFeatureFlags,
  clearDevFeatureFlags,
  defaultFeatureFlags
} from "@/lib/feature-flags"

export function DevFeatureSwitcher() {
  const router = useRouter()
  const [featureFlags, setFeatureFlags] = useState<FeatureFlags>(defaultFeatureFlags)
  const [serviceType, setServiceType] = useState<trainerServiceType>("full-service")
  const [isClient, setIsClient] = useState(false)
  const [isChanging, setIsChanging] = useState(false)

  // Function to determine service type based on flags
  const determineServiceType = (flags: FeatureFlags): trainerServiceType => {
    // Check if it matches any predefined service type
    for (const [type, config] of Object.entries(trainerServiceConfigs)) {
      if (
        flags.enableStore === config.enableStore &&
        flags.enableSubscriptions === config.enableSubscriptions &&
        flags.enablePremiumCoaching === config.enablePremiumCoaching
      ) {
        return type as trainerServiceType;
      }
    }

    // If no match, it's custom
    return 'custom';
  };

  // Only run on client side
  useEffect(() => {
    setIsClient(true)

    // Get the feature flags from the cookie
    const devFlags = getDevFeatureFlags()
    if (devFlags) {
      setFeatureFlags(devFlags)

      // Determine service type based on flags
      const currentType = determineServiceType(devFlags)
      setServiceType(currentType)
    }
  }, [])

  // Only show in development environment and for admin/trainer roles
  const { data: session } = useSession()
  const userRole = session?.user?.role

  if (process.env.NODE_ENV !== 'development' || !isClient || (userRole !== 'admin' && userRole !== 'trainer')) {
    return null
  }

  // Apply service type configuration
  const applyServiceType = (type: trainerServiceType) => {
    setIsChanging(true)
    setServiceType(type)

    const newFlags = { ...trainerServiceConfigs[type] }
    setFeatureFlags(newFlags)
    setDevFeatureFlags(newFlags)

    // Use setTimeout to ensure the cookie is set before reloading
    setTimeout(() => {
      window.location.reload()
    }, 300)
  }

  // Toggle individual feature
  const toggleFeature = (feature: keyof FeatureFlags) => {
    setIsChanging(true)

    const newFlags = {
      ...featureFlags,
      [feature]: !featureFlags[feature]
    }

    setFeatureFlags(newFlags)
    setDevFeatureFlags(newFlags)
    setServiceType('custom')  // When toggling individual features, it becomes custom

    // Use setTimeout to ensure the cookie is set before reloading
    setTimeout(() => {
      window.location.reload()
    }, 300)
  }

  // Reset feature flags
  const resetFeatureFlags = () => {
    setIsChanging(true)
    clearDevFeatureFlags()
    setFeatureFlags(defaultFeatureFlags)
    setServiceType('full-service')

    // Use setTimeout to ensure the cookie is cleared before reloading
    setTimeout(() => {
      window.location.reload()
    }, 300)
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-background border p-3 rounded-lg shadow-lg w-64">
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Service Type</span>
          <Badge className="ml-2 capitalize">
            {serviceType}
          </Badge>
        </div>

        {isChanging ? (
          <div className="flex items-center justify-center p-2">
            <RotateCw className="h-4 w-4 mr-2 animate-spin" />
            <span className="text-sm">Applying changes...</span>
          </div>
        ) : (
          <div className="flex flex-col gap-2">
            <Select value={serviceType} onValueChange={(value) => applyServiceType(value as trainerServiceType)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select service type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="full-service">Full Service</SelectItem>
                <SelectItem value="products-only">Products Only</SelectItem>
                <SelectItem value="subscriptions-only">Subscriptions Only</SelectItem>
                <SelectItem value="coaching-only">Coaching Only</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>

            <div className="space-y-2 mt-2 border-t pt-2">
              <div className="flex items-center justify-between">
                <span className="text-xs flex items-center">
                  <Store className="h-3 w-3 mr-1" /> Digital Store
                </span>
                <Switch
                  checked={featureFlags.enableStore}
                  onCheckedChange={() => toggleFeature('enableStore')}
                />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-xs flex items-center">
                  <ScrollText className="h-3 w-3 mr-1" /> Subscriptions
                </span>
                <Switch
                  checked={featureFlags.enableSubscriptions}
                  onCheckedChange={() => toggleFeature('enableSubscriptions')}
                />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-xs flex items-center">
                  <Sparkles className="h-3 w-3 mr-1" /> Premium Coaching
                </span>
                <Switch
                  checked={featureFlags.enablePremiumCoaching}
                  onCheckedChange={() => toggleFeature('enablePremiumCoaching')}
                />
              </div>
            </div>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" size="sm" className="mt-2">
                  Reset
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Reset feature flags?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will reset all feature flags to their default values.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={resetFeatureFlags}>Reset</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )}
      </div>
    </div>
  )
}