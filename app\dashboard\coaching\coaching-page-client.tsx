"use client"

import {
  <PERSON>,
  UserPlus,
  MessageSquare,
  ClipboardList,
  Search,
  Filter,
  Bell,
  RefreshCw,
  DollarSign,
  ArrowRight,
} from "lucide-react"
import Link from "next/link"
import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DataTable } from "@/components/ui/data-table"
import { ClientTableColumns } from "./client-table-columns"
import { AddClientModal } from "@/components/coaching/add-client-modal"
import { RenewSubscriptionModal } from "@/components/coaching/renew-subscription-modal"
import { renewCoachingRelationship, cancelCoachingRelationship } from "@/app/actions/coaching"
import { toast } from "sonner"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface Client {
  id: string
  name: string
  email: string
  status: string
  joinedDate: string
  nextSession: string | null
  unreadMessages: number
  plan: string
  relationshipId: string
  monthlyFee: number
  expirationDate?: string
}

interface Inquiry {
  id: string
  name: string
  email: string
  date: string
  goals: string
}

interface CoachingPageClientProps {
  clients: Client[]
  inquiries: Inquiry[]
}

interface SubscriptionEventDetail {
  clientId: string
  clientName: string
  relationshipId: string
  monthlyFee: number
}

export function CoachingPageClient({ clients: initialClients, inquiries }: CoachingPageClientProps) {
  const [clients, setClients] = useState<Client[]>(initialClients)
  const [isAddClientModalOpen, setIsAddClientModalOpen] = useState(false)
  const [renewSubscriptionData, setRenewSubscriptionData] = useState<SubscriptionEventDetail | null>(null)
  const [cancelSubscriptionData, setCancelSubscriptionData] = useState<SubscriptionEventDetail | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  useEffect(() => {
    const handleRenewSubscription = (event: Event) => {
      const customEvent = event as CustomEvent<SubscriptionEventDetail>
      setRenewSubscriptionData(customEvent.detail)
    }

    const handleCancelSubscription = (event: Event) => {
      const customEvent = event as CustomEvent<SubscriptionEventDetail>
      setCancelSubscriptionData(customEvent.detail)
    }

    document.addEventListener('renewSubscription', handleRenewSubscription)
    document.addEventListener('cancelSubscription', handleCancelSubscription)
    
    return () => {
      document.removeEventListener('renewSubscription', handleRenewSubscription)
      document.removeEventListener('cancelSubscription', handleCancelSubscription)
    }
  }, [])

  const handleCancelConfirm = async () => {
    if (!cancelSubscriptionData) return

    try {
      await cancelCoachingRelationship(cancelSubscriptionData.relationshipId)
      
      // Update the client's status in the local state
      setClients(prevClients => 
        prevClients.map(client => 
          client.relationshipId === cancelSubscriptionData.relationshipId
            ? { ...client, status: "inactive" }
            : client
        )
      )

      toast.success("Subscription cancelled successfully")
    } catch (error) {
      console.error("Error cancelling subscription:", error)
      toast.error("Failed to cancel subscription")
    }

    setCancelSubscriptionData(null)
  }

  const handleAddClientSuccess = (newClient: Client) => {
    setClients(prevClients => [...prevClients, newClient])
    setIsAddClientModalOpen(false)
    toast.success("Client added successfully")
  }

  // Filter clients based on search query and status
  const filteredClients = clients.filter(client => {
    const matchesSearch = 
      client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.email.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = 
      statusFilter === "all" || client.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const expiredCount = clients.filter(c => c.status === "inactive").length
  const activeCount = clients.filter(c => c.status === "active").length
  const totalRevenue = clients
    .filter(c => c.status === "active")
    .reduce((sum, client) => sum + client.monthlyFee, 0)

  return (
    <div className="container py-8 space-y-8">
      {/* Enhanced Header Section */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-primary/10 via-primary/5 to-background border border-primary/10 p-8 mb-8">
        <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] dark:bg-grid-black/10" />
        <div className="relative">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                Premium Coaching
              </h1>
              <p className="text-muted-foreground/80 max-w-[600px] leading-relaxed">
                Manage your premium coaching clients and inquiries. Provide personalized guidance and track their progress.
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button asChild variant="outline" className="shadow-sm hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors">
                <Link href="/dashboard/coaching/plans">
                  <ClipboardList className="mr-2 h-4 w-4" />
                  Training Plans
                </Link>
              </Button>
              <Button onClick={() => setIsAddClientModalOpen(true)} className="shadow-sm bg-primary hover:bg-primary/90">
                <UserPlus className="mr-2 h-4 w-4" />
                Add Client
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Section */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="bg-gradient-to-br from-background to-primary/5 border-primary/10 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Users className="h-4 w-4 text-primary/70" />
              Active Clients
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{activeCount}</div>
            <p className="text-muted-foreground text-sm mt-1">Currently active premium clients</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-background to-primary/5 border-primary/10 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <RefreshCw className="h-4 w-4 text-primary/70" />
              Pending Renewals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{expiredCount}</div>
            <p className="text-muted-foreground text-sm mt-1">Clients pending subscription renewal</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-background to-primary/5 border-primary/10 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <DollarSign className="h-4 w-4 text-primary/70" />
              Monthly Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">${totalRevenue.toFixed(2)}</div>
            <p className="text-muted-foreground text-sm mt-1">Total monthly coaching revenue</p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Search and Filter Section */}
      <Card className="border-primary/10 shadow-sm">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search clients..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 bg-muted/30 border-primary/10 focus-visible:ring-primary/20"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px] bg-muted/30 border-primary/10">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Clients</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Client Table with Tabs */}
      <Card className="border-primary/10 shadow-sm overflow-hidden">
        <Tabs defaultValue="clients" className="w-full">
          <CardHeader className="bg-muted/30 border-b border-primary/5">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <CardTitle className="text-lg">Client Management</CardTitle>
                <CardDescription>View and manage your premium coaching clients</CardDescription>
              </div>
              <TabsList className="grid w-full sm:w-auto grid-cols-2 h-9">
                <TabsTrigger value="clients" className="relative">
                  <Users className="mr-2 h-4 w-4" />
                  Clients
                  {expiredCount > 0 && (
                    <Badge variant="destructive" className="ml-2 absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0">
                      {expiredCount}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="inquiries">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Inquiries
                  {inquiries.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {inquiries.length}
                    </Badge>
                  )}
                </TabsTrigger>
              </TabsList>
            </div>
          </CardHeader>

          <TabsContent value="clients" className="m-0">
            <CardContent className="p-0">
              <DataTable columns={ClientTableColumns} data={filteredClients} />
            </CardContent>
          </TabsContent>

          <TabsContent value="inquiries" className="m-0">
            <CardContent>
              <div className="space-y-4">
                {inquiries.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                      <MessageSquare className="h-6 w-6 text-primary/70" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">No Active Inquiries</h3>
                    <p className="text-muted-foreground text-sm">
                      You don't have any pending coaching inquiries at the moment.
                    </p>
                  </div>
                ) : (
                  inquiries.map((inquiry) => (
                    <div
                      key={inquiry.id}
                      className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 border border-primary/10 rounded-lg bg-gradient-to-r from-background to-primary/5 hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex-1">
                        <div className="font-medium">{inquiry.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {inquiry.email}
                        </div>
                        <div className="text-sm mt-2 text-muted-foreground/90">{inquiry.goals}</div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20">
                          New Inquiry
                        </Badge>
                        <Button variant="outline" size="sm" className="hover:bg-primary/5 hover:text-primary hover:border-primary/20">
                          View Details
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </TabsContent>
        </Tabs>
      </Card>

      <AddClientModal
        isOpen={isAddClientModalOpen}
        onClose={() => setIsAddClientModalOpen(false)}
        onSuccess={handleAddClientSuccess}
      />

      {renewSubscriptionData && (
        <RenewSubscriptionModal
          isOpen={true}
          onClose={() => setRenewSubscriptionData(null)}
          clientName={renewSubscriptionData.clientName}
          relationshipId={renewSubscriptionData.relationshipId}
          currentFee={renewSubscriptionData.monthlyFee}
          onSuccess={(updatedClient) => {
            setClients(prevClients =>
              prevClients.map(client =>
                client.id === renewSubscriptionData.clientId
                  ? {
                      ...client,
                      status: updatedClient.status,
                      monthlyFee: updatedClient.monthlyFee,
                      relationshipId: updatedClient.relationshipId
                    }
                  : client
              )
            )
            setRenewSubscriptionData(null)
            toast.success("Subscription renewed successfully")
          }}
        />
      )}

      <AlertDialog open={!!cancelSubscriptionData} onOpenChange={() => setCancelSubscriptionData(null)}>
        <AlertDialogContent className="bg-gradient-to-b from-background to-muted/50 border-primary/10">
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Subscription</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to cancel the subscription for {cancelSubscriptionData?.clientName}? 
              This will immediately end their premium coaching access.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="hover:bg-primary/5 hover:text-primary hover:border-primary/20">Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleCancelConfirm} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Confirm Cancellation
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 