"use client";

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";

interface WorkoutFeedbackProps {
  workoutId: string;
  clientId: string;
  existingFeedback?: string;
}

/**
 * WorkoutFeedback component for 1:1 coaching
 * Allows trainers to provide feedback on client workouts
 */
export function WorkoutFeedback({ 
  workoutId, 
  clientId, 
  existingFeedback = '' 
}: WorkoutFeedbackProps) {
  const [feedback, setFeedback] = useState(existingFeedback);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  
  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/workouts/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workoutId,
          clientId,
          feedback,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save feedback');
      }
      
      toast({
        title: 'Feedback saved',
        description: 'Your feedback has been saved successfully.',
      });
    } catch (error) {
      console.error('Error saving feedback:', error);
      toast({
        title: 'Error',
        description: 'Failed to save feedback. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Workout Feedback</h3>
      <Textarea
        value={feedback}
        onChange={(e) => setFeedback(e.target.value)}
        placeholder="Enter your feedback for this workout..."
        rows={4}
      />
      <Button 
        onClick={handleSubmit} 
        disabled={isSubmitting}
      >
        {isSubmitting ? 'Saving...' : 'Save Feedback'}
      </Button>
    </div>
  );
}
