"use client"

import { useSession } from "next-auth/react"
import React, { useEffect, useState, useRef, useCallback } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/components/ui/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { MessageSquare, Send } from "lucide-react"

interface Message {
  id: string
  content: string
  senderId: string
  receiverId: string
  createdAt: string
  isOptimistic?: boolean
  sender?: {
    id: string
    name?: string
    avatarUrl?: string | null
  }
  receiver?: {
    id: string
    name?: string
    avatarUrl?: string | null
  }
}

interface PremiumClientChatProps {
  clientId: string
  clientName: string
  clientAvatar?: string
}

export function PremiumClientChat({ clientId, clientName, clientAvatar }: PremiumClientChatProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState("")
  const [loading, setLoading] = useState(false)
  const [fetchingMessages, setFetchingMessages] = useState(false)
  const [updatingMessages, setUpdatingMessages] = useState(false)
  const [conversationId, setConversationId] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Scroll to bottom utility
  const scrollToBottom = useCallback(() => {
    requestAnimationFrame(() => {
      if (messagesEndRef.current) {
        // Force a more aggressive scroll to ensure it works with the fixed container
        messagesEndRef.current.scrollIntoView({ behavior: "smooth", block: "end" })

        // Also try to scroll the parent ScrollArea if needed
        const scrollArea = document.querySelector('[data-radix-scroll-area-viewport]')
        if (scrollArea) {
          scrollArea.scrollTop = scrollArea.scrollHeight
        }
      }
    })
  }, [])

  // Fetch conversation and messages
  const fetchConversation = useCallback(async () => {
    if (!session?.user?.id || !clientId) return

    try {
      // First, find or create a conversation with this client
      const response = await fetch(`/api/coaching/conversations?clientId=${clientId}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch conversation: ${response.status}`)
      }

      const conversations = await response.json()

      // Find the conversation with this client
      const conversation = conversations.find((conv: any) =>
        conv.user.id === clientId ||
        (conv.relationship && (conv.relationship.clientId === clientId || conv.relationship.trainerId === clientId))
      )

      if (conversation) {
        setConversationId(conversation.id)

        // Fetch messages only if we have a conversation ID
        // This is handled separately to avoid circular dependencies
        const fetchMessagesForConversation = async (convId: string) => {
          try {
            const msgResponse = await fetch(`/api/coaching/messages?conversationId=${convId}`)

            if (!msgResponse.ok) {
              throw new Error(`Failed to fetch messages: ${msgResponse.status}`)
            }

            const data = await msgResponse.json()
            if (data && Array.isArray(data)) {
              setMessages(data)
              setTimeout(scrollToBottom, 100)
            }
          } catch (msgError) {
            console.error("Error fetching initial messages:", msgError)
          } finally {
            setFetchingMessages(false)
          }
        }

        setFetchingMessages(true)
        fetchMessagesForConversation(conversation.id)
      } else {
        // If no conversation exists, we'll create one when sending the first message
        console.log("No existing conversation found with this client")
        setFetchingMessages(false)
      }
    } catch (error) {
      console.error("Error fetching conversation:", error)
      toast({
        title: "Error",
        description: "Failed to load conversation. Please try again.",
        variant: "destructive",
      })
      setFetchingMessages(false)
    }
  }, [session, clientId, toast, scrollToBottom])

  // Fetch messages for a conversation with smart updates
  const fetchMessages = useCallback(async (convId: string) => {
    if (!convId) return

    // Don't show loading indicator for background refreshes if we already have messages
    const isInitialLoad = messages.length === 0
    if (isInitialLoad) {
      setFetchingMessages(true)
    } else {
      // For background refreshes, we'll use a more subtle approach
      // by showing a minimal loading indicator
      setUpdatingMessages(true)
      console.log('Background refresh of messages, using subtle indicator')
    }

    try {
      // Add cache-busting parameter and headers to prevent caching
      const timestamp = new Date().getTime()
      const response = await fetch(`/api/coaching/messages?conversationId=${convId}&t=${timestamp}`, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch messages: ${response.status}`)
      }

      const data = await response.json()

      // Only update state if we got new data and it's different from what we have
      if (data && Array.isArray(data)) {
        // Check if we have new messages by comparing the latest message IDs
        const currentLatestMessageId = messages.length > 0 ?
          messages.filter(m => !m.isOptimistic).slice(-1)[0]?.id : null
        const newLatestMessageId = data.length > 0 ? data[data.length - 1].id : null

        // Check if the message count has changed (excluding optimistic messages)
        const currentMessageCount = messages.filter(m => !m.isOptimistic).length
        const newMessageCount = data.length

        // Check if we need to update
        const needsUpdate = newLatestMessageId !== currentLatestMessageId ||
                           currentMessageCount !== newMessageCount ||
                           messages.length === 0

        if (needsUpdate) {
          console.log(`Smart update: found ${data.length} messages, current count: ${currentMessageCount}`)

          // Preserve optimistic messages that aren't in the fetched data
          const optimisticMessages = messages.filter(m =>
            m.isOptimistic && !data.some(d => d.id === m.id)
          )

          // Combine fetched messages with optimistic ones
          const combinedMessages = [...data, ...optimisticMessages]

          // Sort by creation time
          combinedMessages.sort((a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          )

          // Update state with minimal UI disruption
          setMessages(combinedMessages)

          // Only scroll to bottom if we have new messages and it's not the initial load
          if (newLatestMessageId !== currentLatestMessageId && !isInitialLoad) {
            // Use a small delay to ensure the DOM has updated
            setTimeout(scrollToBottom, 50)
          }
        } else {
          console.log('No new messages found, skipping update')
        }
      } else {
        console.warn('Received invalid data format from API:', data)
      }
    } catch (error) {
      console.error("Error fetching messages:", error)

      // Only show toast for initial load errors, not background refreshes
      if (isInitialLoad) {
        toast({
          title: "Error",
          description: "Failed to load messages. Please try again.",
          variant: "destructive",
        })
      }

      // If we have a conversation ID but no messages, try to recover
      if (messages.length === 0 && conversationId) {
        console.log('Attempting to recover messages...')
        // Instead of calling fetchConversation which would create a circular dependency,
        // we'll just retry the fetch after a short delay
        setTimeout(() => {
          if (conversationId) {
            console.log('Retrying message fetch...')
            fetch(`/api/coaching/messages?conversationId=${conversationId}`)
              .then(response => response.json())
              .then(data => {
                if (data && Array.isArray(data) && data.length > 0) {
                  console.log(`Recovery successful: found ${data.length} messages`)
                  setMessages(data)
                  setTimeout(scrollToBottom, 50)
                }
              })
              .catch(err => console.error('Recovery attempt failed:', err))
          }
        }, 1000) // Wait 1 second before retrying
      }
    } finally {
      if (isInitialLoad) {
        setFetchingMessages(false)
        // Scroll to bottom after initial messages load
        setTimeout(scrollToBottom, 50)
      } else {
        // Reset the subtle loading indicator after a short delay
        // to make it visible but not too distracting
        setTimeout(() => {
          setUpdatingMessages(false)
        }, 300)
      }
    }
  }, [toast, messages, conversationId, scrollToBottom])

  // Create a new conversation with the client
  const createConversation = useCallback(async () => {
    if (!session?.user?.id || !clientId) return null

    try {
      const response = await fetch(`/api/coaching/conversations`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId,
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to create conversation: ${response.status}`)
      }

      const data = await response.json()
      setConversationId(data.id)
      return data.id
    } catch (error) {
      console.error("Error creating conversation:", error)
      toast({
        title: "Error",
        description: "Failed to create conversation. Please try again.",
        variant: "destructive",
      })
      return null
    }
  }, [session, clientId, toast])

  // Send a message with optimistic UI updates
  const sendMessage = useCallback(async () => {
    if (!session?.user?.id || !newMessage.trim()) return

    // Store the message content before clearing the input
    const messageContent = newMessage.trim()

    // Clear the input immediately for better UX
    setNewMessage("")

    // Create an optimistic message to show immediately
    const optimisticId = `temp-${Date.now()}`
    const optimisticMessage: Message = {
      id: optimisticId,
      content: messageContent,
      senderId: session.user.id,
      receiverId: "",
      conversationId: conversationId || "",
      createdAt: new Date().toISOString(),
      isOptimistic: true // Flag to identify this as an optimistic update
    }

    // Add the optimistic message to the UI immediately
    setMessages(prev => [...prev, optimisticMessage])

    // Scroll to bottom immediately
    scrollToBottom()

    // Now start the actual sending process
    setLoading(true)
    try {
      let currentConversationId = conversationId

      // If no conversation exists, create one
      if (!currentConversationId) {
        currentConversationId = await createConversation()
        if (!currentConversationId) {
          throw new Error("Failed to create conversation")
        }
      }

      // Send the message
      const response = await fetch("/api/coaching/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: messageContent,
          conversationId: currentConversationId,
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.status}`)
      }

      const data = await response.json()

      // Replace the optimistic message with the real one
      setMessages(prev =>
        prev.map(msg =>
          msg.id === optimisticId ? data : msg
        )
      )

      // Dispatch a custom event to notify other components
      window.dispatchEvent(new CustomEvent('message-sent', {
        detail: { message: data }
      }))
    } catch (error) {
      console.error("Error sending message:", error)

      // Remove the optimistic message on error
      setMessages(prev => prev.filter(msg => msg.id !== optimisticId))

      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      })

      // Put the message back in the input
      setNewMessage(messageContent)
    } finally {
      setLoading(false)
      // Focus back on input
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [session, newMessage, conversationId, createConversation, toast, scrollToBottom])

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    sendMessage()
  }

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value)
  }

  // Initialize conversation
  useEffect(() => {
    if (session?.user?.id && clientId) {
      fetchConversation()
    }
  }, [session, clientId, fetchConversation])

  // Set up real-time updates with WebSocket-style events and smart polling
  useEffect(() => {
    if (!conversationId) return

    console.log('Setting up real-time updates for conversation:', conversationId)

    // Track if we're currently refreshing to prevent overlapping refreshes
    let isRefreshing = false

    // Function to handle message events
    const handleMessageEvent = (event: Event) => {
      const detail = (event as CustomEvent).detail
      console.log('Message event received:', event.type, detail)

      // Only refresh if we're not already refreshing and have a valid conversation ID
      if (!isRefreshing && conversationId) {
        isRefreshing = true

        // Add a small delay to allow the server to process the message
        setTimeout(() => {
          fetchMessages(conversationId).finally(() => {
            isRefreshing = false
          })
        }, 300)
      }
    }

    // Set up event listeners for various message events
    window.addEventListener('message-received', handleMessageEvent)
    window.addEventListener('socket-event', handleMessageEvent)
    window.addEventListener('force-refresh-chat', handleMessageEvent)

    // Set up polling as a fallback, but with a longer interval
    const intervalId = setInterval(() => {
      if (!isRefreshing) {
        isRefreshing = true
        console.log('Polling for new messages (fallback)...')
        fetchMessages(conversationId).finally(() => {
          isRefreshing = false
        })
      }
    }, 15000) // 15 seconds - longer interval since we have event-based updates

    // Clean up interval and event listeners on unmount
    return () => {
      console.log('Cleaning up real-time update handlers')
      clearInterval(intervalId)
      window.removeEventListener('message-received', handleMessageEvent)
      window.removeEventListener('socket-event', handleMessageEvent)
      window.removeEventListener('force-refresh-chat', handleMessageEvent)
    }
  }, [conversationId, fetchMessages])

  // Store conversation ID in localStorage to persist between tab switches
  useEffect(() => {
    // When conversation ID is set, store it in localStorage
    if (conversationId) {
      localStorage.setItem(`chat_conversation_${clientId}`, conversationId)
    } else {
      // Try to retrieve from localStorage if not set
      const storedConversationId = localStorage.getItem(`chat_conversation_${clientId}`)
      if (storedConversationId) {
        console.log('Retrieved conversation ID from localStorage:', storedConversationId)
        setConversationId(storedConversationId)
        fetchMessages(storedConversationId)
      }
    }
  }, [conversationId, clientId, fetchMessages])

  // Format messages for display
  const messageElements = messages.map((message) => {
    const isCurrentUser = message.senderId === session?.user?.id
    const senderName = isCurrentUser ? session?.user?.name || "You" : clientName
    const avatarUrl = isCurrentUser
      ? session?.user?.image
      : clientAvatar || `https://avatar.vercel.sh/${clientName}`
    const isOptimistic = message.isOptimistic

    return (
      <div
        key={message.id}
        className={`flex items-start gap-2 ${isCurrentUser ? "flex-row-reverse" : ""} ${
          isOptimistic ? "opacity-80" : ""
        }`}
      >
        <Avatar className="h-8 w-8 mt-1">
          <AvatarImage src={avatarUrl} alt={senderName} />
          <AvatarFallback>{senderName?.[0]?.toUpperCase() || "?"}</AvatarFallback>
        </Avatar>
        <div
          className={`rounded-lg px-3 py-2 max-w-[80%] ${
            isCurrentUser
              ? `${isOptimistic ? "bg-primary/80" : "bg-primary"} text-primary-foreground`
              : "bg-muted"
          } transition-opacity duration-200`}
        >
          <div className="flex flex-col">
            <span className="text-xs opacity-70">{senderName}</span>
            <p className="text-sm">{message.content}</p>
            <div className="flex items-center justify-end mt-1 gap-1">
              {isOptimistic && (
                <div className="w-3 h-3 rounded-full border-2 border-t-transparent border-primary-foreground/50 animate-spin"></div>
              )}
              <span className="text-xs opacity-70 self-end">
                {isOptimistic ? "Sending..." : new Date(message.createdAt).toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </span>
            </div>
          </div>
        </div>
      </div>
    )
  })

  // If no messages yet, show a welcome message
  const emptyState = (
    <div className="flex flex-col items-center justify-center h-full p-8 text-center">
      <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
        <MessageSquare className="h-8 w-8 text-primary/70" />
      </div>
      <h3 className="text-lg font-medium mb-2">Start a conversation</h3>
      <p className="text-muted-foreground mb-6 max-w-md">
        Send a message to {clientName} to start your coaching conversation.
      </p>
    </div>
  )

  return (
    <Card className="flex flex-col" style={{ height: "700px" }}>
      <CardHeader className="pb-3 flex-shrink-0">
        <CardTitle>Chat with {clientName}</CardTitle>
        <CardDescription>
          Send messages and provide coaching support
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col p-0 overflow-hidden">
        <div className="flex-1 overflow-hidden relative">
          {updatingMessages && (
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 z-10 bg-background/80 px-3 py-1 rounded-full text-xs text-muted-foreground flex items-center gap-1 shadow-sm border border-border">
              <div className="w-2 h-2 rounded-full border-2 border-t-transparent border-primary/50 animate-spin"></div>
              <span>Updating...</span>
            </div>
          )}
          <ScrollArea className="absolute inset-0 p-4">
            {fetchingMessages ? (
              <div className="flex justify-center items-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : messages.length > 0 ? (
              <div className="space-y-4">
                {messageElements}
                <div ref={messagesEndRef} />
              </div>
            ) : (
              emptyState
            )}
          </ScrollArea>
        </div>
        <form onSubmit={handleSubmit} className="border-t p-4 flex-shrink-0">
          <div className="flex gap-2">
            <Input
              value={newMessage}
              onChange={handleInputChange}
              placeholder="Type your message..."
              disabled={loading}
              ref={inputRef}
              className="flex-1"
            />
            <Button type="submit" disabled={loading || !newMessage.trim()}>
              <Send className="h-4 w-4 mr-2" />
              Send
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
