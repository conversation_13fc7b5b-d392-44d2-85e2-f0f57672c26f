/**
 * API caching utility for Clear Coach
 * Uses Upstash Redis for distributed caching
 */

import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL || '',
  token: process.env.UPSTASH_REDIS_REST_TOKEN || '',
});

const DEFAULT_TTL = 60 * 5; // 5 minutes

/**
 * Get data from cache or fetch it if not cached
 * @param key Cache key
 * @param fetchFn Function to fetch data if not in cache
 * @param ttl Time to live in seconds
 * @returns Cached or freshly fetched data
 */
export async function getCachedData<T>(
  key: string,
  fetchFn: () => Promise<T>,
  ttl: number = DEFAULT_TTL
): Promise<T> {
  try {
    const cachedData = await redis.get(key);
    if (cachedData) {
      return cachedData as T;
    }
  } catch (error) {
    console.error('Cache error:', error);
  }
  
  const data = await fetchFn();
  
  try {
    await redis.set(key, data, { ex: ttl });
  } catch (error) {
    console.error('Cache set error:', error);
  }
  
  return data;
}

/**
 * Invalidate a cache key
 * @param key Cache key to invalidate
 */
export async function invalidateCache(key: string): Promise<void> {
  try {
    await redis.del(key);
  } catch (error) {
    console.error('Cache invalidation error:', error);
  }
}

/**
 * Invalidate multiple cache keys by pattern
 * @param pattern Pattern to match keys (e.g., "user:*")
 */
export async function invalidateCacheByPattern(pattern: string): Promise<void> {
  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
  } catch (error) {
    console.error('Cache pattern invalidation error:', error);
  }
}

/**
 * Create a cache key from parts
 * @param parts Parts to combine into a cache key
 * @returns Cache key string
 */
export function createCacheKey(...parts: (string | number | undefined)[]): string {
  return parts.filter(Boolean).join(':');
}
