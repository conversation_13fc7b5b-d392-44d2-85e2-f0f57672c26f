import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { UserRole } from '../types';

type HandleOptions = {
  allowedRoles?: UserRole[];
  requireAuth?: boolean;
};

/**
 * Middleware to protect API routes
 * 
 * @param req The NextRequest object
 * @param handler The handler function to execute if authorized
 * @param options Options for authorization
 * @returns The response from the handler or an unauthorized response
 */
export async function withAuth(
  req: NextRequest,
  handler: (req: NextRequest, session: any) => Promise<NextResponse>,
  options: HandleOptions = { requireAuth: true }
) {
  const { allowedRoles = [], requireAuth = true } = options;
  
  try {
    // Get session
    const session = await getServerSession();
    
    // Check if authenticated
    if (requireAuth && !session?.user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Unauthorized. Please sign in to access this resource.' 
        },
        { status: 401 }
      );
    }
    
    // Check role-based access if roles are specified
    if (allowedRoles.length > 0 && session?.user) {
      const hasAccess = allowedRoles.includes(session.user.role as UserRole);
      
      if (!hasAccess) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Forbidden. You do not have permission to access this resource.' 
          },
          { status: 403 }
        );
      }
    }
    
    // Call the handler with the authenticated request
    return handler(req, session);
  } catch (error) {
    console.error('Auth middleware error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
} 