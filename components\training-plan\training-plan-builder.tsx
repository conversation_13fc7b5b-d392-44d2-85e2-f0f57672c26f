"use client"

import { <PERSON>nd<PERSON>ontext, DragEndEvent, DragOverlay, DragStartEvent, closestCenter } from "@dnd-kit/core"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { DraggableExercise } from "@/components/workout/draggable-exercise"
import { DroppableWorkout } from "@/components/workout/droppable-workout"
import { Week, Workout, Exercise } from "@prisma/client"

type WeekWithWorkouts = Week & {
  workouts: (Workout & {
    exercises: Exercise[]
  })[]
}

interface DragData {
  type: 'exercise';
  exercise: Exercise;
  source: {
    weekId: string;
    workoutId: string;
  };
}

interface TrainingPlanBuilderProps {
  planId: string
  initialWeeks?: WeekWithWorkouts[]
}

export function TrainingPlanBuilder({ planId, initialWeeks = [] }: TrainingPlanBuilderProps) {
  const router = useRouter()
  const [weeks, setWeeks] = useState<WeekWithWorkouts[]>(initialWeeks)
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [activeDragData, setActiveDragData] = useState<DragData | null>(null)

  const addWeek = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/training-plans/${planId}/weeks`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          weekNumber: weeks.length + 1,
        }),
      })

      if (!response.ok) throw new Error("Failed to add week")

      const newWeek = await response.json()
      setWeeks([...weeks, newWeek as WeekWithWorkouts])
    } catch (error) {
      console.error("Error adding week:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const addWorkout = async (weekId: string) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/training-plans/${planId}/workouts`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          weekId,
          title: `Workout ${weeks.find(w => w.id === weekId)?.workouts.length || 0 + 1}`,
          type: "strength", // Default type
        }),
      })

      if (!response.ok) throw new Error("Failed to add workout")

      const newWorkout = await response.json()
      setWeeks(weeks.map(week =>
        week.id === weekId
          ? { ...week, workouts: [...week.workouts, newWorkout as Workout & { exercises: Exercise[] }] }
          : week
      ))
    } catch (error) {
      console.error("Error adding workout:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    const activeId = active.id as string

    let foundExercise: Exercise | null = null
    let sourceDetails: { weekId: string; workoutId: string } | null = null

    for (const week of weeks) {
      for (const workout of week.workouts) {
        const exercise = workout.exercises.find(e => e.id === activeId)
        if (exercise) {
          foundExercise = exercise
          sourceDetails = { weekId: week.id, workoutId: workout.id }
          break
        }
      }
      if (foundExercise) break
    }

    if (foundExercise && sourceDetails) {
      setActiveDragData({
        type: 'exercise',
        exercise: foundExercise,
        source: sourceDetails,
      })
    }
  }

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event
    const activeData = activeDragData
    setActiveDragData(null)

    if (!over || !activeData || activeData.type !== 'exercise') return

    const sourceWeekId = activeData.source.weekId
    const sourceWorkoutId = activeData.source.workoutId
    const exerciseToMove = activeData.exercise
    const overId = over.id as string

    // Check if we're dropping onto another exercise
    const isOverExercise = over.data?.current?.type === 'exercise'

    console.log('Drag end event:', {
      isOverExercise,
      overData: over.data?.current,
      activeData,
      overId
    })

    if (isOverExercise) {
      // We're dropping onto another exercise - create an alternative
      const targetExerciseId = overId
      let targetExercise: Exercise | null = null
      let targetWorkoutId: string | null = null

      // Find the target exercise and its workout
      for (const week of weeks) {
        for (const workout of week.workouts) {
          const exercise = workout.exercises.find(e => e.id === targetExerciseId)
          if (exercise) {
            targetExercise = exercise
            targetWorkoutId = workout.id
            break
          }
        }
        if (targetExercise) break
      }

      if (!targetExercise || !targetWorkoutId) return

      // Don't allow dropping on itself
      if (targetExerciseId === exerciseToMove.id) return

      // Don't allow dropping on an alternative exercise
      if (targetExercise.isAlternative) return

      // Create a copy of the exercise as an alternative
      const alternativeExercise = {
        ...exerciseToMove,
        parentExerciseId: targetExerciseId,
        isAlternative: true
      }

      setWeeks(prevWeeks => {
        const newWeeks: WeekWithWorkouts[] = JSON.parse(JSON.stringify(prevWeeks))

        // Remove the exercise from its source if it's not already an alternative
        if (!exerciseToMove.isAlternative) {
          const sourceWeek = newWeeks.find(w => w.id === sourceWeekId)
          if (sourceWeek) {
            const sourceWorkout = sourceWeek.workouts.find(w => w.id === sourceWorkoutId)
            if (sourceWorkout) {
              const exerciseIndex = sourceWorkout.exercises.findIndex(e => e.id === exerciseToMove.id)
              if (exerciseIndex > -1) {
                sourceWorkout.exercises.splice(exerciseIndex, 1)
              }
            }
          }
        }

        // Add the alternative exercise to the target workout
        for (const week of newWeeks) {
          for (const workout of week.workouts) {
            if (workout.id === targetWorkoutId) {
              workout.exercises.push(alternativeExercise)
              break
            }
          }
        }

        return newWeeks
      })

      try {
        // First update the exercise to be an alternative
        const response = await fetch(`/api/exercises/${exerciseToMove.id}/alternative`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            workoutId: targetWorkoutId,
            parentExerciseId: targetExerciseId,
            isAlternative: true
          }),
        })

        if (!response.ok) {
          throw new Error('Failed to update exercise as alternative')
        }
        console.log("Exercise set as alternative successfully in DB")
      } catch (error) {
        console.error("Error setting exercise as alternative:", error)
        setWeeks(initialWeeks)
      }
    } else {
      // Regular drag and drop to a workout
      const destinationWorkoutId = overId
      let destinationWeekId: string | null = null

      for (const week of weeks) {
        if (week.workouts.some(w => w.id === destinationWorkoutId)) {
          destinationWeekId = week.id
          break
        }
      }

      if (!destinationWeekId) return

      if (sourceWorkoutId === destinationWorkoutId) return

      setWeeks(prevWeeks => {
        const newWeeks: WeekWithWorkouts[] = JSON.parse(JSON.stringify(prevWeeks))

        let sourceWorkout: (Workout & { exercises: Exercise[] }) | undefined
        let destWorkout: (Workout & { exercises: Exercise[] }) | undefined

        const sourceWeek = newWeeks.find(w => w.id === sourceWeekId)
        const destWeek = newWeeks.find(w => w.id === destinationWeekId)

        if (!sourceWeek || !destWeek) return prevWeeks

        sourceWorkout = sourceWeek.workouts.find(w => w.id === sourceWorkoutId)
        destWorkout = destWeek.workouts.find(w => w.id === destinationWorkoutId)

        if (!sourceWorkout || !destWorkout) return prevWeeks

        const exerciseIndex = sourceWorkout.exercises.findIndex((e: Exercise) => e.id === exerciseToMove.id)
        if (exerciseIndex > -1) {
          sourceWorkout.exercises.splice(exerciseIndex, 1)
        }

        if (!destWorkout.exercises.some((e: Exercise) => e.id === exerciseToMove.id)) {
          destWorkout.exercises.push(exerciseToMove)
        }

        return newWeeks
      })

      try {
        const response = await fetch(`/api/exercises/${exerciseToMove.id}/alternative`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            workoutId: destinationWorkoutId,
            // Reset parent relationship if this was an alternative
            parentExerciseId: null,
            isAlternative: false
          }),
        })

        if (!response.ok) {
          throw new Error('Failed to update exercise in database')
        }
        console.log("Exercise moved successfully in DB")
      } catch (error) {
        console.error("Error updating exercise position:", error)
        setWeeks(initialWeeks)
      }
    }
  }

  const handleExerciseDelete = async (exerciseId: string) => {
    let weekId: string | null = null
    let workoutId: string | null = null

    for (const week of weeks) {
      for (const workout of week.workouts) {
        if (workout.exercises.some(e => e.id === exerciseId)) {
          weekId = week.id
          workoutId = workout.id
          break
        }
      }
      if (weekId && workoutId) break
    }

    if (!weekId || !workoutId) return

    setIsLoading(true)
    try {
      const response = await fetch(`/api/training-plans/workouts/${workoutId}/exercises/${exerciseId}`, {
        method: "DELETE",
      })

      if (!response.ok) throw new Error("Failed to delete exercise")

      setWeeks(weeks.map(week =>
        week.id === weekId
          ? {
              ...week,
              workouts: week.workouts.map(workout =>
                workout.id === workoutId
                  ? {
                      ...workout,
                      exercises: workout.exercises.filter(e => e.id !== exerciseId)
                    }
                  : workout
              ),
            }
          : week
      ))
    } catch (error) {
      console.error("Error deleting exercise:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleWorkoutTypeChange = async (weekId: string, workoutId: string, type: string) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/training-plans/workouts/${workoutId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ type }),
      })

      if (!response.ok) throw new Error("Failed to update workout type")

      setWeeks(weeks.map(week =>
        week.id === weekId
          ? {
              ...week,
              workouts: week.workouts.map(workout =>
                workout.id === workoutId
                  ? { ...workout, type }
                  : workout
              ),
            }
          : week
      ))
    } catch (error) {
      console.error("Error updating workout type:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* Step 1: Basic Plan Info */}
      {currentStep === 1 && (
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold">Plan Details</h2>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input id="title" name="title" required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea id="description" name="description" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="difficulty">Difficulty</Label>
              <Select name="difficulty" required>
                <SelectTrigger>
                  <SelectValue placeholder="Select difficulty" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="beginner">Beginner</SelectItem>
                  <SelectItem value="intermediate">Intermediate</SelectItem>
                  <SelectItem value="advanced">Advanced</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      )}

      {/* Step 2: Weeks and Workouts */}
      {currentStep === 2 && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold">Weeks and Workouts</h2>
            <Button onClick={addWeek} disabled={isLoading}>
              Add Week
            </Button>
          </div>

          <DndContext
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            collisionDetection={closestCenter}
          >
            <div className="space-y-6">
              {weeks.map((week) => (
                <Card key={week.id} className="p-6">


                  <div className="space-y-4">
                    {week.workouts.map((workout) => (
                      <DroppableWorkout
                        key={workout.id}
                        workout={workout}
                        onExerciseDelete={(exerciseId) => handleExerciseDelete(exerciseId)}
                        onTypeChange={(type) =>
                          handleWorkoutTypeChange(week.id, workout.id, type)
                        }
                      />
                    ))}
                  </div>
                </Card>
              ))}
            </div>

            <DragOverlay>
              {activeDragData && activeDragData.type === 'exercise' ? (
                <div className="opacity-50">
                  <DraggableExercise
                    exercise={activeDragData.exercise}
                    onDelete={() => {}}
                  />
                </div>
              ) : null}
            </DragOverlay>
          </DndContext>
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => setCurrentStep(currentStep - 1)}
          disabled={currentStep === 1}
        >
          Previous
        </Button>
        <Button
          onClick={() => setCurrentStep(currentStep + 1)}
          disabled={currentStep === 2}
        >
          Next
        </Button>
      </div>
    </div>
  )
}