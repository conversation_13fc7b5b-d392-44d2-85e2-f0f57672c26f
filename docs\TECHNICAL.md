# Technical Documentation

## Architecture Overview

The Clear Coach App is built using a modern, scalable architecture that separates concerns and follows best practices for web application development.

### Core Architecture

```
Frontend (Next.js) <-> API Routes <-> Database (PostgreSQL)
     ^                      ^
     |                      |
     v                      v
  Client-side           Server-side
  State Management      Business Logic
```

## Database Schema

### Key Entities

1. **User**
   - Base user information
   - Role-based access (<PERSON><PERSON>, Trainer, Client)
   - Authentication details

2. **TrainingProgram**
   - Program details
   - Type (1:1 Coaching, Subscription, Digital Product)
   - Progress tracking
   - Content management

3. **Session**
   - 1:1 coaching sessions
   - Scheduling
   - Progress tracking

4. **Content**
   - Training content
   - Nutrition guides
   - Educational materials

5. **Subscription**
   - Subscription details
   - Payment information
   - Access management

## API Routes

### Authentication
- `/api/auth/*` - NextAuth.js routes
- `/api/auth/session` - Session management
- `/api/auth/callback/*` - OAuth callbacks

### Training Programs
- `/api/programs` - Program management
- `/api/programs/[id]` - Individual program operations
- `/api/programs/[id]/content` - Program content management

### Subscriptions
- `/api/subscriptions` - Subscription management
- `/api/subscriptions/[id]` - Individual subscription operations
- `/api/subscriptions/webhook` - Stripe webhook handler

### Content
- `/api/content` - Content management
- `/api/content/[id]` - Individual content operations
- `/api/content/upload` - Media upload handling

## State Management

### Client-side State
- React Context for global state
- React Query for server state
- Local state with useState for component-specific data

### Server-side State
- Database for persistent storage
- Redis for caching (optional)
- Session management with NextAuth.js

## Authentication Flow

1. **Initial Authentication**
   ```typescript
   // Example from lib/auth.ts
   export const authOptions: NextAuthOptions = {
     providers: [
       CredentialsProvider({
         // Custom authentication logic
       })
     ],
     callbacks: {
       // Session and JWT handling
     }
   }
   ```

2. **Session Management**
   ```typescript
   // Example from components/auth/SessionProvider.tsx
   export function SessionProvider({ children }: { children: React.ReactNode }) {
     return (
       <SessionProvider>
         {children}
       </SessionProvider>
     )
   }
   ```

## Payment Integration

### Stripe Setup
1. **Subscription Management**
   ```typescript
   // Example from lib/stripe.ts
   export async function createSubscription(
     customerId: string,
     priceId: string
   ) {
     // Stripe subscription creation logic
   }
   ```

2. **Webhook Handling**
   ```typescript
   // Example from app/api/subscriptions/webhook/route.ts
   export async function POST(req: Request) {
     // Stripe webhook handling logic
   }
   ```

## Real-time Features

### WebSocket Implementation
1. **Connection Setup**
   ```typescript
   // Example from lib/websocket.ts
   export function setupWebSocket() {
     // WebSocket connection logic
   }
   ```

2. **Message Handling**
   ```typescript
   // Example from components/chat/ChatProvider.tsx
   export function ChatProvider({ children }: { children: React.ReactNode }) {
     // Real-time chat implementation
   }
   ```

## File Upload

### Cloudinary Integration
1. **Upload Configuration**
   ```typescript
   // Example from lib/cloudinary.ts
   export async function uploadFile(file: File) {
     // Cloudinary upload logic
   }
   ```

2. **Media Management**
   ```typescript
   // Example from components/upload/MediaUpload.tsx
   export function MediaUpload() {
     // File upload component
   }
   ```

## Error Handling

### Global Error Handling
```typescript
// Example from app/error.tsx
export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  // Error handling component
}
```

### API Error Handling
```typescript
// Example from lib/api.ts
export class APIError extends Error {
  constructor(
    public statusCode: number,
    message: string
  ) {
    super(message)
  }
}
```

## Performance Optimization

### Image Optimization
- Next.js Image component
- Cloudinary transformations
- Lazy loading

### Code Splitting
- Dynamic imports
- Route-based code splitting
- Component lazy loading

### Caching Strategy
- API response caching
- Static page generation
- Incremental static regeneration

## Testing

### Unit Tests
```typescript
// Example from __tests__/components/Button.test.tsx
describe('Button', () => {
  it('renders correctly', () => {
    // Test implementation
  })
})
```

### Integration Tests
```typescript
// Example from __tests__/api/auth.test.ts
describe('Authentication', () => {
  it('handles login correctly', () => {
    // Test implementation
  })
})
```

### E2E Tests
```typescript
// Example from cypress/integration/auth.spec.ts
describe('Authentication Flow', () => {
  it('completes login process', () => {
    // Test implementation
  })
})
```

## Deployment

### Production Build
```bash
npm run build
npm start
```

### Environment Setup
```bash
# Production environment variables
NODE_ENV=production
DATABASE_URL=production_database_url
# ... other production variables
```

### Monitoring
- Error tracking with Sentry
- Performance monitoring
- User analytics

## Security Best Practices

1. **Authentication**
   - JWT token management
   - Session security
   - Password hashing

2. **API Security**
   - Rate limiting
   - Input validation
   - CORS configuration

3. **Data Protection**
   - Encryption at rest
   - Secure communication
   - Access control

## Development Guidelines

### Code Style
- ESLint configuration
- Prettier formatting
- TypeScript strict mode

### Git Workflow
- Feature branches
- Pull request reviews
- Semantic versioning

### Documentation
- JSDoc comments
- API documentation
- Component documentation

## Troubleshooting

### Common Issues
1. **Database Connection**
   - Check connection string
   - Verify database status
   - Check Prisma migrations

2. **Authentication Problems**
   - Verify environment variables
   - Check session configuration
   - Debug OAuth flow

3. **Payment Issues**
   - Verify Stripe keys
   - Check webhook configuration
   - Debug subscription flow

### Debug Tools
- Chrome DevTools
- Network monitoring
- Database query logging 