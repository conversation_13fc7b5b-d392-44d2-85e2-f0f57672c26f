'use client';

import { useState, useRef, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Send, ArrowLeft, RefreshCw, UserPlus, MessageSquare } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { useToast } from '@/components/ui/use-toast';

// Message type
interface Message {
  id: string;
  content: string;
  senderId: string;
  receiverId: string;
  createdAt: string;
  sender?: {
    id: string;
    name: string;
    avatarUrl: string | null;
  };
  receiver?: {
    id: string;
    name: string;
    avatarUrl: string | null;
  };
}

// User type
interface User {
  id: string;
  name: string;
  avatarUrl: string | null;
}

// Conversation type
interface Conversation {
  id: string;
  user: User;
  lastMessage: Message | null;
}

export default function RealChatTestPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const { toast } = useToast();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [setupStatus, setSetupStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Load conversations when session is available
  useEffect(() => {
    if (session?.user) {
      fetchConversations();
    }
  }, [session]);

  // Load messages when a conversation is selected
  useEffect(() => {
    if (selectedConversation) {
      fetchMessages(selectedConversation.id);
    }
  }, [selectedConversation]);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchConversations = async () => {
    try {
      setIsLoading(true);

      // Try to fetch conversations from localStorage first
      const savedConversations = localStorage.getItem('real-chat-conversations');
      if (savedConversations) {
        const parsedConversations = JSON.parse(savedConversations);
        setConversations(parsedConversations);

        if (parsedConversations.length > 0) {
          setSelectedConversation(parsedConversations[0]);
        }

        setIsLoading(false);
        return;
      }

      // If no saved conversations, try to fetch from API
      const response = await fetch('/api/coaching/conversations');

      if (!response.ok) {
        // If API fails, create mock conversation
        createMockConversation();
        return;
      }

      const data = await response.json();
      setConversations(data);

      // Save to localStorage
      localStorage.setItem('real-chat-conversations', JSON.stringify(data));

      if (data.length > 0) {
        setSelectedConversation(data[0]);
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);
      // Create mock conversation on error
      createMockConversation();
    } finally {
      setIsLoading(false);
    }
  };

  const createMockConversation = () => {
    // Create a mock conversation based on the current user
    const isTrainer = session?.user?.role === 'trainer';

    const mockUser = {
      id: isTrainer ? 'client-1' : 'trainer-1',
      name: isTrainer ? 'Sarah Client' : 'John Trainer',
      avatarUrl: null,
    };

    const mockConversation = {
      id: 'conv-1',
      user: mockUser,
      lastMessage: null,
    };

    setConversations([mockConversation]);
    setSelectedConversation(mockConversation);

    // Save to localStorage
    localStorage.setItem('real-chat-conversations', JSON.stringify([mockConversation]));

    // Create mock messages
    const mockMessages = [
      {
        id: 'msg-1',
        content: 'Hello! Welcome to your coaching chat. How can I help you today?',
        senderId: isTrainer ? session?.user?.id! : mockUser.id,
        receiverId: isTrainer ? mockUser.id : session?.user?.id!,
        createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        sender: {
          id: isTrainer ? session?.user?.id! : mockUser.id,
          name: isTrainer ? session?.user?.name! : mockUser.name,
          avatarUrl: null,
        },
        receiver: {
          id: isTrainer ? mockUser.id : session?.user?.id!,
          name: isTrainer ? mockUser.name : session?.user?.name!,
          avatarUrl: null,
        },
      },
    ];

    setMessages(mockMessages);

    // Save to localStorage
    localStorage.setItem(`real-chat-messages-${mockConversation.id}`, JSON.stringify(mockMessages));
  };

  const fetchMessages = async (conversationId: string) => {
    try {
      setIsLoading(true);

      // Try to fetch messages from localStorage first
      const savedMessages = localStorage.getItem(`real-chat-messages-${conversationId}`);
      if (savedMessages) {
        setMessages(JSON.parse(savedMessages));
        setIsLoading(false);
        return;
      }

      // If no saved messages, try to fetch from API
      const response = await fetch(`/api/coaching/messages?conversationId=${conversationId}`);

      if (!response.ok) {
        // If API fails, create mock messages
        createMockMessages(conversationId);
        return;
      }

      const data = await response.json();
      setMessages(data);

      // Save to localStorage
      localStorage.setItem(`real-chat-messages-${conversationId}`, JSON.stringify(data));
    } catch (error) {
      console.error('Error fetching messages:', error);
      // Create mock messages on error
      createMockMessages(conversationId);
    } finally {
      setIsLoading(false);
    }
  };

  const createMockMessages = (conversationId: string) => {
    // Create mock messages based on the current user
    const isTrainer = session?.user?.role === 'trainer';
    const otherUser = selectedConversation?.user;

    if (!otherUser) return;

    const mockMessages = [
      {
        id: 'msg-1',
        content: 'Hello! Welcome to your coaching chat. How can I help you today?',
        senderId: isTrainer ? session?.user?.id! : otherUser.id,
        receiverId: isTrainer ? otherUser.id : session?.user?.id!,
        createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        sender: {
          id: isTrainer ? session?.user?.id! : otherUser.id,
          name: isTrainer ? session?.user?.name! : otherUser.name,
          avatarUrl: null,
        },
        receiver: {
          id: isTrainer ? otherUser.id : session?.user?.id!,
          name: isTrainer ? otherUser.name : session?.user?.name!,
          avatarUrl: null,
        },
      },
    ];

    setMessages(mockMessages);

    // Save to localStorage
    localStorage.setItem(`real-chat-messages-${conversationId}`, JSON.stringify(mockMessages));
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim() || !selectedConversation || !session?.user?.id) return;

    setIsSending(true);

    // Create a new message
    const tempId = `temp-${Date.now()}`;
    const newMsg: Message = {
      id: tempId,
      content: newMessage,
      senderId: session.user.id,
      receiverId: selectedConversation.user.id,
      createdAt: new Date().toISOString(),
      sender: {
        id: session.user.id,
        name: session.user.name || '',
        avatarUrl: null,
      },
      receiver: {
        id: selectedConversation.user.id,
        name: selectedConversation.user.name,
        avatarUrl: selectedConversation.user.avatarUrl,
      },
    };

    // Add to messages immediately for UI responsiveness
    setMessages((prev) => [...prev, newMsg]);
    setNewMessage('');

    try {
      // Try to send message via API
      const response = await fetch('/api/coaching/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newMsg.content,
          conversationId: selectedConversation.id,
        }),
      });

      if (!response.ok) {
        // If API fails, just keep the local message
        console.log('API failed, keeping local message');
      } else {
        // If API succeeds, replace temp message with real one
        const data = await response.json();
        setMessages((prev) =>
          prev.map((msg) => (msg.id === tempId ? data : msg))
        );
      }

      // Update localStorage
      const updatedMessages = [...messages, newMsg];
      localStorage.setItem(`real-chat-messages-${selectedConversation.id}`, JSON.stringify(updatedMessages));

      // If this is a trainer, simulate a response from the client
      if (session.user.role === 'trainer') {
        setTimeout(() => {
          const clientResponse: Message = {
            id: `auto-${Date.now()}`,
            content: getClientResponse(newMsg.content),
            senderId: selectedConversation.user.id,
            receiverId: session.user.id!,
            createdAt: new Date().toISOString(),
            sender: {
              id: selectedConversation.user.id,
              name: selectedConversation.user.name,
              avatarUrl: selectedConversation.user.avatarUrl,
            },
            receiver: {
              id: session.user.id!,
              name: session.user.name || '',
              avatarUrl: null,
            },
          };

          setMessages((prev) => [...prev, clientResponse]);

          // Update localStorage
          const updatedWithResponse = [...updatedMessages, clientResponse];
          localStorage.setItem(`real-chat-messages-${selectedConversation.id}`, JSON.stringify(updatedWithResponse));
        }, 2000);
      }

    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to send message. Please try again.',
      });
    } finally {
      setIsSending(false);
    }
  };

  const getClientResponse = (message: string): string => {
    // Simple response logic for client responses
    if (message.toLowerCase().includes('hello') || message.toLowerCase().includes('hi')) {
      return 'Hi coach! Thanks for checking in. How are you?';
    }

    if (message.toLowerCase().includes('workout') || message.toLowerCase().includes('exercise')) {
      return 'I completed all the workouts you assigned this week. The leg day was especially challenging!';
    }

    if (message.toLowerCase().includes('diet') || message.toLowerCase().includes('nutrition') || message.toLowerCase().includes('food')) {
      return 'I\'ve been sticking to the meal plan, but I\'m struggling with the protein intake. Any suggestions for vegetarian options?';
    }

    if (message.toLowerCase().includes('progress') || message.toLowerCase().includes('results')) {
      return 'I\'m seeing some progress in my strength, but not much change in weight yet. Is that normal at this stage?';
    }

    if (message.toLowerCase().includes('schedule') || message.toLowerCase().includes('appointment') || message.toLowerCase().includes('meeting')) {
      return 'Tuesday afternoon works for me. Should I book it through the calendar or will you send me a link?';
    }

    if (message.toLowerCase().includes('goal') || message.toLowerCase().includes('target')) {
      return 'I\'d like to focus on improving my endurance first. The 5K run is coming up in two months.';
    }

    // Default response
    return "Thanks for the advice! I'll definitely incorporate that into my routine.";
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const clearChat = () => {
    if (confirm('Are you sure you want to clear the chat history? This cannot be undone.')) {
      if (selectedConversation) {
        localStorage.removeItem(`real-chat-messages-${selectedConversation.id}`);
      }
      localStorage.removeItem('real-chat-conversations');
      fetchConversations();
    }
  };

  const setupCoachingRelationship = async () => {
    try {
      setSetupStatus('loading');

      // Run the setup script via API
      const response = await fetch('/api/direct-setup');
      const result = await response.json();

      if (!response.ok || !result.success) {
        toast({
          variant: 'destructive',
          title: 'Setup Failed',
          description: result.error || 'Failed to set up coaching relationship',
        });
        setSetupStatus('error');
        return;
      }

      toast({
        title: 'Setup Complete',
        description: 'Coaching relationship has been set up successfully',
      });

      setSetupStatus('success');

      // Clear localStorage to fetch fresh data
      localStorage.removeItem('real-chat-conversations');
      if (selectedConversation) {
        localStorage.removeItem(`real-chat-messages-${selectedConversation.id}`);
      }

      // Fetch conversations again
      fetchConversations();

    } catch (error) {
      console.error('Error setting up coaching relationship:', error);
      toast({
        variant: 'destructive',
        title: 'Setup Failed',
        description: 'An error occurred while setting up the coaching relationship',
      });
      setSetupStatus('error');
    }
  };

  if (!session?.user) {
    return (
      <div className="container py-6">
        <Card>
          <CardHeader>
            <CardTitle>Not Authenticated</CardTitle>
            <CardDescription>Please sign in to access the coaching chat.</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <Link href="/api/auth/dev-login?role=client">
              <Button>
                Login as Client
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-6 space-y-6">
      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        <div className="flex gap-2">
          <Button variant="outline" onClick={clearChat}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Clear Chat
          </Button>

          <Button
            variant="outline"
            onClick={setupCoachingRelationship}
            disabled={setupStatus === 'loading'}
          >
            {setupStatus === 'loading' ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Setting Up...
              </>
            ) : (
              <>
                <UserPlus className="mr-2 h-4 w-4" />
                Setup Coaching
              </>
            )}
          </Button>

          <Link href="/mock-chat">
            <Button variant="outline">
              <MessageSquare className="mr-2 h-4 w-4" />
              Switch to Mock
            </Button>
          </Link>
        </div>
      </div>

      <h1 className="text-2xl font-bold">Real Chat Test</h1>
      <p className="text-muted-foreground">
        This page attempts to use real data from the database, but falls back to localStorage if the API fails.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Conversations Sidebar */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Conversations
            </CardTitle>
            <CardDescription>Your coaching conversations</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {isLoading ? (
              <div className="p-4 text-center">
                <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto"></div>
                <p className="mt-2 text-sm text-muted-foreground">Loading conversations...</p>
              </div>
            ) : conversations.length === 0 ? (
              <div className="p-4 text-center">
                <p className="text-muted-foreground">No conversations found.</p>
                <Button
                  className="mt-4"
                  size="sm"
                  onClick={setupCoachingRelationship}
                  disabled={setupStatus === 'loading'}
                >
                  {setupStatus === 'loading' ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Setting Up...
                    </>
                  ) : (
                    <>
                      <UserPlus className="mr-2 h-4 w-4" />
                      Setup Coaching
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <div className="divide-y">
                {conversations.map((conversation) => (
                  <div
                    key={conversation.id}
                    className={`flex items-center gap-3 p-4 cursor-pointer hover:bg-muted transition-colors ${
                      selectedConversation?.id === conversation.id ? 'bg-muted' : ''
                    }`}
                    onClick={() => setSelectedConversation(conversation)}
                  >
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={conversation.user.avatarUrl || undefined} alt={conversation.user.name} />
                      <AvatarFallback>{getInitials(conversation.user.name)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{conversation.user.name}</p>
                      <p className="text-xs text-muted-foreground truncate">
                        {conversation.lastMessage
                          ? conversation.lastMessage.content
                          : 'No messages yet'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Chat Area */}
        <Card className="md:col-span-3 flex flex-col h-[600px]">
          {selectedConversation ? (
            <>
              <CardHeader className="border-b pb-3">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage
                      src={selectedConversation.user.avatarUrl || undefined}
                      alt={selectedConversation.user.name}
                    />
                    <AvatarFallback>{getInitials(selectedConversation.user.name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">{selectedConversation.user.name}</CardTitle>
                    <p className="text-sm text-muted-foreground">1:1 Coaching Chat</p>
                  </div>
                </div>
              </CardHeader>
              <ScrollArea className="flex-1 p-4">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                    <span className="ml-2">Loading messages...</span>
                  </div>
                ) : messages.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-center">
                    <div>
                      <MessageSquare className="h-12 w-12 text-muted-foreground/50 mx-auto mb-4" />
                      <p className="text-muted-foreground">No messages yet. Start the conversation!</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.map((message) => {
                      const isCurrentUser = message.senderId === session?.user?.id;

                      return (
                        <div key={message.id} className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
                          <div className="flex items-start gap-2 max-w-[80%]">
                            {!isCurrentUser && (
                              <Avatar className="h-8 w-8">
                                <AvatarImage
                                  src={message.sender?.avatarUrl || undefined}
                                  alt={message.sender?.name || 'User'}
                                />
                                <AvatarFallback>
                                  {getInitials(message.sender?.name || 'User')}
                                </AvatarFallback>
                              </Avatar>
                            )}
                            <div>
                              <div className={`p-3 rounded-lg ${isCurrentUser ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}>
                                <p className="text-sm">{message.content}</p>
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">{formatTime(message.createdAt)}</p>
                            </div>
                            {isCurrentUser && (
                              <Avatar className="h-8 w-8">
                                <AvatarImage
                                  src={session.user.image || undefined}
                                  alt={session.user.name || 'You'}
                                />
                                <AvatarFallback>
                                  {getInitials(session.user.name || 'You')}
                                </AvatarFallback>
                              </Avatar>
                            )}
                          </div>
                        </div>
                      );
                    })}
                    {isSending && (
                      <div className="flex justify-end">
                        <div className="flex items-start gap-2">
                          <div>
                            <div className="p-3 rounded-lg bg-primary/80 text-primary-foreground">
                              <div className="flex items-center space-x-2">
                                <div className="h-2 w-2 bg-white rounded-full animate-pulse"></div>
                                <div className="h-2 w-2 bg-white rounded-full animate-pulse delay-75"></div>
                                <div className="h-2 w-2 bg-white rounded-full animate-pulse delay-150"></div>
                              </div>
                            </div>
                          </div>
                          <Avatar className="h-8 w-8">
                            <AvatarImage
                              src={session.user.image || undefined}
                              alt={session.user.name || 'You'}
                            />
                            <AvatarFallback>
                              {getInitials(session.user.name || 'You')}
                            </AvatarFallback>
                          </Avatar>
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                )}
              </ScrollArea>
              <CardFooter className="border-t p-3">
                <form onSubmit={handleSendMessage} className="flex w-full gap-2">
                  <Input
                    ref={inputRef}
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type your message..."
                    className="flex-1"
                    disabled={isLoading || isSending}
                  />
                  <Button
                    type="submit"
                    disabled={!newMessage.trim() || isLoading || isSending}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </form>
              </CardFooter>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-full p-6 text-center">
              <MessageSquare className="h-16 w-16 text-muted-foreground/50 mb-4" />
              <CardTitle className="text-xl mb-2">No Conversation Selected</CardTitle>
              <CardDescription className="mb-6">
                {conversations.length > 0
                  ? 'Select a conversation from the sidebar to start chatting'
                  : 'You don\'t have any active coaching relationships'}
              </CardDescription>
              {conversations.length === 0 && (
                <Button
                  onClick={setupCoachingRelationship}
                  disabled={setupStatus === 'loading'}
                >
                  {setupStatus === 'loading' ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Setting Up...
                    </>
                  ) : (
                    <>
                      <UserPlus className="mr-2 h-4 w-4" />
                      Setup Coaching Relationship
                    </>
                  )}
                </Button>
              )}
            </div>
          )}
        </Card>
      </div>

      <div className="bg-muted p-4 rounded-lg">
        <h2 className="font-medium mb-2">Test Prompts</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          <Button
            variant="outline"
            onClick={() => {
              setNewMessage("Hi, how are you today?");
              inputRef.current?.focus();
            }}
            disabled={isLoading || isSending || !selectedConversation}
          >
            Greeting
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              setNewMessage("How's your workout routine going?");
              inputRef.current?.focus();
            }}
            disabled={isLoading || isSending || !selectedConversation}
          >
            Workout Check-in
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              setNewMessage("Let's discuss your nutrition plan for next week");
              inputRef.current?.focus();
            }}
            disabled={isLoading || isSending || !selectedConversation}
          >
            Nutrition Plan
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              setNewMessage("I'm seeing great progress in your latest metrics!");
              inputRef.current?.focus();
            }}
            disabled={isLoading || isSending || !selectedConversation}
          >
            Progress Update
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              setNewMessage("Would you like to schedule a 1:1 session next week?");
              inputRef.current?.focus();
            }}
            disabled={isLoading || isSending || !selectedConversation}
          >
            Schedule Session
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              setNewMessage("Let's set some new goals for the upcoming month");
              inputRef.current?.focus();
            }}
            disabled={isLoading || isSending || !selectedConversation}
          >
            Goal Setting
          </Button>
        </div>
      </div>
    </div>
  );
}
