import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { prisma } from '../../lib/prisma'
import bcrypt from 'bcryptjs'
import { v4 as uuidv4 } from 'uuid'

/**
 * This test file tests the trainer workflow using the services
 * It focuses on the basic functionality of creating users and profiles
 */
describe('Trainer Service Workflow', () => {
  // Test data
  let trainerUser: any
  let trainerProfile: any
  let clientUser: any
  let clientProfile: any
  
  // Setup: Create test users and profiles
  beforeAll(async () => {
    // Create trainer user
    const trainerPassword = await bcrypt.hash('password123', 10)
    trainerUser = await prisma.user.create({
      data: {
        id: uuidv4(),
        name: 'Test Trainer',
        email: `test-trainer-${Date.now()}@example.com`,
        password: trainerPassword,
        role: 'trainer',
        emailVerified: new Date()
      }
    })
    
    // Create trainer profile
    trainerProfile = await prisma.trainerProfile.create({
      data: {
        userId: trainerUser.id
      }
    })
    
    // Create client user
    const clientPassword = await bcrypt.hash('password123', 10)
    clientUser = await prisma.user.create({
      data: {
        id: uuidv4(),
        name: 'Test Client',
        email: `test-client-${Date.now()}@example.com`,
        password: clientPassword,
        role: 'client',
        emailVerified: new Date()
      }
    })
    
    // Create client profile
    clientProfile = await prisma.clientProfile.create({
      data: {
        userId: clientUser.id,
        assignedTrainerId: trainerProfile.id
      }
    })
  })
  
  // Cleanup: Remove test data
  afterAll(async () => {
    // Delete client profile
    await prisma.clientProfile.delete({
      where: { id: clientProfile.id }
    })
    
    // Delete client user
    await prisma.user.delete({
      where: { id: clientUser.id }
    })
    
    // Delete trainer profile
    await prisma.trainerProfile.delete({
      where: { id: trainerProfile.id }
    })
    
    // Delete trainer user
    await prisma.user.delete({
      where: { id: trainerUser.id }
    })
    
    // Disconnect from the database
    await prisma.$disconnect()
  })
  
  // Test: Client Management
  describe('Client Management', () => {
    it('should retrieve the trainer\'s clients', async () => {
      const clients = await prisma.clientProfile.findMany({
        where: { assignedTrainerId: trainerProfile.id },
        include: { user: true }
      })
      
      expect(clients).toBeDefined()
      expect(clients.length).toBeGreaterThan(0)
      expect(clients.some(client => client.userId === clientUser.id)).toBe(true)
    })
    
    it('should retrieve a specific client\'s details', async () => {
      const client = await prisma.clientProfile.findUnique({
        where: { id: clientProfile.id },
        include: { user: true }
      })
      
      expect(client).toBeDefined()
      expect(client?.userId).toBe(clientUser.id)
      expect(client?.assignedTrainerId).toBe(trainerProfile.id)
    })
  })
})
