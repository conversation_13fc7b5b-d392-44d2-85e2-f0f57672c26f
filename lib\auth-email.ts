import { randomBytes } from "crypto"
import { Resend } from "resend"
import { hash } from "bcryptjs"
import { prisma } from "@/lib/prisma"

const resend = new Resend(process.env.RESEND_API_KEY)
const FROM_EMAIL = process.env.EMAIL_FROM || "<EMAIL>"
const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"

/**
 * Generates a verification token, saves its hashed version, and sends the verification email.
 * @param email - User's email address.
 */
export async function generateAndSendVerificationEmail(email: string) {
  // 1. Generate a secure token
  const token = randomBytes(32).toString("hex")
  const hashedToken = await hash(token, 10)
  const expires = new Date(Date.now() + 3600 * 1000) // Token expires in 1 hour

  // 2. Store hashed token in the database
  // Delete any existing token for this identifier first
  await prisma.verificationToken.deleteMany({
    where: { identifier: email },
  })
  await prisma.verificationToken.create({
    data: {
      identifier: email,
      token: hashedToken,
      expires,
    },
  })

  // 3. Send the email with the original token
  const verificationLink = `${BASE_URL}/api/auth/verify-email?token=${token}`
  
  try {
    await resend.emails.send({
      from: FROM_EMAIL,
      to: email,
      subject: "Verify your email address for ClearCoach",
      html: `
        <h1>Welcome to ClearCoach!</h1>
        <p>Please click the link below to verify your email address and activate your account:</p>
        <p><a href="${verificationLink}" target="_blank">Verify Email Address</a></p>
        <p>If you did not request this email, please ignore it.</p>
        <p>This link will expire in 1 hour.</p>
        <hr>
        <p>Link: ${verificationLink}</p> 
      `,
    })
    console.log(`Verification email sent successfully to ${email}`)
  } catch (error) {
    console.error(`Failed to send verification email to ${email}:`, error)
    // If email fails, we should ideally roll back the token creation or have a retry mechanism.
    // For now, rethrow to indicate a problem during registration.
    throw new Error("Failed to send verification email. Please try registering again later or contact support.")
  }
}
