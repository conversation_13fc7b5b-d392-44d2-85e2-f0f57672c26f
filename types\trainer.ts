// types/trainer.ts

export interface TrainerTheme {
  primaryColor: string
  secondaryColor: string
  logoUrl: string | null
  bannerUrl: string | null
  fontFamily: string

  // Enhanced header options
  videoBannerUrl?: string | null
  overlayOpacity?: number
  textColor?: string
  buttonStyle?: 'rounded' | 'square' | 'pill'
  buttonText?: string
  showCredentials?: boolean
  credentials?: string[]
  testimonialHighlight?: {
    quote: string
    author: string
  } | null

  // Section styling
  sectionBackground?: string
  sectionTextColor?: string

  // Footer options
  footerStyle?: 'minimal' | 'standard' | 'expanded'
  footerBackground?: string
  footerTextColor?: string
  showNewsletter?: boolean
  showMap?: boolean
  mapLocation?: {
    address: string
    embedUrl: string
  }
  contactInfo?: {
    email?: string
    phone?: string
    address?: string
  }
  footerLinks?: Array<{
    title: string
    url: string
  }>
  copyrightText?: string
}

export interface SubscriptionTier {
  id: string
  name: string
  description: string
  price: number
  features: string[]
}

export interface DigitalProduct {
  id: string
  title: string
  name?: string // For backward compatibility
  description: string
  price: number
  thumbnailUrl?: string
  imageUrl?: string
}

export interface CoachingService {
  id: string
  name: string
  description: string
  price: number
  duration: string
  features: string[]
  spotsAvailable?: number
  availableSlots?: Array<{
    id: string
    date: string
    startTime: string
    endTime: string
    isBooked: boolean
  }>
}

export interface Trainer {
  id: string
  name: string | null
  email: string | null
  bio: string | null
  avatarUrl: string | null
  socialLinks: any // Consider Record<string, string | null> | null
  themeSettings?: any // Consider Partial<TrainerTheme> | null
}

// Type for the selected item in purchase flow
export interface SelectedItem {
  id: string;
  type: 'subscription' | 'product' | 'coaching';
  name: string;
  price: number;
}