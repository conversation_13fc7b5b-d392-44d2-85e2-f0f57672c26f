# Application
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/clear_coach

# Authentication
NEXTAUTH_SECRET=generate-a-secure-random-secret-here
NEXTAUTH_URL=http://localhost:3000

# Stripe
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# CSRF
CSRF_SECRET=generate-a-complex-random-string-here

# Cloudinary (for file uploads)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Email (for notifications)
EMAIL_SERVER_HOST=smtp.example.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your_password
EMAIL_FROM=<EMAIL>

# Feature flags
ENABLE_STRIPE=true
ENABLE_CLOUDINARY=true
ENABLE_WEBSOCKETS=false

# Optional services - uncomment as needed
# SENTRY_DSN=your_sentry_dsn
# NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
# OPENAI_API_KEY=your_openai_api_key

# Rate limiting
UPSTASH_REDIS_REST_URL=your_upstash_redis_url
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token