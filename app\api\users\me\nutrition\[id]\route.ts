import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function PUT(
  request: Request,
  context: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Safely access params
    const logId = context?.params?.id;
    if (!logId) {
        return new NextResponse("Nutrition log ID missing in URL", { status: 400 });
    }

    const body = await request.json()
    const { date, mealType, name, calories, protein, carbs, fat, notes } = body

    // Check if the nutrition log exists and belongs to the user
    const log = await prisma.nutritionLog.findUnique({
      where: {
        id: logId,
      },
      select: { clientId: true }
    })

    if (!log) {
      return new NextResponse("Nutrition log not found", { status: 404 })
    }

    // Check ownership
    if (log.clientId !== session.user.id) {
      return new NextResponse("Forbidden: You do not own this nutrition log", { status: 403 })
    }

    // Update the nutrition log
    const updatedLog = await prisma.nutritionLog.update({
      where: {
        id: logId,
      },
      data: {
        date: date ? new Date(date) : undefined,
        mealType: mealType || undefined,
        name: name || undefined,
        calories: calories !== undefined ? parseInt(calories.toString()) : undefined,
        protein: protein !== undefined ? parseFloat(protein.toString()) : undefined,
        carbs: carbs !== undefined ? parseFloat(carbs.toString()) : undefined,
        fat: fat !== undefined ? parseFloat(fat.toString()) : undefined,
        notes: notes !== undefined ? notes : undefined,
      },
    })

    // Return the full list of nutrition logs after updating
    const nutritionLogs = await prisma.nutritionLog.findMany({
      where: {
        clientId: session.user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
    })
    
    return NextResponse.json(nutritionLogs)
  } catch (error) {
    console.error("[NUTRITION_UPDATE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function DELETE(
  request: Request,
  context: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Safely access params
    const logId = context?.params?.id;
    if (!logId) {
        return new NextResponse("Nutrition log ID missing in URL", { status: 400 });
    }

    // Check if the nutrition log exists and belongs to the user
    const log = await prisma.nutritionLog.findUnique({
      where: {
        id: logId,
      },
      select: { clientId: true }
    })

    if (!log) {
      // Idempotent: Already deleted or never existed
      return new NextResponse(null, { status: 204 });
    }

    // Check ownership
    if (log.clientId !== session.user.id) {
      return new NextResponse("Forbidden: You do not own this nutrition log", { status: 403 })
    }

    // Delete the nutrition log
    await prisma.nutritionLog.delete({
      where: {
        id: logId,
      },
    })

    // Return the full list of nutrition logs after deleting
    const nutritionLogs = await prisma.nutritionLog.findMany({
      where: {
        clientId: session.user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
    })
    
    return NextResponse.json(nutritionLogs)
  } catch (error) {
    console.error("[NUTRITION_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
