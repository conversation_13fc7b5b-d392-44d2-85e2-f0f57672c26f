import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { TrainerProfileClient } from "@/components/profile/trainer-profile-client"
import { prisma } from "@/lib/prisma"
import { getMonthlyRevenue as getMonthlyRevenueAction } from "@/app/actions/coaching"
import {
  Users,
  Dumbbell,
  Shield,
  Calendar,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Edit,
  ExternalLink,
  MessageSquare,
  InfoIcon
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertTitle } from "@/components/ui/alert"
import { Prisma } from "@prisma/client"

// Function to get trainer's subscription tiers
async function getTrainerTiers(trainerId: string) {
  // Subscription tiers not implemented yet
  return []
}

// Get trainer's training plans
async function getTrainerPlans(trainerId: string) {
  const plans = await prisma.trainingPlanTemplate.findMany({
    where: {
      trainerId: trainerId,
      type: "template"
    },
    orderBy: {
      createdAt: "desc"
    }
  })
  return plans
}

// Get trainer's clients with their relationships
async function getTrainerClientsWithRelationships(trainerId: string) {
  const relationships = await prisma.coachingRelationship.findMany({
    where: {
      trainerId: trainerId,
    },
    select: {
      id: true,
      status: true,
      startDate: true,
      endDate: true,
      monthlyFee: true,
      client: {
        select: {
          id: true,
          name: true,
          email: true,
          avatarUrl: true,
          role: true
        }
      }
    },
    orderBy: {
      startDate: 'desc'
    }
  })

  return relationships
}

// Function to get monthly revenue
async function getMonthlyRevenue(month: number, year: number) {
  // Implementation of getMonthlyRevenue function
  // This is a placeholder and should be replaced with the actual implementation
  return { revenue: 0 } // Placeholder return, actual implementation needed
}

export default async function TrainerProfilePage() {
  const session = await getServerSession(authOptions)
  if (!session?.user?.email) {
    redirect('/login')
  }

  const trainer = await prisma.user.findUnique({
    where: { email: session.user.email },
    include: {
      _count: {
        select: {
          trainingPlanTemplates: true,
          dietPlans: true
        }
      }
    }
  })

  if (!trainer) {
    redirect('/login')
  }

  const plans = await getTrainerPlans(trainer.id)
  const relationships = await getTrainerClientsWithRelationships(trainer.id)

  // Parse trainer preferences to get bio
  const preferences = trainer.preferences as { bio?: string } | null
  const bio = preferences?.bio || null

  // Get current date
  const now = new Date()
  const currentMonth = now.getMonth() + 1 // getMonth() returns 0-11
  const currentYear = now.getFullYear()

  // Calculate revenue for the last 6 months
  const revenueData = []
  
  // Debug: Log all relationships and their fees
  console.log("All relationships:", relationships.map(rel => ({
    status: rel.status,
    fee: rel.monthlyFee,
    startDate: rel.startDate,
    endDate: rel.endDate
  })))

  for (let i = 5; i >= 0; i--) {
    let month = currentMonth - i
    let year = currentYear
    if (month <= 0) {
      month += 12
      year -= 1
    }

    // For each month, find relationships that were active during that month
    const activeRelationships = relationships.filter(rel => {
      const startDate = rel.startDate
      const endDate = rel.endDate
      const startMonth = startDate.getMonth() + 1
      const startYear = startDate.getFullYear()

      // Check if this relationship was active during this month
      const targetDate = new Date(year, month - 1)
      const isCurrentMonth = i === 0
      const isFutureMonth = targetDate > new Date()

      // For current month, only count active relationships
      if (isCurrentMonth) {
        return rel.status === 'active'
      }

      // For future months
      if (isFutureMonth) {
        // Only count active relationships that start in or before that month
        if (rel.status !== 'active') return false
        return startYear < year || (startYear === year && startMonth <= month)
      }

      // For past months
      // Check if relationship was active during this month
      const wasActive = (
        // Started before or during this month
        (startYear < year || (startYear === year && startMonth <= month)) &&
        // And either has no end date or ended after this month
        (!endDate || (
          endDate.getFullYear() > year || 
          (endDate.getFullYear() === year && (endDate.getMonth() + 1) >= month)
        ))
      )

      return wasActive
    })

    // Debug: Log filtered relationships for this month
    console.log(`${new Date(year, month - 1).toLocaleString('default', { month: 'short' })} ${year} active relationships:`, 
      activeRelationships.map(rel => ({
        status: rel.status,
        fee: rel.monthlyFee,
        startDate: rel.startDate,
        endDate: rel.endDate
      }))
    )

    const monthlyRevenue = activeRelationships.reduce((total, rel) => {
      return total + Number(rel.monthlyFee || 0)
    }, 0)

    revenueData.push({
      month: new Date(year, month - 1).toLocaleString('default', { month: 'short' }),
      revenue: monthlyRevenue
    })
  }

  // Calculate current and previous month revenue for stats
  const currentMonthRevenue = revenueData[revenueData.length - 1].revenue
  const previousMonthRevenue = revenueData[revenueData.length - 2].revenue
  const revenuePercentChange = previousMonthRevenue === 0 
    ? currentMonthRevenue > 0 ? 100 : 0 
    : Number((((currentMonthRevenue - previousMonthRevenue) / previousMonthRevenue) * 100).toFixed(2))

  // Get current active clients (for stats)
  const activeClients = relationships.filter(rel => rel.status === 'active')

  // Calculate previous month and year for last month's stats
  let previousMonth = currentMonth - 1
  let previousYear = currentYear
  if (previousMonth <= 0) {
    previousMonth += 12
    previousYear -= 1
  }

  // Get last month's active clients count - based on dates, not status
  const lastMonthActiveClients = relationships.filter(rel => {
    const startDate = rel.startDate
    const endDate = rel.endDate
    const startMonth = startDate.getMonth() + 1
    const startYear = startDate.getFullYear()
    
    return (
      // Started before or during last month
      (startYear < previousYear || (startYear === previousYear && startMonth <= previousMonth)) &&
      // And either has no end date or ended in/after last month
      (!endDate || (
        endDate.getFullYear() > previousYear || 
        (endDate.getFullYear() === previousYear && (endDate.getMonth() + 1) >= previousMonth)
      ))
    )
  })

  // Prepare data for the client component
  const trainerData = {
    id: trainer.id,
    name: trainer.name || session.user.name || "Trainer",
    email: trainer.email,
    bio: bio,
    avatarUrl: trainer.avatarUrl || null,
    role: trainer.role,
    createdAt: trainer.createdAt.toISOString(),
    updatedAt: trainer.updatedAt.toISOString(),
    stats: {
      trainingPlans: trainer._count.trainingPlanTemplates,
      dietPlans: trainer._count.dietPlans,
      clients: activeClients.length,
      revenue: {
        current: currentMonthRevenue,
        previous: previousMonthRevenue,
        percentChange: revenuePercentChange
      }
    },
    plans: plans.map(plan => ({
      id: plan.id,
      title: plan.title,
      description: plan.description || "",
      difficulty: plan.difficulty || "Intermediate",
      type: plan.type || "Strength"
    })),
    clients: activeClients.map(rel => ({
      id: rel.client.id,
      name: rel.client.name || "No name",
      email: rel.client.email,
      avatarUrl: rel.client.avatarUrl,
      startDate: rel.startDate.toISOString(),
      monthlyFee: Number(rel.monthlyFee?.toString() || 0)
    }))
  }

  // Prepare mock data for charts
  const mockData = {
    socialLinks: {
      instagram: "",
      twitter: "",
      youtube: "",
      website: ""
    },
    revenueData: revenueData,
    stats: {
      activeClients: activeClients.length,
      totalClients: relationships.length,
      clientsLastMonth: lastMonthActiveClients.length,
      revenue: {
        current: currentMonthRevenue,
        previous: previousMonthRevenue,
        percentChange: revenuePercentChange
      },
      messages: 18,
      sessions: 5
    },
    clientsData: [],
    revenueBreakdown: [
      { name: "Coaching Fees", value: 100 },
      { name: "Products", value: 0 }
    ]
  }

  return (
    <div className="container mx-auto py-6">
      <TrainerProfileClient trainer={trainerData} mockData={mockData} />
    </div>
  )
}
