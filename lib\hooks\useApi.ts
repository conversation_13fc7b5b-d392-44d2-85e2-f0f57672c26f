import { useState, useCallback } from 'react';

export type ApiError = {
  message: string;
  status?: number;
  errors?: Record<string, string[]>;
};

export type ApiResponse<T> = {
  data: T | null;
  error: ApiError | null;
  isLoading: boolean;
};

export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export function useApi<T>() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<ApiError | null>(null);
  const [data, setData] = useState<T | null>(null);

  const request = useCallback(
    async (
      url: string,
      method: ApiMethod = 'GET',
      body?: unknown,
      headers?: HeadersInit
    ): Promise<ApiResponse<T>> => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          ...(body ? { body: JSON.stringify(body) } : {}),
        });

        const responseData = await response.json().catch(() => ({}));

        if (!response.ok) {
          const errorData: ApiError = {
            message: responseData?.message || 'An error occurred',
            status: response.status,
            errors: responseData?.errors,
          };
          
          setError(errorData);
          setIsLoading(false);
          
          return { data: null, error: errorData, isLoading: false };
        }

        setData(responseData);
        setIsLoading(false);
        
        return { data: responseData, error: null, isLoading: false };
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
        const errorData: ApiError = {
          message: errorMessage,
        };
        
        setError(errorData);
        setIsLoading(false);
        
        return { data: null, error: errorData, isLoading: false };
      }
    },
    []
  );

  return {
    data,
    error,
    isLoading,
    request,
  };
} 