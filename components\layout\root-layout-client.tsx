'use client'

import { Toaster } from "sonner"
import { ErrorBoundary } from "@/components/monitoring/error-boundary"
import { NextAuthProvider } from "@/components/providers/next-auth-provider"
import { ThemeProvider } from "@/components/providers/theme-provider"

import { RoleSwitcher } from "@/components/dev/role-switcher"
import { SocketProvider } from "@/components/socket-provider"

export function RootLayoutClient({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ErrorBoundary>
      <NextAuthProvider>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <SocketProvider>
            <div className="relative min-h-screen">
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10" />

              {/* Main Content */}
              <div className="relative">
                {children}
              </div>

              {/* Toast Notifications */}
              <Toaster
                position="top-right"
                toastOptions={{
                  className: 'glass',
                  duration: 1500,
                  style: {
                    background: 'rgba(255, 255, 255, 0.8)',
                    backdropFilter: 'blur(8px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    zIndex: 1000,
                  },
                }}
              />

              {/* Development Role Switcher */}
              <RoleSwitcher />
            </div>
          </SocketProvider>
        </ThemeProvider>
      </NextAuthProvider>
    </ErrorBoundary>
  )
}