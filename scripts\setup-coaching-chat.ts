import { prisma } from '../lib/prisma';

async function main() {
  try {
    console.log('Setting up coaching relationship and chat for testing...');

    // 1. Find a trainer and a client user
    const trainer = await prisma.user.findFirst({
      where: {
        role: 'trainer',
      },
    });

    const client = await prisma.user.findFirst({
      where: {
        role: 'client',
      },
    });

    if (!trainer || !client) {
      console.error('Could not find a trainer and client user. Please create them first.');
      return;
    }

    console.log(`Found trainer: ${trainer.name} (${trainer.id})`);
    console.log(`Found client: ${client.name} (${client.id})`);

    // 2. Create or find a coaching relationship
    let coachingRelationship = await prisma.coachingRelationship.findFirst({
      where: {
        trainerId: trainer.id,
        clientId: client.id,
      },
    });

    if (!coachingRelationship) {
      console.log('Creating new coaching relationship...');
      coachingRelationship = await prisma.coachingRelationship.create({
        data: {
          trainerId: trainer.id,
          clientId: client.id,
          status: 'active',
          plan: 'Premium Coaching',
          notes: 'Test coaching relationship for chat testing',
        },
      });
    } else {
      console.log('Found existing coaching relationship:', coachingRelationship.id);
    }

    // 3. Create or find a conversation
    let conversation = await prisma.conversation.findFirst({
      where: {
        OR: [
          { user1Id: trainer.id, user2Id: client.id },
          { user1Id: client.id, user2Id: trainer.id },
        ],
      },
    });

    if (!conversation) {
      console.log('Creating new conversation...');
      conversation = await prisma.conversation.create({
        data: {
          user1Id: trainer.id,
          user2Id: client.id,
        },
      });
    } else {
      console.log('Found existing conversation:', conversation.id);
    }

    // 4. Create some test messages
    const messages = [
      {
        senderId: trainer.id,
        receiverId: client.id,
        content: 'Hello! How are you doing with your training program?',
      },
      {
        senderId: client.id,
        receiverId: trainer.id,
        content: 'Hi coach! I\'m doing well. I completed all my workouts this week.',
      },
      {
        senderId: trainer.id,
        receiverId: client.id,
        content: 'That\'s great to hear! How are you feeling about your progress?',
      },
    ];

    console.log('Creating test messages...');
    for (const message of messages) {
      await prisma.message.create({
        data: {
          ...message,
          conversationId: conversation.id,
        },
      });
    }

    // 5. Update the conversation's lastMessageAt
    await prisma.conversation.update({
      where: {
        id: conversation.id,
      },
      data: {
        lastMessageAt: new Date(),
      },
    });

    console.log('Setup complete!');
    console.log('Trainer URL: http://localhost:3000/dashboard/coaching-chat');
    console.log('Client URL: http://localhost:3000/dashboard/coaching-chat');
    console.log('Conversation ID:', conversation.id);

  } catch (error) {
    console.error('Error setting up coaching chat:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
