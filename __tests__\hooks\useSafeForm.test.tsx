import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useSafeForm } from '@/lib/hooks/useSafeForm';
import { z } from 'zod';

// Mock the toast components
jest.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

// Test form validation schema
const testSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email format'),
  age: z.string().refine((val) => !isNaN(Number(val)), {
    message: 'Age must be a number',
  }),
});

type TestFormValues = z.infer<typeof testSchema>;

// Test component using the hook
function TestForm({
  onSubmit,
  initialValues,
  sanitize = true,
}: {
  onSubmit: (values: TestFormValues) => void | Promise<void>;
  initialValues: TestFormValues;
  sanitize?: boolean;
}) {
  const {
    values,
    errors,
    isSubmitting,
    handleChange,
    handleSubmit,
    getFieldError,
  } = useSafeForm<TestFormValues>({
    initialValues,
    validationSchema: testSchema,
    onSubmit,
    sanitize,
  });

  return (
    <form onSubmit={handleSubmit} data-testid="test-form">
      <div>
        <label htmlFor="name">Name</label>
        <input
          id="name"
          name="name"
          value={values.name}
          onChange={handleChange}
          data-testid="name-input"
        />
        {getFieldError('name') && (
          <p data-testid="name-error">{getFieldError('name')}</p>
        )}
      </div>

      <div>
        <label htmlFor="email">Email</label>
        <input
          id="email"
          name="email"
          value={values.email}
          onChange={handleChange}
          data-testid="email-input"
        />
        {getFieldError('email') && (
          <p data-testid="email-error">{getFieldError('email')}</p>
        )}
      </div>

      <div>
        <label htmlFor="age">Age</label>
        <input
          id="age"
          name="age"
          value={values.age}
          onChange={handleChange}
          data-testid="age-input"
        />
        {getFieldError('age') && (
          <p data-testid="age-error">{getFieldError('age')}</p>
        )}
      </div>

      <button type="submit" disabled={isSubmitting} data-testid="submit-button">
        {isSubmitting ? 'Submitting...' : 'Submit'}
      </button>
    </form>
  );
}

describe('useSafeForm Hook', () => {
  const initialValues: TestFormValues = {
    name: '',
    email: '',
    age: '',
  };

  test('renders form with initial values', () => {
    const handleSubmit = jest.fn();
    render(<TestForm onSubmit={handleSubmit} initialValues={initialValues} />);

    expect(screen.getByTestId('name-input')).toHaveValue('');
    expect(screen.getByTestId('email-input')).toHaveValue('');
    expect(screen.getByTestId('age-input')).toHaveValue('');
  });

  test('updates values when inputs change', async () => {
    const handleSubmit = jest.fn();
    render(<TestForm onSubmit={handleSubmit} initialValues={initialValues} />);

    const nameInput = screen.getByTestId('name-input');
    const emailInput = screen.getByTestId('email-input');
    const ageInput = screen.getByTestId('age-input');

    await userEvent.type(nameInput, 'John Doe');
    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.type(ageInput, '30');

    expect(nameInput).toHaveValue('John Doe');
    expect(emailInput).toHaveValue('<EMAIL>');
    expect(ageInput).toHaveValue('30');
  });

  test('validates form on submission and shows errors', async () => {
    const handleSubmit = jest.fn();
    render(<TestForm onSubmit={handleSubmit} initialValues={initialValues} />);

    // Submit with empty values should show validation errors
    await userEvent.click(screen.getByTestId('submit-button'));

    await waitFor(() => {
      expect(screen.getByTestId('name-error')).toBeInTheDocument();
      expect(screen.getByTestId('email-error')).toBeInTheDocument();
    });

    // handleSubmit should not be called when validation fails
    expect(handleSubmit).not.toHaveBeenCalled();
  });

  test('submits form with valid data', async () => {
    const handleSubmit = jest.fn();
    render(<TestForm onSubmit={handleSubmit} initialValues={initialValues} />);

    const nameInput = screen.getByTestId('name-input');
    const emailInput = screen.getByTestId('email-input');
    const ageInput = screen.getByTestId('age-input');

    await userEvent.type(nameInput, 'John Doe');
    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.type(ageInput, '30');

    await userEvent.click(screen.getByTestId('submit-button'));

    await waitFor(() => {
      expect(handleSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'John Doe',
          email: '<EMAIL>',
          age: '30',
        })
      );
    });
  });

  test('handles async submission with loading state', async () => {
    // Create a delayed submit handler to simulate async operation
    const asyncSubmit = jest.fn().mockImplementation(() => {
      return new Promise((resolve) => setTimeout(resolve, 100));
    });

    render(<TestForm onSubmit={asyncSubmit} initialValues={initialValues} />);

    const nameInput = screen.getByTestId('name-input');
    const emailInput = screen.getByTestId('email-input');
    const ageInput = screen.getByTestId('age-input');
    const submitButton = screen.getByTestId('submit-button');

    await userEvent.type(nameInput, 'John Doe');
    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.type(ageInput, '30');

    await userEvent.click(submitButton);

    // Button should be disabled during submission
    expect(submitButton).toBeDisabled();
    expect(submitButton).toHaveTextContent('Submitting...');

    await waitFor(() => {
      expect(submitButton).not.toBeDisabled();
      expect(submitButton).toHaveTextContent('Submit');
    });

    expect(asyncSubmit).toHaveBeenCalled();
  });

  test('sanitizes form data before submission when sanitize=true', async () => {
    const handleSubmit = jest.fn();
    render(
      <TestForm
        onSubmit={handleSubmit}
        initialValues={initialValues}
        sanitize={true}
      />
    );

    const nameInput = screen.getByTestId('name-input');
    const emailInput = screen.getByTestId('email-input');

    // Input potentially dangerous values
    await userEvent.type(nameInput, 'John<script>alert("xss")</script>');
    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.type(screen.getByTestId('age-input'), '30');

    await userEvent.click(screen.getByTestId('submit-button'));

    await waitFor(() => {
      // The sanitized value should not contain the script tag
      expect(handleSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          name: expect.not.stringContaining('<script>'),
          email: '<EMAIL>',
        })
      );
    });
  });

  test('clears field errors when input changes', async () => {
    const handleSubmit = jest.fn();
    render(<TestForm onSubmit={handleSubmit} initialValues={initialValues} />);

    // Submit with empty values to trigger validation errors
    await userEvent.click(screen.getByTestId('submit-button'));

    // Wait for errors to appear
    await waitFor(() => {
      expect(screen.getByTestId('name-error')).toBeInTheDocument();
    });

    // Fix one of the errors
    await userEvent.type(screen.getByTestId('name-input'), 'John');

    // The error for the fixed field should disappear
    await waitFor(() => {
      expect(screen.queryByTestId('name-error')).not.toBeInTheDocument();
    });

    // But other errors should remain
    expect(screen.getByTestId('email-error')).toBeInTheDocument();
  });

  test('handles submission error and displays toast', async () => {
    // Mock a submission function that throws an error
    const failingSubmit = jest.fn().mockImplementation(() => {
      throw new Error('Submission failed');
    });

    render(<TestForm onSubmit={failingSubmit} initialValues={initialValues} />);

    const nameInput = screen.getByTestId('name-input');
    const emailInput = screen.getByTestId('email-input');
    const ageInput = screen.getByTestId('age-input');

    // Fill in valid data
    await userEvent.type(nameInput, 'John Doe');
    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.type(ageInput, '30');

    // Submit form which will trigger the error
    await userEvent.click(screen.getByTestId('submit-button'));

    // Toast should be triggered
    await waitFor(() => {
      expect(failingSubmit).toHaveBeenCalled();
      // In a real test, we'd check the toast content
      // but for this mock we just verify the function was called
    });
  });
}); 