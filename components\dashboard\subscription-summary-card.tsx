'use client';

import Link from "next/link";
import { <PERSON><PERSON><PERSON>, Star, Settings, Calendar } from "lucide-react";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface SubscriptionSummaryCardProps {
  trainerName: string;
  trainerAvatar?: string;
  trainerRating?: number;
  subscriptionType: string;
  nextRenewalDate: Date;
  price: string;
  paymentMethod?: string;
}

export function SubscriptionSummaryCard({
  trainerName,
  trainerAvatar,
  trainerRating = 4.8,
  subscriptionType,
  nextRenewalDate,
  price,
  paymentMethod
}: SubscriptionSummaryCardProps) {
  // Format the renewal date
  const formattedRenewalDate = new Date(nextRenewalDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <Card className="shadow-md hover:shadow-lg transition-all overflow-hidden group">
      <div className="bg-gradient-to-r from-background to-muted/20 px-4 py-3 border-b border-border/30 group-hover:from-background/80 group-hover:to-muted/30 transition-all">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <CreditCard className="h-4 w-4 text-primary" />
          Subscription Summary
        </CardTitle>
      </div>

      <CardContent className="p-5 space-y-5">
        <div className="flex items-center gap-4">
          <Avatar className="h-14 w-14 border-2 border-primary/20 shadow-sm">
            <AvatarImage src={trainerAvatar} alt={trainerName} />
            <AvatarFallback className="bg-primary/10 text-primary font-medium">{trainerName.charAt(0)}</AvatarFallback>
          </Avatar>

          <div>
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-lg">{trainerName}</h3>
              <div className="flex items-center text-amber-500 text-xs bg-amber-50 px-2 py-0.5 rounded-full font-medium">
                <Star className="h-3 w-3 mr-1 fill-current" />
                {trainerRating}
              </div>
            </div>
            <div className="flex items-center">
              <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20 text-xs font-normal">
                {subscriptionType}
              </Badge>
            </div>
          </div>
        </div>

        <div className="space-y-3 bg-muted/10 p-4 rounded-xl border border-border/20">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-full bg-blue-500/10 flex items-center justify-center">
                <Calendar className="h-4 w-4 text-blue-500" />
              </div>
              <div>
                <p className="text-xs text-muted-foreground">Next Renewal</p>
                <p className="font-medium">{formattedRenewalDate}</p>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-full bg-green-500/10 flex items-center justify-center">
                <CreditCard className="h-4 w-4 text-green-500" />
              </div>
              <div>
                <p className="text-xs text-muted-foreground">Price</p>
                <p className="font-medium">{price}</p>
              </div>
            </div>
          </div>

          {paymentMethod && (
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-amber-500/10 flex items-center justify-center">
                  <CreditCard className="h-4 w-4 text-amber-500" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Payment Method</p>
                  <p className="font-medium">{paymentMethod}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        <Button asChild variant="outline" className="w-full hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors">
          <Link href="/dashboard/subscription">
            <Settings className="mr-2 h-4 w-4" />
            Manage Subscription
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
