/**
 * Database optimization middleware
 * This middleware helps optimize database queries by:
 * 1. Batching similar queries
 * 2. Caching responses for read operations
 * 3. Monitoring query performance
 */

import { NextRequest, NextResponse } from 'next/server';
import { queryCache } from '@/lib/cache';

// Routes that should be cached
const CACHEABLE_ROUTES = [
  '/api/users',
  '/api/products',
  '/api/exercises',
  '/api/workouts',
  '/api/trainers',
];

// Routes that should never be cached
const NEVER_CACHE_ROUTES = [
  '/api/auth',
  '/api/checkout',
  '/api/webhook',
];

// Cache TTL for different route types (in milliseconds)
const CACHE_TTL = {
  default: 60 * 1000, // 1 minute
  static: 5 * 60 * 1000, // 5 minutes
  dynamic: 30 * 1000, // 30 seconds
};

/**
 * Determine if a route should be cached
 */
function isCacheableRoute(pathname: string): boolean {
  // Never cache certain routes
  if (NEVER_CACHE_ROUTES.some(route => pathname.startsWith(route))) {
    return false;
  }
  
  // Cache specific API routes
  return CACHEABLE_ROUTES.some(route => pathname.startsWith(route));
}

/**
 * Get cache TTL for a route
 */
function getCacheTTL(pathname: string): number {
  // Static data can be cached longer
  if (pathname.includes('/api/products') || pathname.includes('/api/exercises')) {
    return CACHE_TTL.static;
  }
  
  // Dynamic data needs shorter cache times
  if (pathname.includes('/api/messages') || pathname.includes('/api/notifications')) {
    return CACHE_TTL.dynamic;
  }
  
  return CACHE_TTL.default;
}

/**
 * Generate a cache key from the request
 */
function generateCacheKey(req: NextRequest): string {
  const url = new URL(req.url);
  const pathname = url.pathname;
  const search = url.search;
  
  // Include auth state in cache key to prevent sharing data between users
  // This is a simplified example - in production you'd use a more secure method
  const authHeader = req.headers.get('authorization') || '';
  const authHash = authHeader ? hashString(authHeader) : 'noauth';
  
  return `${pathname}${search}:${authHash}`;
}

/**
 * Simple string hashing function
 */
function hashString(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString(16);
}

/**
 * Database optimization middleware
 */
export async function databaseOptimizationMiddleware(
  req: NextRequest,
  next: () => Promise<NextResponse>
): Promise<NextResponse> {
  const url = new URL(req.url);
  const pathname = url.pathname;
  
  // Only apply to API routes
  if (!pathname.startsWith('/api/')) {
    return next();
  }
  
  // Only cache GET requests
  if (req.method !== 'GET') {
    return next();
  }
  
  // Check if route is cacheable
  if (!isCacheableRoute(pathname)) {
    return next();
  }
  
  // Generate cache key
  const cacheKey = generateCacheKey(req);
  
  // Try to get from cache
  const cachedResponse = queryCache.get<NextResponse>(cacheKey);
  if (cachedResponse) {
    // Clone the response to avoid modifying the cached one
    const response = NextResponse.json(
      await cachedResponse.json(),
      {
        status: cachedResponse.status,
        statusText: cachedResponse.statusText,
        headers: new Headers(cachedResponse.headers)
      }
    );
    
    // Add cache header for debugging
    response.headers.set('X-Cache', 'HIT');
    return response;
  }
  
  // Not in cache, execute the request
  const response = await next();
  
  // Only cache successful responses
  if (response.ok) {
    // Clone the response before caching
    const responseToCache = response.clone();
    
    // Get appropriate TTL for this route
    const ttl = getCacheTTL(pathname);
    
    // Store in cache
    queryCache.set(cacheKey, responseToCache, ttl);
    
    // Add cache header for debugging
    response.headers.set('X-Cache', 'MISS');
  }
  
  return response;
}
