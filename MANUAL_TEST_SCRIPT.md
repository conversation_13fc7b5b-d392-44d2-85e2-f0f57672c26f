# Manual Test Script for Clear Coach App

This document provides a step-by-step guide to manually test all key workflows in the Clear Coach App from client, premium client, and trainer perspectives.

## Prerequisites

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Open the app in your browser:
   ```
   http://localhost:3000
   ```

## Client Workflows

### 1. Authentication

- [ ] Navigate to http://localhost:3000/api/auth/dev-login?role=client to log in as a client
- [ ] Verify you are redirected to the client dashboard
- [ ] Check that the navigation shows client-specific options

### 2. Browse Products

- [ ] Navigate to "Digital Products" tab
- [ ] Verify products are displayed with images, titles, and prices
- [ ] Test filtering products by category
- [ ] Test sorting products by price
- [ ] Click on a product to view details
- [ ] Verify product details page shows complete information

### 3. Cart Management

- [ ] Add a product to cart
- [ ] Navigate to cart
- [ ] Verify product appears in cart with correct price
- [ ] Update quantity and verify total updates
- [ ] Remove item from cart
- [ ] Verify cart updates correctly

### 4. Checkout Process

- [ ] Add products to cart
- [ ] Proceed to checkout
- [ ] Fill in payment information
- [ ] Complete purchase
- [ ] Verify order confirmation is displayed
- [ ] Check that purchased products appear in "My Products" section

### 5. Training Tracking

- [ ] Navigate to "My Programs" tab
- [ ] Select an assigned training plan
- [ ] Start a workout
- [ ] Log sets, reps, and weights for exercises
- [ ] Complete workout
- [ ] Verify workout is marked as completed
- [ ] Check that progress is tracked correctly

### 6. Progress Tracking

- [ ] Navigate to "Analytics" tab
- [ ] Log a new weight measurement
- [ ] Log body measurements
- [ ] Verify measurements are displayed in charts
- [ ] Check historical data is accessible

## Premium Client Workflows

### 1. Authentication

- [ ] Navigate to http://localhost:3000/api/auth/dev-login?role=premium_client to log in as a premium client
- [ ] Verify you are redirected to the premium client dashboard
- [ ] Check that the navigation shows premium client-specific options

### 2. Access Premium Content

- [ ] Navigate to "Premium Dashboard"
- [ ] Verify premium content is accessible
- [ ] Check that subscription details are displayed correctly

### 3. Training Subscriptions

- [ ] Navigate to "Training Subscriptions" tab
- [ ] Verify active subscriptions are displayed
- [ ] Access subscription-specific content
- [ ] Check that subscription benefits are available

### 4. Nutrition Logging

- [ ] Navigate to "Analytics" tab
- [ ] Log daily nutrition intake
- [ ] Track macronutrients (protein, carbs, fat)
- [ ] Verify nutrition data is displayed in charts
- [ ] Check historical nutrition data is accessible

### 5. Advanced Analytics

- [ ] Navigate to "Analytics" tab
- [ ] Check that premium analytics features are available
- [ ] View detailed performance metrics
- [ ] Access trend analysis
- [ ] Export data if available

## Trainer Workflows

### 1. Authentication

- [ ] Navigate to http://localhost:3000/api/auth/dev-login?role=trainer to log in as a trainer
- [ ] Verify you are redirected to the trainer dashboard
- [ ] Check that the navigation shows trainer-specific options

### 2. Client Management

- [ ] Navigate to "Clients" tab
- [ ] View list of clients
- [ ] Search for specific clients
- [ ] Filter clients by status
- [ ] View client details
- [ ] Check client progress

### 3. Training Plan Management

- [ ] Navigate to "Training Plans" tab
- [ ] Create a new training plan
- [ ] Add weeks to the plan
- [ ] Add workouts to each week
- [ ] Add exercises to workouts
- [ ] Save the training plan
- [ ] Verify the plan appears in the list
- [ ] Edit an existing plan
- [ ] Duplicate a plan
- [ ] Delete a plan

### 4. Diet Plan Management

- [ ] Navigate to "Diet Plans" tab
- [ ] Create a new diet plan
- [ ] Add meals to the plan
- [ ] Specify macronutrients
- [ ] Save the diet plan
- [ ] Verify the plan appears in the list
- [ ] Edit an existing plan
- [ ] Duplicate a plan
- [ ] Delete a plan

### 5. Product Management

- [ ] Navigate to "Products" tab
- [ ] Create a new digital product
- [ ] Upload product content
- [ ] Set price and details
- [ ] Save the product
- [ ] Verify the product appears in the list
- [ ] Edit an existing product
- [ ] Delete a product

### 6. Subscription Management

- [ ] Navigate to "Subscriptions" tab
- [ ] Create a new subscription tier
- [ ] Set price and features
- [ ] Save the subscription tier
- [ ] Verify the tier appears in the list
- [ ] Edit an existing tier
- [ ] Delete a tier

### 7. Assign Plans to Clients

- [ ] Navigate to "Clients" tab
- [ ] Select a client
- [ ] Assign a training plan
- [ ] Assign a diet plan
- [ ] Verify plans appear in client's profile

## Reporting Issues

If you encounter any issues during testing, please document them with the following information:

1. Workflow being tested
2. Steps to reproduce
3. Expected behavior
4. Actual behavior
5. Screenshots if applicable
6. Browser and device information

## Test Completion

After completing all tests, please mark this document with:

- Date of testing
- Name of tester
- Overall status (Pass/Fail)
- List of any failed tests
- Notes on any workarounds or issues
