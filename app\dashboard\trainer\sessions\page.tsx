'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { format, isPast, isToday } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Calendar, Clock, Video, ExternalLink, Calendar as CalendarIcon, MoreHorizontal, Plus } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';

interface CoachingSession {
  id: string;
  title: string;
  description: string | null;
  scheduledDate: string;
  duration: number;
  status: string;
  type: string;
  location: string | null;
  notes: string | null;
  videoConferenceUrl: string | null;
  coachingRelationship: {
    client: {
      id: string;
      name: string;
      email: string;
      avatarUrl: string | null;
    };
  };
}

export default function TrainerSessionsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [upcomingSessions, setUpcomingSessions] = useState<CoachingSession[]>([]);
  const [pastSessions, setPastSessions] = useState<CoachingSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [cancelReason, setCancelReason] = useState('');
  const [sessionToCancel, setSessionToCancel] = useState<string | null>(null);
  const [isCancelling, setIsCancelling] = useState(false);
  const [sessionNotes, setSessionNotes] = useState('');
  const [sessionToUpdate, setSessionToUpdate] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    const fetchSessions = async () => {
      try {
        setIsLoading(true);

        // Fetch upcoming sessions
        const upcomingResponse = await fetch('/api/coaching/sessions?action=upcoming');
        if (!upcomingResponse.ok) {
          throw new Error('Failed to fetch upcoming sessions');
        }
        const upcomingData = await upcomingResponse.json();
        setUpcomingSessions(upcomingData.sessions || []);

        // Fetch past sessions
        const pastResponse = await fetch('/api/coaching/sessions?action=past');
        if (!pastResponse.ok) {
          throw new Error('Failed to fetch past sessions');
        }
        const pastData = await pastResponse.json();
        setPastSessions(pastData.sessions || []);
      } catch (error) {
        console.error('Error fetching sessions:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load your sessions. Please try again later.',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSessions();
  }, [toast]);

  const handleJoinSession = (session: CoachingSession) => {
    if (session.videoConferenceUrl) {
      window.open(session.videoConferenceUrl, '_blank');
    } else {
      toast({
        title: 'No video link available',
        description: 'The video conference link for this session is not available yet.',
      });
    }
  };

  const handleCancelSession = async () => {
    if (!sessionToCancel) return;

    try {
      setIsCancelling(true);

      const response = await fetch('/api/coaching/sessions?action=cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: sessionToCancel,
          reason: cancelReason,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to cancel session');
      }

      // Update the local state
      setUpcomingSessions(prevSessions =>
        prevSessions.map(session =>
          session.id === sessionToCancel
            ? { ...session, status: 'cancelled' }
            : session
        )
      );

      toast({
        title: 'Session cancelled',
        description: 'The coaching session has been cancelled successfully.',
      });

      // Reset the form
      setSessionToCancel(null);
      setCancelReason('');
    } catch (error) {
      console.error('Error cancelling session:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to cancel the session. Please try again later.',
      });
    } finally {
      setIsCancelling(false);
    }
  };

  const handleUpdateSessionNotes = async () => {
    if (!sessionToUpdate) return;

    try {
      setIsUpdating(true);

      const response = await fetch('/api/coaching/sessions?action=update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: sessionToUpdate,
          notes: sessionNotes,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update session notes');
      }

      // Update the local state
      const updateSession = (sessions: CoachingSession[]) =>
        sessions.map(session =>
          session.id === sessionToUpdate
            ? { ...session, notes: sessionNotes }
            : session
        );

      setUpcomingSessions(updateSession);
      setPastSessions(updateSession);

      toast({
        title: 'Notes updated',
        description: 'Session notes have been updated successfully.',
      });

      // Reset the form
      setSessionToUpdate(null);
      setSessionNotes('');
    } catch (error) {
      console.error('Error updating session notes:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to update session notes. Please try again later.',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const renderSessionCard = (session: CoachingSession) => {
    const sessionDate = new Date(session.scheduledDate);
    const isPastSession = isPast(sessionDate) && !isToday(sessionDate);
    const isTodays = isToday(sessionDate);

    return (
      <Card key={session.id} className="overflow-hidden">
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{session.title}</CardTitle>
              <CardDescription>
                {session.description || 'No description provided'}
              </CardDescription>
            </div>
            <Badge
              variant={
                session.status === 'cancelled'
                  ? 'destructive'
                  : isTodays
                  ? 'default'
                  : 'outline'
              }
            >
              {session.status === 'cancelled'
                ? 'Cancelled'
                : isTodays
                ? 'Today'
                : format(sessionDate, 'MMM d')}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pb-3">
          <div className="flex flex-col space-y-3">
            <div className="flex items-center">
              <Avatar className="h-9 w-9 mr-3">
                <AvatarImage src={session.coachingRelationship.client.avatarUrl || ''} alt={session.coachingRelationship.client.name} />
                <AvatarFallback>{session.coachingRelationship.client.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">{session.coachingRelationship.client.name}</p>
                <p className="text-xs text-muted-foreground">Client</p>
              </div>
            </div>

            <div className="flex items-center text-sm">
              <CalendarIcon className="mr-2 h-4 w-4 text-muted-foreground" />
              <span>{format(sessionDate, 'EEEE, MMMM d, yyyy')}</span>
            </div>

            <div className="flex items-center text-sm">
              <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
              <span>{format(sessionDate, 'h:mm a')} ({session.duration} minutes)</span>
            </div>

            {session.type === 'video' && (
              <div className="flex items-center text-sm">
                <Video className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Video Conference</span>
              </div>
            )}

            {session.notes && (
              <div className="mt-2 p-3 bg-muted rounded-md text-sm">
                <p className="font-medium mb-1">Notes:</p>
                <p>{session.notes}</p>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between pt-0">
          {!isPastSession && session.status !== 'cancelled' && (
            <>
              {isTodays && (
                <Button onClick={() => handleJoinSession(session)}>
                  <Video className="mr-2 h-4 w-4" />
                  Join Session
                </Button>
              )}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <Dialog>
                    <DialogTrigger asChild>
                      <DropdownMenuItem onSelect={(e) => {
                        e.preventDefault();
                        setSessionToUpdate(session.id);
                        setSessionNotes(session.notes || '');
                      }}>
                        Add/Edit Notes
                      </DropdownMenuItem>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Session Notes</DialogTitle>
                        <DialogDescription>
                          Add or edit notes for this coaching session.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="py-4">
                        <Textarea
                          value={sessionNotes}
                          onChange={(e) => setSessionNotes(e.target.value)}
                          placeholder="Add notes about this session..."
                          className="min-h-[150px]"
                        />
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setSessionToUpdate(null)}>
                          Cancel
                        </Button>
                        <Button onClick={handleUpdateSessionNotes} disabled={isUpdating}>
                          {isUpdating ? 'Saving...' : 'Save Notes'}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  <Dialog>
                    <DialogTrigger asChild>
                      <DropdownMenuItem onSelect={(e) => {
                        e.preventDefault();
                        setSessionToCancel(session.id);
                      }}>
                        Cancel Session
                      </DropdownMenuItem>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Cancel Coaching Session</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to cancel this coaching session? This action cannot be undone.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="py-4">
                        <p className="text-sm font-medium mb-2">Reason for cancellation (optional):</p>
                        <Textarea
                          value={cancelReason}
                          onChange={(e) => setCancelReason(e.target.value)}
                          placeholder="Please provide a reason for cancellation..."
                        />
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setSessionToCancel(null)}>
                          Keep Session
                        </Button>
                        <Button variant="destructive" onClick={handleCancelSession} disabled={isCancelling}>
                          {isCancelling ? 'Cancelling...' : 'Cancel Session'}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  <DropdownMenuItem onSelect={(e) => {
                    e.preventDefault();
                    router.push(`/dashboard/trainer/sessions/${session.id}`);
                  }}>
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem onSelect={(e) => {
                    e.preventDefault();
                    router.push(`/dashboard/coaching-chat`);
                  }}>
                    Message Client
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          )}

          {(isPastSession || session.status === 'cancelled') && (
            <>
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    {session.notes ? 'Edit Notes' : 'Add Notes'}
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Session Notes</DialogTitle>
                    <DialogDescription>
                      {isPastSession
                        ? 'Add or edit notes for this completed coaching session.'
                        : 'Add or edit notes for this cancelled coaching session.'}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4">
                    <Textarea
                      value={sessionNotes || session.notes || ''}
                      onChange={(e) => setSessionNotes(e.target.value)}
                      placeholder="Add notes about this session..."
                      className="min-h-[150px]"
                      onFocus={() => {
                        setSessionToUpdate(session.id);
                        setSessionNotes(session.notes || '');
                      }}
                    />
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setSessionToUpdate(null)}>
                      Cancel
                    </Button>
                    <Button onClick={handleUpdateSessionNotes} disabled={isUpdating}>
                      {isUpdating ? 'Saving...' : 'Save Notes'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Button variant="outline" onClick={() => router.push(`/dashboard/trainer/sessions/${session.id}`)}>
                <ExternalLink className="mr-2 h-4 w-4" />
                View Details
              </Button>
            </>
          )}
        </CardFooter>
      </Card>
    );
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Coaching Sessions</h1>
        <p className="text-muted-foreground">
          Manage your 1:1 coaching sessions with clients
        </p>
      </div>

      <div className="flex justify-end space-x-4">
        <Button variant="outline" onClick={() => router.push('/dashboard/trainer/calendly')}>
          <Calendar className="mr-2 h-4 w-4" />
          Calendly Settings
        </Button>
        <Button onClick={() => router.push('/dashboard/trainer/sessions/create')}>
          <Plus className="mr-2 h-4 w-4" />
          Create Manual Session
        </Button>
      </div>

      <Tabs defaultValue="upcoming" className="space-y-6">
        <TabsList>
          <TabsTrigger value="upcoming">Upcoming Sessions</TabsTrigger>
          <TabsTrigger value="past">Past Sessions</TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming" className="space-y-6">
          {isLoading ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3].map((i) => (
                <Card key={i}>
                  <CardHeader>
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <Skeleton className="h-10 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Skeleton className="h-10 w-28" />
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : upcomingSessions.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {upcomingSessions.map(renderSessionCard)}
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>No Upcoming Sessions</CardTitle>
                <CardDescription>
                  You don't have any upcoming coaching sessions scheduled.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Set up your Calendly integration to allow clients to schedule sessions with you.
                </p>
              </CardContent>
              <CardFooter>
                <Button onClick={() => router.push('/dashboard/trainer/calendly')}>
                  <Calendar className="mr-2 h-4 w-4" />
                  Set Up Calendly
                </Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="past" className="space-y-6">
          {isLoading ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3].map((i) => (
                <Card key={i}>
                  <CardHeader>
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <Skeleton className="h-10 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Skeleton className="h-10 w-28" />
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : pastSessions.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {pastSessions.map(renderSessionCard)}
            </div>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>No Past Sessions</CardTitle>
                <CardDescription>
                  You don't have any past coaching sessions.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Once you complete coaching sessions, they will appear here for your reference.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
