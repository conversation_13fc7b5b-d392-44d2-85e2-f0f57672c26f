import { NextResponse } from "next/server"
import { BaseApiHandler } from "./base-api-handler"
import { TrainingPlanService } from "../services/training-plan-service"

export class TrainingPlanTemplateHandler extends BaseApiHandler {
  /**
   * Get all training plan templates
   */
  protected async get(req: Request, userId: string): Promise<NextResponse> {
    const templates = await TrainingPlanService.getTemplatesForTrainer(userId)
    return NextResponse.json(templates)
  }

  /**
   * Create a new training plan template
   */
  protected async post(req: Request, userId: string): Promise<NextResponse> {
    try {
      const planData = await req.json()
      console.log('Received plan data:', JSON.stringify(planData))

      // Validate required fields
      if (!planData.title) {
        return NextResponse.json({ error: 'Title is required' }, { status: 400 })
      }

      // Ensure weeks is properly formatted as JSON
      if (planData.weeks && typeof planData.weeks !== 'object') {
        try {
          planData.weeks = JSON.parse(planData.weeks)
        } catch (e) {
          console.error('Error parsing weeks JSON:', e)
          return NextResponse.json({ error: 'Invalid weeks format' }, { status: 400 })
        }
      }

      const template = await TrainingPlanService.createTemplate(userId, planData)
      return NextResponse.json(template)
    } catch (error) {
      console.error('Error creating training plan template:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return NextResponse.json({
        error: 'Failed to create training plan template',
        details: errorMessage
      }, { status: 500 })
    }
  }

  /**
   * Update a training plan template (not used in route handler)
   */
  protected async put(req: Request, userId: string): Promise<NextResponse> {
    // This method is not used in the main route handler
    // It's implemented in the [id] route handler
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete a training plan template (not used in route handler)
   */
  protected async delete(req: Request, userId: string): Promise<NextResponse> {
    // This method is not used in the main route handler
    // It's implemented in the [id] route handler
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}

export class TrainingPlanTemplateByIdHandler extends BaseApiHandler {
  /**
   * Get a training plan template by ID
   */
  protected async get(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    try {
      const template = await TrainingPlanService.getTemplateById(params.id, userId)
      
      if (!template) {
        return NextResponse.json({ error: "Template not found" }, { status: 404 })
      }
      
      return NextResponse.json(template)
    } catch (error) {
      console.error('Error getting template by ID:', error)
      return NextResponse.json({ error: "Failed to get template" }, { status: 500 })
    }
  }

  /**
   * Create a new training plan template (not used in [id] route handler)
   */
  protected async post(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Update a training plan template
   */
  protected async put(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    try {
      const data = await req.json()
      console.log('Updating training plan template:', params.id, 'with data:', JSON.stringify(data))

      // Check if the training plan template exists and belongs to the user
      const existingTemplate = await TrainingPlanService.getTemplateById(params.id, userId)

      if (!existingTemplate) {
        return NextResponse.json({ error: "Training plan template not found" }, { status: 404 })
      }

      // Ensure weeks is properly formatted as JSON
      if (data.weeks && typeof data.weeks !== 'object') {
        try {
          data.weeks = JSON.parse(data.weeks)
        } catch (e) {
          console.error('Error parsing weeks JSON:', e)
          return NextResponse.json({ error: 'Invalid weeks format' }, { status: 400 })
        }
      }

      const updatedTemplate = await TrainingPlanService.updateTemplate(
        params.id,
        userId,
        data
      )

      return NextResponse.json(updatedTemplate)
    } catch (error) {
      console.error('Error updating template:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return NextResponse.json({
        error: 'Failed to update training plan template',
        details: errorMessage
      }, { status: 500 })
    }
  }

  /**
   * Delete a training plan template
   */
  protected async delete(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    try {
      await TrainingPlanService.deleteTemplate(params.id, userId)
      return NextResponse.json({ success: true })
    } catch (error) {
      console.error('Error deleting template:', error)
      return NextResponse.json({ error: "Failed to delete template" }, { status: 500 })
    }
  }
}
