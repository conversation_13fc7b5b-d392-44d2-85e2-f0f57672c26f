import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { Progress<PERSON>hart } from "@/components/progress/progress-chart"
import { ProgressHistory } from "@/components/progress/progress-history"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, ClipboardList } from "lucide-react"
import { ProgressEntryDialog } from "@/components/progress/progress-entry-dialog"

// Define the Progress type expected by the components
interface Progress {
  id: string
  date: Date
  weight: number
  bodyFat: number | null
  measurements: any
  notes?: string
  sleepHours?: number | null
  stressLevel?: number | null
  coffeeCount?: number | null
  createdAt: Date
  updatedAt: Date
  clientId: string
}

export default async function ProgressPage() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect("/login")
  }

  const dbProgressEntries = await prisma.measurement.findMany({
    where: {
      userId: session.user.id,
    },
    orderBy: {
      date: "desc",
    },
  })

  // Map database entries to the Progress type expected by components
  const progressEntries: Progress[] = dbProgressEntries.map(entry => ({
    id: entry.id,
    date: entry.date,
    weight: entry.weight ?? 0, // Default to 0 if null
    bodyFat: entry.bodyFat,
    measurements: {
      waist: entry.waist,
      chest: entry.chest,
      arms: entry.arms,
      thighs: entry.thighs
    },
    notes: entry.notes || undefined,
    // Health metrics
    sleepHours: entry.sleepHours || undefined,
    stressLevel: entry.stressLevel || undefined,
    coffeeCount: entry.coffeeCount || undefined,
    createdAt: entry.createdAt,
    updatedAt: entry.updatedAt,
    clientId: entry.userId, // Map userId to clientId
  }))

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Progress Tracking</h2>
        <div className="flex items-center space-x-4">
          <p className="text-sm text-muted-foreground">
            Track and monitor your fitness journey
          </p>
        </div>
      </div>
      <div className="grid gap-4 md:grid-cols-1">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart className="mr-2 h-5 w-5" />
              Progress Overview
            </CardTitle>
            <CardDescription>Your recent weight and body fat percentage changes</CardDescription>
          </CardHeader>
          <CardContent>
            {progressEntries.length > 0 ? (
              <ProgressChart entries={progressEntries} />
            ) : (
              <p className="text-center py-8 text-muted-foreground">
                No progress data logged yet. Start tracking your journey!
              </p>
            )}
          </CardContent>
        </Card>
      </div>
      <Card className="col-span-1 md:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center">
            <ClipboardList className="mr-2 h-5 w-5" />
            Progress Log
          </CardTitle>
          <CardDescription>Detailed history of your progress entries</CardDescription>
        </CardHeader>
        <CardContent>
          {progressEntries.length > 0 ? (
            <ProgressHistory entries={progressEntries} />
          ) : (
            <p className="text-center py-8 text-muted-foreground">
              Your logged progress entries will appear here.
            </p>
          )}
        </CardContent>
      </Card>

      {/* Add Progress Entry Modal */}
      <ProgressEntryDialog
        open={false}
        onOpenChange={() => {}}
        onSubmit={() => {}}
      />
    </div>
  )
}

