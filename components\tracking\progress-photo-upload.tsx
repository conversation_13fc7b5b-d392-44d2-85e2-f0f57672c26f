'use client';

import { useState } from 'react';
import { Camera, Upload, X, Image as ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface ProgressPhotoUploadProps {
  onPhotosChange?: (photos: { front?: File; side?: File; back?: File }) => void;
  className?: string;
}

export function ProgressPhotoUpload({ onPhotosChange, className }: ProgressPhotoUploadProps) {
  const { toast } = useToast();
  const [photos, setPhotos] = useState<{
    front?: { file: File; preview: string };
    side?: { file: File; preview: string };
    back?: { file: File; preview: string };
  }>({});
  const [activeTab, setActiveTab] = useState('front');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, view: 'front' | 'side' | 'back') => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Check file type
      if (!file.type.startsWith('image/')) {
        toast({
          variant: 'destructive',
          title: 'Invalid file type',
          description: 'Please upload an image file (JPEG, PNG, etc.)',
        });
        return;
      }
      
      // Check file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          variant: 'destructive',
          title: 'File too large',
          description: 'Please upload an image smaller than 5MB',
        });
        return;
      }
      
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setPhotos(prev => {
            const newPhotos = {
              ...prev,
              [view]: { file, preview: event.target!.result as string }
            };
            
            // Call the callback with just the files
            if (onPhotosChange) {
              onPhotosChange({
                front: newPhotos.front?.file,
                side: newPhotos.side?.file,
                back: newPhotos.back?.file,
              });
            }
            
            return newPhotos;
          });
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const removePhoto = (view: 'front' | 'side' | 'back') => {
    setPhotos(prev => {
      const newPhotos = { ...prev };
      delete newPhotos[view];
      
      // Call the callback with updated files
      if (onPhotosChange) {
        onPhotosChange({
          front: newPhotos.front?.file,
          side: newPhotos.side?.file,
          back: newPhotos.back?.file,
        });
      }
      
      return newPhotos;
    });
  };

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="front" className="flex items-center gap-2">
              <Camera className="h-4 w-4" />
              <span>Front</span>
            </TabsTrigger>
            <TabsTrigger value="side" className="flex items-center gap-2">
              <Camera className="h-4 w-4" />
              <span>Side</span>
            </TabsTrigger>
            <TabsTrigger value="back" className="flex items-center gap-2">
              <Camera className="h-4 w-4" />
              <span>Back</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="front" className="m-0">
            <PhotoUploadView
              photo={photos.front}
              onFileChange={(e) => handleFileChange(e, 'front')}
              onRemove={() => removePhoto('front')}
              label="Front View"
              description="Stand straight facing the camera with arms slightly away from your sides"
            />
          </TabsContent>
          
          <TabsContent value="side" className="m-0">
            <PhotoUploadView
              photo={photos.side}
              onFileChange={(e) => handleFileChange(e, 'side')}
              onRemove={() => removePhoto('side')}
              label="Side View"
              description="Stand straight with your side to the camera, arms at your sides"
            />
          </TabsContent>
          
          <TabsContent value="back" className="m-0">
            <PhotoUploadView
              photo={photos.back}
              onFileChange={(e) => handleFileChange(e, 'back')}
              onRemove={() => removePhoto('back')}
              label="Back View"
              description="Stand straight with your back to the camera, arms slightly away from your sides"
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

interface PhotoUploadViewProps {
  photo?: { file: File; preview: string };
  onFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRemove: () => void;
  label: string;
  description: string;
}

function PhotoUploadView({ photo, onFileChange, onRemove, label, description }: PhotoUploadViewProps) {
  return (
    <div className="p-4">
      <div className="flex flex-col items-center">
        <div className="w-full aspect-square max-h-[300px] bg-muted/50 rounded-lg flex flex-col items-center justify-center relative overflow-hidden">
          {photo ? (
            <>
              <img 
                src={photo.preview} 
                alt={label} 
                className="w-full h-full object-cover"
              />
              <Button 
                variant="destructive" 
                size="icon" 
                className="absolute top-2 right-2 h-8 w-8 rounded-full"
                onClick={onRemove}
              >
                <X className="h-4 w-4" />
              </Button>
            </>
          ) : (
            <>
              <ImageIcon className="h-12 w-12 text-muted-foreground mb-2" />
              <p className="text-muted-foreground text-center mb-4 px-4">{description}</p>
              <Button variant="outline" className="relative">
                <Upload className="h-4 w-4 mr-2" />
                Upload Photo
                <input
                  type="file"
                  accept="image/*"
                  onChange={onFileChange}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
