"use client"

import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  ChevronDown,
  ChevronUp,
  XCircle
} from "lucide-react"
import React, { useState, useEffect } from "react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"

interface FeatureMonitorProps {
  featureName: string
}

export function FeatureMonitor({ featureName }: FeatureMonitorProps) {
  const [status, setStatus] = useState<'online' | 'offline' | 'degraded' | 'unknown'>('unknown')
  const [expanded, setExpanded] = useState(false)
  const [logs, setLogs] = useState<string[]>([])
  const [incidents, setIncidents] = useState<number>(0)

  // Fetch feature status
  useEffect(() => {
    const fetchStatus = async () => {
      try {
        const response = await fetch(`/api/monitoring/feature-status?name=${featureName}`)
        if (!response.ok) {
          throw new Error(`Failed to fetch status: ${response.statusText}`)
        }
        const data = await response.json()
        setStatus(data.status)
        setLogs(data.logs || [])
        setIncidents(data.incidents || 0)
      } catch (_error) {
        // Error handling
        setStatus('unknown')
      }
    }

    fetchStatus()
    const interval = setInterval(fetchStatus, 30000) // Check every 30 seconds
    return () => clearInterval(interval)
  }, [featureName])

  // Restart feature function
  const restartFeature = async () => {
    try {
      const response = await fetch(`/api/monitoring/restart-feature?name=${featureName}`, {
        method: 'POST'
      })
      if (!response.ok) {
        throw new Error(`Failed to restart feature: ${response.statusText}`)
      }
      const data = await response.json()
      setLogs([`Feature restart requested at ${new Date().toLocaleTimeString()}`, ...logs])
    } catch (_error) {
      // Log error
      setLogs([`Failed to restart feature at ${new Date().toLocaleTimeString()}`, ...logs])
    }
  }

  // Clear incidents function
  const clearIncidents = async () => {
    try {
      const response = await fetch(`/api/monitoring/clear-incidents?name=${featureName}`, {
        method: 'POST'
      })
      if (!response.ok) {
        throw new Error(`Failed to clear incidents: ${response.statusText}`)
      }
      setIncidents(0)
      setLogs([`Incidents cleared at ${new Date().toLocaleTimeString()}`, ...logs])
    } catch (_error) {
      // Log error
      setLogs([`Failed to clear incidents at ${new Date().toLocaleTimeString()}`, ...logs])
    }
  }

  // Force refresh function
  const forceRefresh = async () => {
    try {
      const response = await fetch(`/api/monitoring/refresh-feature?name=${featureName}`, {
        method: 'POST'
      })
      if (!response.ok) {
        throw new Error(`Failed to refresh feature: ${response.statusText}`)
      }
      setLogs([`Feature refresh requested at ${new Date().toLocaleTimeString()}`, ...logs])
    } catch (_error) {
      // Log error
      setLogs([`Failed to refresh feature at ${new Date().toLocaleTimeString()}`, ...logs])
    }
  }

  return (
    <Card className="mb-6">
      <CardHeader className="flex flex-row items-center justify-between py-4">
        <CardTitle className="text-xl font-bold">{featureName}</CardTitle>
        <div className="flex items-center gap-2">
          <Badge
            variant={
              status === 'online' ? 'default' :
              status === 'degraded' ? 'warning' :
              status === 'offline' ? 'destructive' : 'outline'
            }
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
          {incidents > 0 && (
            <Badge variant="destructive" className="ml-2">
              {incidents} {incidents === 1 ? 'incident' : 'incidents'}
            </Badge>
          )}
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
      </CardHeader>
      
      {expanded && (
        <CardContent>
          <div className="mb-4 flex flex-wrap gap-2">
            <Button size="sm" variant="outline" onClick={restartFeature}>
              Restart Feature
            </Button>
            <Button size="sm" variant="outline" onClick={forceRefresh}>
              Force Refresh
            </Button>
            {incidents > 0 && (
              <Button size="sm" variant="outline" onClick={clearIncidents}>
                Clear Incidents
              </Button>
            )}
          </div>

          {status === 'degraded' && (
            <Alert variant="default" className="bg-yellow-50 border-yellow-300 text-yellow-700">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Performance Issues</AlertTitle>
              <AlertDescription>
                This feature is experiencing performance issues but is still operational.
              </AlertDescription>
            </Alert>
          )}
          
          {status === 'offline' && (
            <Alert className="mb-4" variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertTitle>Feature Unavailable</AlertTitle>
              <AlertDescription>
                This feature is currently unavailable. Our team has been notified.
              </AlertDescription>
            </Alert>
          )}
          
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Recent Logs</h4>
            {logs.length > 0 ? (
              <div className="text-xs space-y-1 max-h-24 overflow-y-auto border rounded-md p-2">
                {logs.map((log, i) => (
                  <div key={i} className="py-1 border-b last:border-0">
                    {log}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-xs text-muted-foreground">No logs available</p>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  )
} 