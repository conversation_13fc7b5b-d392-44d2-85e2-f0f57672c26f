import { validateServerRequest, extractAndValidateRequestData } from '@/lib/validation/serverValidation';
import { sanitizeFormData } from '@/lib/validation/sanitization';
import { z } from 'zod';

describe('Server Validation Tests', () => {
  // Create a test schema
  const testSchema = z.object({
    name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
    email: z.string().email({ message: 'Invalid email format' }),
    age: z.number().min(18, { message: 'Must be at least 18 years old' }),
  });

  describe('validateServerRequest', () => {
    test('returns success for valid data', () => {
      const validData = {
        name: '<PERSON>',
        email: '<EMAIL>',
        age: 25
      };

      const [isValid, result, errors] = validateServerRequest(testSchema, validData);

      expect(isValid).toBe(true);
      expect(result).toEqual(validData);
      expect(errors).toEqual([]);
    });

    test('returns validation errors for invalid data', () => {
      const invalidData = {
        name: 'J', // Too short
        email: 'not-an-email',
        age: 16 // Too young
      };

      const [isValid, result, errors] = validateServerRequest(testSchema, invalidData);

      expect(isValid).toBe(false);
      expect(result).toBeNull();
      expect(errors.length).toBe(3);
      expect(errors).toContainEqual(expect.objectContaining({
        field: 'name',
        message: 'Name must be at least 2 characters'
      }));
      expect(errors).toContainEqual(expect.objectContaining({
        field: 'email',
        message: 'Invalid email format'
      }));
      expect(errors).toContainEqual(expect.objectContaining({
        field: 'age',
        message: 'Must be at least 18 years old'
      }));
    });

    test('sanitizes data before validation', () => {
      const dataWithXSS = {
        name: 'John<script>alert("XSS")</script>',
        email: '<EMAIL>',
        age: 25
      };

      const [isValid, result, errors] = validateServerRequest(testSchema, dataWithXSS);

      expect(isValid).toBe(true);
      expect(result?.name).not.toContain('<script>');
      expect(errors).toEqual([]);
    });

    test('handles unexpected errors gracefully', () => {
      // Mock schema.safeParse to throw an error
      const originalSafeParse = testSchema.safeParse;
      testSchema.safeParse = jest.fn().mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      try {
        const [isValid, result, errors] = validateServerRequest(testSchema, {});

        expect(isValid).toBe(false);
        expect(result).toBeNull();
        expect(errors).toHaveLength(1);
        expect(errors[0]).toEqual(expect.objectContaining({
          field: 'server',
          message: 'Server validation error occurred'
        }));
      } finally {
        // Restore the original function
        testSchema.safeParse = originalSafeParse;
      }
    });
  });

  describe('extractAndValidateRequestData', () => {
    test('extracts and validates JSON data', async () => {
      // Mock Request object with JSON content
      const requestBody = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 25
      };

      const request = new Request('https://example.com/api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      const [result, errors] = await extractAndValidateRequestData(request, testSchema);

      expect(result).toEqual(requestBody);
      expect(errors).toEqual([]);
    });

    test('extracts and validates form data', async () => {
      // Mock FormData
      const formData = new FormData();
      formData.append('name', 'John Doe');
      formData.append('email', '<EMAIL>');
      formData.append('age', '25'); // Note: FormData values are strings

      // Create a modified test schema for FormData (where age is a string)
      const formDataSchema = z.object({
        name: z.string().min(2),
        email: z.string().email(),
        age: z.string().refine(val => !isNaN(Number(val)) && Number(val) >= 18, {
          message: 'Must be at least 18 years old'
        })
      });

      // Mock request with FormData
      const request = new Request('https://example.com/api', {
        method: 'POST',
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        // In a real test, we'd need to properly mock formData support
      });

      // Mock the formData() method
      request.formData = jest.fn().mockResolvedValue(formData);

      // Mock the Object.fromEntries function for FormData
      const originalFromEntries = Object.fromEntries;
      Object.fromEntries = jest.fn().mockReturnValue({
        name: 'John Doe',
        email: '<EMAIL>',
        age: '25'
      });

      try {
        const [result, errors] = await extractAndValidateRequestData(request, formDataSchema);

        expect(request.formData).toHaveBeenCalled();
        expect(errors).toEqual([]);
        expect(result).toEqual({
          name: 'John Doe',
          email: '<EMAIL>',
          age: '25'
        });
      } finally {
        // Restore the original function
        Object.fromEntries = originalFromEntries;
      }
    });

    test('handles validation errors', async () => {
      // Mock Request object with invalid JSON content
      const invalidBody = {
        name: 'J', // Too short
        email: 'not-an-email',
        age: 16 // Too young
      };

      const request = new Request('https://example.com/api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(invalidBody)
      });

      const [result, errors] = await extractAndValidateRequestData(request, testSchema);

      expect(result).toBeNull();
      expect(errors.length).toBe(3);
    });

    test('handles request parsing errors', async () => {
      // Mock Request object that will throw when trying to parse JSON
      const request = new Request('https://example.com/api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: '{invalid-json'
      });

      // Mock json() to throw an error
      const originalJson = request.json;
      request.json = jest.fn().mockRejectedValue(new Error('Invalid JSON'));

      try {
        const [result, errors] = await extractAndValidateRequestData(request, testSchema);

        expect(result).toBeNull();
        expect(errors).toHaveLength(1);
        expect(errors[0]).toEqual(expect.objectContaining({
          field: 'request',
          message: 'Failed to process request data'
        }));
      } finally {
        // No need to restore since we're using a new Request object
      }
    });
  });
});