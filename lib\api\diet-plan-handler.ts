import { NextResponse } from "next/server"
import { BaseApiHandler } from "./base-api-handler"
import { DietPlanService } from "../services/diet-plan-service"

export class DietPlanHandler extends BaseApiHandler {
  /**
   * Get all diet plans
   */
  protected async get(req: Request, userId: string): Promise<NextResponse> {
    const dietPlans = await DietPlanService.findByTrainerId(userId)
    return NextResponse.json(dietPlans)
  }

  /**
   * Create a new diet plan
   */
  protected async post(req: Request, userId: string): Promise<NextResponse> {
    const data = await req.json()

    if (!data.title) {
      return NextResponse.json({ error: "Title is required" }, { status: 400 })
    }

    const dietPlan = await DietPlanService.create({
      title: data.title,
      description: data.description,
      calories: data.calories,
      protein: data.protein,
      carbs: data.carbs,
      fat: data.fat,
      notes: data.notes,
      meals: {
        create: data.meals?.map(meal => ({
          name: meal.name,
          description: meal.description,
          calories: meal.calories,
          protein: meal.protein,
          carbs: meal.carbs,
          fat: meal.fat,
          timeOfDay: meal.timeOfDay,
          foodSuggestions: meal.foodSuggestions
        })) || []
      },
      trainer: {
        connect: {
          id: userId
        }
      }
    })

    return NextResponse.json(dietPlan)
  }

  /**
   * Update a diet plan (not implemented in main route)
   */
  protected async put(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete a diet plan (not implemented in main route)
   */
  protected async delete(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}

export class DietPlanByIdHandler extends BaseApiHandler {
  /**
   * Get a diet plan by ID
   */
  protected async get(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    const dietPlan = await DietPlanService.findById(params.id)

    if (!dietPlan) {
      return NextResponse.json({ error: "Diet plan not found" }, { status: 404 })
    }

    // Check if the user owns the diet plan
    if (dietPlan.athleteId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    return NextResponse.json(dietPlan)
  }

  /**
   * Create a diet plan (not implemented in [id] route)
   */
  protected async post(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Update a diet plan
   */
  protected async put(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    const data = await req.json()

    // Check if the diet plan exists
    const dietPlan = await DietPlanService.findById(params.id)

    if (!dietPlan) {
      return NextResponse.json({ error: "Diet plan not found" }, { status: 404 })
    }

    // Check if the user owns the diet plan
    if (dietPlan.athleteId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const updatedDietPlan = await DietPlanService.update(params.id, data)

    return NextResponse.json(updatedDietPlan)
  }

  /**
   * Delete a diet plan
   */
  protected async delete(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    // Check if the diet plan exists
    const dietPlan = await DietPlanService.findById(params.id)

    if (!dietPlan) {
      return NextResponse.json({ error: "Diet plan not found" }, { status: 404 })
    }

    // Check if the user owns the diet plan
    if (dietPlan.athleteId !== userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    await DietPlanService.delete(params.id)

    return NextResponse.json({ success: true }, { status: 204 })
  }
}

export class DietPlanDuplicateHandler extends BaseApiHandler {
  /**
   * Get a diet plan to duplicate (not implemented)
   */
  protected async get(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Duplicate a diet plan
   */
  protected async post(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    let title = ''

    try {
      // Try to get title from request body if provided
      const body = await req.json().catch(() => ({}))
      title = body?.title || ''
    } catch (error) {
      // If no body or parsing fails, continue without a custom title
    }

    try {
      // Check if the diet plan exists
      const dietPlan = await DietPlanService.findById(params.id)

      if (!dietPlan) {
        return NextResponse.json({ error: "Diet plan not found" }, { status: 404 })
      }

      // If no title provided, create a default title
      const newTitle = title || `${dietPlan.title} (Copy)`

      const duplicatedDietPlan = await DietPlanService.duplicate(params.id, userId, newTitle)
      return NextResponse.json(duplicatedDietPlan)
    } catch (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }
  }

  /**
   * Update a duplicated diet plan (not implemented)
   */
  protected async put(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete a duplicated diet plan (not implemented)
   */
  protected async delete(req: Request, userId: string, params: { id: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}

export class DietPlanAssignHandler extends BaseApiHandler {
  /**
   * Get assigned diet plans (not implemented)
   */
  protected async get(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Assign a diet plan to a client
   */
  protected async post(req: Request, userId: string): Promise<NextResponse> {
    const { dietPlanId, clientId } = await req.json()

    if (!dietPlanId || !clientId) {
      return NextResponse.json({ error: "Diet plan ID and client ID are required" }, { status: 400 })
    }

    try {
      const result = await DietPlanService.assignToClient(dietPlanId, clientId, userId)
      return NextResponse.json(result)
    } catch (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
  }

  /**
   * Update an assigned diet plan (not implemented)
   */
  protected async put(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete an assigned diet plan (not implemented)
   */
  protected async delete(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}
