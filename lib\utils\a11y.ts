/**
 * Accessibility utilities for Clear Coach
 * Provides functions for ensuring accessible UI components
 */

/**
 * Calculate the contrast ratio between two colors
 * Based on WCAG 2.0 guidelines
 * @param foreground Foreground color in hex format (e.g., "#ffffff")
 * @param background Background color in hex format (e.g., "#000000")
 * @returns Contrast ratio (higher is better)
 */
export function getContrastRatio(foreground: string, background: string): number {
  const getLuminance = (color: string): number => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;
    
    const rgb = [r, g, b].map(component => {
      return component <= 0.03928
        ? component / 12.92
        : Math.pow((component + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * rgb[0] + 0.7152 * rgb[1] + 0.0722 * rgb[2];
  };
  
  const foregroundLuminance = getLuminance(foreground);
  const backgroundLuminance = getLuminance(background);
  
  const lighterLuminance = Math.max(foregroundLuminance, backgroundLuminance);
  const darkerLuminance = Math.min(foregroundLuminance, backgroundLuminance);
  
  return (lighterLuminance + 0.05) / (darkerLuminance + 0.05);
}

/**
 * Check if the contrast ratio meets WCAG AA standards
 * - 4.5:1 for normal text
 * - 3:1 for large text (18pt+)
 * @param foreground Foreground color in hex format
 * @param background Background color in hex format
 * @param isLargeText Whether the text is large (18pt+)
 * @returns Whether the contrast meets WCAG AA standards
 */
export function meetsContrastWCAG(
  foreground: string, 
  background: string, 
  isLargeText: boolean = false
): boolean {
  const ratio = getContrastRatio(foreground, background);
  return isLargeText ? ratio >= 3 : ratio >= 4.5;
}

/**
 * Create a skip-to-content link component
 * @param targetId ID of the main content element
 * @returns JSX for the skip link
 */
export function createSkipLink(targetId: string = 'main-content'): string {
  return `
    <a 
      href="#${targetId}" 
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground py-2 px-4 rounded"
    >
      Skip to content
    </a>
  `;
}
