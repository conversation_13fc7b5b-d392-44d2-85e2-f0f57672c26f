"use client"

import { format } from "date-fns"
import { MessageSquare, Calendar, PlusCircle, Search, ExternalLink } from "lucide-react"
import Link from "next/link"
import { useState, useEffect } from "react"
import { toast } from "sonner"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface Client {
  id: string
  name: string
  email: string
  avatarUrl: string | null
  subscriptionTier: string
  subscriptionStatus: "active" | "inactive" | "pending"
  joinedDate: string
  nextSessionDate: string | null
  lastActive: string
  completedWorkouts: number
  progressScore: number
}

export default function MyClientsPage() {
  const [loading, setLoading] = useState(true)
  const [clients, setClients] = useState<Client[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredClients, setFilteredClients] = useState<Client[]>([])
  
  useEffect(() => {
    // Mock client data
    const mockClients: Client[] = [
      {
        id: "c1",
        name: "John Smith",
        email: "<EMAIL>",
        avatarUrl: null,
        subscriptionTier: "Premium Coaching",
        subscriptionStatus: "active",
        joinedDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        nextSessionDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
        lastActive: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        completedWorkouts: 12,
        progressScore: 85
      },
      {
        id: "c2",
        name: "Emma Wilson",
        email: "<EMAIL>",
        avatarUrl: null,
        subscriptionTier: "Basic Coaching",
        subscriptionStatus: "active",
        joinedDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
        nextSessionDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        lastActive: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        completedWorkouts: 24,
        progressScore: 92
      },
      {
        id: "c3",
        name: "Michael Chen",
        email: "<EMAIL>",
        avatarUrl: null,
        subscriptionTier: "Premium Coaching",
        subscriptionStatus: "inactive",
        joinedDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
        nextSessionDate: null,
        lastActive: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        completedWorkouts: 5,
        progressScore: 40
      },
      {
        id: "c4",
        name: "Sophia Rodriguez",
        email: "<EMAIL>",
        avatarUrl: null,
        subscriptionTier: "Elite Coaching",
        subscriptionStatus: "active",
        joinedDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        nextSessionDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
        lastActive: new Date(Date.now() - 0.5 * 24 * 60 * 60 * 1000).toISOString(),
        completedWorkouts: 6,
        progressScore: 78
      }
    ];
    
    // Simulate loading
    const timer = setTimeout(() => {
      setClients(mockClients);
      setFilteredClients(mockClients);
      setLoading(false);
    }, 500);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Filter clients based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredClients(clients);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    const filtered = clients.filter(client => 
      client.name.toLowerCase().includes(query) || 
      client.email.toLowerCase().includes(query) ||
      client.subscriptionTier.toLowerCase().includes(query)
    );
    
    setFilteredClients(filtered);
  }, [searchQuery, clients]);
  
  const handleMessageClient = (clientId: string, clientName: string) => {
    toast.success(`Message sent to ${clientName}`);
  };
  
  const handleScheduleSession = (clientId: string, clientName: string) => {
    toast.success(`Session scheduled with ${clientName}`);
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">My Clients</h1>
          <p className="text-muted-foreground">
            Manage and monitor your client progress
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search clients..."
              className="pl-8 w-full sm:w-[200px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Client
          </Button>
        </div>
      </div>
      
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Clients ({clients.length})</TabsTrigger>
          <TabsTrigger value="active">Active ({clients.filter(c => c.subscriptionStatus === "active").length})</TabsTrigger>
          <TabsTrigger value="inactive">Inactive ({clients.filter(c => c.subscriptionStatus !== "active").length})</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all">
          <div className="grid gap-4">
            {filteredClients.length > 0 ? (
              filteredClients.map(client => (
                <ClientCard 
                  key={client.id} 
                  client={client} 
                  onMessage={handleMessageClient}
                  onSchedule={handleScheduleSession}
                />
              ))
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center p-6">
                  <p className="text-muted-foreground mb-4">No clients found matching your search.</p>
                  <Button 
                    variant="outline" 
                    onClick={() => setSearchQuery("")}
                  >
                    Clear Search
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="active">
          <div className="grid gap-4">
            {filteredClients
              .filter(client => client.subscriptionStatus === "active")
              .map(client => (
                <ClientCard 
                  key={client.id} 
                  client={client} 
                  onMessage={handleMessageClient}
                  onSchedule={handleScheduleSession}
                />
              ))}
          </div>
        </TabsContent>
        
        <TabsContent value="inactive">
          <div className="grid gap-4">
            {filteredClients
              .filter(client => client.subscriptionStatus !== "active")
              .map(client => (
                <ClientCard 
                  key={client.id} 
                  client={client} 
                  onMessage={handleMessageClient}
                  onSchedule={handleScheduleSession}
                />
              ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface ClientCardProps {
  client: Client
  onMessage: (clientId: string, clientName: string) => void
  onSchedule: (clientId: string, clientName: string) => void
}

function ClientCard({ client, onMessage, onSchedule }: ClientCardProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row gap-4 md:gap-6 justify-between">
          {/* Client Info */}
          <div className="flex gap-4">
            <Avatar className="h-12 w-12">
              <AvatarImage src={client.avatarUrl || ""} alt={client.name} />
              <AvatarFallback>{client.name.charAt(0)}</AvatarFallback>
            </Avatar>
            
            <div>
              <h3 className="font-bold text-lg">{client.name}</h3>
              <p className="text-sm text-muted-foreground">{client.email}</p>
              <div className="flex items-center mt-1 gap-2">
                <Badge variant={client.subscriptionStatus === "active" ? "default" : "secondary"}>
                  {client.subscriptionStatus === "active" ? "Active" : "Inactive"}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {client.subscriptionTier}
                </span>
              </div>
            </div>
          </div>
          
          {/* Client Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Joined</p>
              <p className="font-medium">{format(new Date(client.joinedDate), "MMM d, yyyy")}</p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Last Active</p>
              <p className="font-medium">{format(new Date(client.lastActive), "MMM d, yyyy")}</p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Workouts</p>
              <p className="font-medium">{client.completedWorkouts}</p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Progress</p>
              <p className="font-medium">{client.progressScore}%</p>
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex justify-end gap-2 md:flex-col">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onMessage(client.id, client.name)}
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              Message
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onSchedule(client.id, client.name)}
              disabled={client.subscriptionStatus !== "active"}
            >
              <Calendar className="mr-2 h-4 w-4" />
              Schedule
            </Button>
            
            <Button asChild variant="ghost" size="sm">
              <Link href={`/dashboard/clients/${client.id}`}>
                <ExternalLink className="mr-2 h-4 w-4" />
                View
              </Link>
            </Button>
          </div>
        </div>
        
        {client.nextSessionDate && (
          <div className="mt-4 pt-4 border-t text-sm">
            <span className="text-muted-foreground">Next Session:</span>{" "}
            <span className="font-medium">{format(new Date(client.nextSessionDate), "EEEE, MMMM d, yyyy 'at' h:mm a")}</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 