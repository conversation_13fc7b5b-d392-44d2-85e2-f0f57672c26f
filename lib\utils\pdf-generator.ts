import { TrainingPlanTemplate } from '@prisma/client';

/**
 * Generates a PDF from a training plan template
 * This is a placeholder that will be replaced with actual PDF generation code
 * using jspdf and html2canvas once those dependencies are installed
 */
export async function generatePdfFromTemplate(template: TrainingPlanTemplate): Promise<Blob> {
  // This is a placeholder implementation
  // In a real implementation, we would use jspdf and html2canvas to generate a PDF
  
  // Create a simple text representation of the template
  const content = `
    Training Plan: ${template.title}
    Description: ${template.description || 'No description'}
    Difficulty: ${template.difficulty || 'Not specified'}
    
    Weeks:
    ${JSON.stringify(template.weeks, null, 2)}
  `;
  
  // Create a blob with the content
  return new Blob([content], { type: 'application/pdf' });
}

/**
 * Formats a training plan template for PDF display
 * Converts the JSON structure into a more readable format
 */
export function formatTrainingPlanForPdf(template: TrainingPlanTemplate): string {
  let formattedContent = `
    <h1>${template.title}</h1>
    <p>${template.description || 'No description'}</p>
    <p><strong>Difficulty:</strong> ${template.difficulty || 'Not specified'}</p>
  `;
  
  // Parse the weeks data (assuming it's stored as JSON)
  const weeks = typeof template.weeks === 'string' 
    ? JSON.parse(template.weeks) 
    : template.weeks;
  
  // Format each week
  Object.entries(weeks).forEach(([weekKey, weekData]: [string, any]) => {
    formattedContent += `<h2>${weekData.title || `Week ${weekKey.replace('week', '')}`}</h2>`;
    
    // Format workouts if they exist
    if (weekData.workouts && Array.isArray(weekData.workouts)) {
      weekData.workouts.forEach((workout: any, index: number) => {
        formattedContent += `
          <h3>${workout.title || `Workout ${index + 1}`}</h3>
          <p>${workout.description || ''}</p>
          <ul>
        `;
        
        // Format exercises if they exist
        if (workout.exercises && Array.isArray(workout.exercises)) {
          workout.exercises.forEach((exercise: any) => {
            formattedContent += `
              <li>
                <strong>${exercise.name}</strong>
                ${exercise.sets ? ` - ${exercise.sets} sets` : ''}
                ${exercise.reps ? ` x ${exercise.reps} reps` : ''}
                ${exercise.weight ? ` @ ${exercise.weight}` : ''}
                ${exercise.rest ? ` (Rest: ${exercise.rest}s)` : ''}
              </li>
            `;
          });
        }
        
        formattedContent += `</ul>`;
      });
    }
  });
  
  return formattedContent;
}
