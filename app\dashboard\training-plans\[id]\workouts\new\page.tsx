import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { WorkoutForm } from "@/components/forms/workout-form"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// Use 'any' as a targeted workaround for props typing issue
export default async function NewWorkoutPage(props: any) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    redirect("/auth/signin")
  }

  // Safely access params
  const trainingPlanId = props.params?.id;
  if (!trainingPlanId) {
      console.error("Training Plan ID missing from params");
      // Redirect or show error if ID is missing
      redirect("/dashboard/training-plans"); 
  }

  // Fetch plan to ensure ownership and get weeks
  const plan = await prisma.trainingPlan.findUnique({
    where: {
      id: trainingPlanId,
      athleteId: session.user.id, // Verify ownership
    },
    include: {
      weeks: { orderBy: { order: 'asc' } }, // Fetch weeks in order
    },
  })

  if (!plan) {
    console.error(`Training plan ${trainingPlanId} not found or user ${session.user.id} does not own it.`);
    redirect("/dashboard/training-plans")
  }

  return (
    <div className="container mx-auto py-6 max-w-3xl">
      <h1 className="text-2xl font-bold mb-6">Add New Workout</h1>
      {/* Pass id prop (which is the trainingPlanId) and weeks */}
      <WorkoutForm id={trainingPlanId} weeks={plan.weeks} />
    </div>
  )
} 