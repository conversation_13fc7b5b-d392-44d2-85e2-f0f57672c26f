import { PrismaClient } from "@prisma/client"
import { compare } from "bcryptjs"

const prisma = new PrismaClient()

async function main() {
  const email = "<EMAIL>"
  const password = "test123"
  
  console.log("Checking user with email:", email)
  
  const user = await prisma.user.findUnique({
    where: {
      email: email
    }
  })

  if (!user) {
    console.log("User not found!")
    return
  }

  console.log("User found:", {
    id: user.id,
    email: user.email,
    fullName: user.fullName,
    role: user.role,
    createdAt: user.createdAt
  })

  // Test password verification
  const isPasswordValid = await compare(password, user.password)
  console.log("Password verification test:", isPasswordValid)
}

main()
  .catch((error: unknown) => {
    if (error instanceof Error) {
      console.error("Error checking user:", error.message)
    } else {
      console.error("Unknown error checking user")
    }
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 