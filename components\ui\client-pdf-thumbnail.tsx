"use client"

import { useState, useEffect } from "react"
import { FileText } from "lucide-react"
import Image from "next/image"

interface ClientPdfThumbnailProps {
  pdfUrl?: string
  title: string
  className?: string
}

export function ClientPdfThumbnail({ pdfUrl, title, className = "" }: ClientPdfThumbnailProps) {
  const [thumbnailUrl, setThumbnailUrl] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(false)
  
  useEffect(() => {
    if (!pdfUrl) {
      setIsLoading(false)
      return
    }
    
    // Check if PdfThumbnailGenerator is available
    if (typeof window !== 'undefined' && window.PdfThumbnailGenerator) {
      setIsLoading(true)
      
      // Generate thumbnail
      window.PdfThumbnailGenerator.generateThumbnail(pdfUrl)
        .then((dataUrl: string) => {
          setThumbnailUrl(dataUrl)
          setIsLoading(false)
        })
        .catch((err: any) => {
          console.error('Error generating thumbnail:', err)
          setError(true)
          setIsLoading(false)
        })
    } else {
      // Fallback if PdfThumbnailGenerator is not available
      setIsLoading(false)
    }
  }, [pdfUrl])
  
  // If we have a thumbnail URL, use it
  if (thumbnailUrl) {
    return (
      <div className={`relative w-full h-full ${className}`}>
        <img
          src={thumbnailUrl}
          alt={`Thumbnail for ${title}`}
          className="w-full h-full object-cover rounded-md"
        />
        <span className="absolute bottom-2 right-2 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100 text-xs px-2 py-1 rounded-md">
          PDF
        </span>
      </div>
    )
  }
  
  // If we're loading, show a loading state
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-full bg-muted rounded-md ${className}`}>
        <div className="animate-pulse">
          <FileText className="h-10 w-10 text-muted-foreground" />
        </div>
      </div>
    )
  }
  
  // If we have an error or no PDF URL, show a placeholder
  return (
    <div className={`flex items-center justify-center h-full bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-md ${className}`}>
      <FileText className="h-16 w-16 text-blue-500 dark:text-blue-400" />
      <span className="absolute bottom-2 right-2 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100 text-xs px-2 py-1 rounded-md">
        PDF
      </span>
    </div>
  )
}

// Add the PdfThumbnailGenerator type for TypeScript
declare global {
  interface Window {
    PdfThumbnailGenerator: {
      generateThumbnail: (pdfUrl: string, width?: number, height?: number) => Promise<string>
      generatePlaceholder: (width?: number, height?: number) => string
    }
  }
}
