/**
 * Server-side validation utilities
 *
 * These utilities mirror client-side validation but are used on the server
 * to ensure consistent validation across both environments.
 */

import { z } from 'zod';
import { checkoutFormSchema, FormValidationError } from './formValidation';
import { sanitizeFormData } from './sanitization';

/**
 * Validate incoming request data on the server side
 * @param schema Zod schema to validate against
 * @param data Request data to validate
 * @returns Tuple with [isValid, sanitizedData, errors]
 */
export function validateServerRequest<T>(
  schema: z.ZodType<T>,
  data: unknown
): [boolean, T | null, FormValidationError[]] {
  try {
    // First sanitize the data
    const sanitizedData = typeof data === 'object' && data !== null
      ? sanitizeFormData(data as Record<string, unknown>)
      : data;

    // Then validate using the schema
    const result = schema.safeParse(sanitizedData);

    if (result.success) {
      return [true, result.data, []];
    }

    // Format validation errors
    const errors = result.error.errors.map(err => ({
      field: err.path.join('.') || 'value',
      message: err.message
    }));

    return [false, null, errors];
  } catch (error) {
    // Handle unexpected errors
    return [
      false,
      null,
      [{ field: 'server', message: 'Server validation error occurred' }]
    ];
  }
}

/**
 * Predefined server validation functions for common schemas
 */
export const serverValidators = {
  checkout: (data: unknown) => validateServerRequest(checkoutFormSchema, data),
  // Add more validators for other schemas as needed
};

/**
 * Type guard to check if a value is a valid JSON object
 */
export function isValidJsonObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

/**
 * Extract and validate data from a request
 * @param request Next.js or Express request object
 * @returns Sanitized and validated data or null with errors
 */
export async function extractAndValidateRequestData<T>(
  request: Request,
  schema: z.ZodType<T>
): Promise<[T | null, FormValidationError[]]> {
  try {
    const contentType = request.headers.get('content-type');
    let data: unknown;

    if (contentType?.includes('application/json')) {
      // Handle case where request.json is not available in test environment
      if (typeof request.json === 'function') {
        data = await request.json();
      } else if (request.body) {
        // For test environments where request.json is mocked or not available
        try {
          const bodyText = typeof request.body === 'string'
            ? request.body
            : JSON.stringify(request.body);
          data = JSON.parse(bodyText);
        } catch (e) {
          throw new Error('Invalid JSON');
        }
      } else {
        throw new Error('Request body is empty');
      }
    } else if (contentType?.includes('multipart/form-data')) {
      const formData = await request.formData();
      data = Object.fromEntries(formData);
    } else {
      // Default to URL search params for GET requests
      const url = new URL(request.url);
      data = Object.fromEntries(url.searchParams);
    }

    const [isValid, validatedData, errors] = validateServerRequest(schema, data);
    return [validatedData, errors];
  } catch (error) {
    console.error('Error extracting or validating request data:', error);
    return [
      null,
      [{ field: 'request', message: 'Failed to process request data' }]
    ];
  }
}