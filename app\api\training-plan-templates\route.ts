// API route handler for training plan templates
import { TrainingPlanTemplateHandler } from "@/lib/api/training-plan-template-handler"
import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

const handler = new TrainingPlanTemplateHandler()

export async function GET(req: Request) {
  try {
    console.log('GET /api/training-plan-templates - Request received')
    const session = await getServerSession(authOptions)
    console.log('User session:', session?.user?.email, 'Role:', session?.user?.role)

    // Don't read the response data before returning it
    // This was causing the ReadableStream to be locked
    return handler.handleGet(req)
  } catch (error) {
    console.error('GET /api/training-plan-templates - Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(req: Request) {
  try {
    console.log('POST /api/training-plan-templates - Request received')
    const session = await getServerSession(authOptions)
    console.log('User session:', session?.user?.email, 'Role:', session?.user?.role)

    return handler.handlePost(req)
  } catch (error) {
    console.error('POST /api/training-plan-templates - Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(req: Request, { params }: { params: { id: string } }) {
  try {
    console.log('DELETE /api/training-plan-templates - Request received')
    const session = await getServerSession(authOptions)
    console.log('User session:', session?.user?.email, 'Role:', session?.user?.role)

    return handler.handleDelete(req, params)
  } catch (error) {
    console.error('DELETE /api/training-plan-templates - Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}