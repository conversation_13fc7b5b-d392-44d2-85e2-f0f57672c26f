// API route handler for diet plan by ID
import { DietPlanByIdHandler } from "@/lib/api/diet-plan-handler";

const handler = new DietPlanByIdHandler();

export async function GET(req: Request, { params }: { params: { id: string } }) {
  return handler.handleGet(req, params);
}

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  return handler.handlePut(req, params);
}

export async function PATCH(req: Request, { params }: { params: { id: string } }) {
  return handler.handlePut(req, params);
}

export async function DELETE(req: Request, { params }: { params: { id: string } }) {
  return handler.handleDelete(req, params);
}