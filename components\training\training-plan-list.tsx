"use client"

import { CalendarD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>bell } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

interface TrainingPlanListProps {
  plans: any[]
}

export function TrainingPlanList({ plans }: TrainingPlanListProps) {
  const router = useRouter();
  return (
    <div className="flex flex-col gap-4">
      <h3 className="text-lg font-medium">Your Training Plans</h3>

      {plans.length > 0 ? (
        plans.map((plan) => (
          <Card key={plan.id} className="group relative cursor-pointer transition-all hover:shadow-md" onClick={() => {
              console.log('Card clicked, redirecting to:', `/dashboard/training-plans/preview/${plan.training_plans.id}`);
              router.push(`/dashboard/training-plans/preview/${plan.training_plans.id}`);
            }}>
            <div className="absolute inset-0 z-10"></div>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>{plan.training_plans.title}</CardTitle>
                  <CardDescription className="flex items-center gap-1 mt-1">
                    <Avatar className="h-5 w-5">
                      <AvatarImage
                        src={plan.training_plans.users.avatar_url || "/placeholder.svg?height=20&width=20"}
                        alt={plan.training_plans.users.full_name}
                      />
                      <AvatarFallback>{plan.training_plans.users.full_name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <span>By {plan.training_plans.users.full_name}</span>
                  </CardDescription>
                </div>
                <Badge variant={plan.status === "active" ? "default" : "outline"}>{plan.status}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-3">{plan.training_plans.description}</p>
              <div className="flex gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <CalendarDays className="h-4 w-4 text-muted-foreground" />
                  <span>{plan.training_plans.duration_weeks} weeks</span>
                </div>
                <div className="flex items-center gap-1">
                  <Dumbbell className="h-4 w-4 text-muted-foreground" />
                  <span>{plan.training_plans.difficulty}</span>
                </div>
              </div>
              {plan.weeks && plan.weeks.length > 0 ? (
                <div className="mt-3">
                  <ul className="list-disc pl-5 space-y-1">
                    {plan.weeks.map((week, weekIndex) => (
                      <li key={weekIndex}>
                        Week {weekIndex + 1}: {week.focus || 'General Conditioning'}
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground italic">
                  Weekly breakdown isn&apos;t available for this plan.
                </p>
              )}
            </CardContent>
            <CardFooter className="relative z-20" onClick={(e) => e.stopPropagation()}>
              <Button asChild variant="outline" className="w-full">
                <Link href={`/dashboard/training-plans/preview/${plan.training_plans.id}`}>
                  <span>Preview Plan</span>
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        ))
      ) : (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground">
              You don&apos;t have an active training plan assigned.
            </p>
            <Button className="mt-4" asChild>
              <Link href="/dashboard/my-subscriptions">
                Explore Subscription Plans
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

