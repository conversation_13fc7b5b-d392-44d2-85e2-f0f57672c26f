name: End-to-End Tests

on:
  pull_request:
    branches: [ main ]
  push:
    branches: [ feature/*, develop ]

permissions:
  contents: read
  actions: write

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Install Playwright browsers
        run: npx playwright install --with-deps chromium

      - name: Set up environment variables
        run: |
          echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_db" >> .env
          echo "NEXTAUTH_SECRET=test_secret" >> .env
          echo "NEXTAUTH_URL=http://localhost:3000" >> .env

      - name: Run linting
        run: npm run lint

      - name: Set up test database
        run: npx prisma migrate deploy

      - name: Run unit tests
        run: npm test

      - name: Start application in background
        run: |
          npm run dev &
          sleep 10 # Give the app time to start

      - name: Run client journey E2E test
        run: npm run test:e2e:client

      - name: Run premium client journey E2E test
        run: npm run test:e2e:premium

      - name: Run client upgrade journey E2E test
        run: npm run test:e2e:upgrade

      - name: Run trainer journey E2E test
        run: npm run test:e2e:trainer

      - name: Run coaching journey E2E test
        run: npm run test:e2e:coaching

      - name: Ensure Playwright report directory exists
        run: mkdir -p playwright-report/

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
