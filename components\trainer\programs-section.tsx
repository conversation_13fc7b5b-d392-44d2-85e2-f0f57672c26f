"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, CheckCheck } from "lucide-react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { TabsContent } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { DigitalProduct } from "@/types/trainer"

interface ProgramsSectionProps {
  digitalProducts: DigitalProduct[]
  onSelectProduct: (product: DigitalProduct) => void
}

export function ProgramsSection({ 
  digitalProducts,
  onSelectProduct
}: ProgramsSectionProps) {
  return (
    <TabsContent value="programs" className="space-y-4">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-2">Digital Programs</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Instant access to premium workout and nutrition programs
        </p>
      </div>

      {digitalProducts.length > 0 ? (
        <div className="space-y-6">
          {digitalProducts.map((product, index) => (
            <Card key={product.id} className="overflow-hidden card-hover">
              <div className="flex flex-col md:flex-row">
                <div className="md:w-2/5">
                  <div className="relative h-48 md:h-full">
                    {product.thumbnailUrl ? (
                      <Image
                        src={product.thumbnailUrl}
                        alt={product.title}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-muted flex items-center justify-center">
                        <span className="text-muted-foreground">No image</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex flex-col md:w-3/5 md:pl-5">
                  <CardHeader className="pb-2">
                    <div className="space-y-2">
                      <CardTitle className="text-xl font-bold">{product.title}</CardTitle>
                      <CardDescription className="text-sm">{product.description}</CardDescription>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="flex-grow">
                    <div className="flex flex-wrap gap-2 mb-4">
                      <Badge variant="outline" className="flex items-center bg-primary/5 hover:bg-primary/10">
                        <CheckCheck className="mr-1 h-3 w-3" />
                        Instant Access
                      </Badge>
                      <Badge variant="outline" className="flex items-center bg-primary/5 hover:bg-primary/10">
                        <Calendar className="mr-1 h-3 w-3" />
                        Lifetime Updates
                      </Badge>
                      <Badge variant="outline" className="flex items-center bg-primary/5 hover:bg-primary/10">
                        <Star className="mr-1 h-3 w-3" />
                        Premium Content
                      </Badge>
                    </div>
                  </CardContent>
                  
                  <CardFooter className="flex justify-between items-center border-t pt-4">
                    <div className="text-xl font-bold">${product.price}</div>
                    <Button onClick={() => onSelectProduct(product)}>
                      Purchase Now
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center p-10 border rounded-lg bg-muted/40">
          <h3 className="text-xl font-medium mb-2">Digital Programs Coming Soon</h3>
          <p className="text-muted-foreground mb-6">
            This trainer doesn't have any digital programs available yet.
            Check back later for updates.
          </p>
        </div>
      )}
    </TabsContent>
  )
} 