import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { TrainingPlan, Week, Workout, Exercise } from '@prisma/client'; // Import base types

// Define the expected nested structure from the frontend
type ExerciseData = Omit<Exercise, 'id' | 'workoutId' | 'createdAt' | 'updatedAt'>;
type WorkoutData = Omit<Workout, 'id' | 'weekId' | 'createdAt' | 'updatedAt' | 'trainingPlanId'> & {
  exercises: ExerciseData[];
};
type WeekData = Omit<Week, 'id' | 'trainingPlanId' | 'createdAt' | 'updatedAt'> & {
  workouts: WorkoutData[];
};
type TrainingPlanPatchData = Omit<TrainingPlan, 'id' | 'athleteId' | 'createdAt' | 'updatedAt'> & {
  weeks: WeekData[];
};

// Define context type for dynamic route
interface RouteContext {
  params: {
    id?: string; // Use optional chain for safety, though it should be present
  };
}

export async function GET(
  request: Request,
  context: RouteContext
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const trainingPlanId = context.params.id;
    if (!trainingPlanId) {
        return new NextResponse("Training plan ID missing in URL", { status: 400 });
    }

    const plan = await prisma.trainingPlan.findUnique({
      where: {
        id: trainingPlanId,
      },
      include: {
        athlete: {
            select: { id: true, athleteSubscriptions: { where: { status: 'active' } } }
        },
        weeks: {
          orderBy: { order: 'asc' },
          include: {
            workouts: {
              orderBy: { order: 'asc' },
              include: {
                exercises: { orderBy: { order: 'asc' } },
              },
            },
          },
        },
      },
    })

    if (!plan) {
      return new NextResponse("Not found", { status: 404 })
    }

    // Log session details before authorization check
    console.log(`[Auth Check] User ID: ${session.user.id}, Role: ${session.user.role} for Plan ID: ${trainingPlanId}`);

    const isOwner = plan.athleteId === session.user.id;
    const isAdmin = session.user.role === "admin";
    const isTrainer = session.user.role === "trainer";
    const isSubscribedClient = session.user.role === "client" && 
                              plan.athlete?.athleteSubscriptions?.some(sub => sub.clientId === session.user.id);

    if (!isOwner && !isAdmin && !isTrainer && !isSubscribedClient) {
       return new NextResponse("Forbidden: You do not have access to this plan", { status: 403 })
    }

    return NextResponse.json(plan)
  } catch (error) {
    console.error("[TRAINING_PLAN_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function PATCH(
  request: Request,
  context: RouteContext
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) { 
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const trainingPlanId = context.params.id;
     if (!trainingPlanId) {
        return new NextResponse("Training plan ID missing in URL", { status: 400 });
    }

    // Type the incoming body according to our expected structure
    const body: TrainingPlanPatchData = await request.json()
    const { weeks, ...planData } = body // Separate top-level fields from weeks array

    // --- Authorization Check ---
    const plan = await prisma.trainingPlan.findUnique({
      where: { id: trainingPlanId },
      select: { athleteId: true }
    })

    if (!plan) {
      return new NextResponse("Not found", { status: 404 })
    }

    // Update the PATCH authorization to allow owner, admin, OR trainer
    const isOwner = plan.athleteId === session.user.id;
    const isAdmin = session.user.role === "admin";
    const isTrainer = session.user.role === "trainer"; 

    if (!isOwner && !isAdmin && !isTrainer) { // Allow owner, admin, or trainer
      return new NextResponse("Forbidden: You cannot edit this plan", { status: 403 })
    }

    // --- Transactional Update --- 
    const updatedPlan = await prisma.$transaction(async (tx) => {
      // 1. Update top-level TrainingPlan fields
      await tx.trainingPlan.update({
        where: { id: trainingPlanId },
        data: {
          title: planData.title,
          description: planData.description,
          difficulty: planData.difficulty,
          // Add any other top-level fields from planData here
        },
      });

      // 2. Delete existing nested data (Exercises -> Workouts -> Weeks)
      // Find existing week IDs to find workouts
      const existingWeeks = await tx.week.findMany({
        where: { trainingPlanId: trainingPlanId },
        select: { id: true }
      });
      const existingWeekIds = existingWeeks.map(w => w.id);

      if (existingWeekIds.length > 0) {
          // Find existing workout IDs to find exercises
          const existingWorkouts = await tx.workout.findMany({
              where: { weekId: { in: existingWeekIds } },
              select: { id: true }
          });
          const existingWorkoutIds = existingWorkouts.map(wo => wo.id);

          if (existingWorkoutIds.length > 0) {
              // Delete exercises first
              await tx.exercise.deleteMany({ 
                  where: { workoutId: { in: existingWorkoutIds } } 
              });
          }
          // Delete workouts second
          await tx.workout.deleteMany({ 
              where: { weekId: { in: existingWeekIds } } 
          });
      }
      // Delete weeks last
      await tx.week.deleteMany({ 
          where: { trainingPlanId: trainingPlanId } 
      });

      // 3. Recreate Weeks, Workouts, and Exercises from the request body
      for (const [weekIndex, week] of weeks.entries()) {
        const createdWeek = await tx.week.create({
          data: {
            trainingPlan: { connect: { id: trainingPlanId } },
            weekNumber: week.weekNumber ?? (weekIndex + 1),
            order: week.order ?? weekIndex,
            // Add other week fields if they exist in WeekData
            workouts: {
              create: week.workouts.map((workout, workoutIndex) => ({
                trainingPlan: { connect: { id: trainingPlanId } }, // Connect workout to plan
                title: workout.title,
                description: workout.description,
                order: workout.order ?? workoutIndex,
                type: workout.type,
                // Add other workout fields if they exist in WorkoutData
                exercises: {
                  create: workout.exercises.map((exercise, exerciseIndex) => ({
                    name: exercise.name,
                    description: exercise.description,
                    sets: exercise.sets,
                    reps: exercise.reps,
                    duration: exercise.duration,
                    restTime: exercise.restTime,
                    videoUrl: exercise.videoUrl,
                    thumbnailUrl: exercise.thumbnailUrl,
                    muscleGroup: exercise.muscleGroup,
                    type: exercise.type,
                    equipment: exercise.equipment,
                    difficulty: exercise.difficulty,
                    calories: exercise.calories,
                    order: exercise.order ?? exerciseIndex,
                    isTemplate: false, // Ensure these instances are not marked as templates
                    // createdBy: session.user.id, // Optional: Link exercise instance to user?
                  })),
                },
              })),
            },
          },
        });
      }

      // 4. Fetch the fully updated plan with includes to return
      const result = await tx.trainingPlan.findUnique({
        where: { id: trainingPlanId },
        include: { // Use the same includes as the GET request
           weeks: {
             orderBy: { order: 'asc' },
             include: {
               workouts: {
                 orderBy: { order: 'asc' },
                 include: {
                   exercises: { orderBy: { order: 'asc' } },
                 },
               },
             },
           },
        }
      });
      if (!result) {
          // This should not happen if the initial findUnique succeeded
          throw new Error("Failed to fetch updated plan after transaction.");
      }
      return result;
    });

    return NextResponse.json(updatedPlan);

  } catch (error) {
    console.error("[TRAINING_PLAN_UPDATE]", error);
    // Consider more specific error handling (e.g., validation errors)
    return new NextResponse("Internal error", { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  context: RouteContext
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const trainingPlanId = context.params.id;
     if (!trainingPlanId) {
        return new NextResponse("Training plan ID missing in URL", { status: 400 });
    }

    const plan = await prisma.trainingPlan.findUnique({
      where: {
        id: trainingPlanId,
      },
       select: { athleteId: true }
    })

    if (!plan) {
      // Already deleted or never existed, return success
      return new NextResponse(null, { status: 204 })
    }

    // Authorization check
    if (plan.athleteId !== session.user.id && session.user.role !== "admin") {
      return new NextResponse("Forbidden: You cannot delete this plan", { status: 403 })
    }

    // Use transaction for deletion if there are complex relations or potential race conditions
    // For simple delete, transaction might be overkill but safer
    await prisma.$transaction(async (tx) => {
        // Cascading delete should handle related weeks, workouts, exercises if setup in schema
        // If not, manual deletion steps like in PATCH would be needed here before deleting the plan
        await tx.trainingPlan.delete({
            where: { id: trainingPlanId },
        });
    });

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[TRAINING_PLAN_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 