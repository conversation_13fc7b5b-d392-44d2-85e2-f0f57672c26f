import { spawn } from "child_process"
import { PrismaClient } from "@prisma/client"
import chalk from "chalk"

const prisma = new PrismaClient({
  log: [
    { emit: "event", level: "query" },
    { emit: "event", level: "error" },
    { emit: "event", level: "info" },
    { emit: "event", level: "warn" },
  ],
})

async function startDevServer() {
  console.log(chalk.blue("🚀 Starting development server with monitoring..."))

  // Start Next.js dev server
  const nextDev = spawn("npm", ["run", "dev"], {
    stdio: "inherit",
  })

  // Monitor database queries
  prisma.$on("query", (e) => {
    console.log(chalk.cyan("📝 Query:"), e.query)
    console.log(chalk.gray("⏱️  Duration:"), `${e.duration}ms`)
  })

  // Monitor database errors
  prisma.$on("error", (e) => {
    console.log(chalk.red("❌ Database Error:"), e.message)
  })

  // Monitor API routes
  process.on("unhandledRejection", (reason, promise) => {
    console.log(chalk.red("🔥 Unhandled Rejection at:"), promise)
    console.log(chalk.red("Reason:"), reason)
  })

  // Monitor authentication
  const authEvents = new Set()
  prisma.$use(async (params, next) => {
    if (params.model === "User" && params.action === "findUnique") {
      console.log(chalk.yellow("🔐 Auth attempt for user:"), params.args.where.email)
      authEvents.add(params.args.where.email)
    }
    return next(params)
  })

  // Health check
  setInterval(async () => {
    try {
      await prisma.$queryRaw`SELECT 1`
      console.log(chalk.green("💚 Database connection healthy"))
    } catch (error) {
      console.log(chalk.red("💔 Database connection error:"), error)
    }
  }, 30000) // Check every 30 seconds

  // Monitor feature usage
  const featureUsage = new Map()
  prisma.$use(async (params, next) => {
    const feature = `${params.model}.${params.action}`
    featureUsage.set(feature, (featureUsage.get(feature) || 0) + 1)
    return next(params)
  })

  // Print feature usage stats every 5 minutes
  setInterval(() => {
    console.log(chalk.blue("\n📊 Feature Usage Statistics:"))
    for (const [feature, count] of featureUsage.entries()) {
      console.log(chalk.cyan(`${feature}:`), count)
    }
    console.log()
  }, 300000)

  // Cleanup on exit
  process.on("SIGINT", async () => {
    console.log(chalk.yellow("\n🛑 Shutting down..."))
    await prisma.$disconnect()
    nextDev.kill()
    process.exit()
  })
}

startDevServer().catch((error) => {
  console.error(chalk.red("❌ Error starting development server:"), error)
  process.exit(1)
}) 