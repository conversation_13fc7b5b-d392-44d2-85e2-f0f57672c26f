import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const trainerId = session.user.id
    console.log(`Fetching clients for trainer: ${trainerId}`)

    // Get clients through coaching relationships
    const coachingRelationships = await prisma.coachingRelationship.findMany({
      where: {
        trainerId: trainerId,
        status: "active"
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            avatarUrl: true,
            role: true
          }
        }
      }
    })

    // Map to client format for compatibility
    const clients = coachingRelationships.map(relationship => ({
      id: relationship.client.id,
      name: relationship.client.name,
      email: relationship.client.email,
      avatarUrl: relationship.client.avatarUrl,
      subscription: 'Premium Coaching', // All clients are premium coaching
      relationshipId: relationship.id
    }))

    console.log(`Found ${clients.length} clients for trainer ${trainerId}`)

    return NextResponse.json(clients)
  } catch (error) {
    console.error("Error fetching trainer clients:", error)
    return NextResponse.json(
      { error: "An error occurred while fetching clients" },
      { status: 500 }
    )
  }
}
