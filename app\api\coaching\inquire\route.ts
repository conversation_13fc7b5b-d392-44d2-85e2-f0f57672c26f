import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const {
      name,
      email,
      phone,
      goals,
      experience,
      availability,
      message,
      athleteId,
    } = body

    if (!name || !email || !goals || !experience || !availability || !athleteId) {
      return new NextResponse("Missing required fields", { status: 400 })
    }

    // Create coaching inquiry
    const inquiry = await prisma.coachingInquiry.create({
      data: {
        name,
        email,
        phone,
        goals,
        experience,
        availability,
        message,
        athleteId,
        status: "PENDING"
      },
    })

    // Create notification for the athlete
    await prisma.notification.create({
      data: {
        userId: athleteId,
        title: "New Coaching Inquiry",
        content: `New coaching inquiry from ${name}. Check your dashboard to review the application.`,
        read: false
      }
    })

    return NextResponse.json(inquiry)
  } catch (error) {
    console.error("[COACHING_INQUIRY_ERROR]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function PUT(request: Request) {
  const session = await getServerSession(authOptions)

  // Check authentication
  if (!session?.user) {
    return new NextResponse("Unauthorized", { status: 401 })
  }

  try {
    const body = await request.json()
    const {
      inquiryId,
      action,
      responseNote,
    } = body

    if (!inquiryId || !action) {
      return new NextResponse("Missing required fields", { status: 400 })
    }

    // Verify the inquiry exists and belongs to this coach
    const inquiry = await prisma.coachingInquiry.findUnique({
      where: { 
        id: inquiryId,
        athleteId: session.user.id
      },
    })

    if (!inquiry) {
      return new NextResponse("Inquiry not found or unauthorized", { status: 404 })
    }

    // Handle approve or reject
    if (action === "approve") {
      // Update the inquiry status
      const updatedInquiry = await prisma.coachingInquiry.update({
        where: { id: inquiryId },
        data: { status: "APPROVED" }
      })

      // Find or create the client user
      let clientUser = await prisma.user.findUnique({
        where: { email: inquiry.email }
      })

      if (!clientUser) {
        // Create a new user with a temporary password
        // In a real app, you would send an invitation email with password setup
        const tempPassword = Math.random().toString(36).slice(-8)
        clientUser = await prisma.user.create({
          data: {
            email: inquiry.email,
            name: inquiry.name,
            fullName: inquiry.name,
            password: tempPassword, // In a real app, hash this
            role: "client"
          }
        })
      }

      // For now, just return success
      // In a complete implementation, we would create coaching relationships and plans
      
      // Create notification for client
      if (clientUser) {
        await prisma.notification.create({
          data: {
            userId: clientUser.id,
            title: "Coaching Application Approved",
            content: "Your coaching application has been approved! Your coach will contact you soon.",
            read: false
          }
        })
      }

      return NextResponse.json({
        success: true,
        inquiryId,
        clientUser: {
          id: clientUser.id,
          email: clientUser.email,
          name: clientUser.name
        },
        action
      })
    } else if (action === "reject") {
      // Update the inquiry status
      const updatedInquiry = await prisma.coachingInquiry.update({
        where: { id: inquiryId },
        data: { status: "REJECTED" }
      })

      // Find the client user if they exist
      const clientUser = await prisma.user.findUnique({
        where: { email: inquiry.email }
      })

      // Create notification for the client if they have an account
      if (clientUser) {
        await prisma.notification.create({
          data: {
            userId: clientUser.id,
            title: "Coaching Application Update",
            content: `Your coaching application was not approved at this time. ${responseNote ? `\n\n${responseNote}` : ""}`,
            read: false
          }
        })
      }

      return NextResponse.json({
        success: true,
        inquiryId,
        action
      })
    } else {
      return new NextResponse("Invalid action", { status: 400 })
    }
  } catch (error) {
    console.error("[COACHING_INQUIRY_UPDATE_ERROR]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 