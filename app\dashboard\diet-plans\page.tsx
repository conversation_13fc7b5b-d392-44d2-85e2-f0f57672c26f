import { <PERSON><PERSON>ircle, Apple, Clock, Users } from "lucide-react"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { authOptions } from "@/lib/auth"

export default async function DietPlansPage() {
  const session = await getServerSession(authOptions)
  
  // Get cookie store - must be awaited in Server Components
  const cookieStore = await cookies()
  const devRoleOverride = cookieStore.get('dev_override_role')?.value

  if (!session?.user) {
    redirect("/login")
  }

  // Get effective role (respect dev override)
  const effectiveRole = process.env.NODE_ENV === 'development' && devRoleOverride
    ? devRoleOverride
    : session.user.role

  // Ensure user is admin or trainer
  if (effectiveRole !== "admin" && effectiveRole !== "trainer") {
    redirect("/dashboard")
  }

  // Mock diet plans data
  const mockDietPlans = [
    {
      id: "1",
      title: "Fat Loss Meal Plan",
      description: "Calorie-controlled diet focused on protein and nutrient-dense foods",
      type: "weight loss",
      duration: "12 weeks",
      mealsPerDay: 5,
      clientCount: 24,
      createdAt: "April 15, 2023",
      featured: true,
    },
    {
      id: "2",
      title: "Muscle Building Plan",
      description: "High protein, calorie surplus diet for maximum muscle growth",
      type: "bulking",
      duration: "16 weeks",
      mealsPerDay: 6,
      clientCount: 15,
      createdAt: "July 7, 2023",
      featured: false,
    },
    {
      id: "3",
      title: "Athletic Performance",
      description: "Balanced macronutrient approach for optimal energy and recovery",
      type: "performance",
      duration: "8 weeks",
      mealsPerDay: 4,
      clientCount: 8,
      createdAt: "March 3, 2023",
      featured: false,
    },
    {
      id: "4",
      title: "Plant-Based Nutrition",
      description: "Vegan meal plan focusing on complete proteins and essential nutrients",
      type: "specialized",
      duration: "12 weeks",
      mealsPerDay: 5,
      clientCount: 6,
      createdAt: "June 10, 2023",
      featured: false,
    },
  ]

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Diet Plans</h2>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Create New Plan
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Stats cards */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Plans</CardTitle>
            <Apple className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockDietPlans.length}</div>
            <p className="text-xs text-muted-foreground">
              +2 from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Clients</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockDietPlans.reduce((sum, plan) => sum + plan.clientCount, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              +7 from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12 weeks</div>
            <p className="text-xs text-muted-foreground">
              Same as last year
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <h3 className="text-xl font-semibold">Your Diet Plans</h3>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {mockDietPlans.map((plan) => (
            <Card key={plan.id} className="overflow-hidden">
              {plan.featured && (
                <div className="bg-primary text-primary-foreground text-xs py-1 text-center">
                  Featured Plan
                </div>
              )}
              <CardHeader>
                <CardTitle className="flex justify-between items-start">
                  <span>{plan.title}</span>
                  <span className="text-sm px-2 py-1 bg-muted rounded-md">
                    {plan.type}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">{plan.description}</p>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <p className="text-muted-foreground">Duration:</p>
                    <p className="font-medium">{plan.duration}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Meals/day:</p>
                    <p className="font-medium">{plan.mealsPerDay}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Active clients:</p>
                    <p className="font-medium">{plan.clientCount}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Created:</p>
                    <p className="font-medium">{plan.createdAt}</p>
                  </div>
                </div>
                <div className="flex space-x-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">Edit</Button>
                  <Button variant="outline" size="sm" className="flex-1">Assign</Button>
                  <Button variant="outline" size="sm" className="flex-1">View</Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

