import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    // Only allow this in development
    if (process.env.NODE_ENV !== "development") {
      return new NextResponse("Only available in development", { status: 403 });
    }
    
    // 1. Find a trainer and a client user
    const trainer = await prisma.user.findFirst({
      where: {
        role: "trainer",
      },
    });

    const client = await prisma.user.findFirst({
      where: {
        role: "client",
      },
    });

    if (!trainer || !client) {
      return NextResponse.json({
        success: false,
        error: "Could not find trainer and client users",
      }, { status: 404 });
    }

    // 2. Delete all messages in conversations between trainer and client
    const conversations = await prisma.conversation.findMany({
      where: {
        OR: [
          { user1Id: trainer.id, user2Id: client.id },
          { user1Id: client.id, user2Id: trainer.id },
        ],
      },
      select: {
        id: true,
      },
    });

    const conversationIds = conversations.map(c => c.id);
    
    if (conversationIds.length > 0) {
      // Delete all messages in these conversations
      await prisma.message.deleteMany({
        where: {
          conversationId: {
            in: conversationIds,
          },
        },
      });
      
      // Delete the conversations
      await prisma.conversation.deleteMany({
        where: {
          id: {
            in: conversationIds,
          },
        },
      });
    }

    // 3. Delete all coaching relationships between trainer and client
    await prisma.coachingRelationship.deleteMany({
      where: {
        OR: [
          { trainerId: trainer.id, clientId: client.id },
          { trainerId: client.id, clientId: trainer.id },
        ],
      },
    });

    return NextResponse.json({
      success: true,
      message: "Successfully reset coaching relationships and conversations",
      trainer: {
        id: trainer.id,
        name: trainer.name,
      },
      client: {
        id: client.id,
        name: client.name,
      },
    });
  } catch (error) {
    console.error("Error resetting coaching relationships:", error);
    
    // Return more detailed error information in development
    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      }, { status: 500 });
    }
    
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
