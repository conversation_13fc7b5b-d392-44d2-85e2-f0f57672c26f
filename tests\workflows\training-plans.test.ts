import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { createTestUser, cleanupTestData, prisma } from './setup'
import { TrainingPlanService } from '../../lib/services/training-plan-service'
import { WorkoutService } from '../../lib/services/workout-service'

describe('Training Plan Workflows', () => {
  let trainerUser: any
  let clientUser: any
  let testTrainingPlan: any
  let testWorkout: any
  
  beforeAll(async () => {
    // Create test users
    trainerUser = await createTestUser('trainer')
    clientUser = await createTestUser('client')
    
    // Get the trainer profile
    const trainerProfile = await prisma.trainerProfile.findUnique({
      where: {
        userId: trainerUser.id
      }
    })
    
    // Get the client profile
    const clientProfile = await prisma.clientProfile.findUnique({
      where: {
        userId: clientUser.id
      }
    })
    
    // Assign the client to the trainer
    await prisma.clientProfile.update({
      where: {
        id: clientProfile.id
      },
      data: {
        assignedTrainerId: trainerProfile.id
      }
    })
  })
  
  afterAll(async () => {
    // Clean up test data
    if (testWorkout) {
      await prisma.exercise.deleteMany({
        where: {
          workoutId: testWorkout.id
        }
      })
      
      await prisma.workout.delete({
        where: {
          id: testWorkout.id
        }
      })
    }
    
    if (testTrainingPlan) {
      await prisma.week.deleteMany({
        where: {
          trainingPlanId: testTrainingPlan.id
        }
      })
      
      await prisma.trainingPlan.delete({
        where: {
          id: testTrainingPlan.id
        }
      })
    }
    
    await cleanupTestData(trainerUser.id)
    await cleanupTestData(clientUser.id)
    
    await prisma.$disconnect()
  })
  
  describe('Training Plan Creation', () => {
    it('should allow trainers to create training plans', async () => {
      testTrainingPlan = await TrainingPlanService.create({
        title: 'Test Training Plan',
        description: 'A test training plan',
        type: 'strength',
        difficulty: 'intermediate',
        duration: 4,
        trainer: {
          connect: {
            id: trainerUser.id
          }
        }
      })
      
      expect(testTrainingPlan).toBeDefined()
      expect(testTrainingPlan.title).toBe('Test Training Plan')
      expect(testTrainingPlan.trainerId).toBe(trainerUser.id)
    })
  })
  
  describe('Workout Management', () => {
    it('should allow trainers to add workouts to training plans', async () => {
      // Create a week first
      const week = await prisma.week.create({
        data: {
          number: 1,
          trainingPlan: {
            connect: {
              id: testTrainingPlan.id
            }
          }
        }
      })
      
      // Create a workout
      testWorkout = await WorkoutService.create({
        name: 'Test Workout',
        description: 'A test workout',
        day: 1,
        week: {
          connect: {
            id: week.id
          }
        },
        trainer: {
          connect: {
            id: trainerUser.id
          }
        }
      })
      
      expect(testWorkout).toBeDefined()
      expect(testWorkout.name).toBe('Test Workout')
      expect(testWorkout.trainerId).toBe(trainerUser.id)
    })
    
    it('should allow trainers to add exercises to workouts', async () => {
      const exercise = await WorkoutService.addExerciseToWorkout(testWorkout.id, {
        name: 'Push-ups',
        description: 'Standard push-ups',
        sets: 3,
        reps: 10,
        restTime: 60,
        order: 1,
        createdById: trainerUser.id
      })
      
      expect(exercise).toBeDefined()
      expect(exercise.name).toBe('Push-ups')
      expect(exercise.workoutId).toBe(testWorkout.id)
    })
  })
  
  describe('Training Plan Assignment', () => {
    it('should allow trainers to assign training plans to clients', async () => {
      // Get the client profile
      const clientProfile = await prisma.clientProfile.findUnique({
        where: {
          userId: clientUser.id
        }
      })
      
      const result = await TrainingPlanService.assignPlanToClient(
        trainerUser.id,
        clientProfile.id,
        {
          title: 'Client Training Plan',
          description: 'A personalized training plan for the client',
          type: 'strength',
          difficulty: 'beginner'
        }
      )
      
      expect(result).toBeDefined()
      expect(result.clientProfileId).toBe(clientProfile.id)
    })
  })
})
