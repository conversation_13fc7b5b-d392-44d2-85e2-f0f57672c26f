import { UserRole } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

/**
 * Gets the role override for a user in development mode
 * This is useful for testing different roles without needing multiple accounts
 * 
 * @param email User's email address
 * @returns The overridden role or null if none
 */
export async function getUserRoleOverride(email: string): Promise<UserRole | null> {
  try {
    if (process.env.NODE_ENV !== "development") {
      return null
    }
    
    // Check for role override in the database
    const user = await prisma.user.findUnique({
      where: { email },
      select: { devRoleOverride: true }
    })

    const override = user?.devRoleOverride
    
    // Validate that the override is a valid role
    if (override === "admin" || override === "trainer" || override === "client") {
      return override as UserRole
    }
    
    return null
  } catch (error) {
    console.error("Error getting role override:", error)
    return null
  }
} 