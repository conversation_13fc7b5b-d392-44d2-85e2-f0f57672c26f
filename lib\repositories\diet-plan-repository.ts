import { prisma } from "@/lib/prisma"
import { DietPlan, Prisma } from "@prisma/client"

export interface IDietPlanRepository {
  findById(id: string): Promise<DietPlan | null>
  create(data: Prisma.DietPlanCreateInput): Promise<DietPlan>
  update(id: string, data: Partial<DietPlan>): Promise<DietPlan>
  delete(id: string): Promise<DietPlan>
  findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.DietPlanWhereInput
    orderBy?: Prisma.DietPlanOrderByWithRelationInput
  }): Promise<DietPlan[]>
  findByTrainerId(trainerId: string): Promise<DietPlan[]>
  duplicate(id: string, trainerId: string, newTitle: string): Promise<DietPlan>
  assignToClient(dietPlanId: string, clientId: string, trainerId: string): Promise<any>
}

export class DietPlanRepository implements IDietPlanRepository {
  async findById(id: string): Promise<DietPlan | null> {
    return prisma.dietPlan.findUnique({
      where: { id },
      include: {
        meals: true
      }
    })
  }

  async create(data: Prisma.DietPlanCreateInput): Promise<DietPlan> {
    return prisma.dietPlan.create({
      data
    })
  }

  async update(id: string, data: any): Promise<DietPlan> {
    // Extract meals from the data to handle them separately
    const { meals, ...dietPlanData } = data;

    // Start a transaction to update both the diet plan and its meals
    return prisma.$transaction(async (tx) => {
      // Update the diet plan
      const updatedPlan = await tx.dietPlan.update({
        where: { id },
        data: dietPlanData,
        include: {
          meals: true
        }
      });

      // If meals are provided, update them
      if (meals && Array.isArray(meals)) {
        // Delete existing meals that are not in the new list
        const mealIds = meals.filter(m => m.id).map(m => m.id);

        if (mealIds.length > 0) {
          await tx.meal.deleteMany({
            where: {
              dietPlanId: id,
              id: {
                notIn: mealIds
              }
            }
          });
        } else {
          // If no meal IDs provided, delete all existing meals
          await tx.meal.deleteMany({
            where: {
              dietPlanId: id
            }
          });
        }

        // Update or create meals
        for (const meal of meals) {
          if (meal.id) {
            // Update existing meal
            await tx.meal.update({
              where: { id: meal.id },
              data: {
                name: meal.name,
                description: meal.description,
                calories: meal.calories,
                protein: meal.protein,
                carbs: meal.carbs,
                fat: meal.fat,
                timeOfDay: meal.timeOfDay,
                foodSuggestions: meal.foodSuggestions
              }
            });
          } else {
            // Create new meal
            await tx.meal.create({
              data: {
                name: meal.name,
                description: meal.description,
                calories: meal.calories,
                protein: meal.protein,
                carbs: meal.carbs,
                fat: meal.fat,
                timeOfDay: meal.timeOfDay,
                foodSuggestions: meal.foodSuggestions,
                dietPlanId: id
              }
            });
          }
        }
      }

      // Return the updated plan with meals
      return tx.dietPlan.findUnique({
        where: { id },
        include: {
          meals: true
        }
      }) as Promise<DietPlan>;
    });
  }

  async delete(id: string): Promise<DietPlan> {
    return prisma.dietPlan.delete({
      where: { id }
    })
  }

  async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.DietPlanWhereInput
    orderBy?: Prisma.DietPlanOrderByWithRelationInput
  }): Promise<DietPlan[]> {
    return prisma.dietPlan.findMany(params)
  }

  async findByTrainerId(trainerId: string): Promise<DietPlan[]> {
    return prisma.dietPlan.findMany({
      where: {
        trainerId: trainerId
      },
      include: {
        meals: true,
        assignments: {
          include: {
            client: {
              select: {
                id: true,
                name: true,
                avatarUrl: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
  }

  async duplicate(id: string, trainerId: string, newTitle: string): Promise<DietPlan> {
    const existingPlan = await this.findById(id)

    if (!existingPlan) {
      throw new Error("Diet plan not found")
    }

    // Start a transaction to create the diet plan and its meals
    return prisma.$transaction(async (tx) => {
      // Create new diet plan
      const newPlan = await tx.dietPlan.create({
        data: {
          title: newTitle,
          description: existingPlan.description,
          calories: existingPlan.calories,
          protein: existingPlan.protein,
          carbs: existingPlan.carbs,
          fat: existingPlan.fat,
          notes: existingPlan.notes,
          trainerId: trainerId
        }
      })

      // Duplicate meals
      if (existingPlan.meals && existingPlan.meals.length > 0) {
        for (const meal of existingPlan.meals) {
          await tx.meal.create({
            data: {
              name: meal.name,
              description: meal.description,
              calories: meal.calories,
              protein: meal.protein,
              carbs: meal.carbs,
              fat: meal.fat,
              timeOfDay: meal.timeOfDay,
              foodSuggestions: meal.foodSuggestions,
              dietPlanId: newPlan.id
            }
          })
        }
      }

      // Return the new plan with meals
      return tx.dietPlan.findUnique({
        where: { id: newPlan.id },
        include: {
          meals: true
        }
      }) as Promise<DietPlan>
    })
  }

  async assignToClient(dietPlanId: string, clientId: string, trainerId: string): Promise<any> {
    return prisma.dietPlanAssignment.create({
      data: {
        dietPlanId,
        clientId,
        assignedById: trainerId,
        startDate: new Date()
      },
      include: {
        dietPlan: {
          include: {
            meals: true
          }
        },
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            avatarUrl: true
          }
        }
      }
    })
  }
}
