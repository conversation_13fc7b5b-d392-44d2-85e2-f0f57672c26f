"use client"

import { Drag<PERSON><PERSON><PERSON>ontext, Droppable, Draggable } from "@hello-pangea/dnd"
import { Exercise } from "@prisma/client"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { deleteExercise, updateExercise, reorderExercises } from "@/app/actions/exercise"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface ExerciseListProps {
  exercises: Exercise[]
  workoutId: string
}

export function ExerciseList({ exercises, workoutId }: ExerciseListProps) {
  const router = useRouter()

  const handleDelete = async (exerciseId: string) => {
    try {
      await deleteExercise(exerciseId)
      toast.success("Exercise removed successfully")
      router.refresh()
    } catch (error) {
      console.error("Error deleting exercise:", error)
      toast.error("Failed to remove exercise")
    }
  }

  const handleDragEnd = async (result: any) => {
    if (!result.destination) return

    const items = Array.from(exercises)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    try {
      await reorderExercises(workoutId, items)
      toast.success("Exercise order updated")
      router.refresh()
    } catch (error) {
      console.error("Error reordering exercises:", error)
      toast.error("Failed to update exercise order")
    }
  }

  const handleUpdate = async (exerciseId: string, field: keyof Exercise, value: string | number) => {
    try {
      await updateExercise(exerciseId, { [field]: value })
      toast.success("Exercise updated")
      router.refresh()
    } catch (error) {
      console.error("Error updating exercise:", error)
      toast.error("Failed to update exercise")
    }
  }

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Droppable droppableId="exercises">
        {(provided) => (
          <div
            {...provided.droppableProps}
            ref={provided.innerRef}
            className="space-y-4"
          >
            {exercises.map((exercise, index) => (
              <Draggable
                key={exercise.id}
                draggableId={exercise.id}
                index={index}
              >
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                    className="p-4 border rounded-lg bg-white shadow-sm"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium">{exercise.name}</h3>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDelete(exercise.id)}
                      >
                        Remove
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div>
                        <Label htmlFor={`sets-${exercise.id}`}>Sets</Label>
                        <Input
                          id={`sets-${exercise.id}`}
                          type="number"
                          value={exercise.sets || 0}
                          onChange={(e) =>
                            handleUpdate(exercise.id, "sets", parseInt(e.target.value))
                          }
                          min={0}
                          className="w-full"
                        />
                      </div>

                      <div>
                        <Label htmlFor={`reps-${exercise.id}`}>Reps</Label>
                        <Input
                          id={`reps-${exercise.id}`}
                          type="number"
                          value={exercise.reps || 0}
                          onChange={(e) =>
                            handleUpdate(exercise.id, "reps", parseInt(e.target.value))
                          }
                          min={0}
                          className="w-full"
                        />
                      </div>

                      <div>
                        <Label htmlFor={`duration-${exercise.id}`}>Duration (sec)</Label>
                        <Input
                          id={`duration-${exercise.id}`}
                          type="number"
                          value={exercise.duration || 0}
                          onChange={(e) =>
                            handleUpdate(exercise.id, "duration", parseInt(e.target.value))
                          }
                          min={0}
                          className="w-full"
                        />
                      </div>

                      <div>
                        <Label htmlFor={`rest-${exercise.id}`}>Rest Time (sec)</Label>
                        <Input
                          id={`rest-${exercise.id}`}
                          type="number"
                          value={exercise.restTime || 0}
                          onChange={(e) =>
                            handleUpdate(exercise.id, "restTime", parseInt(e.target.value))
                          }
                          min={0}
                          className="w-full"
                        />
                      </div>
                    </div>

                    {exercise.description && (
                      <div className="mt-4">
                        <Label>Description</Label>
                        <p className="text-sm text-gray-600">{exercise.description}</p>
                      </div>
                    )}
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  )
} 