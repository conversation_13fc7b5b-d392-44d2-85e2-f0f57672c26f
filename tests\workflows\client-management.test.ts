import { describe, it, expect } from 'vitest'
import { prisma } from './setup'

describe('Client Management Workflows', () => {
  describe('Client Profile Database Access', () => {
    it('should be able to access the client profiles table', async () => {
      const count = await prisma.clientProfile.count()
      expect(count).toBeGreaterThanOrEqual(0)
    })

    it('should be able to create and delete a client profile', async () => {
      // Create a test user first
      const user = await prisma.user.create({
        data: {
          name: 'Test Client',
          email: `test-client-${Date.now()}@example.com`,
          password: 'password123',
          role: 'client'
        }
      })

      // Create a client profile
      const clientProfile = await prisma.clientProfile.create({
        data: {
          user: {
            connect: {
              id: user.id
            }
          }
        }
      })

      expect(clientProfile).toBeDefined()
      expect(clientProfile.userId).toBe(user.id)

      // Delete the client profile
      const deletedClientProfile = await prisma.clientProfile.delete({
        where: {
          id: clientProfile.id
        }
      })

      expect(deletedClientProfile).toBeDefined()
      expect(deletedClientProfile.id).toBe(clientProfile.id)

      // Delete the user
      await prisma.user.delete({
        where: {
          id: user.id
        }
      })
    })
  })
})
