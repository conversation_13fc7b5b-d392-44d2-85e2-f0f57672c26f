import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const userId = session.user.id

    // Mock coaching sessions since the model doesn't exist yet
    const formattedSessions = []

    // Format session information
    const sessionInformation = {
      format: "Video Call (Zoom)",
      frequency: "Weekly",
      duration: "30-60 minutes",
      cancellationPolicy: "24 hours notice",
    }

    return NextResponse.json({
      sessions: formattedSessions,
      calendarConnected: false,
      calendarProvider: "Google Calendar",
      sessionInformation,
    })
  } catch (error) {
    console.error("[COACHING_SESSIONS_GET]", error)
    return new NextResponse("Internal Error", { status: 500 })
  }
}

// Helper function to format date
function formatDate(date: Date) {
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  })
}

// Helper function to format time
function formatTime(date: Date) {
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  })
}

// Helper function to get initials
function getInitials(name: string) {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2)
}

// Helper function to get session status
function getSessionStatus(date: Date) {
  const now = new Date()
  const diffMs = date.getTime() - now.getTime()
  const diffHours = diffMs / (1000 * 60 * 60)

  if (diffHours < 1) return "upcoming"
  if (diffHours < 24) return "today"
  return "scheduled"
}
