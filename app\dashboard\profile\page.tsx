import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { ProfileView } from "@/components/profile/profile-view"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export default async function Page() {
  const session = await getServerSession(authOptions)

  if (!session?.user?.email) {
    redirect("/login")
  }

  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    include: {
      _count: {
        select: {
          trainingPlans: true,
          dietPlans: true,
          products: true,
          clientSubscriptions: true,
          athleteSubscriptions: true,
        },
      },
    },
  })

  if (!user) {
    redirect("/login")
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Profile</h1>
      <ProfileView user={user} />
    </div>
  )
} 