"use client"

import { format } from "date-fns"
import { Trash2, Edit, MoreVertical } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface Progress {
  id: string
  date: Date
  weight: number
  bodyFat: number | null
  measurements: any
  notes?: string
  sleepHours?: number | null
  stressLevel?: number | null
  coffeeCount?: number | null
  createdAt: Date
  updatedAt: Date
  clientId: string
}

interface ProgressHistoryProps {
  entries: Progress[]
}

export function ProgressHistory({ entries }: ProgressHistoryProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [currentEntry, setCurrentEntry] = useState<Progress | null>(null)

  const handleDelete = async (id: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/progress/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error("Failed to delete entry")
      }

      toast({
        title: "Success",
        description: "Progress entry deleted successfully",
      })

      router.refresh()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete progress entry",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (entry: Progress) => {
    setCurrentEntry(entry)
    setEditDialogOpen(true)
  }

  const handleEditSuccess = () => {
    setEditDialogOpen(false)
    setCurrentEntry(null)
    router.refresh()
    toast({
      title: "Success",
      description: "Progress entry updated successfully",
    })
  }

  const sortedEntries = [...entries].sort((a, b) =>
    new Date(b.date).getTime() - new Date(a.date).getTime()
  )

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Progress History</CardTitle>
          <CardDescription>View and manage your progress entries</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sortedEntries.map((entry) => (
              <div
                key={entry.id}
                className="flex items-center justify-between border-b pb-4 last:border-0"
              >
                <div className="space-y-1">
                  <p className="text-sm font-medium">
                    {format(new Date(entry.date), "PPP")}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Weight: {entry.weight} lbs
                    {entry.bodyFat && ` • Body Fat: ${entry.bodyFat}%`}
                  </p>
                  {entry.measurements && Object.values(entry.measurements).some(val => val) && (
                    <p className="text-sm text-muted-foreground">
                      Measurements:
                      {entry.measurements.waist && `Waist: ${entry.measurements.waist}" `}
                      {entry.measurements.chest && `Chest: ${entry.measurements.chest}" `}
                      {entry.measurements.arms && `Arms: ${entry.measurements.arms}" `}
                      {entry.measurements.thighs && `Thighs: ${entry.measurements.thighs}" `}
                    </p>
                  )}
                  {/* Health metrics */}
                  {(entry.sleepHours || entry.stressLevel || entry.coffeeCount) && (
                    <p className="text-sm text-muted-foreground">
                      Health:
                      {entry.sleepHours && `Sleep: ${entry.sleepHours}h `}
                      {entry.stressLevel && `Stress: ${entry.stressLevel}/10 `}
                      {entry.coffeeCount && `Coffee: ${entry.coffeeCount} cups`}
                    </p>
                  )}
                  {entry.notes && (
                    <p className="text-sm text-muted-foreground italic">
                      "{entry.notes}"
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    disabled={loading}
                    onClick={() => handleEdit(entry)}
                  >
                    <Edit className="h-4 w-4" />
                    <span className="sr-only">Edit entry</span>
                  </Button>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        disabled={loading}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete entry</span>
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Progress Entry</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this progress entry? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDelete(entry.id)}
                          disabled={loading}
                        >
                          {loading ? "Deleting..." : "Delete"}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            ))}
            {entries.length === 0 && (
              <p className="text-center text-muted-foreground py-4">
                No progress entries yet
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Measurement</DialogTitle>
            <DialogDescription>
              Update your measurement details
            </DialogDescription>
          </DialogHeader>
          {currentEntry && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                const data = {
                  weight: parseFloat(formData.get('weight') as string),
                  bodyFat: formData.get('bodyFat') ? parseFloat(formData.get('bodyFat') as string) : undefined,
                  measurements: {
                    waist: formData.get('waist') ? parseFloat(formData.get('waist') as string) : undefined,
                    chest: formData.get('chest') ? parseFloat(formData.get('chest') as string) : undefined,
                  },
                  date: currentEntry.date,
                  notes: formData.get('notes') as string || '',
                };

                fetch(`/api/progress/${currentEntry.id}`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify(data),
                })
                .then(response => {
                  if (!response.ok) throw new Error('Failed to update');
                  return response.json();
                })
                .then(() => handleEditSuccess())
                .catch(error => {
                  console.error('Error updating measurement:', error);
                  toast({
                    title: "Error",
                    description: "Failed to update measurement",
                    variant: "destructive",
                  });
                });
              }}
              className="space-y-4"
            >
              <div className="space-y-2">
                <Label htmlFor="weight">Weight (lbs)</Label>
                <Input
                  id="weight"
                  name="weight"
                  type="number"
                  step="0.1"
                  defaultValue={currentEntry.weight}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bodyFat">Body Fat %</Label>
                <Input
                  id="bodyFat"
                  name="bodyFat"
                  type="number"
                  step="0.1"
                  defaultValue={currentEntry.bodyFat || ''}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="waist">Waist (inches)</Label>
                  <Input
                    id="waist"
                    name="waist"
                    type="number"
                    step="0.1"
                    defaultValue={currentEntry.measurements?.waist || ''}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="chest">Chest (inches)</Label>
                  <Input
                    id="chest"
                    name="chest"
                    type="number"
                    step="0.1"
                    defaultValue={currentEntry.measurements?.chest || ''}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Input
                  id="notes"
                  name="notes"
                  defaultValue={currentEntry.notes || ''}
                />
              </div>

              <div className="pt-4 flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setEditDialogOpen(false)}>Cancel</Button>
                <Button type="submit">Save Changes</Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}

