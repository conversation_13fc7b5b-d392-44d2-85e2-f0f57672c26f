import { NextRequest, NextResponse } from 'next/server';

// Define allowed origins
const allowedOrigins = [
  // Local development
  'http://localhost:3000',
  'http://localhost:3001',
  
  // Production domains
  'https://clear-coach.com',
  'https://www.clear-coach.com',
  
  // Staging domains
  'https://staging.clear-coach.com',
  
  // Add any other domains that need to access your API
];

/**
 * Apply CORS headers to a response
 * @param req The incoming request
 * @param res The response to modify
 * @returns The modified response with CORS headers
 */
export function applyCorsHeaders(req: NextRequest, res: NextResponse): NextResponse {
  // Get the origin from the request
  const origin = req.headers.get('origin') || '';
  
  // Check if the origin is allowed
  const isAllowedOrigin = allowedOrigins.includes(origin);
  
  // Create a new response with the appropriate headers
  const response = res.clone();
  
  // Set CORS headers
  if (isAllowedOrigin) {
    // For allowed origins, set specific origin
    response.headers.set('Access-Control-Allow-Origin', origin);
  } else {
    // For other origins, don't allow CORS
    response.headers.set('Access-Control-Allow-Origin', 'null');
  }
  
  // Common CORS headers
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-CSRF-Token');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Max-Age', '86400'); // 24 hours
  
  return response;
}

/**
 * Handle preflight OPTIONS requests
 * @param req The incoming request
 * @returns A response for OPTIONS requests or null for other methods
 */
export function handleCorsOptions(req: NextRequest): NextResponse | null {
  // Only handle OPTIONS method
  if (req.method !== 'OPTIONS') {
    return null;
  }
  
  // Create a response for the OPTIONS request
  const response = new NextResponse(null, { status: 204 }); // No content
  
  // Apply CORS headers
  return applyCorsHeaders(req, response);
}

/**
 * Middleware to handle CORS for API routes
 * @param handler The API route handler
 * @returns A function that handles CORS and then calls the handler
 */
export function withCors(handler: (req: NextRequest) => Promise<NextResponse>) {
  return async (req: NextRequest) => {
    // Handle OPTIONS requests
    const optionsResponse = handleCorsOptions(req);
    if (optionsResponse) {
      return optionsResponse;
    }
    
    // For other methods, call the handler and then apply CORS headers
    const response = await handler(req);
    return applyCorsHeaders(req, response);
  };
}
