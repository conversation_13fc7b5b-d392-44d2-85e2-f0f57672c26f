import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions, getAuthSession } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { TrainingPlan, Week, Workout, Exercise } from '@prisma/client'

// Define the expected nested structure from the frontend for POST
// We can reuse the types defined for PATCH, adjusting if needed
type ExercisePostData = Omit<Exercise, 'id' | 'workoutId' | 'createdAt' | 'updatedAt'>
type WorkoutPostData = Omit<Workout, 'id' | 'weekId' | 'createdAt' | 'updatedAt' | 'trainingPlanId'> & {
  exercises: ExercisePostData[]
}
type WeekPostData = Omit<Week, 'id' | 'trainingPlanId' | 'createdAt' | 'updatedAt'> & {
  workouts: WorkoutPostData[]
}
// For POST, we expect the full structure, but athleteId will be set from session
type TrainingPlanPostData = Omit<TrainingPlan, 'id' | 'athleteId' | 'createdAt' | 'updatedAt'> & {
  weeks: WeekPostData[]
}

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access this resource" },
        { status: 401 }
      )
    }

    // Parse query parameters
    const url = new URL(request.url)
    const includeWeeks = url.searchParams.get("includeWeeks") === "true"
    const includeWorkouts = url.searchParams.get("includeWorkouts") === "true"
    const includeExercises = url.searchParams.get("includeExercises") === "true"

    // Determine query includes based on parameters
    const include: any = {}
    
    if (includeWeeks) {
      include.weeks = {
        orderBy: { order: "asc" },
      }
      
      if (includeWorkouts) {
        include.weeks.include = {
          workouts: {
            orderBy: { order: "asc" },
          }
        }
        
        if (includeExercises) {
          include.weeks.include.workouts.include = {
            exercises: {
              orderBy: { order: "asc" },
            }
          }
        }
      }
    }
    
    if (includeWorkouts && !includeWeeks) {
      include.workouts = {
        orderBy: { order: "asc" },
      }
      
      if (includeExercises) {
        include.workouts.include = {
          exercises: {
            orderBy: { order: "asc" },
          }
        }
      }
    }

    // Query options
    const options: any = {
      where: {
        athleteId: session.user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
    }
    
    if (Object.keys(include).length > 0) {
      options.include = include
    }

    const trainingPlans = await prisma.trainingPlan.findMany(options)
    
    return NextResponse.json(trainingPlans)
  } catch (error) {
    console.error("[TRAINING_PLANS_GET]", error)
    return NextResponse.json(
      { error: "An error occurred while fetching training plans" },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const session = await getAuthSession()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized. You must be logged in." },
        { status: 401 }
      )
    }

    // Authorization: Allow admins or trainers to create plans
    if (session.user.role !== "trainer" && session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Forbidden: Only trainers or admins can create training plans." },
        { status: 403 }
      )
    }

    const body: TrainingPlanPostData = await request.json()
    console.log("Received training plan creation request body:", JSON.stringify(body, null, 2)); // Log the full request body
    const { weeks, ...planData } = body

    // Log the user ID being used
    console.log(`Attempting to create plan for user ID: ${session.user.id}`);

    // --- Development Only: Ensure dev-bypass-user exists --- 
    if (session.user.id === 'dev-bypass-user') {
      try {
        const devUser = await prisma.user.findUnique({
          where: { id: 'dev-bypass-user' }
        });
        if (!devUser) {
          console.log("Creating dev-bypass-user as it doesn't exist...");
          await prisma.user.create({
            data: {
              id: 'dev-bypass-user',
              email: '<EMAIL>', // Use a placeholder email
              name: 'Dev Bypass User',
              role: session.user.role || 'admin', // Use role from session or default
              password: '-' // Placeholder - Password handling should be robust in real scenarios
            }
          });
          console.log("dev-bypass-user created.");
        }
      } catch (userCreateError) {
        console.error("Error checking/creating dev-bypass-user:", userCreateError);
        // Decide if you want to block plan creation if user creation fails
        return NextResponse.json(
          { error: "Failed to ensure development user exists." },
          { status: 500 }
        );
      }
    }
    // --- End Development Only Section ---

    // Validation
    if (!planData.title?.trim()) {
      return NextResponse.json(
        { error: "Training plan title is required." },
        { status: 400 }
      )
    }
    
    if (!planData.difficulty) {
      return NextResponse.json(
        { error: "Training plan difficulty is required" },
        { status: 400 }
      )
    }

    // --- Create Plan (Step 1: Create TrainingPlan shell) ---
    const newTrainingPlan = await prisma.trainingPlan.create({
      data: {
        // Top-level plan data
        title: planData.title,
        description: planData.description,
        difficulty: planData.difficulty,
        athlete: { connect: { id: session.user.id } }, // Connect to the logged-in user
        // Add any other top-level fields from planData here
        // NOTE: Weeks are NOT created here initially
      },
    });

    // --- Create Plan (Step 2: Create Weeks, Workouts, Exercises with connections) ---
    if (weeks && weeks.length > 0) {
      const createdWeekIds: string[] = []; // Store week IDs if needed later

      for (const [weekIndex, weekData] of weeks.entries()) {
        // Step 2a: Create the Week record first
        const createdWeek = await prisma.week.create({
          data: {
            trainingPlan: { connect: { id: newTrainingPlan.id } }, // Connect Week to Plan
            weekNumber: weekData.weekNumber ?? (weekIndex + 1),
            order: weekData.order ?? weekIndex,
            // Add other week fields if they exist
            // DO NOT create workouts nested here anymore
          },
        });
        createdWeekIds.push(createdWeek.id);

        // Step 2b: Loop through workouts for this week and create them with explicit connections
        if (weekData.workouts && weekData.workouts.length > 0) {
          for (const [workoutIndex, workoutData] of weekData.workouts.entries()) {
            await prisma.workout.create({
              data: {
                trainingPlan: { connect: { id: newTrainingPlan.id } }, // Connect Workout to Plan
                week: { connect: { id: createdWeek.id } },           // Connect Workout to the created Week
                title: workoutData.title,
                description: workoutData.description,
                order: workoutData.order ?? workoutIndex,
                type: workoutData.type,
                // Step 2c: Create exercises nested within workout (implicit connection should be ok)
                exercises: {
                  create: workoutData.exercises.map((exerciseData, exerciseIndex) => ({
                    // Workout connection is implicit here
                    name: exerciseData.name,
                    description: exerciseData.description,
                    sets: exerciseData.sets,
                    reps: exerciseData.reps,
                    duration: exerciseData.duration,
                    restTime: exerciseData.restTime,
                    videoUrl: exerciseData.videoUrl,
                    thumbnailUrl: exerciseData.thumbnailUrl,
                    muscleGroup: exerciseData.muscleGroup,
                    type: exerciseData.type,
                    equipment: exerciseData.equipment,
                    difficulty: exerciseData.difficulty,
                    calories: exerciseData.calories,
                    order: exerciseData.order ?? exerciseIndex,
                    isTemplate: false,
                  })),
                },
              },
            });
          }
        }
      }
    }

    // --- Retrieve the full plan with nested data for the response ---
    const createdTrainingPlanWithDetails = await prisma.trainingPlan.findUnique({
      where: { id: newTrainingPlan.id },
      include: {
        weeks: {
          orderBy: { order: 'asc' },
          include: {
            workouts: {
              orderBy: { order: 'asc' },
              include: {
                exercises: { orderBy: { order: 'asc' } },
              },
            },
          },
        },
      },
    });

    return NextResponse.json(createdTrainingPlanWithDetails);
  } catch (error) {
    console.error("[TRAINING_PLAN_CREATE]", error)
    // Provide more context on Prisma errors if possible
    if (error instanceof Error && 'code' in error && 'meta' in error) { // Check if it looks like a Prisma error
      console.error("Prisma Error Code:", error.code)
      console.error("Prisma Error Meta:", error.meta)
      return NextResponse.json(
        { error: "Database error during creation.", details: { code: error.code, target: (error.meta as any)?.target } },
        { status: 500 }
      )
    }
    return NextResponse.json(
      { error: "An error occurred while creating the training plan." },
      { status: 500 }
    )
  }
} 