import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { createTestUser, cleanupTestData, prisma } from './setup'
import { DietPlanService } from '../../lib/services/diet-plan-service'

describe('Diet Plan Workflows', () => {
  let trainerUser: any
  let clientUser: any
  let testDietPlan: any
  
  beforeAll(async () => {
    // Create test users
    trainerUser = await createTestUser('trainer')
    clientUser = await createTestUser('client')
    
    // Get the trainer profile
    const trainerProfile = await prisma.trainerProfile.findUnique({
      where: {
        userId: trainerUser.id
      }
    })
    
    // Get the client profile
    const clientProfile = await prisma.clientProfile.findUnique({
      where: {
        userId: clientUser.id
      }
    })
    
    // Assign the client to the trainer
    await prisma.clientProfile.update({
      where: {
        id: clientProfile.id
      },
      data: {
        assignedTrainerId: trainerProfile.id
      }
    })
  })
  
  afterAll(async () => {
    // Clean up test data
    if (testDietPlan) {
      await prisma.meal.deleteMany({
        where: {
          dietPlanId: testDietPlan.id
        }
      })
      
      await prisma.dietPlan.delete({
        where: {
          id: testDietPlan.id
        }
      })
    }
    
    await cleanupTestData(trainerUser.id)
    await cleanupTestData(clientUser.id)
    
    await prisma.$disconnect()
  })
  
  describe('Diet Plan Creation', () => {
    it('should allow trainers to create diet plans', async () => {
      testDietPlan = await DietPlanService.create({
        title: 'Test Diet Plan',
        description: 'A test diet plan',
        calories: 2000,
        protein: 150,
        carbs: 200,
        fat: 70,
        trainer: {
          connect: {
            id: trainerUser.id
          }
        },
        meals: {
          create: [
            {
              name: 'Breakfast',
              description: 'Healthy breakfast',
              calories: 500,
              protein: 30,
              carbs: 60,
              fat: 15
            },
            {
              name: 'Lunch',
              description: 'Nutritious lunch',
              calories: 700,
              protein: 40,
              carbs: 70,
              fat: 25
            },
            {
              name: 'Dinner',
              description: 'Light dinner',
              calories: 600,
              protein: 35,
              carbs: 50,
              fat: 20
            }
          ]
        }
      })
      
      expect(testDietPlan).toBeDefined()
      expect(testDietPlan.title).toBe('Test Diet Plan')
      expect(testDietPlan.trainerId).toBe(trainerUser.id)
    })
  })
  
  describe('Diet Plan Listing', () => {
    it('should allow trainers to view their diet plans', async () => {
      const dietPlans = await DietPlanService.findByTrainerId(trainerUser.id)
      
      expect(dietPlans).toBeDefined()
      expect(dietPlans.length).toBeGreaterThan(0)
      expect(dietPlans.some(plan => plan.id === testDietPlan.id)).toBe(true)
    })
  })
  
  describe('Diet Plan Details', () => {
    it('should allow trainers to view diet plan details', async () => {
      const dietPlan = await DietPlanService.findById(testDietPlan.id)
      
      expect(dietPlan).toBeDefined()
      expect(dietPlan?.id).toBe(testDietPlan.id)
      expect(dietPlan?.title).toBe('Test Diet Plan')
    })
  })
  
  describe('Diet Plan Duplication', () => {
    it('should allow trainers to duplicate diet plans', async () => {
      const duplicatedPlan = await DietPlanService.duplicate(
        testDietPlan.id,
        trainerUser.id,
        'Duplicated Diet Plan'
      )
      
      expect(duplicatedPlan).toBeDefined()
      expect(duplicatedPlan.title).toBe('Duplicated Diet Plan')
      expect(duplicatedPlan.trainerId).toBe(trainerUser.id)
      
      // Clean up the duplicated plan
      await prisma.meal.deleteMany({
        where: {
          dietPlanId: duplicatedPlan.id
        }
      })
      
      await prisma.dietPlan.delete({
        where: {
          id: duplicatedPlan.id
        }
      })
    })
  })
  
  describe('Diet Plan Assignment', () => {
    it('should allow trainers to assign diet plans to clients', async () => {
      // Get the client profile
      const clientProfile = await prisma.clientProfile.findUnique({
        where: {
          userId: clientUser.id
        }
      })
      
      const result = await DietPlanService.assignToClient(
        testDietPlan.id,
        clientProfile.id,
        trainerUser.id
      )
      
      expect(result).toBeDefined()
      expect(result.clientProfileId).toBe(clientProfile.id)
      expect(result.dietPlanId).toBe(testDietPlan.id)
    })
  })
})
