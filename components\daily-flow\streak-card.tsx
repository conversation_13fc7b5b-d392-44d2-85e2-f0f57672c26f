"use client"

import { Flame, <PERSON>, Coffee, Zap, Activity, Utensils, Scale } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface Streak {
  id: string
  type: string
  currentCount: number
  longestCount: number
  lastLoggedAt: string | null
}

interface StreakCardProps {
  streak: Streak
  onLog: (type: string) => void
  className?: string
}

export function StreakCard({ streak, onLog, className }: StreakCardProps) {
  // Get icon based on streak type
  const getIcon = () => {
    switch (streak.type) {
      case "sleep":
        return <Moon className="h-5 w-5" />
      case "coffee":
        return <Coffee className="h-5 w-5" />
      case "stress":
        return <Zap className="h-5 w-5" />
      case "nutrition":
        return <Utensils className="h-5 w-5" />
      case "measurement":
        return <Scale className="h-5 w-5" />
      default:
        return <Activity className="h-5 w-5" />
    }
  }
  
  // Get color based on streak type
  const getTypeColor = () => {
    switch (streak.type) {
      case "sleep":
        return "text-indigo-500"
      case "coffee":
        return "text-amber-700"
      case "stress":
        return "text-amber-500"
      case "nutrition":
        return "text-green-500"
      case "measurement":
        return "text-blue-500"
      default:
        return "text-primary"
    }
  }
  
  // Get background color based on streak type
  const getTypeBgColor = () => {
    switch (streak.type) {
      case "sleep":
        return "bg-indigo-100 dark:bg-indigo-900/30"
      case "coffee":
        return "bg-amber-100 dark:bg-amber-900/30"
      case "stress":
        return "bg-amber-100 dark:bg-amber-900/30"
      case "nutrition":
        return "bg-green-100 dark:bg-green-900/30"
      case "measurement":
        return "bg-blue-100 dark:bg-blue-900/30"
      default:
        return "bg-primary/10"
    }
  }
  
  // Get button color based on streak type
  const getButtonColor = () => {
    switch (streak.type) {
      case "sleep":
        return "bg-indigo-500 hover:bg-indigo-600 text-white"
      case "coffee":
        return "bg-amber-700 hover:bg-amber-800 text-white"
      case "stress":
        return "bg-amber-500 hover:bg-amber-600 text-white"
      case "nutrition":
        return "bg-green-500 hover:bg-green-600 text-white"
      case "measurement":
        return "bg-blue-500 hover:bg-blue-600 text-white"
      default:
        return "bg-primary hover:bg-primary/90 text-primary-foreground"
    }
  }
  
  // Calculate progress percentage towards next milestone
  const getProgressToNextMilestone = () => {
    if (streak.currentCount < 7) return (streak.currentCount / 7) * 100
    if (streak.currentCount < 30) return (streak.currentCount / 30) * 100
    if (streak.currentCount < 100) return (streak.currentCount / 100) * 100
    return 100
  }
  
  // Get next milestone
  const getNextMilestone = () => {
    if (streak.currentCount < 7) return 7
    if (streak.currentCount < 30) return 30
    if (streak.currentCount < 100) return 100
    return streak.currentCount + 100
  }
  
  return (
    <Card className={cn("premium-card group overflow-hidden", className)}>
      <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
      <CardHeader className="relative z-10 pb-2">
        <div className="flex items-center gap-2">
          <div className={`p-2 rounded-full ${getTypeBgColor()}`}>
            <div className={getTypeColor()}>{getIcon()}</div>
          </div>
          <div>
            <CardTitle className="capitalize">{streak.type}</CardTitle>
            <CardDescription>
              {streak.lastLoggedAt 
                ? `Last logged: ${format(new Date(streak.lastLoggedAt), 'PPP')}` 
                : 'Not started yet'}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="relative z-10 pt-0">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Flame className="h-5 w-5 text-orange-500 mr-2" />
              <span className="text-2xl font-bold">{streak.currentCount}</span>
              <span className="text-sm text-muted-foreground ml-1">day streak</span>
            </div>
            <div className="text-sm text-muted-foreground">
              Best: {streak.longestCount} days
            </div>
          </div>
          
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span>Progress to next milestone ({getNextMilestone()} days)</span>
              <span>{Math.round(getProgressToNextMilestone())}%</span>
            </div>
            <Progress value={getProgressToNextMilestone()} className="h-2" />
          </div>
        </div>
      </CardContent>
      <CardFooter className="relative z-10 pt-0">
        <Button 
          onClick={() => onLog(streak.type)} 
          className={cn("w-full", getButtonColor())}
        >
          Log Today
        </Button>
      </CardFooter>
    </Card>
  )
}
