import { NextResponse } from "next/server"
import crypto from "crypto"
import { hash } from "bcryptjs"
import { prisma } from "@/lib/prisma"
import { resetPasswordSchema } from "@/lib/validations/auth"
import logger from "@/lib/logger"

// --- Helper: Hash token for lookup ---
function hashToken(token: string) {
  return crypto.createHash("sha256").update(token).digest("hex")
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // --- Zod Validation ---
    const validationResult = resetPasswordSchema.safeParse(body)
    if (!validationResult.success) {
      logger.warn({ 
        validationErrors: validationResult.error.flatten().fieldErrors, 
        tokenReceived: body.token ? '******' : 'null'
      }, "Invalid password reset input.")
      return NextResponse.json(
        { message: "Invalid input.", errors: validationResult.error.flatten().fieldErrors },
        { status: 400 },
      )
    }
    const { token, password } = validationResult.data
    // --- End Zod Validation ---

    const hashedToken = hashToken(token)

    // 1. Find the reset token
    const passwordResetToken = await (prisma as any).passwordResetToken.findUnique({
      where: { token: hashedToken },
    })

    if (!passwordResetToken) {
      logger.warn({ receivedToken: token ? '******' : 'null' }, "Password reset attempt with invalid token.")
      return NextResponse.json({ message: "Invalid token." }, { status: 400 })
    }

    // 2. Check if token has expired
    const hasExpired = new Date(passwordResetToken.expires) < new Date()
    if (hasExpired) {
      logger.warn({ identifier: (passwordResetToken as any).identifier }, "Password reset attempt with expired token.")
      await (prisma as any).passwordResetToken.delete({ 
          where: { 
            identifier_token: { 
              identifier: (passwordResetToken as any).identifier, 
              token: hashedToken 
            } 
          } 
      });
      return NextResponse.json({ message: "Token has expired." }, { status: 410 })
    }

    // 3. Find the user associated with the token
    const user = await prisma.user.findUnique({
      where: { email: passwordResetToken.identifier },
    })

    if (!user) {
      logger.error({ identifier: (passwordResetToken as any).identifier }, "User not found for valid password reset token.")
      return NextResponse.json({ message: "User not found." }, { status: 404 })
    }

    // 4. Hash the new password and update the user
    const newHashedPassword = await hash(password, 10)
    await prisma.user.update({
      where: { id: user.id },
      data: { password: newHashedPassword },
    })

    // 5. Delete the password reset token
    await (prisma as any).passwordResetToken.delete({
       where: { 
         identifier_token: { 
           identifier: (passwordResetToken as any).identifier, 
           token: hashedToken 
         } 
       } 
    })

    logger.info({ userId: user.id, email: user.email }, "Password reset successfully.")
    return NextResponse.json({ message: "Password reset successful." }, { status: 200 })

  } catch (error) {
    logger.error({ error }, "Password reset process failed")
    return NextResponse.json(
      { message: "An internal server error occurred." },
      { status: 500 },
    )
  }
}