export interface Exercise {
  id: string
  name: string
  sets?: number | null
  reps?: number | string | null
  weight?: number | string | null
  description?: string | null
  duration?: number | null
  restTime?: number | null
  videoUrl?: string | null
  muscleGroup?: string | null
  order?: number
  type?: string | null
  thumbnailUrl?: string | null
  calories?: number | null
  equipment?: string | null
  difficulty?: string | null
  targetMuscles?: string[] | null
  category?: string | null
  video?: string | null
}

export interface Week {
  id: string
  weekNumber: number
  order: number
  exercises?: Exercise[]
  workouts?: any[]
}

export interface TrainingPlanBase {
  id: string
  title: string
  description: string
  type: string
  createdAt: string | Date
}

export interface TrainingPlanTemplate extends TrainingPlanBase {
  difficulty: string
  trainerId: string
  weeks: any[]
  updatedAt: string
  featured: boolean
  activeClients: number
}

export interface PersonalizedTrainingPlan extends TrainingPlanBase {
  difficulty: string
  clientId: string
  trainerId: string
  weeks: any[]
  updatedAt: string
}

export interface TrainingPlanWithDetails extends TrainingPlanBase {
  difficulty?: string | null
  targetLevel?: string | null
  weeks?: Week[]
  price?: number | null
  isPublic?: boolean
  trainerNotes?: string | null
  estimatedDuration?: string | null
  category?: string | null
}
