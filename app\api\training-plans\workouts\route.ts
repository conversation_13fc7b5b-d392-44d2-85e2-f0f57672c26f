"use server";

import { NextResponse } from "next/server"
import { getAuthSession } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: Request) {
  try {
    const session = await getAuthSession()
    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access this resource" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { title, description, type, weekId, trainingPlanId } = body

    // Validation
    if (!title) {
      return NextResponse.json(
        { error: "Workout title is required" },
        { status: 400 }
      )
    }

    if (!type) {
      return NextResponse.json(
        { error: "Workout type is required" },
        { status: 400 }
      )
    }

    if (!weekId) {
      return NextResponse.json(
        { error: "Week ID is required" },
        { status: 400 }
      )
    }

    // Log request information for debugging
    console.log("Creating workout with params:", {
      title,
      type,
      weekId,
      trainingPlanId,
      userId: session.user.id,
      userRole: session.user.role
    });

    // Handle development mode with dev-bypass-user
    const isDevelopment = process.env.NODE_ENV === "development";
    const isDevBypassUser = session.user.id === "dev-bypass-user";
    
    // Check if this is a mock week ID from development mode
    const isMockWeek = weekId.startsWith('mock-week-');
    
    if ((isDevelopment && isDevBypassUser) || isMockWeek) {
      // In development mode with dev bypass, create a mock response
      console.log("Development mode: Creating mock workout");
      const mockWorkout = {
        id: `mock-workout-${Date.now()}`,
        title,
        description: description || null,
        type,
        weekId,
        trainingPlanId,
        order: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
        exercises: []
      };
      
      return NextResponse.json(mockWorkout);
    }

    try {
      // Check if user owns the week
      const week = await prisma.week.findUnique({
        where: {
          id: weekId,
        },
        include: {
          trainingPlan: true,
        },
      });

      if (!week) {
        return NextResponse.json(
          { error: "Week not found" },
          { status: 404 }
        );
      }

      if (week.trainingPlan.athleteId !== session.user.id && session.user.role !== "admin") {
        return NextResponse.json(
          { error: "You do not have permission to edit this week" },
          { status: 403 }
        );
      }

      // Get highest order in the week
      const highestOrderWorkout = await prisma.workout.findFirst({
        where: {
          weekId,
        },
        orderBy: {
          order: "desc",
        },
      });

      const newOrder = highestOrderWorkout ? highestOrderWorkout.order + 1 : 0;

      // Create the workout
      const workout = await prisma.workout.create({
        data: {
          title,
          description,
          type,
          weekId,
          trainingPlanId: week.trainingPlanId,
          order: newOrder,
        },
      });

      return NextResponse.json(workout);
    } catch (error) {
      console.error("[WORKOUT_CREATE]", error);
      return NextResponse.json(
        { error: "An error occurred while creating the workout", details: JSON.stringify(error) },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("[WORKOUT_CREATE]", error);
    return NextResponse.json(
      { error: "An error occurred while creating the workout" },
      { status: 500 }
    );
  }
}

export async function PATCH(request: Request) {
  try {
    const session = await getAuthSession()
    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access this resource" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { id, title, description, type, order } = body

    // Validation
    if (!id) {
      return NextResponse.json(
        { error: "Workout ID is required" },
        { status: 400 }
      )
    }

    // Handle mock workouts for development
    if (id.startsWith('mock-workout-')) {
      console.log("Development mode: Updating mock workout");
      const mockWorkout = {
        id,
        title: title || "Updated Workout",
        description: description || null,
        type: type || "Strength",
        order: order || 0,
        updatedAt: new Date()
      };
      
      return NextResponse.json(mockWorkout);
    }

    try {
      // Check if user owns the workout
      const workout = await prisma.workout.findUnique({
        where: {
          id,
        },
        include: {
          week: {
            include: {
              trainingPlan: true,
            },
          },
        },
      })

      if (!workout) {
        return NextResponse.json(
          { error: "Workout not found" },
          { status: 404 }
        )
      }

      if (workout.week.trainingPlan.athleteId !== session.user.id && session.user.role !== "admin") {
        return NextResponse.json(
          { error: "You do not have permission to edit this workout" },
          { status: 403 }
        )
      }

      // Update the workout
      const updatedWorkout = await prisma.workout.update({
        where: {
          id,
        },
        data: {
          title: title !== undefined ? title : workout.title,
          description: description !== undefined ? description : workout.description,
          type: type !== undefined ? type : workout.type,
          order: order !== undefined ? order : workout.order,
        },
      })

      return NextResponse.json(updatedWorkout)
    } catch (error) {
      console.error("[WORKOUT_UPDATE]", error)
      return NextResponse.json(
        { error: "An error occurred while updating the workout" },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("[WORKOUT_UPDATE]", error)
    return NextResponse.json(
      { error: "An error occurred while updating the workout" },
      { status: 500 }
    )
  }
} 