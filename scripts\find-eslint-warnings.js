const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Run ESLint and capture the output
try {
  const eslintOutput = execSync('npx eslint --format json .').toString();
  const eslintResults = JSON.parse(eslintOutput);
  
  // Count warnings per file
  const fileWarnings = {};
  
  eslintResults.forEach(result => {
    const filePath = result.filePath;
    const warnings = result.messages.filter(msg => msg.severity === 1);
    
    if (warnings.length > 0) {
      fileWarnings[filePath] = {
        count: warnings.length,
        warnings: warnings.map(w => ({
          line: w.line,
          column: w.column,
          message: w.message,
          ruleId: w.ruleId
        }))
      };
    }
  });
  
  // Sort files by warning count
  const sortedFiles = Object.entries(fileWarnings)
    .sort((a, b) => b[1].count - a[1].count)
    .map(([file, data]) => ({
      file: path.relative(process.cwd(), file),
      count: data.count,
      warnings: data.warnings
    }));
  
  // Output results
  console.log('Files with ESLint warnings:');
  console.log(JSON.stringify(sortedFiles, null, 2));
  
  // Summary by warning type
  const warningTypes = {};
  eslintResults.forEach(result => {
    result.messages.forEach(msg => {
      if (msg.severity === 1) {
        warningTypes[msg.ruleId] = (warningTypes[msg.ruleId] || 0) + 1;
      }
    });
  });
  
  console.log('\nWarning types summary:');
  console.log(JSON.stringify(warningTypes, null, 2));
  
} catch (error) {
  console.error('Error running ESLint:', error.message);
}
