'use client';

import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

const calendlyFormSchema = z.object({
  calendlyUrl: z
    .string()
    .url('Please enter a valid URL')
    .refine((url) => url.includes('calendly.com'), {
      message: 'URL must be a Calendly URL',
    }),
  calendlyUserId: z.string().optional(),
});

type CalendlyFormValues = z.infer<typeof calendlyFormSchema>;

interface CalendlySettingsProps {
  initialData?: {
    calendlyUrl?: string;
    calendlyUserId?: string;
  };
  onSave: (data: CalendlyFormValues) => Promise<void>;
}

export function CalendlySettings({ initialData, onSave }: CalendlySettingsProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<CalendlyFormValues>({
    resolver: zodResolver(calendlyFormSchema),
    defaultValues: {
      calendlyUrl: initialData?.calendlyUrl || '',
      calendlyUserId: initialData?.calendlyUserId || '',
    },
  });

  async function onSubmit(data: CalendlyFormValues) {
    try {
      setIsSubmitting(true);
      await onSave(data);
      toast({
        title: 'Calendly settings saved',
        description: 'Your Calendly integration has been set up successfully.',
      });
    } catch (error) {
      console.error('Error saving Calendly settings:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save Calendly settings. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Calendly Integration</CardTitle>
        <CardDescription>
          Connect your Calendly account to allow clients to schedule 1:1 sessions with you.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="calendlyUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Calendly URL</FormLabel>
                  <FormControl>
                    <Input placeholder="https://calendly.com/yourusername" {...field} />
                  </FormControl>
                  <FormDescription>
                    Enter your Calendly scheduling page URL. This will be used to allow clients to book sessions with you.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save Calendly Settings
            </Button>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex flex-col items-start border-t px-6 py-4">
        <h4 className="text-sm font-medium">Don't have a Calendly account?</h4>
        <p className="text-sm text-muted-foreground mt-1">
          <a
            href="https://calendly.com/signup"
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:underline"
          >
            Sign up for Calendly
          </a>{' '}
          to get started with scheduling.
        </p>
      </CardFooter>
    </Card>
  );
}
