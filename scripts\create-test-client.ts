import { PrismaClient } from "@prisma/client"
import { hash } from "bcryptjs"

const prisma = new PrismaClient()

async function main() {
  const hashedPassword = await hash("test123", 12)

  const client = await prisma.user.create({
    data: {
      email: "<EMAIL>",
      password: hashedPassword,
      fullName: "Test Client",
      role: "client",
      bio: "I am a test client account",
    },
  })

  console.log("Created test client:", client)
}

main()
  .catch((error: unknown) => {
    if (error instanceof Error) {
      console.error("Error creating test client:", error.message)
    } else {
      console.error("Unknown error creating test client")
    }
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 