import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const data = await req.json()

    // Get the current user to access preferences
    const currentUser = await prisma.user.findUnique({
      where: {
        email: session.user.email
      }
    })

    if (!currentUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Parse existing preferences or create new object
    let preferences = {}
    if (currentUser.preferences) {
      try {
        preferences = typeof currentUser.preferences === 'string'
          ? JSON.parse(currentUser.preferences)
          : currentUser.preferences
      } catch (e) {
        console.error('Error parsing preferences:', e)
      }
    }

    // Update preferences with bio
    if (data.bio !== undefined) {
      preferences = {
        ...preferences,
        bio: data.bio
      }
    }

    // Update the user profile
    const updatedUser = await prisma.user.update({
      where: {
        email: session.user.email
      },
      data: {
        name: data.name,
        avatarUrl: data.avatarUrl,
        preferences: preferences
      }
    })

    // Make sure trainer profile exists
    const trainerProfile = await prisma.trainerProfile.findUnique({
      where: {
        userId: updatedUser.id
      }
    })

    if (!trainerProfile) {
      // Create a trainer profile if it doesn't exist
      await prisma.trainerProfile.create({
        data: {
          userId: updatedUser.id
        }
      })
    }

    return NextResponse.json(updatedUser)
  } catch (error) {
    console.error("Error updating profile:", error)
    return NextResponse.json(
      { error: "Failed to update profile" },
      { status: 500 }
    )
  }
}
