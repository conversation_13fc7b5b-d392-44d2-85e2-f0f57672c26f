'use client'

import { cva } from 'class-variance-authority'
import { CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'
import React from 'react'
import { Toaster as Sonner, toast as sonnerToast } from 'sonner'
import { cn } from '@/lib/utils'

// Enhanced toast styles using CVA
const toastVariants = cva(
  'group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-5 pr-7 shadow-lg transition-all',
  {
    variants: {
      variant: {
        default: 'bg-background border-border',
        info: 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/50 text-blue-800 dark:text-blue-200',
        success: 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/50 text-green-800 dark:text-green-200',
        warning: 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950/50 text-yellow-800 dark:text-yellow-200',
        error: 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50 text-red-800 dark:text-red-200',
        premium: 'border-purple-300 bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/50 text-purple-800 dark:text-purple-200',
        glassy: 'backdrop-blur-lg bg-white/70 dark:bg-black/50 border-white/20 dark:border-white/10',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
)

const iconMap = {
  info: <Info className="h-5 w-5 text-blue-500" />,
  success: <CheckCircle className="h-5 w-5 text-green-500" />,
  warning: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
  error: <AlertCircle className="h-5 w-5 text-red-500" />,
}

// Enhanced toast API
export const toast = {
  success: (message: string, description?: string) => {
    return sonnerToast(message, {
      description,
      icon: iconMap.success,
      className: cn(toastVariants({ variant: 'success' })),
    })
  },
  
  error: (message: string, description?: string) => {
    return sonnerToast(message, {
      description,
      icon: iconMap.error,
      className: cn(toastVariants({ variant: 'error' })),
    })
  },
  
  info: (message: string, description?: string) => {
    return sonnerToast(message, {
      description,
      icon: iconMap.info,
      className: cn(toastVariants({ variant: 'info' })),
    })
  },
  
  warning: (message: string, description?: string) => {
    return sonnerToast(message, {
      description,
      icon: iconMap.warning,
      className: cn(toastVariants({ variant: 'warning' })),
    })
  },
  
  premium: (message: string, description?: string) => {
    return sonnerToast(message, {
      description,
      className: cn(toastVariants({ variant: 'premium' })),
    })
  },
  
  dismiss: sonnerToast.dismiss,
  
  promise: sonnerToast.promise,
}

// Main Toaster component to place in layout
export function EnhancedToaster({
  position = 'top-right',
  duration = 5000,
  visibleToasts = 3,
  closeButton = true,
  className,
}: {
  position?: NonNullable<React.ComponentProps<typeof Sonner>['position']>
  duration?: number
  visibleToasts?: number
  closeButton?: boolean
  className?: string
}) {
  return (
    <Sonner
      theme="light"
      className={cn('toaster group', className)}
      toastOptions={{
        duration: duration,
        className: cn(
          'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg'
        ),
      }}
      position={position}
      visibleToasts={visibleToasts}
      closeButton={closeButton}
    />
  )
} 