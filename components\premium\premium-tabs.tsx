"use client"

import { <PERSON><PERSON><PERSON>, CalendarDays, Du<PERSON>bell, MessageSquare, Utensils, Activity } from "lucide-react"
import { useRouter } from "next/navigation"

interface PremiumTabsProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

export function PremiumTabs({ activeTab, onTabChange }: PremiumTabsProps) {
  const router = useRouter()

  return (
    <div className="premium-tabs-container grid w-full grid-cols-4 bg-gradient-to-r from-primary/10 via-purple-500/10 to-indigo-500/10 p-1 rounded-xl shadow-inner border border-primary/20">
      <button
        type="button"
        onClick={() => onTabChange("training")}
        className={`flex items-center justify-center gap-2 py-2 rounded-lg transition-all duration-300 cursor-pointer hover:bg-primary/5 ${activeTab === "training" ? "bg-gradient-to-r from-primary/80 to-purple-500/80 text-white shadow-lg" : ""}`}
      >
        <Dumbbell className="h-4 w-4" />
        <span>Training</span>
      </button>

      <button
        type="button"
        onClick={() => onTabChange("daily-flow")}
        className={`flex items-center justify-center gap-2 py-2 rounded-lg transition-all duration-300 cursor-pointer hover:bg-primary/5 ${activeTab === "daily-flow" ? "bg-gradient-to-r from-primary/80 to-purple-500/80 text-white shadow-lg" : ""}`}
      >
        <Activity className="h-4 w-4" />
        <span>Daily Flow</span>
      </button>

      <button
        type="button"
        onClick={() => onTabChange("calendar")}
        className={`flex items-center justify-center gap-2 py-2 rounded-lg transition-all duration-300 cursor-pointer hover:bg-primary/5 ${activeTab === "calendar" ? "bg-gradient-to-r from-primary/80 to-purple-500/80 text-white shadow-lg" : ""}`}
      >
        <CalendarDays className="h-4 w-4" />
        <span>1:1 Sessions</span>
      </button>

      <button
        type="button"
        onClick={() => onTabChange("messages")}
        className={`flex items-center justify-center gap-2 py-2 rounded-lg transition-all duration-300 cursor-pointer hover:bg-primary/5 ${activeTab === "messages" ? "bg-gradient-to-r from-primary/80 to-purple-500/80 text-white shadow-lg" : ""}`}
      >
        <MessageSquare className="h-4 w-4" />
        <span>Coach Chat</span>
      </button>
    </div>
  )
}
