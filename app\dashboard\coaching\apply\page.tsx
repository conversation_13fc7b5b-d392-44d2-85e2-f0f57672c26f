"use client"

import { <PERSON><PERSON><PERSON>ir<PERSON>, <PERSON><PERSON>ircle, Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import React, { useState, useEffect } from "react"
import { toast } from "sonner"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"

interface Trainer {
  id: string
  name: string
  avatarUrl?: string | null
}

export default function CoachingApplicationPage() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { data: session } = useSession()
  const router = useRouter()
  const [selectedTrainerId, setSelectedTrainerId] = useState("")
  const [selectedTrainerName, setSelectedTrainerName] = useState("")
  const [trainers, setTrainers] = useState<Trainer[]>([])
  const [isLoadingTrainers, setIsLoadingTrainers] = useState(true)

  useEffect(() => {
    const fetchTrainers = async () => {
      try {
        const response = await fetch('/api/trainers')
        if (!response.ok) {
          throw new Error('Failed to fetch trainers')
        }
        const data = await response.json()
        setTrainers(data.trainers || [])
      } catch (error) {
        toast.error('Failed to load trainers')
        console.error(error)
      } finally {
        setIsLoadingTrainers(false)
      }
    }

    fetchTrainers()
  }, [])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)

    if (!selectedTrainerId) {
      setError("Please select a trainer")
      setIsSubmitting(false)
      return
    }

    const formData = new FormData(e.currentTarget)
    
    try {
      const requestBody = {
        experienceLevel: formData.get("experience"),
        fitnessGoals: formData.get("goals"),
        preferredSchedule: formData.get("schedule"),
        medicalConditions: formData.get("medical"),
        additionalNotes: formData.get("notes"),
        trainerId: selectedTrainerId
      }

      const response = await fetch("/api/coaching/inquire", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to submit application")
      }

      setIsSuccess(true)
      e.currentTarget.reset()
      toast.success(`Thank you! We&apos;ll connect you with ${selectedTrainerName} soon.`)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Something went wrong")
      toast.error("Failed to submit application")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!session) {
    return (
      <div className="container max-w-4xl py-12">
        <Card>
          <CardHeader>
            <CardTitle>Apply for 1:1 Coaching</CardTitle>
            <CardDescription>
              You need to be signed in to apply for coaching.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => router.push("/login")}>Sign In</Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="container max-w-4xl py-12">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Apply for 1:1 Coaching</CardTitle>
          <CardDescription>
            Get personalized coaching to achieve your fitness goals.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isSuccess ? (
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertTitle>Application Submitted</AlertTitle>
              <AlertDescription>
                Thank you for your interest in our coaching program! We've received your application and will contact you soon to discuss the next steps.
              </AlertDescription>
              <div className="mt-4">
                <Button variant="outline" onClick={() => setIsSuccess(false)}>
                  Submit Another Application
                </Button>
              </div>
            </Alert>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <Label htmlFor="trainer" className="block text-sm font-medium mb-2">
                  Select Trainer
                </Label>
                {isLoadingTrainers ? (
                  <div className="flex items-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">Loading trainers...</span>
                  </div>
                ) : (
                  <Select
                    value={selectedTrainerId}
                    onValueChange={(value) => {
                      setSelectedTrainerId(value)
                      const trainer = trainers.find(t => t.id === value)
                      if (trainer) {
                        setSelectedTrainerName(trainer.name)
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a trainer" />
                    </SelectTrigger>
                    <SelectContent>
                      {trainers.map((trainer) => (
                        <SelectItem key={trainer.id} value={trainer.id}>
                          {trainer.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>

              <div>
                <Label htmlFor="experience" className="block text-sm font-medium mb-2">
                  Fitness Experience Level
                </Label>
                <RadioGroup defaultValue="beginner" name="experience" className="flex flex-col space-y-1">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="beginner" id="beginner" />
                    <Label htmlFor="beginner">Beginner - New to fitness/training</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="intermediate" id="intermediate" />
                    <Label htmlFor="intermediate">Intermediate - 1-3 years of consistent training</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="advanced" id="advanced" />
                    <Label htmlFor="advanced">Advanced - 3+ years of dedicated training</Label>
                  </div>
                </RadioGroup>
              </div>

              <div>
                <Label htmlFor="goals" className="block text-sm font-medium mb-2">
                  Your Fitness Goals
                </Label>
                <Textarea
                  id="goals"
                  name="goals"
                  placeholder="What are you trying to achieve? E.g., weight loss, muscle gain, improved performance, etc."
                  rows={3}
                  required
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Be specific to help us match you with the right program.
                </p>
              </div>

              <div>
                <Label htmlFor="schedule" className="block text-sm font-medium mb-2">
                  Preferred Training Schedule
                </Label>
                <Textarea
                  id="schedule"
                  name="schedule"
                  placeholder="How many days per week can you train? Any specific time preferences? (Include your timezone)"
                  rows={2}
                  required
                />
              </div>

              <Separator />

              <div>
                <Label htmlFor="medical" className="block text-sm font-medium mb-2">
                  Medical Conditions & Limitations
                </Label>
                <Textarea
                  id="medical"
                  name="medical"
                  placeholder="Any injuries, medical conditions, or limitations we should know about? (Optional)"
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="notes" className="block text-sm font-medium mb-2">
                  Additional Notes
                </Label>
                <Textarea
                  id="notes"
                  name="notes"
                  placeholder="Anything else you'd like to share with your potential coach? (Optional)"
                  rows={2}
                />
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button type="submit" disabled={isSubmitting || isLoadingTrainers}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  "Submit Application"
                )}
              </Button>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 