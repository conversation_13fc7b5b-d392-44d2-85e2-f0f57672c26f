"use client"

import React, { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Filter, MoreHorizontal, SlidersHorizontal } from "lucide-react"

interface TrainingPlan {
  id: string
  name: string
  duration: number
  difficulty: string
  isTemplate: boolean
  assignedCount: number
}

interface PlanTableProps {
  data: TrainingPlan[]
}

export function PlanTable({ data }: PlanTableProps) {
  const router = useRouter()
  const [selectedRows, setSelectedRows] = useState<string[]>([])
  const [selectedColumns, setSelectedColumns] = useState<string[]>([
    "name",
    "duration",
    "difficulty",
    "template",
    "assigned",
  ])

  const toggleColumn = (columnName: string) => {
    if (selectedColumns.includes(columnName)) {
      setSelectedColumns(selectedColumns.filter((col) => col !== columnName))
    } else {
      setSelectedColumns([...selectedColumns, columnName])
    }
  }

  return (
    <div>
      <div className="flex items-center py-4 justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <Filter className="h-3.5 w-3.5" />
            <span>Filter</span>
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="ml-auto h-8 gap-1">
                <SlidersHorizontal className="h-3.5 w-3.5" />
                <span>Columns</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuCheckboxItem
                checked={selectedColumns.includes("name")}
                onCheckedChange={() => toggleColumn("name")}
              >
                Name
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={selectedColumns.includes("duration")}
                onCheckedChange={() => toggleColumn("duration")}
              >
                Duration (Weeks)
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={selectedColumns.includes("difficulty")}
                onCheckedChange={() => toggleColumn("difficulty")}
              >
                Difficulty
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={selectedColumns.includes("template")}
                onCheckedChange={() => toggleColumn("template")}
              >
                Type (Template/Custom)
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={selectedColumns.includes("assigned")}
                onCheckedChange={() => toggleColumn("assigned")}
              >
                Assigned Clients
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox
                  checked={selectedRows.length === data.length && data.length > 0}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedRows(data.map((row) => row.id));
                    } else {
                      setSelectedRows([]);
                    }
                  }}
                  aria-label="Select all"
                />
              </TableHead>
              {selectedColumns.includes("name") && <TableHead>Name</TableHead>}
              {selectedColumns.includes("duration") && <TableHead>Duration</TableHead>}
              {selectedColumns.includes("difficulty") && <TableHead>Difficulty</TableHead>}
              {selectedColumns.includes("template") && <TableHead>Type</TableHead>}
              {selectedColumns.includes("assigned") && <TableHead>Assigned</TableHead>}
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length ? (
              data.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={selectedRows.includes(row.id) && "selected"}
                >
                  <TableCell>
                    <Checkbox
                      checked={selectedRows.includes(row.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedRows([...selectedRows, row.id]);
                        } else {
                          setSelectedRows(selectedRows.filter((id) => id !== row.id));
                        }
                      }}
                      aria-label="Select row"
                    />
                  </TableCell>
                  {selectedColumns.includes("name") && <TableCell className="font-medium">{row.name}</TableCell>}
                  {selectedColumns.includes("duration") && <TableCell>{row.duration}</TableCell>}
                  {selectedColumns.includes("difficulty") && <TableCell>{row.difficulty}</TableCell>}
                  {selectedColumns.includes("template") && <TableCell>{row.isTemplate ? 'Template' : 'Custom'}</TableCell>}
                  {selectedColumns.includes("assigned") && <TableCell>{row.assignedCount}</TableCell>}
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => router.push(`/dashboard/training-plans/preview/${row.id}`)}>View Plan</DropdownMenuItem>
                        <DropdownMenuItem onClick={() => router.push(`/dashboard/training-plans/${row.id}/edit`)}>Edit</DropdownMenuItem>
                        <DropdownMenuItem>Duplicate</DropdownMenuItem>
                        <DropdownMenuItem>Assign</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={selectedColumns.length + 2} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="text-sm text-muted-foreground">
          {selectedRows.length} of {data.length} row(s) selected.
        </div>
        <div className="flex items-center space-x-2">
          {selectedRows.length > 0 && (
             <Button variant="outline" size="sm" className="h-8">
              Assign Selected ({selectedRows.length})
            </Button>
          )}
          <Button variant="outline" size="sm" className="h-8">
            Previous
          </Button>
          <Button variant="outline" size="sm" className="h-8">
            Next
          </Button>
        </div>
      </div>
    </div>
  )
}