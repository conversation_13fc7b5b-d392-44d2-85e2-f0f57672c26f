import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('Setting up advanced customization for demo trainer...')

  // Get the current theme settings
  const demoTrainer = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  })

  if (!demoTrainer) {
    console.error('Demo trainer not found. Please run setup-demo-trainer.ts first.')
    return
  }

  // Parse current theme settings
  let currentTheme = {}
  try {
    if (demoTrainer.themeSettings) {
      currentTheme = JSON.parse(demoTrainer.themeSettings as string)
    }
  } catch (e) {
    console.error('Error parsing current theme settings:', e)
  }

  // Add advanced customization options
  const enhancedTheme = {
    ...currentTheme,
    // Enhanced header options
    videoBannerUrl: "https://player.vimeo.com/external/370331493.hd.mp4?s=ce49c8c6268e46525b22dfe77a4417ef2d387cb1&profile_id=175&oauth2_token_id=57447761",
    overlayOpacity: 0.6,
    textColor: "#FFFFFF",
    buttonStyle: "pill",
    buttonText: "View My Programs",
    showCredentials: true,
    credentials: ["Certified Trainer", "Nutrition Specialist", "5+ Years Experience"],
    testimonialHighlight: {
      quote: "Working with Demo Trainer completely transformed my fitness journey. I've lost 20 pounds and feel stronger than ever!",
      author: "John D., Client"
    },

    // Section styling
    sectionBackground: "#f8f9fa",
    sectionTextColor: "#333333",

    // Footer options
    footerStyle: "standard",
    footerBackground: "#212529",
    footerTextColor: "#ffffff",
    showNewsletter: true,
    copyrightText: `© ${new Date().getFullYear()} Demo Trainer. All rights reserved.`
  }

  // Update the trainer's theme settings
  await prisma.user.update({
    where: { email: '<EMAIL>' },
    data: {
      themeSettings: JSON.stringify(enhancedTheme)
    }
  })

  console.log('Advanced customization for demo trainer complete!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
