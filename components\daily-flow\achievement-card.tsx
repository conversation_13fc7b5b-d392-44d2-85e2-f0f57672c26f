"use client"

import { Award, Flame, Trophy, Star, Target, Zap } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface Achievement {
  id: string
  type: string
  name: string
  description: string
  earnedAt: string
  iconName: string | null
  level: number
}

interface AchievementCardProps {
  achievement: Achievement
  className?: string
}

export function AchievementCard({ achievement, className }: AchievementCardProps) {
  // Get icon based on achievement type and iconName
  const getIcon = () => {
    if (achievement.iconName === "fire") return <Flame className="h-5 w-5" />
    if (achievement.iconName === "trophy") return <Trophy className="h-5 w-5" />
    if (achievement.iconName === "star") return <Star className="h-5 w-5" />
    if (achievement.iconName === "target") return <Target className="h-5 w-5" />
    if (achievement.iconName === "zap") return <Zap className="h-5 w-5" />
    
    // Default to award icon
    return <Award className="h-5 w-5" />
  }
  
  // Get color based on achievement level
  const getLevelColor = () => {
    switch (achievement.level) {
      case 1:
        return "bg-bronze-100 text-bronze-800 border-bronze-200 dark:bg-bronze-900/30 dark:text-bronze-400 dark:border-bronze-800"
      case 2:
        return "bg-silver-100 text-silver-800 border-silver-200 dark:bg-silver-900/30 dark:text-silver-400 dark:border-silver-800"
      case 3:
        return "bg-gold-100 text-gold-800 border-gold-200 dark:bg-gold-900/30 dark:text-gold-400 dark:border-gold-800"
      default:
        return "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-800"
    }
  }
  
  // Get level name
  const getLevelName = () => {
    switch (achievement.level) {
      case 1:
        return "Bronze"
      case 2:
        return "Silver"
      case 3:
        return "Gold"
      default:
        return "Standard"
    }
  }
  
  return (
    <Card className={cn("premium-card group overflow-hidden", className)}>
      <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
      <CardHeader className="relative z-10 pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg font-bold premium-gradient-text">{achievement.name}</CardTitle>
          <Badge variant="outline" className={getLevelColor()}>
            {getLevelName()}
          </Badge>
        </div>
        <CardDescription>{achievement.description}</CardDescription>
      </CardHeader>
      <CardContent className="relative z-10 pt-0">
        <div className="flex justify-between items-center">
          <div className={`p-3 rounded-full ${getLevelColor()}`}>
            {getIcon()}
          </div>
          <div className="text-sm text-muted-foreground">
            Earned on {format(new Date(achievement.earnedAt), 'PPP')}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
