'use client';

import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Apple, Beef, Coffee, Pizza, Plus, Utensils } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const mealSchema = z.object({
  mealType: z.string({
    required_error: 'Please select a meal type.',
  }),
  name: z.string().min(2, {
    message: 'Meal name must be at least 2 characters.',
  }),
  calories: z.coerce.number().min(0).optional(),
  protein: z.coerce.number().min(0).optional(),
  carbs: z.coerce.number().min(0).optional(),
  fat: z.coerce.number().min(0).optional(),
});

const dailySchema = z.object({
  calories: z.coerce.number().min(0),
  protein: z.coerce.number().min(0),
  carbs: z.coerce.number().min(0),
  fat: z.coerce.number().min(0),
});

type MealFormValues = z.infer<typeof mealSchema>;
type DailyFormValues = z.infer<typeof dailySchema>;

interface RecentMeal {
  id: string;
  mealType: string;
  name: string;
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
}

interface QuickMealFormProps {
  onSuccess?: () => void;
  isEditing?: boolean;
  logId?: string;
  defaultValues?: {
    date?: Date;
    mealType?: string;
    name?: string;
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  };
}

export function QuickMealForm({ onSuccess, isEditing = false, logId, defaultValues }: QuickMealFormProps) {
  // Meal type colors
  const getMealTypeColor = (mealType: string) => {
    const typeColors: Record<string, { bg: string, text: string, border: string, hover: string }> = {
      'Breakfast': { bg: 'bg-amber-100', text: 'text-amber-800', border: 'border-amber-200', hover: 'hover:bg-amber-200' },
      'Lunch': { bg: 'bg-emerald-100', text: 'text-emerald-800', border: 'border-emerald-200', hover: 'hover:bg-emerald-200' },
      'Dinner': { bg: 'bg-indigo-100', text: 'text-indigo-800', border: 'border-indigo-200', hover: 'hover:bg-indigo-200' },
      'Snack': { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200', hover: 'hover:bg-purple-200' },
      'Pre-Workout': { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200', hover: 'hover:bg-blue-200' },
      'Post-Workout': { bg: 'bg-cyan-100', text: 'text-cyan-800', border: 'border-cyan-200', hover: 'hover:bg-cyan-200' },
      'Daily Total': { bg: 'bg-rose-100', text: 'text-rose-800', border: 'border-rose-200', hover: 'hover:bg-rose-200' },
      'Meal': { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200', hover: 'hover:bg-gray-200' }
    }

    // Default color if meal type not found
    return typeColors[mealType] || { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200', hover: 'hover:bg-gray-200' }
  }
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('meal');
  const [recentMeals, setRecentMeals] = useState<RecentMeal[]>([]);
  const [isLoadingMeals, setIsLoadingMeals] = useState(false);

  // Fetch recent meals for quick logging
  useEffect(() => {
    const fetchRecentMeals = async () => {
      try {
        setIsLoadingMeals(true);
        const response = await fetch('/api/nutrition-logs');
        if (response.ok) {
          const data = await response.json();
          // Get unique meals by name
          const uniqueMeals = data.reduce((acc: RecentMeal[], meal: RecentMeal) => {
            if (!acc.some(m => m.name === meal.name)) {
              acc.push(meal);
            }
            return acc;
          }, []);
          setRecentMeals(uniqueMeals.slice(0, 6)); // Limit to 6 recent meals
        }
      } catch (error) {
        console.error('Error fetching recent meals:', error);
      } finally {
        setIsLoadingMeals(false);
      }
    };

    fetchRecentMeals();
  }, []);

  const mealForm = useForm<MealFormValues>({
    resolver: zodResolver(mealSchema),
    defaultValues: defaultValues ? {
      mealType: defaultValues.mealType || '',
      name: defaultValues.name || '',
      calories: defaultValues.calories,
      protein: defaultValues.protein,
      carbs: defaultValues.carbs,
      fat: defaultValues.fat,
    } : {
      mealType: '',
      name: '',
      calories: undefined,
      protein: undefined,
      carbs: undefined,
      fat: undefined,
    },
  });

  const dailyForm = useForm<DailyFormValues>({
    resolver: zodResolver(dailySchema),
    defaultValues: {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
    },
  });

  // Function to log a previous meal quickly
  const logPreviousMeal = async (meal: RecentMeal) => {
    setIsSubmitting(true);
    try {
      const payload = {
        date: new Date().toISOString(),
        mealType: meal.mealType,
        name: meal.name,
        calories: meal.calories,
        protein: meal.protein,
        carbs: meal.carbs,
        fat: meal.fat,
      };

      const response = await fetch('/api/nutrition-logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Failed to save nutrition log');
      }

      toast({
        title: 'Meal logged',
        description: `${meal.name} has been saved.`,
      });

      // Call onSuccess callback immediately to close the dialog
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving nutrition log:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save your nutrition log. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  async function onSubmitMeal(values: MealFormValues) {
    setIsSubmitting(true);
    try {
      const payload = {
        date: new Date().toISOString(),
        mealType: values.mealType,
        name: values.name,
        calories: values.calories,
        protein: values.protein,
        carbs: values.carbs,
        fat: values.fat,
      };

      const url = isEditing && logId ? `/api/nutrition-logs/${logId}` : '/api/nutrition-logs';
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Failed to save nutrition log');
      }

      toast({
        title: isEditing ? 'Meal updated' : 'Meal logged',
        description: isEditing
          ? `Your ${values.mealType.toLowerCase()} has been updated.`
          : `Your ${values.mealType.toLowerCase()} has been saved.`,
      });

      mealForm.reset();

      // Call onSuccess callback immediately to close the dialog
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving nutrition log:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save your nutrition log. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  async function onSubmitDaily(values: DailyFormValues) {
    setIsSubmitting(true);
    try {
      // Create a breakfast entry with the daily totals
      const payload = {
        date: new Date().toISOString(),
        mealType: 'Daily Total',
        name: 'Daily Nutrition',
        calories: values.calories,
        protein: values.protein,
        carbs: values.carbs,
        fat: values.fat,
      };

      const response = await fetch('/api/nutrition-logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Failed to save daily nutrition');
      }

      toast({
        title: 'Daily nutrition logged',
        description: 'Your daily nutrition totals have been saved.',
      });

      dailyForm.reset();

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving daily nutrition:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save your daily nutrition. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  // Quick meal type selection
  const mealTypeOptions = [
    { value: 'Breakfast', label: 'Breakfast', icon: <Coffee className="h-4 w-4 mr-2" /> },
    { value: 'Lunch', label: 'Lunch', icon: <Pizza className="h-4 w-4 mr-2" /> },
    { value: 'Dinner', label: 'Dinner', icon: <Utensils className="h-4 w-4 mr-2" /> },
    { value: 'Snack', label: 'Snack', icon: <Apple className="h-4 w-4 mr-2" /> },
    { value: 'Pre-Workout', label: 'Pre-Workout', icon: <Beef className="h-4 w-4 mr-2" /> },
    { value: 'Post-Workout', label: 'Post-Workout', icon: <Plus className="h-4 w-4 mr-2" /> },
  ];

  return (
    <Tabs defaultValue="meal" value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-2 mb-4">
        <TabsTrigger value="meal" className="text-base py-2">Log a Meal</TabsTrigger>
        <TabsTrigger value="daily" className="text-base py-2">Daily Totals</TabsTrigger>
      </TabsList>

      <TabsContent value="meal">
        {/* Quick Log Section */}
        {recentMeals.length > 0 && (
          <div className="mb-6">
            <h3 className="text-base font-medium mb-3">Quick Log Previous Meals</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {isLoadingMeals ? (
                Array(6).fill(0).map((_, i) => (
                  <div key={i} className="h-20 rounded-md bg-muted animate-pulse"></div>
                ))
              ) : (
                recentMeals.map((meal) => (
                  <Button
                    key={meal.id}
                    variant="outline"
                    className={`h-auto py-3 px-3 flex flex-col items-start text-left justify-start ${getMealTypeColor(meal.mealType).bg} ${getMealTypeColor(meal.mealType).text} ${getMealTypeColor(meal.mealType).hover}`}
                    onClick={() => logPreviousMeal(meal)}
                    disabled={isSubmitting}
                  >
                    <span className="font-medium text-sm truncate w-full">{meal.name}</span>
                    <div className="flex items-center gap-1 mt-1">
                      <span className="text-xs text-muted-foreground">{meal.mealType}</span>
                      {meal.calories && (
                        <span className="text-xs bg-primary/10 text-primary px-1 rounded">
                          {meal.calories} kcal
                        </span>
                      )}
                    </div>
                  </Button>
                ))
              )}
            </div>
          </div>
        )}

        <Form {...mealForm}>
          <form onSubmit={mealForm.handleSubmit(onSubmitMeal)} className="space-y-6">
            {/* Meal Type Selection */}
            <FormField
              control={mealForm.control}
              name="mealType"
              render={({ field }) => (
                <FormItem className="space-y-4">
                  <FormLabel className="text-base font-medium">Meal Type</FormLabel>
                  <div className="grid grid-cols-3 gap-2">
                    {mealTypeOptions.map(option => (
                      <Button
                        key={option.value}
                        type="button"
                        variant="outline"
                        className={`flex flex-col items-center justify-center h-20 p-2 ${field.value === option.value ?
                          `${getMealTypeColor(option.value).bg} ${getMealTypeColor(option.value).text} ring-2 ${getMealTypeColor(option.value).border}` :
                          'bg-background'} ${getMealTypeColor(option.value).hover}`}
                        onClick={() => field.onChange(option.value)}
                      >
                        <div className="text-xl mb-1">{option.icon}</div>
                        <span className="text-xs">{option.label}</span>
                      </Button>
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Meal Name */}
            <FormField
              control={mealForm.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-medium">Meal Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter meal name"
                      {...field}
                      className="h-10 text-base"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Macros Card */}
            <Card className="border-2 border-muted">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-base font-medium">Macronutrients</h3>
                  <Badge variant="outline" className="text-xs font-normal">Optional</Badge>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <FormField
                    control={mealForm.control}
                    name="calories"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex justify-between">
                          <span>Calories</span>
                          <span className="text-muted-foreground">kcal</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0"
                            {...field}
                            className="text-center font-medium"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={mealForm.control}
                    name="protein"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex justify-between">
                          <span>Protein</span>
                          <span className="text-muted-foreground">g</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="0"
                            {...field}
                            className="text-center font-medium"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={mealForm.control}
                    name="carbs"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex justify-between">
                          <span>Carbs</span>
                          <span className="text-muted-foreground">g</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="0"
                            {...field}
                            className="text-center font-medium"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={mealForm.control}
                    name="fat"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex justify-between">
                          <span>Fat</span>
                          <span className="text-muted-foreground">g</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="0"
                            {...field}
                            className="text-center font-medium"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save Meal'}
            </Button>
          </form>
        </Form>
      </TabsContent>

      <TabsContent value="daily">
        <Form {...dailyForm}>
          <form onSubmit={dailyForm.handleSubmit(onSubmitDaily)} className="space-y-6">
            <div className="mb-4">
              <h3 className="text-base font-medium mb-2">Daily Nutrition Totals</h3>
              <p className="text-sm text-muted-foreground">Enter your total macros for the entire day</p>
            </div>

            <Card className="border-2 border-muted">
              <CardContent className="pt-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <FormField
                    control={dailyForm.control}
                    name="calories"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex justify-between">
                          <span>Calories</span>
                          <span className="text-muted-foreground">kcal</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0"
                            {...field}
                            className="text-center font-medium"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={dailyForm.control}
                    name="protein"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex justify-between">
                          <span>Protein</span>
                          <span className="text-muted-foreground">g</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="0"
                            {...field}
                            className="text-center font-medium"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={dailyForm.control}
                    name="carbs"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex justify-between">
                          <span>Carbs</span>
                          <span className="text-muted-foreground">g</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="0"
                            {...field}
                            className="text-center font-medium"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={dailyForm.control}
                    name="fat"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex justify-between">
                          <span>Fat</span>
                          <span className="text-muted-foreground">g</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="0"
                            {...field}
                            className="text-center font-medium"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save Daily Totals'}
            </Button>
          </form>
        </Form>
      </TabsContent>
    </Tabs>
  );
}
