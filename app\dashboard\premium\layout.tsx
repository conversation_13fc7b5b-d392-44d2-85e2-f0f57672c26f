"use client"

import { AlertCircle } from "lucide-react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { useEffect, ReactNode, useState } from "react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { LoadingSpinner } from "@/components/ui/loading-spinner"

interface PremiumLayoutProps {
  children: ReactNode
}

export default function PremiumLayout({ children }: PremiumLayoutProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isClient, setIsClient] = useState(false)

  // Get premium status from real user data
  const isPremiumFromUser = (session?.user as any)?.subscriptionTier === 'premium'

  // Check for dev cookie overrides
  const [isPremiumFromCookie, setIsPremiumFromCookie] = useState(false)
  const [hasRoleOverride, setHasRoleOverride] = useState(false)
  const [roleOverride, setRoleOverride] = useState<string>("")

  useEffect(() => {
    setIsClient(true)

    // Get cookies in development mode
    if (process.env.NODE_ENV === 'development') {
      const cookies = document.cookie.split(';')

      // Check for premium status cookie
      const premiumCookie = cookies.find(cookie => cookie.trim().startsWith('dev_premium_status='))
      if (premiumCookie) {
        const isPremium = premiumCookie.split('=')[1] === 'true'
        setIsPremiumFromCookie(isPremium)
        console.log('Dev premium status from cookie:', isPremium)
      }

      // Check for role override
      const roleCookie = cookies.find(cookie => cookie.trim().startsWith('dev_override_role='))
      if (roleCookie) {
        const role = roleCookie.split('=')[1]
        setHasRoleOverride(true)
        setRoleOverride(role)
        console.log('Dev role override from cookie:', role)
      }
    }
  }, [])

  // All clients are premium by default now
  const effectivePremiumStatus = session?.user?.role === 'client' || isPremiumFromUser || isPremiumFromCookie

  // Force log the premium status detection for debugging
  console.log('Premium status detection:', {
    roleOverride,
    isPremiumFromCookie,
    isPremiumFromUser,
    effectivePremiumStatus,
    env: process.env.NODE_ENV,
    isClient
  })

  // Debug logging
  console.log('[PremiumLayout] Access check:', {
    roleOverride,
    isPremiumFromCookie,
    isPremiumFromUser,
    effectivePremiumStatus,
    env: process.env.NODE_ENV,
    isClient
  })

  useEffect(() => {
    // Only run on client side
    if (!isClient) return

    // Only redirect if not in loading state and no premium access
    if (status !== "loading" && !effectivePremiumStatus) {
      router.push("/dashboard/upgrade")
    }
  }, [status, router, effectivePremiumStatus, isClient])

  // Show loading state while checking authentication
  if (status === "loading" || !isClient) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <LoadingSpinner size="md" />
      </div>
    )
  }

  // If not premium, show an error message (will redirect soon)
  if (!effectivePremiumStatus) {
    return (
      <div className="container py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            This area is reserved for premium members only. You'll be redirected momentarily.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  // Premium user - render the content
  return <>{children}</>
}