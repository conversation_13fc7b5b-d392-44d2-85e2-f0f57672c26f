/* Modern Chat UI Styles */
:root {
  /* Light mode - using the website's color palette */
  --chat-primary: hsl(var(--primary));
  --chat-primary-light: hsl(var(--primary) / 0.1);
  --chat-primary-dark: hsl(var(--primary) / 0.8);
  --chat-bg: hsl(var(--background));
  --chat-sidebar-bg: hsl(var(--background));
  --chat-border: hsl(var(--border));
  --chat-text: hsl(var(--foreground));
  --chat-text-light: hsl(var(--muted-foreground));
  --chat-bubble-sender: hsl(var(--secondary));
  --chat-bubble-receiver: hsl(var(--primary));
  --chat-bubble-receiver-text: hsl(var(--primary-foreground));
  --chat-hover: hsl(var(--muted) / 0.5);
  --chat-active: hsl(var(--primary) / 0.1);
  --chat-shadow: rgba(0, 0, 0, 0.05);
  --chat-radius: 16px;
  --chat-input-radius: 24px;
  --chat-app-border: hsl(var(--border));
}

/* Dark mode styles */
.dark {
  --chat-primary: hsl(var(--primary));
  --chat-primary-light: hsl(var(--primary) / 0.2);
  --chat-primary-dark: hsl(var(--primary) / 0.8);
  --chat-bg: hsl(var(--background));
  --chat-sidebar-bg: hsl(var(--card));
  --chat-border: hsl(var(--border));
  --chat-text: hsl(var(--foreground));
  --chat-text-light: hsl(var(--muted-foreground));
  --chat-bubble-sender: hsl(var(--secondary));
  --chat-bubble-receiver: hsl(var(--primary));
  --chat-bubble-receiver-text: hsl(var(--primary-foreground));
  --chat-hover: hsl(var(--muted) / 0.3);
  --chat-active: hsl(var(--primary) / 0.2);
  --chat-shadow: rgba(0, 0, 0, 0.2);
  --chat-app-border: hsl(var(--border));
}

/* Main Chat Container */
.chat-container {
  height: calc(100vh - 120px);
  background-color: var(--chat-bg);
  border-radius: var(--chat-radius);
  overflow: hidden;
  box-shadow: 0 2px 12px var(--chat-shadow);
  border: 1px solid var(--chat-app-border);
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Sidebar Styles */
.chat-sidebar {
  width: 100%;
  border-right: 1px solid var(--chat-border);
  display: flex;
  flex-direction: column;
  background-color: var(--chat-sidebar-bg);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.chat-sidebar-header {
  padding: 16px;
  border-bottom: 1px solid var(--chat-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-sidebar-tabs {
  display: flex;
  padding: 8px;
  gap: 8px;
}

.chat-sidebar-tab {
  flex: 1;
  text-align: center;
  padding: 10px 12px;
  cursor: pointer;
  font-weight: 600;
  color: var(--chat-text-light);
  position: relative;
  transition: all 0.2s ease;
  border-radius: 20px;
}

.chat-sidebar-tab.active,
.chat-sidebar-tab[data-state="active"] {
  color: white;
  background-color: var(--chat-primary);
}

.chat-sidebar-tab .badge {
  background-color: var(--chat-primary);
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 12px;
  margin-left: 5px;
}

/* Search Box */
.chat-search {
  padding: 12px 16px;
  position: relative;
}

.chat-search input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border-radius: var(--chat-input-radius);
  border: 1px solid var(--chat-border);
  background-color: hsl(var(--secondary));
  color: var(--chat-text);
  font-size: 14px;
  transition: all 0.2s ease;
}

.chat-search input:focus {
  border-color: var(--chat-primary);
  box-shadow: 0 0 0 2px var(--chat-primary-light);
  outline: none;
}

.chat-search svg {
  position: absolute;
  left: 28px;
  top: 22px;
  color: var(--chat-text-light);
}

/* Conversation List */
.chat-list {
  flex: 1;
  overflow-y: auto;
}

.chat-list-item {
  padding: 14px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-bottom: 1px solid var(--chat-border);
  transition: background-color 0.2s ease;
  position: relative;
}

.chat-list-item:hover {
  background-color: var(--chat-hover);
}

.chat-list-item.active {
  background-color: var(--chat-primary-light);
}

.chat-list-item.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--chat-primary);
}

/* Selected conversation styling */
.chat-list-item.selected {
  background-color: var(--chat-primary-light);
  border-left: 3px solid var(--chat-primary);
}

.chat-list-item-avatar {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
  border: 1px solid var(--chat-border);
  flex-shrink: 0;
}

.chat-list-item-content {
  flex: 1;
  min-width: 0;
  position: relative;
}

.chat-list-item-name {
  font-weight: 600;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--chat-text);
  font-size: 14px;
}

.chat-list-item-time {
  font-size: 11px;
  color: var(--chat-text-light);
  white-space: nowrap;
}

.chat-list-item-message {
  font-size: 12px;
  color: var(--chat-text-light);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  line-height: 1.4;
  max-width: 95%;
}

.chat-list-item-message svg {
  margin-right: 4px;
  flex-shrink: 0;
}

.chat-list-item-badge {
  background-color: var(--chat-primary);
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  flex-shrink: 0;
  padding: 0 4px;
  box-shadow: 0 1px 2px rgba(255, 107, 157, 0.3);
  margin-right: 4px;
}

/* Unread conversation styling */
.chat-list-item.unread {
  background-color: rgba(255, 107, 157, 0.05);
}

.chat-list-item.unread .chat-list-item-name {
  color: var(--chat-primary-dark);
  font-weight: 600;
}

.chat-list-item.unread .chat-list-item-message {
  color: var(--chat-text);
  font-weight: 500;
}

.chat-list-item.unread::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--chat-primary);
}

/* Ensure the badge is visible and properly positioned */
.chat-list-item-name {
  position: relative; /* Position relative for absolute badge positioning */
}

/* Style for the flex container with badge and time */
.chat-list-item-name .flex {
  display: flex;
  align-items: center;
}

/* Main Chat Area */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--chat-bg);
}

.chat-header {
  padding: 16px;
  border-bottom: 1px solid var(--chat-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--chat-bg);
}

.chat-header-user {
  display: flex;
  align-items: center;
}

.chat-header-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
  border: 1px solid var(--chat-border);
}

.chat-header-info h3 {
  font-weight: 600;
  margin: 0;
  font-size: 16px;
  color: var(--chat-text);
}

.chat-header-info p {
  margin: 0;
  font-size: 13px;
  color: var(--chat-text-light);
}

.chat-header-actions {
  display: flex;
  gap: 16px;
}

.chat-header-actions button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--chat-text-light);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.chat-header-actions button:hover {
  background-color: var(--chat-hover);
  color: var(--chat-primary);
}

/* Messages Area */
.chat-messages {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background-color: var(--chat-bg);
}

.message {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.message.outgoing {
  align-items: flex-end;
}

.message-bubble {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  max-width: 80%;
}

.message-content {
  padding: 14px 18px;
  border-radius: 18px;
  position: relative;
  margin-bottom: 4px;
  box-shadow: 0 1px 3px var(--chat-shadow);
  word-break: break-word;
  line-height: 1.5;
}

.message.incoming .message-content {
  background-color: var(--chat-bubble-sender);
  color: var(--chat-text);
  border-bottom-left-radius: 4px;
  box-shadow: 0 1px 3px var(--chat-shadow);
}

.message.outgoing .message-content {
  background-color: var(--chat-bubble-receiver);
  color: var(--chat-bubble-receiver-text);
  border-bottom-right-radius: 4px;
  box-shadow: 0 1px 3px var(--chat-shadow);
}

/* Optimistic message styling */
.message.outgoing.opacity-80 .message-content {
  background-color: var(--chat-bubble-receiver);
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.message-bubble.optimistic {
  position: relative;
}

.message-bubble.optimistic::after {
  content: "";
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--chat-primary-light);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

.message-time {
  font-size: 11px;
  color: var(--chat-text-light);
  margin-top: 4px;
  opacity: 0.8;
  display: flex;
  align-items: center;
}

.message-time svg {
  margin-left: 4px;
  height: 12px;
  width: 12px;
}

.message.outgoing .message-time {
  justify-content: flex-end;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid var(--chat-border);
}

/* Input Area */
.chat-input {
  padding: 16px 24px;
  border-top: 1px solid var(--chat-border);
  display: flex;
  align-items: center;
  background-color: var(--chat-bg);
}

.chat-input-container {
  display: flex;
  align-items: center;
  flex: 1;
  background-color: hsl(var(--secondary));
  border-radius: var(--chat-input-radius);
  padding: 0 12px 0 18px;
  border: 1px solid var(--chat-border);
  box-shadow: 0 1px 3px var(--chat-shadow);
}

.chat-input-container:focus-within {
  border-color: var(--chat-primary);
  box-shadow: 0 0 0 2px var(--chat-primary-light);
}

.chat-input-actions {
  display: flex;
  gap: 8px;
  margin-right: 8px;
}

.chat-input-actions button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--chat-text-light);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.chat-input-actions button:hover {
  color: var(--chat-primary);
}

.chat-input input {
  flex: 1;
  padding: 12px 0;
  border: none;
  background-color: transparent;
  font-size: 14px;
}

.chat-input input:focus {
  outline: none;
}

.chat-input button.send-button {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: var(--chat-primary);
  color: var(--chat-bubble-receiver-text);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 12px;
  transition: all 0.2s ease;
  box-shadow: 0 3px 8px var(--chat-shadow);
}

.chat-input button.send-button:hover {
  background-color: var(--chat-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 5px 10px var(--chat-shadow);
}

.chat-input button.send-button:disabled {
  background-color: var(--chat-text-light);
  cursor: not-allowed;
  box-shadow: none;
}

/* Empty State */
.chat-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--chat-text);
  text-align: center;
  padding: 0 32px;
  background-color: var(--chat-bg);
}

.chat-empty svg {
  color: var(--chat-primary);
  margin-bottom: 16px;
  width: 64px;
  height: 64px;
  opacity: 0.7;
}

.chat-empty h3 {
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 24px;
  color: var(--chat-text);
}

.chat-empty p {
  color: var(--chat-text-light);
  margin-bottom: 20px;
  max-width: 400px;
  font-size: 15px;
  line-height: 1.6;
}

.chat-empty button {
  background-color: var(--chat-primary);
  color: var(--chat-bubble-receiver-text);
  border: none;
  padding: 12px 24px;
  border-radius: var(--chat-input-radius);
  cursor: pointer;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px var(--chat-shadow);
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-empty button:hover {
  background-color: var(--chat-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px var(--chat-shadow);
}

/* Custom TabsList and TabsTrigger styles to match the design */
[role="tabslist"] {
  background-color: transparent !important;
  border: none !important;
}

[role="tab"][data-state="active"] {
  background-color: var(--chat-primary) !important;
  color: white !important;
  border: none !important;
  font-weight: 500;
}

[role="tab"] {
  border-radius: 20px !important;
  padding: 8px 16px !important;
  transition: all 0.2s ease;
}
