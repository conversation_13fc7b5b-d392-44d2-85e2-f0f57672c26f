import { DndContext, DragEndEvent, DragOverlay, DragStartEvent, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, useDroppable } from "@dnd-kit/core"
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { 
  Trash2, 
  <PERSON><PERSON><PERSON>,
  Heart,
  Flame,
  Activity,
  Clock,
  Youtube,
  Pencil
} from "lucide-react"
import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

export interface Exercise {
  id: string
  name: string
  sets: number
  reps: number
  weight: number
  category?: string
  video?: string
  description?: string
  targetMuscles?: string[]
  difficulty?: "Beginner" | "Intermediate" | "Advanced"
  duration?: string
}

export const AVAILABLE_EXERCISES: Exercise[] = [
  {
    id: "bench-press",
    name: "Bench Press",
    sets: 3,
    reps: 10,
    weight: 0,
    category: "strength",
    video: "https://www.youtube.com/watch?v=rT7DgCr-3pg",
    description: "Classic chest exercise for building upper body strength",
    targetMuscles: ["Chest", "Triceps", "Shoulders"],
    difficulty: "Intermediate",
    duration: "15-20 mins"
  },
  {
    id: "squat",
    name: "Squat",
    sets: 3,
    reps: 12,
    weight: 0,
    category: "strength",
    video: "https://www.youtube.com/watch?v=U3HlEF_E9fo",
    description: "Fundamental lower body exercise targeting multiple muscle groups",
    targetMuscles: ["Quadriceps", "Hamstrings", "Glutes"],
    difficulty: "Intermediate",
    duration: "15-20 mins"
  },
  {
    id: "deadlift",
    name: "Deadlift",
    sets: 3,
    reps: 8,
    weight: 0,
    category: "strength",
    video: "https://www.youtube.com/watch?v=r4MzxtBKyNE",
    description: "Compound exercise for overall strength and power",
    targetMuscles: ["Back", "Hamstrings", "Core"],
    difficulty: "Advanced",
    duration: "20-25 mins"
  },
  {
    id: "running",
    name: "Running",
    sets: 1,
    reps: 1,
    weight: 0,
    category: "cardio",
    description: "Cardiovascular exercise for endurance",
    targetMuscles: ["Legs", "Lungs", "Heart"],
    difficulty: "Beginner",
    duration: "20-30 mins"
  },
  {
    id: "yoga",
    name: "Yoga Flow",
    sets: 1,
    reps: 1,
    weight: 0,
    category: "flexibility",
    description: "Improves flexibility and mind-body connection",
    targetMuscles: ["Full Body"],
    difficulty: "Beginner",
    duration: "15-20 mins"
  }
]

export function getExerciseIcon(type?: string) {
  switch (type?.toLowerCase()) {
    case 'strength':
      return <Dumbbell className="h-5 w-5 text-blue-500" />
    case 'cardio':
      return <Heart className="h-5 w-5 text-green-500" />
    case 'flexibility':
      return <Flame className="h-5 w-5 text-purple-500" />
    default:
      return <Activity className="h-5 w-5 text-gray-500" />
  }
}

function SortableExerciseItem({ exercise, onUpdate, onRemove }: { 
  exercise: Exercise
  onUpdate: (exercise: Exercise) => void
  onRemove: () => void
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useSortable({ id: exercise.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <Card 
      ref={setNodeRef} 
      style={style} 
      className="bg-[#12131A] border-0 text-white hover:bg-[#1A1B23] transition-colors cursor-grab active:cursor-grabbing"
      {...attributes}
      {...listeners}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-4">
          <div className="p-2 rounded-full bg-[#1A1B23]">
            {getExerciseIcon(exercise.category)}
          </div>
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <p className="font-medium">{exercise.name}</p>
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white hover:bg-[#2A2B33]"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onUpdate(exercise);
                  }}
                >
                  <Pencil className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white hover:bg-[#2A2B33]"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onRemove();
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <span className="px-2 py-1 rounded-full text-xs bg-blue-500/20 text-blue-400">
                {exercise.category}
              </span>
              {exercise.difficulty && (
                <span className="px-2 py-1 rounded-full text-xs bg-purple-500/20 text-purple-400">
                  {exercise.difficulty}
                </span>
              )}
              {exercise.targetMuscles?.map((muscle, index) => (
                <span key={index} className="px-2 py-1 rounded-full text-xs bg-green-500/20 text-green-400">
                  {muscle}
                </span>
              ))}
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-1">
                <Activity className="h-4 w-4" />
                <span>{exercise.sets} sets × {exercise.reps} reps</span>
              </div>
              {exercise.weight > 0 && (
                <div className="flex items-center gap-1">
                  <Dumbbell className="h-4 w-4" />
                  <span>{exercise.weight} kg</span>
                </div>
              )}
              {exercise.duration && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>{exercise.duration}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function DraggableExerciseItem({ exercise }: { exercise: Exercise }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: exercise.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <Card 
      ref={setNodeRef} 
      style={style} 
      className="bg-[#12131A] border-0 text-white hover:bg-[#1A1B23] transition-colors cursor-grab active:cursor-grabbing"
      {...attributes}
      {...listeners}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-4">
          <div className="p-2 rounded-full bg-[#1A1B23]">
            {getExerciseIcon(exercise.category)}
          </div>
          <div className="flex-1">
            <p className="font-medium">{exercise.name}</p>
            <p className="text-sm text-gray-400">{exercise.category}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function DroppableArea({ id, children }: { id: string; children: React.ReactNode }) {
  const { setNodeRef } = useDroppable({
    id: id,
  });

  return (
    <div ref={setNodeRef} className="h-full">
      {children}
    </div>
  );
}

interface ExerciseManagerProps {
  exercises: Exercise[];
  onExercisesChange: (exercises: Exercise[]) => void;
  darkTheme?: boolean;
}

export default function ExerciseManager({ 
  exercises, 
  onExercisesChange,
  darkTheme = true
}: ExerciseManagerProps) {
  const [activeId, setActiveId] = useState<string | null>(null);
  const [activeExercise, setActiveExercise] = useState<Exercise | null>(null);
  const [showEditExercise, setShowEditExercise] = useState(false);
  const [editingExercise, setEditingExercise] = useState<Exercise | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
    
    const draggedExercise = [...exercises, ...AVAILABLE_EXERCISES].find(
      (exercise) => exercise.id === active.id
    );
    if (draggedExercise) {
      setActiveExercise(draggedExercise);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);
    setActiveExercise(null);

    if (!over) return;

    const isFromAvailable = AVAILABLE_EXERCISES.some(ex => ex.id === active.id);
    const isToAvailable = over.id === "available-exercises";
    const isFromPlan = exercises.some(ex => ex.id === active.id);
    const isToPlan = over.id === "plan-exercises";

    if (isFromAvailable && isToPlan) {
      // Add to plan - show edit dialog first
      const exercise = AVAILABLE_EXERCISES.find(ex => ex.id === active.id);
      if (exercise) {
        const newExercise = {
          ...exercise,
          id: `${exercise.id}_${Date.now()}`
        };
        setEditingExercise(newExercise);
        setShowEditExercise(true);
      }
    } else if (isFromPlan && isToAvailable) {
      // Remove from plan
      handleRemoveExercise(active.id as string);
    } else if (isFromPlan && isToPlan) {
      // Reorder within plan
      const oldIndex = exercises.findIndex(ex => ex.id === active.id);
      const newIndex = exercises.findIndex(ex => ex.id === over.id);
      
      if (oldIndex !== -1 && newIndex !== -1) {
        const newExercises = arrayMove(exercises, oldIndex, newIndex);
        onExercisesChange(newExercises);
      }
    }
  };

  const handleEditExercise = (updatedExercise: Exercise) => {
    // If it's a new exercise being added
    if (!exercises.find(ex => ex.id === updatedExercise.id)) {
      onExercisesChange([...exercises, updatedExercise]);
    } else {
      // If it's an existing exercise being edited
      onExercisesChange(
        exercises.map(ex => ex.id === updatedExercise.id ? updatedExercise : ex)
      );
    }
    setShowEditExercise(false);
    setEditingExercise(null);
  };

  const handleEditClick = (exercise: Exercise) => {
    setEditingExercise({...exercise});
    setShowEditExercise(true);
  };

  const handleRemoveExercise = (id: string) => {
    onExercisesChange(exercises.filter(exercise => exercise.id !== id));
  };

  return (
    <>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <Card className={darkTheme ? "bg-[#12131A] border-0" : ""}>
              <CardHeader>
                <CardTitle className={darkTheme ? "text-white" : ""}>Available Exercises</CardTitle>
              </CardHeader>
              <DroppableArea id="available-exercises">
                <CardContent className="h-[500px] overflow-y-auto space-y-4 p-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                  {AVAILABLE_EXERCISES.map((exercise) => (
                    <DraggableExerciseItem key={exercise.id} exercise={exercise} />
                  ))}
                </CardContent>
              </DroppableArea>
            </Card>
          </div>

          <div>
            <Card className={darkTheme ? "bg-[#12131A] border-0" : ""}>
              <CardHeader>
                <CardTitle className={darkTheme ? "text-white" : ""}>Plan Exercises ({exercises.length})</CardTitle>
              </CardHeader>
              <DroppableArea id="plan-exercises">
                <CardContent className="h-[500px] overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                  <SortableContext
                    items={exercises.map(ex => ex.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="space-y-4">
                      {exercises.length === 0 ? (
                        <div className={`p-4 text-center ${darkTheme ? "text-gray-400" : "text-gray-500"}`}>
                          Drag exercises here to add them to your plan
                        </div>
                      ) : (
                        exercises.map((exercise) => (
                          <SortableExerciseItem
                            key={exercise.id}
                            exercise={exercise}
                            onUpdate={handleEditClick}
                            onRemove={() => handleRemoveExercise(exercise.id)}
                          />
                        ))
                      )}
                    </div>
                  </SortableContext>
                </CardContent>
              </DroppableArea>
            </Card>
          </div>
          
          <DragOverlay>
            {activeId && activeExercise && (
              <Card className="w-full opacity-80 cursor-grabbing bg-[#12131A] border-0 text-white">
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    <div className="p-2 rounded-full bg-[#1A1B23]">
                      {getExerciseIcon(activeExercise.category)}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{activeExercise.name}</p>
                      <p className="text-sm text-gray-400">{activeExercise.category}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </DragOverlay>
        </div>
      </DndContext>

      <Dialog open={showEditExercise} onOpenChange={setShowEditExercise}>
        <DialogContent className="bg-[#12131A] border-0 text-white sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">Edit Exercise</DialogTitle>
            <DialogDescription className="text-gray-400">
              Customize the exercise details for your plan
            </DialogDescription>
          </DialogHeader>
          {editingExercise && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label className="text-white">
                    Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    value={editingExercise.name}
                    onChange={(e) => setEditingExercise({ ...editingExercise, name: e.target.value })}
                    className="bg-[#1A1B23] border-0 text-white"
                    required
                  />
                </div>

                <div>
                  <Label className="text-white">Description</Label>
                  <Textarea
                    value={editingExercise.description || ""}
                    onChange={(e) => setEditingExercise({ ...editingExercise, description: e.target.value })}
                    placeholder="Describe how to perform this exercise"
                    className="bg-[#1A1B23] border-0 text-white min-h-[100px]"
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label className="text-white">Sets</Label>
                    <Input
                      type="number"
                      value={editingExercise.sets}
                      onChange={(e) => setEditingExercise({ ...editingExercise, sets: parseInt(e.target.value) || 0 })}
                      className="bg-[#1A1B23] border-0 text-white"
                      min={1}
                    />
                  </div>
                  <div>
                    <Label className="text-white">Reps</Label>
                    <Input
                      type="number"
                      value={editingExercise.reps}
                      onChange={(e) => setEditingExercise({ ...editingExercise, reps: parseInt(e.target.value) || 0 })}
                      className="bg-[#1A1B23] border-0 text-white"
                      min={1}
                    />
                  </div>
                  <div>
                    <Label className="text-white">Weight (kg)</Label>
                    <Input
                      type="number"
                      value={editingExercise.weight}
                      onChange={(e) => setEditingExercise({ ...editingExercise, weight: parseInt(e.target.value) || 0 })}
                      className="bg-[#1A1B23] border-0 text-white"
                      min={0}
                    />
                  </div>
                </div>

                <div>
                  <Label className="text-white">YouTube Video URL</Label>
                  <div className="flex gap-2">
                    <Input
                      value={editingExercise.video || ""}
                      onChange={(e) => setEditingExercise({ ...editingExercise, video: e.target.value })}
                      placeholder="https://youtube.com/watch?v=..."
                      className="bg-[#1A1B23] border-0 text-white"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="shrink-0 bg-[#1A1B23] border-0 text-gray-400 hover:text-white hover:bg-[#2A2B33]"
                    >
                      <Youtube className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-white">Category</Label>
                    <Select
                      value={editingExercise.category}
                      onValueChange={(value) => setEditingExercise({ ...editingExercise, category: value })}
                    >
                      <SelectTrigger className="bg-[#1A1B23] border-0 text-white">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent className="bg-[#1A1B23] border-0 text-white">
                        <SelectItem value="strength">Strength</SelectItem>
                        <SelectItem value="cardio">Cardio</SelectItem>
                        <SelectItem value="flexibility">Flexibility</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-white">Difficulty</Label>
                    <Select
                      value={editingExercise.difficulty}
                      onValueChange={(value) => setEditingExercise({ 
                        ...editingExercise, 
                        difficulty: value as "Beginner" | "Intermediate" | "Advanced"
                      })}
                    >
                      <SelectTrigger className="bg-[#1A1B23] border-0 text-white">
                        <SelectValue placeholder="Select difficulty" />
                      </SelectTrigger>
                      <SelectContent className="bg-[#1A1B23] border-0 text-white">
                        <SelectItem value="Beginner">Beginner</SelectItem>
                        <SelectItem value="Intermediate">Intermediate</SelectItem>
                        <SelectItem value="Advanced">Advanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label className="text-white">Target Muscles</Label>
                  <Input
                    value={editingExercise.targetMuscles?.join(", ") || ""}
                    onChange={(e) => setEditingExercise({
                      ...editingExercise,
                      targetMuscles: e.target.value.split(",").map(m => m.trim()).filter(Boolean)
                    })}
                    placeholder="e.g. Chest, Triceps, Shoulders"
                    className="bg-[#1A1B23] border-0 text-white"
                  />
                  <p className="text-sm text-gray-400 mt-1">Separate muscle groups with commas</p>
                </div>
              </div>

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  className="bg-transparent hover:bg-[#1A1B23] text-white border-gray-600"
                  onClick={() => setShowEditExercise(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={() => handleEditExercise(editingExercise)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Save Exercise
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
} 