"use client"

import { Plus, Trash2 } from "lucide-react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

interface Exercise {
  id: string
  name: string
  description: string
  type: string
  muscleGroup: string
  equipment: string[]
  difficulty: string
  sets: number
  reps: number
  duration: number
  restTime: number
  videoUrl: string
}

export default function ExercisePoolPage() {
  const [exercises, setExercises] = useState<Exercise[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [newExercise, setNewExercise] = useState<Partial<Exercise>>({
    name: "",
    description: "",
    type: "strength",
    muscleGroup: "",
    equipment: [],
    difficulty: "beginner",
    sets: 3,
    reps: 12,
    duration: 0,
    restTime: 60,
    videoUrl: "",
  })

  const handleAddExercise = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/exercises", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newExercise),
      })

      if (!response.ok) throw new Error("Failed to add exercise")

      const exercise = await response.json()
      setExercises([...exercises, exercise])
      setNewExercise({
        name: "",
        description: "",
        type: "strength",
        muscleGroup: "",
        equipment: [],
        difficulty: "beginner",
        sets: 3,
        reps: 12,
        duration: 0,
        restTime: 60,
        videoUrl: "",
      })
    } catch (error) {
      console.error("Error adding exercise:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteExercise = async (id: string) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/exercises/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) throw new Error("Failed to delete exercise")

      setExercises(exercises.filter((exercise) => exercise.id !== id))
    } catch (error) {
      console.error("Error deleting exercise:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Exercise Pool</h1>

      {/* Add New Exercise Form */}
      <Card className="p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Add New Exercise</h2>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={newExercise.name}
              onChange={(e) =>
                setNewExercise({ ...newExercise, name: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="type">Type</Label>
            <Select
              value={newExercise.type}
              onValueChange={(value) =>
                setNewExercise({ ...newExercise, type: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="strength">Strength</SelectItem>
                <SelectItem value="cardio">Cardio</SelectItem>
                <SelectItem value="flexibility">Flexibility</SelectItem>
                <SelectItem value="recovery">Recovery</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="muscleGroup">Muscle Group</Label>
            <Select
              value={newExercise.muscleGroup}
              onValueChange={(value) =>
                setNewExercise({ ...newExercise, muscleGroup: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="chest">Chest</SelectItem>
                <SelectItem value="back">Back</SelectItem>
                <SelectItem value="legs">Legs</SelectItem>
                <SelectItem value="shoulders">Shoulders</SelectItem>
                <SelectItem value="arms">Arms</SelectItem>
                <SelectItem value="core">Core</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="difficulty">Difficulty</Label>
            <Select
              value={newExercise.difficulty}
              onValueChange={(value) =>
                setNewExercise({ ...newExercise, difficulty: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="beginner">Beginner</SelectItem>
                <SelectItem value="intermediate">Intermediate</SelectItem>
                <SelectItem value="advanced">Advanced</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="sets">Sets</Label>
            <Input
              id="sets"
              type="number"
              value={newExercise.sets}
              onChange={(e) =>
                setNewExercise({ ...newExercise, sets: parseInt(e.target.value) })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="reps">Reps</Label>
            <Input
              id="reps"
              type="number"
              value={newExercise.reps}
              onChange={(e) =>
                setNewExercise({ ...newExercise, reps: parseInt(e.target.value) })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="duration">Duration (seconds)</Label>
            <Input
              id="duration"
              type="number"
              value={newExercise.duration}
              onChange={(e) =>
                setNewExercise({
                  ...newExercise,
                  duration: parseInt(e.target.value),
                })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="restTime">Rest Time (seconds)</Label>
            <Input
              id="restTime"
              type="number"
              value={newExercise.restTime}
              onChange={(e) =>
                setNewExercise({
                  ...newExercise,
                  restTime: parseInt(e.target.value),
                })
              }
            />
          </div>
          <div className="space-y-2 col-span-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={newExercise.description}
              onChange={(e) =>
                setNewExercise({ ...newExercise, description: e.target.value })
              }
            />
          </div>
          <div className="space-y-2 col-span-2">
            <Label htmlFor="videoUrl">Video URL</Label>
            <Input
              id="videoUrl"
              value={newExercise.videoUrl}
              onChange={(e) =>
                setNewExercise({ ...newExercise, videoUrl: e.target.value })
              }
            />
          </div>
        </div>
        <Button
          className="mt-4"
          onClick={handleAddExercise}
          disabled={isLoading || !newExercise.name}
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Exercise
        </Button>
      </Card>

      {/* Exercise List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {exercises.map((exercise) => (
          <Card key={exercise.id} className="p-4">
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-semibold">{exercise.name}</h3>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleDeleteExercise(exercise.id)}
                disabled={isLoading}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
            <p className="text-sm text-gray-500 mb-2">{exercise.description}</p>
            <div className="flex flex-wrap gap-2">
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                {exercise.type}
              </span>
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                {exercise.muscleGroup}
              </span>
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                {exercise.difficulty}
              </span>
              {exercise.sets > 0 && exercise.reps > 0 && (
                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                  {exercise.sets} sets × {exercise.reps} reps
                </span>
              )}
              {exercise.duration > 0 && (
                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                  {exercise.duration}s
                </span>
              )}
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
} 