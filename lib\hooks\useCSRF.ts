"use client";

import { useState, useEffect } from 'react';
import { getCSRFTokenHeaderName, getCSRFTokenFieldName } from '@/lib/security/csrf';
import { generateSecureToken } from '@/lib/security/password';

/**
 * Custom hook for managing CSRF tokens in the client
 * @returns An object with the CSRF token and methods to interact with it
 */
export function useCSRF() {
  const [csrfToken, setCsrfToken] = useState<string>('');
  const tokenFieldName = getCSRFTokenFieldName();
  const tokenHeaderName = getCSRFTokenHeaderName();

  useEffect(() => {
    // On mount or if the token doesn't exist yet, generate a new one
    if (!csrfToken) {
      const token = localStorage.getItem('csrfToken');
      if (token) {
        setCsrfToken(token);
      } else {
        const newToken = generateSecureToken(32);
        localStorage.setItem('csrfToken', newToken);
        setCsrfToken(newToken);
      }
    }
  }, [csrfToken]);

  /**
   * Add the CSRF token to a form data object
   * @param formData The form data to add the token to
   * @returns A new form data object with the token added
   */
  const addTokenToFormData = <T extends Record<string, unknown>>(formData: T): T & Record<string, unknown> => {
    return {
      ...formData,
      [tokenFieldName]: csrfToken,
    };
  };

  /**
   * Add the CSRF token to fetch headers
   * @param headers Optional existing headers
   * @returns Headers with the CSRF token added
   */
  const getCSRFHeaders = (headers: HeadersInit = {}): HeadersInit => {
    return {
      ...headers,
      [tokenHeaderName]: csrfToken,
    };
  };

  /**
   * Check if a response failed due to CSRF validation
   * @param response The fetch response
   * @returns True if the response failed due to CSRF validation
   */
  const isCSRFError = async (response: Response): Promise<boolean> => {
    if (response.status === 403) {
      try {
        const data = await response.json();
        return data.error?.includes('CSRF');
      } catch {
        return false;
      }
    }
    return false;
  };

  /**
   * Create a fetch wrapper that adds CSRF protection
   * @returns A fetch function with CSRF protection
   */
  const fetchWithCSRF = async (
    url: string,
    options: RequestInit = {}
  ): Promise<Response> => {
    const csrfHeaders = getCSRFHeaders(options.headers || {});
    
    // Add CSRF token to headers
    const fetchOptions: RequestInit = {
      ...options,
      headers: csrfHeaders,
    };
    
    // Make the request
    const response = await fetch(url, fetchOptions);
    
    // If the CSRF token is invalid or expired, refresh it and try again
    if (await isCSRFError(response)) {
      refreshToken();
      
      // Retry the request with the new token
      const newHeaders = getCSRFHeaders(options.headers || {});
      const retryOptions: RequestInit = {
        ...options,
        headers: newHeaders,
      };
      
      return fetch(url, retryOptions);
    }
    
    return response;
  };

  /**
   * Refresh the CSRF token
   */
  const refreshToken = (): void => {
    const newToken = generateSecureToken(32);
    localStorage.setItem('csrfToken', newToken);
    setCsrfToken(newToken);
  };

  return {
    csrfToken,
    tokenFieldName,
    tokenHeaderName,
    addTokenToFormData,
    getCSRFHeaders,
    isCSRFError,
    fetchWithCSRF,
    refreshToken,
  };
} 