// Feature flags configuration for trainer services
export interface FeatureFlags {
  enablePremiumCoaching: boolean; // 1:1 Premium coaching
}

// Default feature flags - premium coaching only
export const defaultFeatureFlags: FeatureFlags = {
  enablePremiumCoaching: true
};

// Type for trainer service types
export type trainerServiceType = 'premium-coaching' | 'custom';

// Predefined feature flag configurations for different trainer types
export const trainerServiceConfigs: Record<trainerServiceType, FeatureFlags> = {
  'premium-coaching': {
    enablePremiumCoaching: true
  },
  'custom': {
    enablePremiumCoaching: false
  }
};

// Function to get cookie-based feature flags for development
export function getDevFeatureFlags(): FeatureFlags | null {
  if (typeof document === 'undefined' || process.env.NODE_ENV !== 'development') {
    return null;
  }

  try {
    const cookies = document.cookie.split(';');
    const featureFlagsCookie = cookies.find(cookie => cookie.trim().startsWith('dev_feature_flags='));
    
    if (featureFlagsCookie) {
      const cookieValue = featureFlagsCookie.split('=')[1];
      return JSON.parse(decodeURIComponent(cookieValue));
    }
  } catch (error) {
    console.error('Error parsing feature flags cookie:', error);
  }
  
  return null;
}

// Function to set cookie-based feature flags for development
export function setDevFeatureFlags(flags: FeatureFlags): void {
  if (typeof document === 'undefined' || process.env.NODE_ENV !== 'development') {
    return;
  }

  try {
    const serializedFlags = encodeURIComponent(JSON.stringify(flags));
    // Set cookie for 12 hours
    document.cookie = `dev_feature_flags=${serializedFlags};path=/;max-age=${60 * 60 * 12}`;
  } catch (error) {
    console.error('Error setting feature flags cookie:', error);
  }
}

// Function to clear cookie-based feature flags for development
export function clearDevFeatureFlags(): void {
  if (typeof document === 'undefined') {
    return;
  }
  
  document.cookie = "dev_feature_flags=;path=/;max-age=0";
} 