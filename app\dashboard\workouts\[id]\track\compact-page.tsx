"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  ArrowLeft,
  CheckCircle,
  Loader2,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CompactExerciseCard } from "./compact-exercise-card";
import { toast } from "sonner";

export default function CompactWorkoutTracker({ params }: { params: { id: string } }) {
  const router = useRouter();
  const workoutId = params.id;
  
  const [isLoading, setIsLoading] = useState(true);
  const [workout, setWorkout] = useState<any>(null);
  const [exercises, setExercises] = useState<any[]>([]);
  const [userTier, setUserTier] = useState<string>('basic');
  const [completionNote, setCompletionNote] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // State for rest timer
  const [activeTimer, setActiveTimer] = useState<{exerciseId: string, timeLeft: number, initialTime: number} | null>(null);
  const [timerRunning, setTimerRunning] = useState(false);

  useEffect(() => {
    const fetchUserAndWorkout = async () => {
      setIsLoading(true);
      try {
        // Fetch user data (simplified for demo)
        setUserTier('basic');
        
        // Fetch workout data from API
        const response = await fetch(`/api/workouts/${workoutId}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch workout: ${response.statusText}`);
        }

        const workoutData = await response.json();

        if (workoutData) {
          setWorkout(workoutData);

          // Initialize exercises with setData for tracking individual sets
          const initializedExercises = workoutData.exercises.map((ex: any) => {
            // Create setData array for each exercise
            const setData = Array.from({ length: ex.sets || 0 }, (_, i) => ({
              setNumber: i + 1,
              weight: ex.weight || 0,
              reps: ex.reps || 0,
              rpe: 7, // Default RPE value
              rir: 2, // Default RIR value
              completed: false
            }));

            return {
              ...ex,
              completed: false,
              setData,
              actualRestTime: ex.restTime // Initialize with default rest time
            };
          });

          setExercises(initializedExercises);
        } else {
          console.error(`Workout with ID ${workoutId} not found`);
          toast.error("Workout not found");
        }
      } catch (error) {
        console.error("Failed to fetch workout:", error);
        toast.error("Failed to load workout");
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAndWorkout();
  }, [workoutId]);

  // Handle updating set data (weight, reps, rpe, rir, completion)
  const handleSetDataUpdate = (exerciseId: string, setNumber: number, field: 'weight' | 'reps' | 'rpe' | 'rir' | 'completed', value: number | boolean) => {
    setExercises(prev =>
      prev.map(ex => {
        if (ex.id !== exerciseId) return ex;

        const updatedSetData = ex.setData?.map(set => {
          if (set.setNumber !== setNumber) return set;
          return { ...set, [field]: value };
        }) || [];

        // Check if all sets are completed to mark the exercise as completed
        const allSetsCompleted = updatedSetData.every(set => set.completed);

        // Auto-start rest timer when a set is marked as completed
        if (field === 'completed' && value === true && ex.actualRestTime && ex.actualRestTime > 0) {
          // Use setTimeout to ensure state is updated before starting timer
          setTimeout(() => startRestTimer(exerciseId), 100);
        }

        return {
          ...ex,
          setData: updatedSetData,
          completed: allSetsCompleted
        };
      })
    );
  };

  // Handle updating rest time
  const handleRestTimeUpdate = (exerciseId: string, restTime: number) => {
    setExercises(prev =>
      prev.map(ex =>
        ex.id === exerciseId ? { ...ex, actualRestTime: restTime } : ex
      )
    );
  };

  // Start rest timer
  const startRestTimer = (exerciseId: string) => {
    const exercise = exercises.find(ex => ex.id === exerciseId);
    if (!exercise || !exercise.actualRestTime) return;

    setActiveTimer({
      exerciseId,
      timeLeft: exercise.actualRestTime,
      initialTime: exercise.actualRestTime
    });
    setTimerRunning(true);
  };

  // Pause the timer
  const pauseTimer = () => {
    setTimerRunning(false);
  };

  // Resume the timer
  const resumeTimer = () => {
    if (activeTimer) {
      setTimerRunning(true);
    }
  };

  // Reset the timer
  const resetTimer = () => {
    if (activeTimer) {
      setActiveTimer({
        ...activeTimer,
        timeLeft: activeTimer.initialTime
      });
      setTimerRunning(false);
    }
  };

  // Handle exercise completion toggle
  const handleExerciseToggle = (id: string) => {
    setExercises(prev =>
      prev.map(ex => {
        if (ex.id !== id) return ex;

        // If toggling to completed, mark all sets as completed
        const updatedSetData = ex.setData?.map(set => ({
          ...set,
          completed: !ex.completed
        })) || [];

        return {
          ...ex,
          completed: !ex.completed,
          setData: updatedSetData
        };
      })
    );
  };

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (timerRunning && activeTimer) {
      interval = setInterval(() => {
        setActiveTimer(prev => {
          if (!prev) return null;

          const newTimeLeft = prev.timeLeft - 1;

          if (newTimeLeft <= 0) {
            setTimerRunning(false);
            toast.success("Rest time completed!");
            return null;
          }

          return { ...prev, timeLeft: newTimeLeft };
        });
      }, 1000);
    }

    return () => clearInterval(interval);
  }, [timerRunning, activeTimer]);

  const allExercisesCompleted = exercises.length > 0 && exercises.every(ex => ex.completed);

  const handleSubmitCompletion = async () => {
    if (!workout) return;
    setIsSubmitting(true);
    try {
      // Prepare workout data for saving
      const workoutData = {
        workoutId: workout.id,
        title: workout.title,
        completedAt: new Date().toISOString(),
        note: completionNote,
        exercises: exercises.map(ex => ({
          id: ex.id,
          name: ex.name,
          completed: ex.completed,
          sets: ex.setData?.map(set => ({
            setNumber: set.setNumber,
            weight: set.weight,
            reps: set.reps,
            rpe: set.rpe || 7,
            rir: set.rir || 2,
            completed: set.completed
          })) || [],
          restTime: ex.actualRestTime
        }))
      };

      console.log("Saving workout data:", workoutData);

      // Send data to API
      const response = await fetch('/api/workout-logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(workoutData),
      });

      if (!response.ok) {
        throw new Error('Failed to save workout completion');
      }

      // Update local state
      setWorkout(prev => prev ? { ...prev, completed: true } : null);

      toast.success("Workout completed successfully!");

      // Redirect back to workouts page after a short delay
      setTimeout(() => {
        router.push("/dashboard/workouts/current");
      }, 1500);
    } catch (error) {
      console.error("Error completing workout:", error);
      toast.error("Failed to save workout completion");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading workout...</p>
        </div>
      </div>
    );
  }

  if (!workout) {
    return (
      <div className="py-4">
        <Button variant="outline" asChild className="mb-4">
          <Link href="/dashboard/workouts/current">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Workouts
          </Link>
        </Button>
        <div className="text-center p-4">
          <h2 className="text-xl font-bold mb-2">Workout Not Found</h2>
          <p className="text-muted-foreground mb-4">
            The workout you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button asChild>
            <Link href="/dashboard/workouts/current">
              View My Workouts
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  // Determine if user can track RPE and RIR (premium feature)
  const canTrackDetails = userTier === 'premium' || userTier === 'coaching';

  return (
    <div className="px-2 py-2 max-w-md mx-auto">
      {/* Sticky Header */}
      <div className="sticky top-0 z-20 bg-background/95 backdrop-blur-sm pb-2 mb-2 flex items-center justify-between">
        <Button variant="ghost" size="sm" className="p-0 h-8 w-8" asChild>
          <Link href="/dashboard/workouts">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-base font-bold truncate flex-1 text-center">{workout.title}</h1>
        {userTier !== 'basic' && (
          <Badge variant="outline" className="bg-primary/5 border-primary/20">
            {userTier === 'mid' ? 'Mid' : userTier === 'premium' ? 'Premium' : 'Coaching'}
          </Badge>
        )}
      </div>

      {/* Exercise Cards */}
      <div className="space-y-3">
        {exercises.map((exercise, index) => (
          <CompactExerciseCard
            key={exercise.id}
            exercise={exercise}
            index={index}
            userTier={userTier}
            canTrackDetails={canTrackDetails}
            handleExerciseToggle={handleExerciseToggle}
            handleSetDataUpdate={handleSetDataUpdate}
            handleRestTimeUpdate={handleRestTimeUpdate}
            startRestTimer={startRestTimer}
            pauseTimer={pauseTimer}
            resumeTimer={resumeTimer}
            resetTimer={resetTimer}
            activeTimer={activeTimer}
            timerRunning={timerRunning}
          />
        ))}
      </div>

      {/* Complete Workout Button */}
      {allExercisesCompleted && (
        <div className="sticky bottom-4 mt-4 flex justify-center">
          <Button
            onClick={handleSubmitCompletion}
            className="bg-green-600 hover:bg-green-700 text-white font-medium shadow-md rounded-full px-6"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Complete Workout
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
