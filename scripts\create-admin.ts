import { PrismaClient } from "@prisma/client"
import { hash } from "bcryptjs"

const prisma = new PrismaClient()

async function main() {
  const email = process.env.ADMIN_EMAIL || "<EMAIL>"
  const password = process.env.ADMIN_PASSWORD || "admin123"
  const fullName = process.env.ADMIN_NAME || "Admin User"

  // Check if admin already exists
  const existingAdmin = await prisma.user.findUnique({
    where: { email }
  })

  if (existingAdmin) {
    console.log("Admin user already exists")
    return
  }

  // Create admin user
  const hashedPassword = await hash(password, 10)
  const admin = await prisma.user.create({
    data: {
      email,
      password: hashedPassword,
      fullName,
      role: "admin",
    },
  })

  console.log("Admin user created successfully:", admin)
}

main()
  .catch((error: unknown) => {
    if (error instanceof Error) {
      console.error("Error creating admin:", error.message)
    } else {
      console.error("Unknown error creating admin")
    }
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 