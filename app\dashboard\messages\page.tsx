"use client"

import {
  <PERSON><PERSON><PERSON><PERSON>,
  Clock,
  PaperclipIcon,
  SmilePlus,
  Send,
  Users,
  ShoppingBag,
  Settings,
  MessageSquare
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState, FormEvent } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"

// Define types for our data
interface Conversation {
  id: string;
  clientName: string;
  avatar: string;
  lastMessage: string;
  timestamp: string;
  unread: number;
  online: boolean;
}

interface Message {
  id: string;
  sender: "client" | "trainer";
  text: string;
  timestamp: string;
}

interface MessagesByConversation {
  [key: string]: Message[];
}

export default function MessagesPage() {
  const [selectedConversationId, setSelectedConversationId] = useState("1");
  
  // Mock user data
  const user = {
    id: "user1",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "client"
  }

  // Check if user has active subscriptions (mock data)
  const hasActiveSubscription = true;
  
  if (!hasActiveSubscription && user.role === "client") {
    // No active subscriptions, show upgrade UI
    return (
      <div className="flex flex-col items-center justify-center h-[70vh] p-8">
        <div className="flex flex-col items-center space-y-4 text-center max-w-md">
          <MessageCircle className="h-12 w-12 text-primary" />
          <h2 className="text-2xl font-bold">No Active Coaching</h2>
          <p className="text-muted-foreground">
            You don't have any active coaching subscriptions. Subscribe to a trainer's program to get access to 1:1 messaging.
          </p>
          <div className="flex gap-4 mt-4">
            <Button asChild>
              <Link href="/dashboard/trainers">
                <Users className="mr-2 h-4 w-4" />
                Find Trainers
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/shop">
                <ShoppingBag className="mr-2 h-4 w-4" />
                Browse Programs
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Mock conversations data
  const mockConversations: Conversation[] = [
    {
      id: "1",
      clientName: "John Smith",
      avatar: "/avatars/01.png",
      lastMessage: "Thanks for the workout plan! I'll start tomorrow.",
      timestamp: "2 min ago",
      unread: 2,
      online: true,
    },
    {
      id: "2",
      clientName: "Sarah Johnson",
      avatar: "/avatars/02.png",
      lastMessage: "Can we adjust my macros? I'm feeling hungry in the evenings.",
      timestamp: "1 hour ago",
      unread: 0,
      online: true,
    },
    {
      id: "3",
      clientName: "Michael Brown",
      avatar: "/avatars/03.png",
      lastMessage: "Just completed today's workout! It was tough but rewarding.",
      timestamp: "3 hours ago",
      unread: 0,
      online: false,
    },
    {
      id: "4",
      clientName: "Emily Wilson",
      avatar: "/avatars/04.png",
      lastMessage: "I'm traveling next week. Can you adjust my training plan?",
      timestamp: "Yesterday",
      unread: 0,
      online: false,
    },
    {
      id: "5",
      clientName: "James Rodriguez",
      avatar: "/avatars/05.png",
      lastMessage: "Progress pics attached. What do you think?",
      timestamp: "2 days ago",
      unread: 0,
      online: true,
    }
  ];

  // Mock messages for each conversation
  const mockMessagesMap: MessagesByConversation = {
    "1": [
      {
        id: "1_1",
        sender: "client",
        text: "Hi Coach! I've been following the new program for a week now.",
        timestamp: "10:30 AM",
      },
      {
        id: "1_2",
        sender: "trainer",
        text: "That's great to hear! How are you finding it so far?",
        timestamp: "10:32 AM",
      },
      {
        id: "1_3",
        sender: "client",
        text: "I'm really enjoying it! The supersets are challenging but I feel stronger already.",
        timestamp: "10:35 AM",
      },
      {
        id: "1_4",
        sender: "trainer",
        text: "Perfect! That's exactly what we want. Are you managing to hit all the rep ranges?",
        timestamp: "10:38 AM",
      },
      {
        id: "1_5",
        sender: "client",
        text: "Most of them, yes. I'm struggling a bit with the Bulgarian split squats though.",
        timestamp: "10:40 AM",
      },
      {
        id: "1_6",
        sender: "trainer",
        text: "That's normal, they're a challenging exercise. Let me send you a quick form video that might help.",
        timestamp: "10:42 AM",
      },
      {
        id: "1_7",
        sender: "client",
        text: "Thanks for the workout plan! I'll start tomorrow.",
        timestamp: "10:45 AM",
      },
    ],
    "2": [
      {
        id: "2_1",
        sender: "client",
        text: "Hi Coach, I'm feeling pretty hungry in the evenings on this meal plan.",
        timestamp: "9:15 AM",
      },
      {
        id: "2_2",
        sender: "trainer",
        text: "Thanks for letting me know. What time do you usually have your last meal?",
        timestamp: "9:20 AM",
      },
      {
        id: "2_3",
        sender: "client",
        text: "Around 6pm, and then I get hungry again around 9pm.",
        timestamp: "9:22 AM",
      },
      {
        id: "2_4",
        sender: "trainer",
        text: "I see. Let's adjust your macros a bit and add a small protein-rich snack before bed.",
        timestamp: "9:25 AM",
      },
      {
        id: "2_5",
        sender: "client",
        text: "That sounds good. Can we adjust my macros?",
        timestamp: "9:28 AM",
      },
    ],
    "3": [
      {
        id: "3_1",
        sender: "client",
        text: "Just finished today's workout! Those drop sets were killer.",
        timestamp: "2:05 PM",
      },
      {
        id: "3_2",
        sender: "trainer",
        text: "Great job! How was your recovery between sets?",
        timestamp: "2:30 PM",
      },
      {
        id: "3_3",
        sender: "client",
        text: "Pretty good, but I was really struggling by the last set.",
        timestamp: "2:35 PM",
      },
      {
        id: "3_4",
        sender: "trainer",
        text: "That's exactly where you should be. Pushing to near failure on those last sets is what drives growth.",
        timestamp: "2:40 PM",
      },
      {
        id: "3_5",
        sender: "client",
        text: "Just completed today's workout! It was tough but rewarding.",
        timestamp: "3:15 PM",
      },
    ]
  };

  const selectedClient = mockConversations.find(c => c.id === selectedConversationId) || mockConversations[0];
  const mockMessages = mockMessagesMap[selectedConversationId] || mockMessagesMap["1"];
  
  // For sending messages
  const [newMessage, setNewMessage] = useState("");
  
  const handleSendMessage = (e: FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;
    
    // In a real app, we would send this message to the server
    // For now, just clear the input
    setNewMessage("");
  };

  return (
    <div className="flex h-full flex-col">
      {/* Sidebar */}
      <div className="w-80 border-r bg-background p-4 flex flex-col">
        <div className="flex items-center justify-between pb-4 border-b">
          <h2 className="text-xl font-semibold">Messages</h2>
          <Button variant="ghost" size="icon">
            <Settings className="h-5 w-5" />
          </Button>
        </div>
        <div className="flex-1 overflow-y-auto mt-4 -mx-4">
          {mockConversations.map((conv) => (
            <div 
              key={conv.id} 
              className={`flex items-center gap-3 p-3 hover:bg-muted cursor-pointer ${
                conv.id === selectedConversationId ? 'bg-muted' : ''
              }`}
              onClick={() => setSelectedConversationId(conv.id)}
            >
              <div className="relative">
                <Image 
                  src={conv.avatar} 
                  alt={conv.clientName}
                  width={40}
                  height={40}
                  className="rounded-full"
                />
                {conv.online && (
                  <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></span>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex justify-between items-baseline">
                  <h4 className="text-sm font-medium truncate">{conv.clientName}</h4>
                  <span className="text-xs text-muted-foreground">{conv.timestamp}</span>
                </div>
                <p className="text-xs text-muted-foreground truncate">
                  {conv.lastMessage}
                </p>
              </div>
              {conv.unread > 0 && (
                <span className="bg-primary text-primary-foreground text-xs font-medium px-2 py-0.5 rounded-full">
                  {conv.unread}
                </span>
              )}
            </div>
          ))}
        </div>
        <div className="pt-4 border-t">
          {/* Add search or filter if needed */}
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col bg-muted/40">
        {selectedConversationId ? (
          <div className="flex-1 flex flex-col">
            {/* Chat header */}
            <div className="p-4 border-b flex justify-between items-center">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <Image 
                    src={selectedClient.avatar}
                    alt={selectedClient.clientName}
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                  {selectedClient.online && (
                    <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></span>
                  )}
                </div>
                <div>
                  <h3 className="font-medium">{selectedClient.clientName}</h3>
                  <p className="text-xs text-muted-foreground">
                    {selectedClient.online ? 'Online' : 'Offline'}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center">
                  <Card className="shadow-none">
                    <CardContent className="p-2 flex items-center gap-1">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-xs">History</span>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-auto p-4">
              <div className="space-y-4">
                {mockMessages.map((message) => (
                  <div 
                    key={message.id} 
                    className={`flex ${message.sender === 'trainer' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div 
                      className={`max-w-[70%] px-4 py-2 rounded-lg ${
                        message.sender === 'trainer' 
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-muted'
                      }`}
                    >
                      <p>{message.text}</p>
                      <p className="text-xs mt-1 opacity-70">{message.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Message input */}
            <div className="border-t p-4">
              <form onSubmit={handleSendMessage} className="flex gap-2">
                <Button type="button" size="icon" variant="ghost">
                  <PaperclipIcon className="h-5 w-5" />
                </Button>
                <Input 
                  type="text" 
                  placeholder="Type a message..." 
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  className="flex-1" 
                />
                <Button type="button" size="icon" variant="ghost">
                  <SmilePlus className="h-5 w-5" />
                </Button>
                <Button type="submit" size="icon">
                  <Send className="h-5 w-5" />
                </Button>
              </form>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full">
            <MessageSquare className="h-16 w-16 text-muted-foreground/50 mb-4" />
            <h3 className="text-xl font-medium text-muted-foreground">
              Select a conversation
            </h3>
            <p className="text-muted-foreground">
              You don&apos;t have any messages yet. Start a new conversation!
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

