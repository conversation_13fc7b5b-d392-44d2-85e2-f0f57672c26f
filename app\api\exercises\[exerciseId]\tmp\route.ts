import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// NOTE: The filename is tmp/route.ts but the param seems to be exerciseId based on usage
// It should likely be { params: { exerciseId: string } } if the folder was named [exerciseId]
// Assuming the intent was exerciseId based on the previous file structure.

export async function GET(
  request: Request,
  context: any // Use 'any' workaround
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Assuming param is exerciseId based on context/previous file
    const exerciseId = context?.params?.exerciseId; 
    // If the folder IS named [id], change above to: context?.params?.id;
    if (!exerciseId) {
        return new NextResponse("Exercise ID missing in URL", { status: 400 });
    }

    const exercise = await prisma.exercise.findUnique({
      where: {
        id: exerciseId,
      },
      include: {
        workout: {
            include: {
                trainingPlan: {
                    select: { athleteId: true }
                }
            }
        }
      },
    })

    if (!exercise) {
      return NextResponse.json({ error: "Exercise not found" }, { status: 404 })
    }

    // Check permissions
    const ownerId = exercise.workout?.trainingPlan?.athleteId;
    if (session.user.role !== "admin" && ownerId !== session.user.id) {
       return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    return NextResponse.json(exercise) 

  } catch (error) {
    console.error("[EXERCISE_GET_TMP]", error)
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 })
  }
}

export async function PATCH(
  request: Request,
  context: any // Use 'any' workaround
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) { 
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const exerciseId = context?.params?.exerciseId; // Or context?.params?.id
     if (!exerciseId) {
        return new NextResponse("Exercise ID missing in URL", { status: 400 });
    }

    const body = await request.json()

    // Fetch exercise for permission check
    const exercise = await prisma.exercise.findUnique({
      where: {
        id: exerciseId,
      },
      include: {
        workout: {
          include: {
            trainingPlan: {
                select: { athleteId: true }
            },
          },
        },
      },
    })

    if (!exercise) {
      return NextResponse.json({ error: "Exercise not found" }, { status: 404 })
    }

    // Check permissions
    const ownerId = exercise.workout?.trainingPlan?.athleteId;
    if (session.user.role !== "admin" && ownerId !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Prepare update data
    const updateData: Record<string, any> = {};
    const allowedFields = ['name', 'description', 'sets', 'reps', 'duration', 'restTime', 'videoUrl', 'order', 'type', 'thumbnailUrl', 'calories', 'difficulty', 'equipment', 'isTemplate', 'muscleGroup'];

    for (const field of allowedFields) {
        if (body[field] !== undefined) {
            updateData[field] = body[field];
        }
    }

    if (Object.keys(updateData).length === 0) {
        return NextResponse.json({ error: "No valid fields provided for update" }, { status: 400 });
    }

    const updatedExercise = await prisma.exercise.update({
      where: {
        id: exerciseId,
      },
      data: updateData,
    })

    return NextResponse.json(updatedExercise)
  } catch (error) {
    console.error("[EXERCISE_UPDATE_TMP]", error)
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 })
  }
}

export async function DELETE(
  request: Request,
  context: any // Use 'any' workaround
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) { 
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const exerciseId = context?.params?.exerciseId; // Or context?.params?.id
    if (!exerciseId) {
        return new NextResponse("Exercise ID missing in URL", { status: 400 });
    }

    // Fetch exercise for permission check
    const exercise = await prisma.exercise.findUnique({
      where: {
        id: exerciseId,
      },
      include: {
        workout: {
          include: {
            trainingPlan: {
                select: { athleteId: true }
            },
          },
        },
      },
    })

    if (!exercise) {
       return new NextResponse(null, { status: 204 }); 
    }

    // Check permissions
    const ownerId = exercise.workout?.trainingPlan?.athleteId;
    if (session.user.role !== "admin" && ownerId !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Delete exercise
    await prisma.exercise.delete({
      where: {
        id: exerciseId,
      },
    })

    return new NextResponse(null, { status: 204 });

  } catch (error) {
    console.error("[EXERCISE_DELETE_TMP]", error)
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 })
  }
} 