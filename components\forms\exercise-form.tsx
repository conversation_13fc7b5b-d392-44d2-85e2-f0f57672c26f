"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  sets: z.number().optional(),
  reps: z.number().optional(),
  duration: z.number().optional(),
  restTime: z.number().optional(),
  videoUrl: z.string().url().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface ExerciseFormProps {
  workoutId: string
  workoutType: "strength" | "cardio" | "flexibility" | "recovery"
  initialData?: {
    id: string
    name: string
    description?: string
    sets?: number
    reps?: number
    duration?: number
    restTime?: number
    videoUrl?: string
  }
}

export function ExerciseForm({ workoutId, workoutType, initialData }: ExerciseFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      name: "",
      description: "",
      sets: undefined,
      reps: undefined,
      duration: undefined,
      restTime: undefined,
      videoUrl: "",
    },
  })

  async function onSubmit(values: FormValues) {
    try {
      setIsLoading(true)

      if (initialData) {
        // Update existing exercise
        const response = await fetch(`/api/exercises/${initialData.id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(values),
        })

        if (!response.ok) {
          throw new Error("Failed to update exercise")
        }

        toast.success("Exercise updated successfully")
      } else {
        // Create new exercise
        const response = await fetch("/api/exercises", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...values,
            workoutId,
          }),
        })

        if (!response.ok) {
          throw new Error("Failed to create exercise")
        }

        const data = await response.json()
        toast.success("Exercise created successfully")
      }

      router.push(`/dashboard/workouts/${workoutId}`)
      router.refresh()
    } catch (error) {
      toast.error("Something went wrong")
      console.error(error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Exercise Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter exercise name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter exercise description"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {workoutType === "strength" && (
          <>
            <FormField
              control={form.control}
              name="sets"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sets</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter number of sets"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="reps"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reps</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter number of reps"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        {(workoutType === "cardio" || workoutType === "flexibility") && (
          <FormField
            control={form.control}
            name="duration"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Duration (minutes)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter duration in minutes"
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="restTime"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rest Time (seconds)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter rest time in seconds"
                  {...field}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="videoUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Video URL (YouTube)</FormLabel>
              <FormControl>
                <Input
                  type="url"
                  placeholder="Enter YouTube video URL"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-4">
          <Button type="submit" disabled={isLoading}>
            {initialData ? "Update Exercise" : "Create Exercise"}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isLoading}
          >
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  )
} 