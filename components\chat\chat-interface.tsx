"use client"

import { useSession } from "next-auth/react"
import React, { useEffect, useState, useRef, useCallback, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/components/ui/use-toast"

interface Message {
  id: string
  content: string
  senderId: string
  createdAt: string
  sender: {
    fullName: string
  }
}

interface ChatInterfaceProps {
  clientId?: string
}

export function ChatInterface({ clientId }: ChatInterfaceProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState("")
  const [loading, setLoading] = useState(false)
  const [fetchingMessages, setFetchingMessages] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  
  // Memoized API URLs for better performance
  const messagesApiUrl = useMemo(() => clientId ? `/api/chat/${clientId}` : '', [clientId]);

  // Function to fetch messages
  const fetchMessages = useCallback(async () => {
    if (!messagesApiUrl) return;
    
    try {
      // Don't fetch if already fetching to prevent race conditions
      if (fetchingMessages) return;
      setFetchingMessages(true);
      
      const response = await fetch(messagesApiUrl, {
        // Add cache control headers
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      
      if (!response.ok) throw new Error("Failed to fetch messages");
      const data = await response.json();
      setMessages(data);
      scrollToBottom();
    } catch (error) {
      console.error("Error fetching messages:", error);
    } finally {
      setFetchingMessages(false);
    }
  }, [messagesApiUrl]);

  // Polling effect for messages
  useEffect(() => {
    if (!clientId) return;
    
    // Initial fetch
    fetchMessages();
    
    // Set up polling interval with exponential backoff
    // Use a 5-second interval for active polling
    const interval = setInterval(() => {
      fetchMessages();
    }, 5000);
    
    return () => clearInterval(interval);
  }, [clientId, fetchMessages]);

  // Form submission handler
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !session?.user || !clientId) return;

    setLoading(true);
    try {
      const response = await fetch("/api/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: newMessage,
          clientId,
        }),
      });

      if (!response.ok) throw new Error("Failed to send message");

      // Optimistically add message to UI
      const optimisticMessage: Message = {
        id: `temp-${Date.now()}`,
        content: newMessage,
        senderId: session.user.id as string,
        createdAt: new Date().toISOString(),
        sender: {
          fullName: session.user.name || "You",
        }
      };
      
      setMessages(prev => [...prev, optimisticMessage]);
      setNewMessage("");
      scrollToBottom();
      
      // Fetch updated messages after a short delay
      setTimeout(fetchMessages, 500);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      // Focus back on input after sending
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [newMessage, session, clientId, toast, fetchMessages]);

  // Scroll to bottom utility
  const scrollToBottom = useCallback(() => {
    requestAnimationFrame(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
      }
    });
  }, []);

  // Update input field handler
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value);
  }, []);

  // Memoize message rendering for better performance
  const messageElements = useMemo(() => {
    return messages.map((message) => (
      <div
        key={message.id}
        className={`flex ${
          message.senderId === session?.user?.id ? "justify-end" : "justify-start"
        }`}
      >
        <div
          className={`max-w-[80%] rounded-lg p-3 ${
            message.senderId === session?.user?.id
              ? "bg-primary text-primary-foreground"
              : "bg-muted"
          }`}
        >
          <p className="text-sm font-medium">{message.sender.fullName}</p>
          <p className="text-sm">{message.content}</p>
          <p className="mt-1 text-xs opacity-70">
            {new Date(message.createdAt).toLocaleTimeString()}
          </p>
        </div>
      </div>
    ));
  }, [messages, session]);

  return (
    <div className="flex h-[600px] flex-col rounded-lg border">
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messageElements}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      <form onSubmit={handleSubmit} className="border-t p-4">
        <div className="flex gap-2">
          <Input
            value={newMessage}
            onChange={handleInputChange}
            placeholder="Type your message..."
            disabled={loading}
            ref={inputRef}
          />
          <Button type="submit" disabled={loading}>
            Send
          </Button>
        </div>
      </form>
    </div>
  );
}

