"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Loader2 } from "lucide-react"

export function TrainerContactForm() {
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [message, setMessage] = useState("")
  const [status, setStatus] = useState<"idle" | "loading" | "success" | "error">("idle")
  const [feedbackMessage, setFeedbackMessage] = useState("")

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setStatus("loading")
    setFeedbackMessage("")

    try {
      const response = await fetch("/api/contact/trainer", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name, email, message }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || "Failed to send message.")
      }

      setStatus("success")
      setFeedbackMessage("Thank you! Your message has been sent. We\'ll be in touch soon.")
      setName("")
      setEmail("")
      setMessage("")
    } catch (error) {
      setStatus("error")
      setFeedbackMessage(error instanceof Error ? error.message : "An unexpected error occurred.")
    } 
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Your Name</Label>
          <Input 
            id="name" 
            type="text" 
            placeholder="Jane Doe" 
            value={name} 
            onChange={(e) => setName(e.target.value)} 
            required 
            disabled={status === "loading"}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Your Email</Label>
          <Input 
            id="email" 
            type="email" 
            placeholder="<EMAIL>" 
            value={email} 
            onChange={(e) => setEmail(e.target.value)} 
            required 
            disabled={status === "loading"}
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="message">Your Message</Label>
        <Textarea 
          id="message" 
          placeholder="Tell us about yourself and how you\'d like to use the platform..." 
          value={message} 
          onChange={(e) => setMessage(e.target.value)} 
          required 
          rows={5}
          disabled={status === "loading"}
        />
      </div>
      <div>
        <Button type="submit" disabled={status === "loading"} className="w-full md:w-auto">
          {status === "loading" ? (
            <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Sending...</>
          ) : (
            "Send Inquiry"
          )}
        </Button>
      </div>
      {feedbackMessage && (
        <p className={`text-sm ${status === "success" ? "text-green-600" : "text-red-600"}`}>
          {feedbackMessage}
        </p>
      )}
    </form>
  )
} 