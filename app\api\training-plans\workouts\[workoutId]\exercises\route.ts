import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(
  request: Request,
  context: any // Use 'any' workaround
) {
  try {
    const session = await getServerSession(authOptions)
    // Authorization Check: Ensure user is logged in and owns the workout or is admin
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { exerciseId } = await request.json()
    // Safely access params
    const workoutId = context?.params?.workoutId;
    if (!workoutId) {
      return new NextResponse("Workout ID missing in URL", { status: 400 });
    }
    if (!exerciseId || typeof exerciseId !== 'string') {
      return new NextResponse("Exercise ID is required in the request body", { status: 400 });
    }

    // Verify the workout exists and check ownership
    const workout = await prisma.workout.findUnique({
      where: { id: workoutId },
      include: { 
        trainingPlan: { select: { athleteId: true } } // Needed for ownership check
      }
    })

    if (!workout) {
      return NextResponse.json(
        { error: "Workout not found" },
        { status: 404 }
      )
    }

    // Check permissions
    if (workout.trainingPlan.athleteId !== session.user.id && session.user.role !== "admin") {
       return NextResponse.json(
        { error: "Forbidden: You do not own this workout" },
        { status: 403 }
      )
    }

    // Verify the exercise template exists (assuming we add from templates)
    // If adding non-template exercises, adjust logic
    const exerciseTemplate = await prisma.exercise.findUnique({
      where: { 
        id: exerciseId, 
        isTemplate: true // Assuming we add templates
      },
    })

    if (!exerciseTemplate) {
      return NextResponse.json(
        { error: "Exercise template not found or provided ID is not a template" },
        { status: 404 }
      )
    }

    // Determine the order for the new exercise
    const lastExercise = await prisma.exercise.findFirst({
        where: { workoutId: workoutId },
        orderBy: { order: 'desc' },
        select: { order: true }
    });
    const newOrder = (lastExercise?.order ?? -1) + 1;

    // Create a new exercise instance linked to the workout, based on the template
    const newExercise = await prisma.exercise.create({
      data: {
        name: exerciseTemplate.name,
        description: exerciseTemplate.description,
        sets: exerciseTemplate.sets,
        reps: exerciseTemplate.reps,
        duration: exerciseTemplate.duration,
        restTime: exerciseTemplate.restTime,
        videoUrl: exerciseTemplate.videoUrl,
        muscleGroup: exerciseTemplate.muscleGroup,
        type: exerciseTemplate.type,
        thumbnailUrl: exerciseTemplate.thumbnailUrl,
        calories: exerciseTemplate.calories,
        difficulty: exerciseTemplate.difficulty,
        equipment: exerciseTemplate.equipment,
        isTemplate: false, // The new instance is not a template itself
        order: newOrder,
        workout: { connect: { id: workoutId } }, // Connect to the workout
        creator: { connect: { id: session.user.id } } // Link to the user adding it
      }
    });

    return NextResponse.json(newExercise)

  } catch (error) {
    console.error("Error adding exercise to workout:", error)
    return NextResponse.json(
      { error: "Failed to add exercise to workout" },
      { status: 500 }
    )
  }
} 