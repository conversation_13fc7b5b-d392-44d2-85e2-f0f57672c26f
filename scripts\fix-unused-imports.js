const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Run ESLint with --fix option to automatically fix what it can
try {
  console.log('Running ESLint with --fix option...');
  execSync('npx eslint . --ext .ts,.tsx --fix', { stdio: 'inherit' });
  console.log('ESLint fixes applied successfully!');
} catch (error) {
  console.error('Error running ESLint:', error.message);
}
