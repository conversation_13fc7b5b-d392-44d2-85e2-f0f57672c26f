"use client";

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

// Query status type
export type QueryStatus = 'idle' | 'loading' | 'success' | 'error';

// Error with additional properties for API errors
export interface ApiError extends Error {
  status?: number;
  data?: any;
}

// Options for the useSafeQuery hook
export interface UseSafeQueryOptions<T> {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: any;
  headers?: HeadersInit;
  skip?: boolean;
  sanitize?: boolean;
  retry?: boolean;
  retryCount?: number;
  retryDelay?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: ApiError) => void;
  queryKey?: string[];
  dependencies?: any[];
}

/**
 * A custom hook for secure data fetching
 * @param options The query options
 * @returns The query result and control functions
 */
export function useSafeQuery<T = any>({
  url,
  method = 'GET',
  body,
  headers,
  skip = false,
  sanitize = true,
  retry = true,
  retryCount = 3,
  retryDelay = 1000,
  onSuccess,
  onError,
  queryKey,
  dependencies = [],
}: UseSafeQueryOptions<T>) {
  // State for query result and status
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<ApiError | null>(null);
  const [status, setStatus] = useState<QueryStatus>('idle');
  const [retries, setRetries] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Use toast for error messages
  const { toast } = useToast();

  // Function to handle fetch errors
  const handleError = useCallback((err: any, statusCode?: number) => {
    const apiError: ApiError = err instanceof Error
      ? err
      : new Error(typeof err === 'string' ? err : 'An error occurred');

    if (statusCode) {
      apiError.status = statusCode;
    }

    setError(apiError);
    setStatus('error');
    setIsLoading(false);

    if (onError) {
      onError(apiError);
    }

    // Show error toast for user feedback
    toast({
      title: 'Error',
      description: apiError.message || 'An error occurred while fetching data',
      variant: 'destructive',
    });
  }, [onError, toast]);

  // Function to execute the query
  const executeQuery = useCallback(async () => {
    if (skip) return;

    setStatus('loading');
    setIsLoading(true);

    try {
      // Build request options
      const requestOptions: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
      };

      // Add body for non-GET requests
      if (method !== 'GET' && body) {
        requestOptions.body = JSON.stringify(body);
      }

      // Execute request
      const response = await fetch(url, requestOptions);

      // Handle HTTP errors
      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch {
          errorData = { message: 'Unknown error' };
        }

        const errorMessage = errorData.message || errorData.error || `HTTP error ${response.status}`;
        const error = new Error(errorMessage) as ApiError;
        error.status = response.status;
        error.data = errorData;

        throw error;
      }

      // Parse response data
      let responseData;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      // Update state with success
      setData(responseData);
      setStatus('success');
      setError(null);
      setRetries(0);
      setIsLoading(false);

      // Call onSuccess callback
      if (onSuccess) {
        onSuccess(responseData);
      }

      return responseData;
    } catch (err) {
      // Handle retries
      if (retry && retries < retryCount) {
        setRetries(prev => prev + 1);

        // Exponential backoff
        const delay = retryDelay * Math.pow(2, retries);

        setTimeout(() => {
          executeQuery();
        }, delay);

        return;
      }

      // Handle error if max retries reached
      handleError(err, (err as ApiError)?.status);
    }
  }, [
    url, method, body, headers, skip, retry, retryCount, retryDelay,
    retries, handleError, onSuccess
  ]);

  // Execute query when dependencies change
  useEffect(() => {
    if (!skip) {
      executeQuery();
    }
  }, [skip, executeQuery, ...(dependencies || [])]);

  // Function to manually refresh the data
  const refetch = useCallback(() => {
    setRetries(0);
    return executeQuery();
  }, [executeQuery]);

  return {
    data,
    error,
    status,
    isLoading,
    isSuccess: status === 'success',
    isError: status === 'error',
    refetch,
  };
}

/**
 * A custom hook for making mutations (POST, PUT, DELETE)
 * @param options The mutation options
 * @returns The mutation function and state
 */
export function useSafeMutation<TData = any, TVariables = any>({
  url,
  method = 'POST',
  headers,
  sanitize = true,
  onSuccess,
  onError,
}: Omit<UseSafeQueryOptions<TData>, 'body' | 'skip'>) {
  // State for mutation result and status
  const [data, setData] = useState<TData | null>(null);
  const [error, setError] = useState<ApiError | null>(null);
  const [status, setStatus] = useState<QueryStatus>('idle');
  const [isLoading, setIsLoading] = useState(false);

  // Use toast for error messages
  const { toast } = useToast();

  // Function to execute the mutation
  const mutate = useCallback(async (variables?: TVariables) => {
    setStatus('loading');
    setIsLoading(true);

    try {
      // Build request options
      const requestOptions: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
      };

      // Add body if variables are provided
      if (variables) {
        requestOptions.body = JSON.stringify(variables);
      }

      // Execute request
      const response = await fetch(url, requestOptions);

      // Handle HTTP errors
      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch {
          errorData = { message: 'Unknown error' };
        }

        const errorMessage = errorData.message || errorData.error || `HTTP error ${response.status}`;
        const error = new Error(errorMessage) as ApiError;
        error.status = response.status;
        error.data = errorData;

        throw error;
      }

      // Parse response data
      let responseData;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      // Update state with success
      setData(responseData);
      setStatus('success');
      setError(null);
      setIsLoading(false);

      // Call onSuccess callback
      if (onSuccess) {
        onSuccess(responseData);
      }

      return responseData;
    } catch (err) {
      // Update state with error
      const apiError = err instanceof Error
        ? err as ApiError
        : new Error(typeof err === 'string' ? err : 'An error occurred');

      setError(apiError);
      setStatus('error');
      setIsLoading(false);

      // Call onError callback
      if (onError) {
        onError(apiError);
      }

      // Show error toast for user feedback
      toast({
        title: 'Error',
        description: apiError.message || 'An error occurred',
        variant: 'destructive',
      });

      throw apiError;
    }
  }, [url, method, headers, onSuccess, onError, toast]);

  // Reset state
  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setStatus('idle');
    setIsLoading(false);
  }, []);

  return {
    mutate,
    data,
    error,
    status,
    isLoading,
    isSuccess: status === 'success',
    isError: status === 'error',
    reset,
  };
}
