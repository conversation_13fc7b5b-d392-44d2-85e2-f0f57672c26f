#!/bin/sh
# entrypoint.sh

# Exit immediately if a command exits with a non-zero status.
set -e

# Variables (replace with your actual DB host and port from docker-compose)
DB_HOST="db"
DB_PORT="5432"

# Wait for the database to be ready
echo "Waiting for database at $DB_HOST:$DB_PORT..."

# Loop until the database port is open
# Use netcat (nc) which is available in alpine
# The -z flag tells nc to scan for listening daemons, without sending any data to them.
# The -w 1 flag sets a 1-second timeout for connection attempts.
while ! nc -z -w 1 "$DB_HOST" "$DB_PORT"; do
  sleep 1
done

echo "Database is ready."

# Add a small delay to allow <PERSON> to fully initialize
echo "Waiting a few seconds for DB initialization..."
sleep 5

# Run Prisma db push with --force-reset --accept-data-loss flag
echo "Running prisma db push..."
pnpm exec prisma db push --accept-data-loss
echo "prisma db push completed."

# Run Prisma seed
echo "Running prisma db seed..."
pnpm exec prisma db seed
echo "prisma db seed completed."

# Start Prisma Studio in the background (if ENABLE_PRISMA_STUDIO is set to true)
if [ "$ENABLE_PRISMA_STUDIO" = "true" ]; then
  echo "Starting Prisma Studio on port 5555..."
  pnpm exec prisma studio --port 5555 --hostname 0.0.0.0 &
  echo "Prisma Studio started in the background."
fi

# Ensure .next directory is removed before starting dev server
# (Keep this commented out for now, as the volume issue might be resolved)
# echo "Removing existing .next directory..."
# rm -rf .next

# Execute the command passed as arguments to this script (CMD from Dockerfile)
echo "Executing command: $@"
exec "$@" 