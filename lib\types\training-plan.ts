export interface Exercise {
  id: string
  name: string
  sets: number
  reps: number
  weight: number
  notes?: string
  category?: string
  iconName?: string
  video?: string
  description?: string
  targetMuscles?: string[]
  difficulty?: "Beginner" | "Intermediate" | "Advanced"
  duration?: string
}

export interface DailyWorkout {
  id: string
  day: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday"
  exercises: Exercise[]
}

export interface WeekPlan {
  id: string
  dailyWorkouts: DailyWorkout[]
}

export interface Plan {
  id: string
  title: string
  description: string
  type: string
  weeks: WeekPlan[]
  [key: string]: any
} 