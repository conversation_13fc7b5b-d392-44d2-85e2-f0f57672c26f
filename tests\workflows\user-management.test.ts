import { describe, it, expect } from 'vitest'
import { prisma } from './setup'

describe('User Management Workflows', () => {
  describe('User Database Access', () => {
    it('should be able to access the users table', async () => {
      const count = await prisma.user.count()
      expect(count).toBeGreaterThanOrEqual(0)
    })

    it('should be able to create and delete a user', async () => {
      // Create a test user
      const user = await prisma.user.create({
        data: {
          name: 'Test User',
          email: `test-${Date.now()}@example.com`,
          password: 'password123', // Add the required password field
          role: 'client'
        }
      })

      expect(user).toBeDefined()
      expect(user.name).toBe('Test User')

      // Delete the user
      const deletedUser = await prisma.user.delete({
        where: {
          id: user.id
        }
      })

      expect(deletedUser).toBeDefined()
      expect(deletedUser.id).toBe(user.id)
    })
  })
})
