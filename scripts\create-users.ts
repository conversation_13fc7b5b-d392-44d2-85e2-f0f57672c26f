import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  // Create admin user
  const adminPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD || 'admin123', 10)
  const admin = await prisma.user.upsert({
    where: { email: process.env.ADMIN_EMAIL || '<EMAIL>' },
    update: {},
    create: {
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      password: adminPassword,
      role: 'admin',
      fullName: process.env.ADMIN_NAME || 'Admin User',
    },
  })

  // Create test client user
  const testPassword = await bcrypt.hash(process.env.TEST_USER_PASSWORD || 'test123', 10)
  const testUser = await prisma.user.upsert({
    where: { email: process.env.TEST_USER_EMAIL || '<EMAIL>' },
    update: {},
    create: {
      email: process.env.TEST_USER_EMAIL || '<EMAIL>',
      password: testPassword,
      role: 'client',
      fullName: process.env.TEST_USER_NAME || 'Test User',
    },
  })

  console.log({ admin, testUser })
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 