import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { 
  createTestUser, 
  createTestClientProfile, 
  createTestTrainerProfile,
  cleanupTestData,
  disconnectDatabase
} from './helpers/user-roles'

// Create a Prisma client for testing
const prisma = new PrismaClient()

describe('Trainer Workflows', () => {
  let trainerUser: any
  let trainerProfile: any
  let clientUser: any
  let clientProfile: any
  let subscriptionTier: any
  
  beforeAll(async () => {
    // Create a trainer
    trainerUser = await createTestUser('trainer')
    trainerProfile = await createTestTrainerProfile(trainerUser.id)
    
    // Create a client
    clientUser = await createTestUser('client')
    clientProfile = await createTestClientProfile(clientUser.id)
    
    // Assign client to trainer
    await prisma.clientProfile.update({
      where: {
        id: clientProfile.id
      },
      data: {
        assignedTrainerId: trainerProfile.id
      }
    })
    
    // Create a subscription tier
    subscriptionTier = await prisma.subscriptionTier.create({
      data: {
        name: 'Test Tier',
        price: 19.99,
        description: 'A test subscription tier',
        features: ['Feature 1', 'Feature 2'],
        trainerId: trainerProfile.id
      }
    })
  })
  
  afterAll(async () => {
    // Clean up test data
    await prisma.subscriptionTier.delete({
      where: {
        id: subscriptionTier.id
      }
    })
    
    await cleanupTestData(clientUser.id)
    await cleanupTestData(trainerUser.id)
    
    await disconnectDatabase()
  })
  
  describe('Client Management', () => {
    it('should allow trainers to view their clients', async () => {
      const clients = await prisma.clientProfile.findMany({
        where: {
          assignedTrainerId: trainerProfile.id
        },
        include: {
          user: true
        }
      })
      
      expect(clients).toBeDefined()
      expect(clients.length).toBeGreaterThan(0)
      expect(clients.some(client => client.userId === clientUser.id)).toBe(true)
    })
  })
  
  describe('Training Plan Management', () => {
    it('should allow trainers to create training plans', async () => {
      // Create a training plan
      const trainingPlan = await prisma.trainingPlan.create({
        data: {
          title: 'Test Training Plan',
          description: 'A test training plan',
          type: 'strength',
          difficulty: 'intermediate',
          trainerId: trainerProfile.id
        }
      })
      
      expect(trainingPlan).toBeDefined()
      expect(trainingPlan.title).toBe('Test Training Plan')
      expect(trainingPlan.trainerId).toBe(trainerProfile.id)
      
      // Create a week
      const week = await prisma.week.create({
        data: {
          number: 1,
          trainingPlanId: trainingPlan.id
        }
      })
      
      expect(week).toBeDefined()
      expect(week.number).toBe(1)
      expect(week.trainingPlanId).toBe(trainingPlan.id)
      
      // Create a workout
      const workout = await prisma.workout.create({
        data: {
          name: 'Test Workout',
          description: 'A test workout',
          day: 1,
          weekId: week.id,
          trainerId: trainerProfile.id
        }
      })
      
      expect(workout).toBeDefined()
      expect(workout.name).toBe('Test Workout')
      expect(workout.weekId).toBe(week.id)
      
      // Create an exercise
      const exercise = await prisma.exercise.create({
        data: {
          name: 'Push-ups',
          description: 'Standard push-ups',
          sets: 3,
          reps: 10,
          restTime: 60,
          order: 1,
          workoutId: workout.id,
          createdById: trainerUser.id
        }
      })
      
      expect(exercise).toBeDefined()
      expect(exercise.name).toBe('Push-ups')
      expect(exercise.workoutId).toBe(workout.id)
      
      // Assign the training plan to the client
      const clientPlan = await prisma.clientPlan.create({
        data: {
          clientProfileId: clientProfile.id,
          trainingPlanId: trainingPlan.id,
          assignedAt: new Date()
        }
      })
      
      expect(clientPlan).toBeDefined()
      expect(clientPlan.clientProfileId).toBe(clientProfile.id)
      expect(clientPlan.trainingPlanId).toBe(trainingPlan.id)
      
      // Clean up
      await prisma.clientPlan.delete({
        where: {
          id: clientPlan.id
        }
      })
      
      await prisma.exercise.delete({
        where: {
          id: exercise.id
        }
      })
      
      await prisma.workout.delete({
        where: {
          id: workout.id
        }
      })
      
      await prisma.week.delete({
        where: {
          id: week.id
        }
      })
      
      await prisma.trainingPlan.delete({
        where: {
          id: trainingPlan.id
        }
      })
    })
  })
  
  describe('Diet Plan Management', () => {
    it('should allow trainers to create diet plans', async () => {
      // Create a diet plan
      const dietPlan = await prisma.dietPlan.create({
        data: {
          title: 'Test Diet Plan',
          description: 'A test diet plan',
          trainerId: trainerProfile.id
        }
      })
      
      expect(dietPlan).toBeDefined()
      expect(dietPlan.title).toBe('Test Diet Plan')
      expect(dietPlan.trainerId).toBe(trainerProfile.id)
      
      // Create a meal
      const meal = await prisma.meal.create({
        data: {
          name: 'Breakfast',
          description: 'A healthy breakfast',
          calories: 500,
          protein: 30,
          carbs: 50,
          fat: 15,
          dietPlanId: dietPlan.id
        }
      })
      
      expect(meal).toBeDefined()
      expect(meal.name).toBe('Breakfast')
      expect(meal.dietPlanId).toBe(dietPlan.id)
      
      // Assign the diet plan to the client
      const clientDietPlan = await prisma.clientDietPlan.create({
        data: {
          clientProfileId: clientProfile.id,
          dietPlanId: dietPlan.id,
          assignedAt: new Date()
        }
      })
      
      expect(clientDietPlan).toBeDefined()
      expect(clientDietPlan.clientProfileId).toBe(clientProfile.id)
      expect(clientDietPlan.dietPlanId).toBe(dietPlan.id)
      
      // Clean up
      await prisma.clientDietPlan.delete({
        where: {
          id: clientDietPlan.id
        }
      })
      
      await prisma.meal.delete({
        where: {
          id: meal.id
        }
      })
      
      await prisma.dietPlan.delete({
        where: {
          id: dietPlan.id
        }
      })
    })
  })
  
  describe('Product Management', () => {
    it('should allow trainers to create and manage products', async () => {
      // Create a product
      const product = await prisma.product.create({
        data: {
          title: 'Test Product',
          description: 'A test product',
          price: 29.99,
          type: 'ebook',
          athleteId: trainerUser.id
        }
      })
      
      expect(product).toBeDefined()
      expect(product.title).toBe('Test Product')
      expect(product.athleteId).toBe(trainerUser.id)
      
      // Update the product
      const updatedProduct = await prisma.product.update({
        where: {
          id: product.id
        },
        data: {
          price: 39.99,
          description: 'Updated description'
        }
      })
      
      expect(updatedProduct).toBeDefined()
      expect(updatedProduct.price).toBe(39.99)
      expect(updatedProduct.description).toBe('Updated description')
      
      // Clean up
      await prisma.product.delete({
        where: {
          id: product.id
        }
      })
    })
  })
  
  describe('Subscription Management', () => {
    it('should allow trainers to manage subscription tiers', async () => {
      // Update subscription tier
      const updatedTier = await prisma.subscriptionTier.update({
        where: {
          id: subscriptionTier.id
        },
        data: {
          price: 29.99,
          features: ['Feature 1', 'Feature 2', 'Feature 3']
        }
      })
      
      expect(updatedTier).toBeDefined()
      expect(updatedTier.price).toBe(29.99)
      expect(updatedTier.features).toHaveLength(3)
      
      // Create a subscription for a client
      const subscription = await prisma.subscription.create({
        data: {
          clientProfileId: clientProfile.id,
          tierId: subscriptionTier.id,
          status: 'active',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
        }
      })
      
      expect(subscription).toBeDefined()
      expect(subscription.clientProfileId).toBe(clientProfile.id)
      expect(subscription.tierId).toBe(subscriptionTier.id)
      expect(subscription.status).toBe('active')
      
      // Clean up
      await prisma.subscription.delete({
        where: {
          id: subscription.id
        }
      })
    })
  })
})
