/**
 * Cloudinary integration for Clear Coach
 * Handles image uploads and transformations
 */

declare module 'cloudinary' {
  export const v2: any;
}

import { v2 as cloudinary } from 'cloudinary';
import fs from 'fs';

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || '',
  api_key: process.env.CLOUDINARY_API_KEY || '',
  api_secret: process.env.CLOUDINARY_API_SECRET || '',
  secure: true,
});

/**
 * Upload a file to Cloudinary
 * @param filePath Path to the file to upload
 * @param options Cloudinary upload options
 * @returns URL of the uploaded file
 */
export async function uploadToCloudinary(
  filePath: string,
  options: {
    folder?: string;
    transformation?: any[];
    [key: string]: any;
  } = {}
): Promise<string> {
  try {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    const uploadOptions = {
      folder: options.folder || 'clear-coach',
      resource_type: 'auto',
      ...options,
    };

    const result = await cloudinary.uploader.upload(filePath, uploadOptions);

    return result.secure_url;
  } catch (error) {
    console.error('Error uploading to Cloudinary:', error);
    throw error;
  }
}

/**
 * Generate a thumbnail URL from an existing Cloudinary image
 * @param imageUrl Original Cloudinary image URL
 * @param width Thumbnail width
 * @param height Thumbnail height
 * @returns Thumbnail URL
 */
export function generateThumbnailUrl(
  imageUrl: string,
  width: number = 300,
  height: number = 300
): string {
  if (!imageUrl.includes('cloudinary.com')) {
    return imageUrl;
  }

  const matches = imageUrl.match(/\/v\d+\/(.+?)(?:\.\w+)?$/);
  if (!matches || !matches[1]) {
    return imageUrl;
  }

  const publicId = matches[1];
  const baseUrl = imageUrl.split('/upload/')[0] + '/upload/';

  return `${baseUrl}c_fill,w_${width},h_${height}/${publicId}`;
}

/**
 * Delete a file from Cloudinary
 * @param publicId Public ID of the file to delete
 * @returns Result of the deletion
 */
export async function deleteFromCloudinary(publicId: string): Promise<any> {
  try {
    return await cloudinary.uploader.destroy(publicId);
  } catch (error) {
    console.error('Error deleting from Cloudinary:', error);
    throw error;
  }
}
