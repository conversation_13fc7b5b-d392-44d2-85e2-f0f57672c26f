"use client"

import { ColumnDef } from "@tanstack/react-table"
import { MessageSquare, FileText, Ban, RefreshCw, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"

export interface Client {
  id: string
  name: string
  email: string
  status: string
  joinedDate: string
  nextSession: string | null
  unreadMessages: number
  plan: string
  relationshipId: string
  monthlyFee: number
  expirationDate?: string
}

export const ClientTableColumns: ColumnDef<Client>[] = [
  {
    accessorKey: "name",
    header: "Client",
    cell: ({ row }) => {
      const client = row.original
      const initials = client.name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()

      return (
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8 border border-primary/10">
            <AvatarFallback className="bg-primary/5 text-primary/70 text-sm">
              {initials}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="font-medium">{client.name}</span>
            <span className="text-xs text-muted-foreground">{client.email}</span>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string
      const expirationDate = row.original.expirationDate ? new Date(row.original.expirationDate) : null
      const isExpired = expirationDate && expirationDate < new Date()

      return (
        <div className="flex items-center gap-2">
          <Badge
            variant={status === "active" && !isExpired ? "default" : "destructive"}
            className="capitalize"
          >
            {isExpired ? "Expired" : status}
          </Badge>
        </div>
      )
    },
  },
  {
    accessorKey: "expirationDate",
    header: "Expires",
    cell: ({ row }) => {
      const expirationDate = row.getValue("expirationDate") as string
      if (!expirationDate) return null
      
      const date = new Date(expirationDate)
      const isExpired = date < new Date()
      const formattedDate = date.toLocaleDateString()
      
      return (
        <div className={cn(
          "font-medium",
          isExpired ? "text-destructive" : "text-muted-foreground"
        )}>
          {formattedDate}
        </div>
      )
    },
  },
  {
    accessorKey: "plan",
    header: "Plan",
    cell: ({ row }) => {
      const plan = row.getValue("plan") as string
      return (
        <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20">
          {plan}
        </Badge>
      )
    },
  },
  {
    accessorKey: "monthlyFee",
    header: "Monthly Fee",
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("monthlyFee"))
      const formatted = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(amount)

      return <span className="font-medium tabular-nums">{formatted}</span>
    },
  },
  {
    accessorKey: "joinedDate",
    header: "Join Date",
    cell: ({ row }) => {
      const date = new Date(row.getValue("joinedDate"))
      const formatted = new Intl.DateTimeFormat("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      }).format(date)

      return <span className="text-muted-foreground text-sm">{formatted}</span>
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const client = row.original
      const isActive = client.status === "active"

      const dispatchEvent = (type: string) => {
        const event = new CustomEvent(type, {
          detail: {
            clientId: client.id,
            clientName: client.name,
            relationshipId: client.relationshipId,
            monthlyFee: client.monthlyFee,
          },
        })
        document.dispatchEvent(event)
      }

      return (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="hover:bg-primary/5 hover:text-primary min-w-[80px]"
            asChild
          >
            <Link href={`/dashboard/messages?client=${client.id}`}>
              <MessageSquare className="h-4 w-4 mr-2" />
              Chat
            </Link>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="hover:bg-primary/5 hover:text-primary min-w-[100px]"
            asChild
          >
            <Link href={`/dashboard/coaching/clients/${client.id}`}>
              <FileText className="h-4 w-4 mr-2" />
              Follow Up
            </Link>
          </Button>
          {isActive ? (
            <Button
              variant="ghost"
              size="sm"
              className="text-destructive hover:bg-destructive/10 hover:text-destructive min-w-[90px]"
              onClick={() => dispatchEvent("cancelSubscription")}
            >
              <Ban className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          ) : (
            <Button
              variant="default"
              size="sm"
              className="bg-primary hover:bg-primary/90 min-w-[90px]"
              onClick={() => dispatchEvent("renewSubscription")}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Renew
            </Button>
          )}
        </div>
      )
    },
  },
] 