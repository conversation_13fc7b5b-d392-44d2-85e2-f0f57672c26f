import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "admin") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const exercises = await prisma.templateExercise.findMany({
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json(exercises)
  } catch (error) {
    console.error("[TEMPLATE_EXERCISES_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "admin") {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await request.json()
    const { name, description, sets, reps, duration, restTime, videoUrl, type, difficulty } = body

    if (!name || !type || !difficulty) {
      return new NextResponse("Name, type, and difficulty are required", { status: 400 })
    }

    const exercise = await prisma.templateExercise.create({
      data: {
        name,
        description,
        sets,
        reps,
        duration,
        restTime,
        videoUrl,
        type,
        difficulty,
      },
    })

    return NextResponse.json(exercise)
  } catch (error) {
    console.error("[TEMPLATE_EXERCISE_CREATE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 