import { ReactNode } from 'react'

export interface DietPlan {
  id: string
  title: string
  description: string
  is_template: boolean
  calories_per_day: number
  createdAt: string
  updatedAt: string
  meals: Array<{
    name: string
    calories: number
    protein: number
    carbs: number
    fats: number
  }>
}

export interface DietPlanGridProps {
  plans: DietPlan[]
  onDuplicatePlan?: (id: string) => void
  onDeletePlan?: (id: string) => void
}

export function DietPlanGrid(props: DietPlanGridProps): ReactNode 