import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

export default async function TrainerDetailPage({ params }: { params: { trainerId: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect("/login")
  }

  // Get the trainer slug based on the ID
  const trainerSlug = params.trainerId === "1" ? "john-smith" :
                      params.trainerId === "2" ? "sarah-johnson" :
                      params.trainerId === "3" ? "michael-rodriguez" : "trainer"

  // Redirect directly to the trainer landing page
  redirect(`/${trainerSlug}`)
}
