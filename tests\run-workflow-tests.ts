import { execSync } from 'child_process'
import { PrismaClient } from '@prisma/client'
import { fileURLToPath } from 'url'
import { dirname, resolve } from 'path'

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Create a Prisma client for test database setup
const prisma = new PrismaClient()

// Set NODE_PATH to include the project root
process.env.NODE_PATH = resolve(__dirname, '..')

async function main() {
  try {
    console.log('🔄 Setting up test database...')

    // Run migrations on test database
    execSync('npx prisma migrate deploy', { stdio: 'inherit' })

    console.log('✅ Test database setup complete')
    console.log('🧪 Running workflow tests...')

    // Run the tests with the correct configuration
    execSync('npx vitest run tests/workflows/*.test.ts --config vitest.config.ts', { stdio: 'inherit' })

    console.log('✅ All workflow tests passed!')
    process.exit(0)
  } catch (error) {
    console.error('❌ Workflow tests failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()
