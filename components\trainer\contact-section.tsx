"use client"

import { useState } from "react"
import { Mail, Send, Instagram, Twitter, Youtube, Facebook } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { TabsContent } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"

interface TrainerContactInfo {
  id: string
  name: string | null
  email: string | null
  socialLinks: any
}

interface ContactSectionProps {
  trainer: TrainerContactInfo
}

export function ContactSection({ trainer }: ContactSectionProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: ""
  })

  // Parse social links
  let socialLinksParsed: Record<string, string | null> = {}
  try {
    socialLinksParsed = typeof trainer.socialLinks === 'string' 
      ? JSON.parse(trainer.socialLinks) 
      : trainer.socialLinks || {}
  } catch (e) {
    console.error("Error parsing social links:", e)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // TODO: Replace with actual API call to send message
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Message sent!",
        description: "Your message has been sent to the trainer."
      })
      
      setFormData({
        name: "",
        email: "",
        message: ""
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // Social media icon mapping
  const socialIcons: Record<string, JSX.Element> = {
    instagram: <Instagram className="h-5 w-5" />,
    twitter: <Twitter className="h-5 w-5" />,
    youtube: <Youtube className="h-5 w-5" />,
    facebook: <Facebook className="h-5 w-5" />,
  }

  return (
    <TabsContent value="contact" className="space-y-4">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-2">Contact {trainer.name || "Trainer"}</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Have questions or want to learn more? Get in touch directly.
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Send a Message</CardTitle>
            <CardDescription>
              Fill out the form below to contact {trainer.name || "the trainer"} directly.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Your Name</Label>
                <Input 
                  id="name" 
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter your name" 
                  required 
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input 
                  id="email" 
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="Enter your email" 
                  required 
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <Textarea 
                  id="message" 
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="What would you like to ask about?" 
                  rows={5}
                  required 
                />
              </div>
              
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>Sending...</>
                ) : (
                  <>
                    Send Message
                    <Send className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>
              Ways to connect with {trainer.name || "the trainer"}.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {trainer.email && (
              <div>
                <h3 className="font-medium mb-2 flex items-center">
                  <Mail className="mr-2 h-5 w-5 text-primary" />
                  Email
                </h3>
                <p className="text-muted-foreground ml-7">
                  <a href={`mailto:${trainer.email}`} className="hover:text-primary">
                    {trainer.email}
                  </a>
                </p>
              </div>
            )}
            
            {Object.keys(socialLinksParsed).length > 0 && (
              <div>
                <h3 className="font-medium mb-3">Social Media</h3>
                <div className="flex flex-wrap gap-3 ml-2">
                  {Object.entries(socialLinksParsed).map(([platform, url]) => 
                    url && socialIcons[platform] ? (
                      <a 
                        key={platform}
                        href={url as string}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-3 rounded-full bg-primary/10 hover:bg-primary/20 text-primary transition-colors"
                        aria-label={`${platform} profile`}
                      >
                        {socialIcons[platform]}
                      </a>
                    ) : null
                  )}
                </div>
              </div>
            )}
            
            <div>
              <h3 className="font-medium mb-3">Response Time</h3>
              <p className="text-muted-foreground ml-2">
                Typically responds within 24-48 hours.
              </p>
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4">
            <p className="text-sm text-muted-foreground">
              For urgent inquiries, please include the word "urgent" in your message subject.
            </p>
          </CardFooter>
        </Card>
      </div>
    </TabsContent>
  )
} 