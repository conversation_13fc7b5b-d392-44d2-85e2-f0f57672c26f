/* Override any fixed position styles */
.premium-tabs-container {
  position: relative !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  z-index: 1 !important;
}

/* Hide any duplicate tab bars */
.premium-tabs-static,
div[role="tablist"],
.fixed-tabs,
.static-tabs,
.premium-static-tabs,
.premium-dashboard-tabs:not(.premium-tabs-container) {
  display: none !important;
}
