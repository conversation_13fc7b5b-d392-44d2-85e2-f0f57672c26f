"use client"

import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { Info, UserPlus } from "lucide-react"
import { WeeklyPlanEditor } from "@/components/training-plan/weekly-plan-editor"
import { AssignToClientDialog } from "@/components/training-plan/assign-to-client-dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"

// Keep the interfaces
interface Exercise {
  id: string
  name: string
  sets: number
  reps: number
  weight: number
  category?: string
  iconName?: string
  video?: string
  description?: string
  targetMuscles?: string[]
  difficulty?: "Beginner" | "Intermediate" | "Advanced"
  duration?: string
  parentExerciseId?: string
  isAlternative?: boolean
  hasAlternatives?: boolean
  restTime?: number
  videoUrl?: string
  thumbnailUrl?: string
  type?: string
  muscleGroup?: string
  equipment?: string
  order?: number
}

interface DailyWorkout {
  id: string
  day: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday"
  exercises: Exercise[]
}

interface WeekPlan {
  id: string
  dailyWorkouts: DailyWorkout[]
}

interface Plan {
  id: string
  title: string
  description: string
  type: "personal" | "template"
  isPublic?: boolean
  trainerNotes?: string
  targetLevel?: "Beginner" | "Intermediate" | "Advanced"
  estimatedDuration?: string
  category?: string
  weeks: WeekPlan[]
  createdAt: string
}

export default function CreateTrainingPlan() {
  const { toast } = useToast()
  const router = useRouter()
  const [availableExercises, setAvailableExercises] = useState<Exercise[]>([])
  const [isLoadingExercises, setIsLoadingExercises] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [createdPlanId, setCreatedPlanId] = useState<string | null>(null)

  // Assignment dialog states - only client assignment for premium coaching
  const [showAssignClientDialog, setShowAssignClientDialog] = useState(false)
  const [showAssignOptions, setShowAssignOptions] = useState(false)

  // Initialize plan with empty weeks array
  const [plan, setPlan] = useState<Plan>({
    id: crypto.randomUUID(),
    title: "",
    description: "",
    type: "template",
    isPublic: false,
    category: "",
    targetLevel: "Beginner",
    weeks: [{
      id: "week-1",
      dailyWorkouts: [
        { id: "week-1-monday", day: "Monday", exercises: [] },
        { id: "week-1-tuesday", day: "Tuesday", exercises: [] },
        { id: "week-1-wednesday", day: "Wednesday", exercises: [] },
        { id: "week-1-thursday", day: "Thursday", exercises: [] },
        { id: "week-1-friday", day: "Friday", exercises: [] },
        { id: "week-1-saturday", day: "Saturday", exercises: [] },
        { id: "week-1-sunday", day: "Sunday", exercises: [] }
      ]
    }],
    createdAt: new Date().toISOString()
  })

  // Fetch exercises when component mounts
  useEffect(() => {
    const fetchExercises = async () => {
      try {
        const response = await fetch('/api/exercises?templateOnly=true', {
          credentials: 'include'
        })
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(`Failed to fetch exercises: ${response.status} ${response.statusText} - ${errorData.error || 'Unknown error'}`)
        }
        const data = await response.json()
        setAvailableExercises(data)
      } catch (error) {
        console.error('Error fetching exercises:', error)
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to load exercises. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoadingExercises(false)
      }
    }

    fetchExercises()
  }, [toast])

  // Update plan function to pass to the WeeklyPlanEditor
  const handlePlanUpdate = (updatedPlan: any) => {
    setPlan({
      ...updatedPlan,
      type: updatedPlan.type || plan.type,
      createdAt: plan.createdAt
    });
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!plan.title.trim()) {
      toast({
        title: "Error",
        description: "Please provide a title for your training plan.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      console.log('Submitting training plan:', plan.title)

      // Save plan to database
      const serializedWeeks = plan.weeks.map(week => ({
        ...week,
        dailyWorkouts: week.dailyWorkouts.map(workout => ({
          ...workout,
          exercises: workout.exercises.map(exercise => ({
            ...exercise,
            id: String(exercise.id),
            name: String(exercise.name || ''),
            sets: Number(exercise.sets || 0),
            reps: String(exercise.reps || ''),
            weight: String(exercise.weight || ''),
            restTime: Number(exercise.restTime || 0),
            duration: Number(exercise.duration || 0),
            description: String(exercise.description || ''),
            videoUrl: String(exercise.videoUrl || ''),
            thumbnailUrl: String(exercise.thumbnailUrl || ''),
            type: String(exercise.type || ''),
            muscleGroup: String(exercise.muscleGroup || ''),
            difficulty: String(exercise.difficulty || ''),
            equipment: String(exercise.equipment || ''),
            order: Number(exercise.order || 0),
          }))
        }))
      }));

      console.log('Submitting plan with serialized weeks:', JSON.stringify(serializedWeeks));

      const response = await fetch('/api/training-plan-templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: plan.title,
          description: plan.description,
          difficulty: plan.targetLevel?.toLowerCase() || 'beginner',
          type: 'template',
          weeks: serializedWeeks
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(`Failed to save plan: ${response.status} ${response.statusText} - ${errorData.error || 'Unknown error'}`)
      }

      const result = await response.json()
      console.log('Plan created successfully:', result)

      // Save the created plan ID
      setCreatedPlanId(result.id)

      toast({
        title: "Success",
        description: "Training plan created successfully!",
        action: (
          <div className="flex gap-2 mt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAssignClientDialog(true)}
            >
              <UserPlus className="mr-1 h-3 w-3" />
              Assign to Client
            </Button>
          </div>
        ),
      })

      // Show assignment options instead of redirecting
      setShowAssignOptions(true)
    } catch (error) {
      console.error("Error saving plan:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create training plan. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <>
      <div className="container mx-auto max-w-6xl px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Create New Training Plan</h1>
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/training-plans')}
            className="hover:bg-accent/10"
          >
            Cancel
          </Button>
        </div>

        <div className="space-y-8">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>Plan Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={plan.title}
                  onChange={(e) => setPlan({ ...plan, title: e.target.value })}
                  placeholder="e.g., 12-Week Strength Building Program"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={plan.description}
                  onChange={(e) => setPlan({ ...plan, description: e.target.value })}
                  placeholder="Describe the goals and focus of this training plan"
                  className="resize-none h-24"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    value={plan.category || ""}
                    onChange={(e) => setPlan({ ...plan, category: e.target.value })}
                    placeholder="e.g., Strength, Cardio, etc."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="targetLevel">Target Level</Label>
                  <Select
                    value={plan.targetLevel}
                    onValueChange={(value) => setPlan({
                      ...plan,
                      targetLevel: value as "Beginner" | "Intermediate" | "Advanced"
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Beginner">Beginner</SelectItem>
                      <SelectItem value="Intermediate">Intermediate</SelectItem>
                      <SelectItem value="Advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label>Premium Coaching Plans</Label>
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700">
                    <p className="flex items-start gap-2">
                      <Info className="h-4 w-4 mt-0.5" />
                      <span>
                        Training plans can be used for premium 1:1 coaching by assigning them directly to your clients.
                        Each plan is personalized and included in the client's coaching package.
                      </span>
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="trainerNotes">Trainer Notes</Label>
                <Textarea
                  id="trainerNotes"
                  value={plan.trainerNotes || ""}
                  onChange={(e) => setPlan({ ...plan, trainerNotes: e.target.value })}
                  placeholder="Private notes for you as the trainer"
                  className="resize-none h-24"
                />
              </div>
            </CardContent>
          </Card>

          {/* Weekly Plan Editor */}
          {isLoadingExercises ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <WeeklyPlanEditor
              plan={plan}
              onPlanUpdate={handlePlanUpdate}
              availableExercises={availableExercises}
            />
          )}

          {/* Submit Button or Assignment Options */}
          <div className="flex justify-end pt-4">
            {showAssignOptions && createdPlanId ? (
              <div className="w-full space-y-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Your training plan has been created!</h3>
                      <p className="text-muted-foreground">Would you like to assign this plan to a client?</p>

                      <div className="flex justify-center">
                        <Card className="cursor-pointer hover:bg-muted/50 transition-colors border-2 hover:border-blue-500 max-w-sm" onClick={() => setShowAssignClientDialog(true)}>
                          <CardContent className="p-6 flex flex-col items-center text-center">
                            <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                              <UserPlus className="h-8 w-8 text-blue-500" />
                            </div>
                            <h4 className="font-medium text-lg">Assign to Client</h4>
                            <p className="text-sm text-muted-foreground mt-2">Assign this plan to one of your premium coaching clients</p>
                          </CardContent>
                        </Card>
                      </div>

                      <div className="flex justify-end mt-6">
                        <Button
                          variant="outline"
                          onClick={() => router.replace("/dashboard/training-plans")}
                          className="px-6"
                        >
                          Done
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Button
                type="button"
                onClick={(e) => handleSubmit(e as any)}
                className="bg-primary hover:bg-primary/90"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-r-transparent"></div>
                    Creating Plan...
                  </>
                ) : (
                  'Create Training Plan'
                )}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Client Assignment Dialog */}
      {createdPlanId && (
        <AssignToClientDialog
          open={showAssignClientDialog}
          onOpenChange={setShowAssignClientDialog}
          trainingPlanId={createdPlanId}
          trainingPlanTitle={plan.title}
          onSuccess={() => {
            toast({
              title: "Success",
              description: "Training plan assigned to client successfully",
            })
          }}
        />
      )}
    </>
  );
} 