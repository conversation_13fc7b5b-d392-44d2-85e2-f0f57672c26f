'use client';

import { useEffect, useState } from 'react';
import { InlineWidget, useCalendlyEventListener } from 'react-calendly';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';

interface CalendlyEmbedProps {
  url: string;
  prefill?: {
    email?: string;
    name?: string;
    customAnswers?: {
      [key: string]: string;
    };
  };
  utm?: {
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;
    utmContent?: string;
    utmTerm?: string;
  };
  onEventScheduled?: (event: any) => void;
  title?: string;
  description?: string;
}

export function CalendlyEmbed({
  url,
  prefill,
  utm,
  onEventScheduled,
  title = 'Schedule a Session',
  description = 'Select a time that works for you',
}: CalendlyEmbedProps) {
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useCalendlyEventListener({
    onEventScheduled: (e) => {
      if (onEventScheduled) {
        onEventScheduled(e);
      } else {
        toast({
          title: 'Session scheduled',
          description: 'Your session has been scheduled successfully.',
        });
      }
    },
    onEventTypeViewed: () => {
      setIsLoading(false);
    },
  });

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        {isLoading && (
          <div className="p-6">
            <Skeleton className="h-[600px] w-full" />
          </div>
        )}
        <div className={isLoading ? 'hidden' : 'block'}>
          <InlineWidget
            url={url}
            prefill={prefill}
            utm={utm}
            styles={{
              height: '650px',
              width: '100%',
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}
