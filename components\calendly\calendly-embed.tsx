'use client';

import { useEffect, useState } from 'react';
import { InlineWidget, useCalendlyEventListener } from 'react-calendly';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';

interface CalendlyEmbedProps {
  url: string;
  prefill?: {
    email?: string;
    name?: string;
    customAnswers?: {
      [key: string]: string;
    };
  };
  utm?: {
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;
    utmContent?: string;
    utmTerm?: string;
  };
  onEventScheduled?: (event: any) => void;
  title?: string;
  description?: string;
}

export function CalendlyEmbed({
  url,
  prefill,
  utm,
  onEventScheduled,
  title = 'Schedule a Session',
  description = 'Select a time that works for you',
}: CalendlyEmbedProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const { toast } = useToast();



  // Validate URL format
  const isValidCalendlyUrl = url && url.includes('calendly.com');
  if (!isValidCalendlyUrl) {
    console.error('Invalid Calendly URL:', url);
  }

  // Set a shorter timeout for loading and force show widget
  useEffect(() => {
    const timer = setTimeout(() => {
      if (isLoading) {
        console.warn('Calendly widget taking too long to load, forcing display');
        setLoadingTimeout(true);
        // Force show the widget after 5 seconds even if events haven't fired
        setTimeout(() => {
          setIsLoading(false);
        }, 1000);
      }
    }, 5000); // 5 seconds timeout (reduced from 10)

    return () => clearTimeout(timer);
  }, [isLoading]);

  useCalendlyEventListener({
    onEventScheduled: (e) => {
      if (onEventScheduled) {
        onEventScheduled(e);
      } else {
        toast({
          title: 'Session scheduled',
          description: 'Your session has been scheduled successfully.',
        });
      }
    },
    onEventTypeViewed: () => {
      setIsLoading(false);
    },
    onProfilePageViewed: () => {
      setIsLoading(false);
    },
    onDateAndTimeSelected: () => {
      // Event fired when user selects date/time
    },
  });

  if (!isValidCalendlyUrl) {
    return (
      <Card className="w-full overflow-hidden">
        <CardHeader>
          <CardTitle>Calendly Integration Error</CardTitle>
          <CardDescription>There's an issue with the Calendly URL configuration</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <p className="text-muted-foreground">
              The Calendly URL appears to be invalid: <code className="bg-muted px-2 py-1 rounded">{url}</code>
            </p>
            <p className="text-sm text-muted-foreground">
              Please check your Calendly URL in the Connections tab of your profile.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        {isLoading && (
          <div className="p-6">
            <Skeleton className="h-[600px] w-full" />
            <div className="text-center mt-4 space-y-4">
              <p className="text-sm text-muted-foreground">
                {loadingTimeout ? 'Calendly widget is taking longer than expected to load...' : 'Loading Calendly widget...'}
              </p>

              {/* Quick access button - always show after 2 seconds */}
              <div className="space-y-2">
                <button
                  onClick={() => {
                    const calendlyWindow = window.open(url, '_blank', 'width=800,height=700,scrollbars=yes,resizable=yes');
                    if (calendlyWindow) {
                      calendlyWindow.focus();
                    }
                  }}
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                >
                  Open Calendly in New Window
                </button>
                <p className="text-xs text-muted-foreground">
                  Click above for faster access while the embed loads
                </p>
              </div>

              {loadingTimeout && (
                <div className="mt-4 space-y-2 p-4 bg-muted/50 rounded-lg">
                  <p className="text-xs text-muted-foreground font-medium">
                    Embed loading slowly? This can happen due to:
                  </p>
                  <ul className="text-xs text-muted-foreground space-y-1 text-left">
                    <li>• Slow internet connection</li>
                    <li>• Calendly server response time</li>
                    <li>• Browser security settings</li>
                  </ul>
                  <p className="text-xs text-muted-foreground">
                    The "Open in New Window" button above provides immediate access.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
        <div className={isLoading ? 'hidden' : 'block'}>
          <InlineWidget
            url={url}
            prefill={prefill}
            utm={utm}
            styles={{
              height: '650px',
              width: '100%',
            }}
            pageSettings={{
              hideEventTypeDetails: false,
              hideLandingPageDetails: false,
              primaryColor: "00a2ff",
              textColor: "4d5055"
            }}
            loadingSpinner={true}
          />
        </div>
      </CardContent>
    </Card>
  );
}
