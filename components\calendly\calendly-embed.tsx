'use client';

import { useEffect, useState } from 'react';
import { InlineWidget, useCalendlyEventListener } from 'react-calendly';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';

interface CalendlyEmbedProps {
  url: string;
  prefill?: {
    email?: string;
    name?: string;
    customAnswers?: {
      [key: string]: string;
    };
  };
  utm?: {
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;
    utmContent?: string;
    utmTerm?: string;
  };
  onEventScheduled?: (event: any) => void;
  title?: string;
  description?: string;
}

export function CalendlyEmbed({
  url,
  prefill,
  utm,
  onEventScheduled,
  title = 'Schedule a Session',
  description = 'Select a time that works for you',
}: CalendlyEmbedProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const { toast } = useToast();

  // Debug logs
  console.log('CalendlyEmbed URL:', url);
  console.log('CalendlyEmbed prefill:', prefill);

  // Validate URL format
  const isValidCalendlyUrl = url && url.includes('calendly.com');
  if (!isValidCalendlyUrl) {
    console.error('Invalid Calendly URL:', url);
  }

  // Set a timeout for loading
  useEffect(() => {
    const timer = setTimeout(() => {
      if (isLoading) {
        console.warn('Calendly widget taking too long to load');
        setLoadingTimeout(true);
      }
    }, 10000); // 10 seconds timeout

    return () => clearTimeout(timer);
  }, [isLoading]);

  useCalendlyEventListener({
    onEventScheduled: (e) => {
      console.log('Calendly event scheduled:', e);
      if (onEventScheduled) {
        onEventScheduled(e);
      } else {
        toast({
          title: 'Session scheduled',
          description: 'Your session has been scheduled successfully.',
        });
      }
    },
    onEventTypeViewed: () => {
      console.log('Calendly event type viewed');
      setIsLoading(false);
    },
    onProfilePageViewed: () => {
      console.log('Calendly profile page viewed');
      setIsLoading(false);
    },
    onDateAndTimeSelected: () => {
      console.log('Calendly date and time selected');
    },
  });

  if (!isValidCalendlyUrl) {
    return (
      <Card className="w-full overflow-hidden">
        <CardHeader>
          <CardTitle>Calendly Integration Error</CardTitle>
          <CardDescription>There's an issue with the Calendly URL configuration</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <p className="text-muted-foreground">
              The Calendly URL appears to be invalid: <code className="bg-muted px-2 py-1 rounded">{url}</code>
            </p>
            <p className="text-sm text-muted-foreground">
              Please check your Calendly URL in the Connections tab of your profile.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        {isLoading && (
          <div className="p-6">
            <Skeleton className="h-[600px] w-full" />
            <div className="text-center mt-4">
              <p className="text-sm text-muted-foreground">
                {loadingTimeout ? 'Calendly widget is taking longer than expected to load...' : 'Loading Calendly widget...'}
              </p>
              <p className="text-xs text-muted-foreground mt-1">URL: {url}</p>
              {loadingTimeout && (
                <div className="mt-4 space-y-2">
                  <p className="text-xs text-muted-foreground">
                    If this continues, please check:
                  </p>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li>• Your Calendly URL is correct</li>
                    <li>• Your Calendly account is active</li>
                    <li>• You have available event types</li>
                  </ul>
                  <button
                    onClick={() => window.open(url, '_blank')}
                    className="mt-2 px-3 py-1 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90"
                  >
                    Test URL in New Tab
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
        <div className={isLoading ? 'hidden' : 'block'}>
          <InlineWidget
            url={url}
            prefill={prefill}
            utm={utm}
            styles={{
              height: '650px',
              width: '100%',
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}
