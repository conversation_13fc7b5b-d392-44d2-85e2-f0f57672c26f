/**
 * Error Tracking Service
 * 
 * This module provides error tracking and reporting functionality.
 * In a production environment, this would typically integrate with
 * external services like Sentry, LogRocket, or similar.
 */

type ErrorSeverity = 'info' | 'warning' | 'error' | 'critical';

interface ErrorContext {
  user?: {
    id?: string;
    email?: string;
    role?: string;
  };
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

interface ErrorEvent {
  message: string;
  source: string;
  severity: ErrorSeverity;
  timestamp: Date;
  context?: ErrorContext;
  stack?: string;
  fingerprint?: string;
}

/**
 * Configuration for the error tracking service
 */
interface ErrorTrackingConfig {
  enabled: boolean;
  environment: string;
  dsn?: string;
  sampleRate: number; // 0.0 to 1.0
  debug: boolean;
  consoleOutput: boolean;
  apiEndpoint?: string;
  maxBreadcrumbs: number;
}

// Default configuration
const defaultConfig: ErrorTrackingConfig = {
  enabled: process.env.NODE_ENV === 'production',
  environment: process.env.NODE_ENV || 'development',
  sampleRate: 1.0, // Capture all errors in dev, would be lower in prod
  debug: process.env.NODE_ENV !== 'production',
  consoleOutput: process.env.NODE_ENV !== 'production',
  maxBreadcrumbs: 100,
};

/**
 * Error Tracking Service
 */
class ErrorTrackingService {
  private config: ErrorTrackingConfig;
  private breadcrumbs: string[] = [];
  private initialized = false;
  private context: ErrorContext = {};
  
  constructor(config: Partial<ErrorTrackingConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }
  
  /**
   * Initialize the error tracking service
   */
  init(): void {
    if (this.initialized) {
      return;
    }
    
    // Set up global error handlers
    if (typeof window !== 'undefined') {
      // Browser environment
      window.addEventListener('error', (event) => {
        this.captureException(event.error);
      });
      
      window.addEventListener('unhandledrejection', (event) => {
        this.captureException(event.reason);
      });
      
      const originalConsoleError = console.error;
      console.error = (...args) => {
        // Capture console errors 
        this.captureMessage(args.map(arg => String(arg)).join(' '), 'error', 'console');
        originalConsoleError.apply(console, args);
      };
    } else {
      // Node.js environment
      process.on('uncaughtException', (error) => {
        this.captureException(error, 'critical', 'node');
      });
      
      process.on('unhandledRejection', (reason) => {
        this.captureException(reason, 'error', 'node');
      });
    }
    
    this.initialized = true;
    this.log('ErrorTrackingService initialized');
  }
  
  /**
   * Set user context for error tracking
   */
  setUser(user: ErrorContext['user']): void {
    this.context.user = user;
    this.log(`User context set: ${user?.id || 'anonymous'}`);
  }
  
  /**
   * Set global tags for error tracking
   */
  setTags(tags: Record<string, string>): void {
    this.context.tags = { ...this.context.tags, ...tags };
  }
  
  /**
   * Set additional metadata for error events
   */
  setContext(metadata: Record<string, any>): void {
    this.context.metadata = { ...this.context.metadata, ...metadata };
  }
  
  /**
   * Add a breadcrumb for error context
   */
  addBreadcrumb(message: string): void {
    if (this.breadcrumbs.length >= this.config.maxBreadcrumbs) {
      this.breadcrumbs.shift();
    }
    
    this.breadcrumbs.push(`[${new Date().toISOString()}] ${message}`);
  }
  
  /**
   * Capture an error message
   */
  captureMessage(
    message: string,
    severity: ErrorSeverity = 'info',
    source: string = 'app'
  ): void {
    if (!this.config.enabled) {
      this.log(`[Disabled] Message: ${message}`);
      return;
    }
    
    // Apply sampling rate
    if (Math.random() > this.config.sampleRate) {
      return;
    }
    
    const event: ErrorEvent = {
      message,
      severity,
      source,
      timestamp: new Date(),
      context: { ...this.context },
    };
    
    this.processEvent(event);
  }
  
  /**
   * Capture an exception
   */
  captureException(
    error: unknown, 
    severity: ErrorSeverity = 'error',
    source: string = 'app'
  ): void {
    if (!this.config.enabled) {
      this.log(`[Disabled] Exception: ${error}`);
      return;
    }
    
    // Apply sampling rate
    if (Math.random() > this.config.sampleRate) {
      return;
    }
    
    let errorMessage = 'Unknown error';
    let errorStack = undefined;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      errorStack = error.stack;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else {
      errorMessage = String(error);
    }
    
    const event: ErrorEvent = {
      message: errorMessage,
      stack: errorStack,
      severity,
      source,
      timestamp: new Date(),
      context: { 
        ...this.context,
        metadata: { 
          ...this.context.metadata,
          breadcrumbs: [...this.breadcrumbs]
        }
      },
    };
    
    this.processEvent(event);
  }
  
  /**
   * Process an error event
   */
  private processEvent(event: ErrorEvent): void {
    // Log to console in development/debug mode
    if (this.config.consoleOutput) {
      const severityColor = this.getSeverityColor(event.severity);
      console.group(`%c${event.severity.toUpperCase()}: ${event.message}`, `color: ${severityColor}`);
      console.log('Timestamp:', event.timestamp);
      console.log('Source:', event.source);
      if (event.stack) {
        console.log('Stack:', event.stack);
      }
      if (event.context) {
        console.log('Context:', event.context);
      }
      console.groupEnd();
    }
    
    // In production, send to error tracking service
    if (this.config.environment === 'production' && this.config.apiEndpoint) {
      this.sendToErrorTrackingService(event);
    }
  }
  
  /**
   * Send event to error tracking service (e.g., Sentry)
   */
  private sendToErrorTrackingService(event: ErrorEvent): void {
    // This would be an implementation for a specific service
    // For now, we just log that we would send the event
    this.log(`Would send to error tracking service: ${event.message}`);
    
    if (this.config.apiEndpoint) {
      fetch(this.config.apiEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(event),
      }).catch(err => {
        this.log(`Failed to send error to tracking service: ${err}`);
      });
    }
  }
  
  /**
   * Internal logging function for debugging
   */
  private log(message: string): void {
    if (this.config.debug) {
      console.log(`[ErrorTracking] ${message}`);
    }
  }
  
  /**
   * Get color code for severity level
   */
  private getSeverityColor(severity: ErrorSeverity): string {
    switch (severity) {
      case 'info': return '#2196F3';
      case 'warning': return '#FF9800';
      case 'error': return '#F44336';
      case 'critical': return '#B71C1C';
      default: return '#757575';
    }
  }
}

// Create and export singleton instance
export const errorTracking = new ErrorTrackingService();

// Convenience exports
export function captureException(error: any, severity?: ErrorSeverity, source?: string): void {
  errorTracking.captureException(error, severity, source);
}

export function captureMessage(message: string, severity?: ErrorSeverity, source?: string): void {
  errorTracking.captureMessage(message, severity, source);
}

export function addBreadcrumb(message: string): void {
  errorTracking.addBreadcrumb(message);
}

export function setUser(user: ErrorContext['user']): void {
  errorTracking.setUser(user);
}

export function withErrorTracking<T extends (...args: any[]) => any>(fn: T): T {
  return ((...args: Parameters<T>): ReturnType<T> => {
    try {
      const result = fn(...args);
      
      // Handle promises
      if (result instanceof Promise) {
        return result.catch(error => {
          errorTracking.captureException(error);
          throw error;
        }) as ReturnType<T>;
      }
      
      return result;
    } catch (error) {
      errorTracking.captureException(error);
      throw error;
    }
  }) as T;
} 