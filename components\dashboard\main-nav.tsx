"use client"

import {
  Home as HomeIcon,
  BarChart3 as BarChart3Icon,
  Users as UsersIcon,
  <PERSON><PERSON><PERSON> as <PERSON>mbbellIcon,
  MessageSquare as MessageSquareIcon,
  Settings as SettingsIcon,
  ClipboardList as ClipboardListIcon,
  <PERSON>rkles as SparklesIcon,
  LucideIcon
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { FeatureFlags, defaultFeatureFlags } from "@/lib/feature-flags"
import { cn } from "@/lib/utils"

// Define the navigation item type
interface NavItem {
  name: string
  href: string
  icon: LucideIcon
  notification?: number
  requiresPremium?: boolean
  isUpgrade?: boolean
  requiresFeature?: keyof FeatureFlags
}

// Function to filter navigation items based on feature flags
export function filterNavByFeatureFlags(
  navItems: NavItem[],
  featureFlags: FeatureFlags = defaultFeatureFlags
): NavItem[] {
  return navItems.filter(item => {
    // If the item doesn't require a specific feature, always include it
    if (!item.requiresFeature) {
      return true;
    }

    // Otherwise, check if the required feature is enabled
    return featureFlags[item.requiresFeature];
  });
}

// Navigation items for different user roles
export const navigation: Record<string, NavItem[]> = {
  // Admin links
  admin: [
    {
      name: "Home",
      href: "/dashboard",
      icon: HomeIcon,
    },
    {
      name: "Monitoring",
      href: "/dashboard/monitoring",
      icon: BarChart3Icon,
    },
    {
      name: "User Management",
      href: "/dashboard/user-management",
      icon: UsersIcon,
    },
    {
      name: "Trainers Overview",
      href: "/dashboard/trainers",
      icon: DumbbellIcon,
    },

    {
      name: "Service Management",
      href: "/dashboard/service-management",
      icon: SettingsIcon,
    },
  ],

  // Trainer links - premium coaching focused
  trainer: [
    {
      name: "Home",
      href: "/dashboard",
      icon: HomeIcon,
    },
    {
      name: "Premium Coaching",
      href: "/dashboard/coaching",
      icon: SparklesIcon,
      requiresFeature: "enablePremiumCoaching"
    },
    {
      name: "Client Chats",
      href: "/dashboard/chats",
      icon: MessageSquareIcon,
      requiresFeature: "enablePremiumCoaching"
    },
    {
      name: "Training Plan Builder",
      href: "/dashboard/training-plans",
      icon: DumbbellIcon,
      requiresFeature: "enablePremiumCoaching"
    },
    {
      name: "Business Analytics",
      href: "/dashboard/analytics",
      icon: BarChart3Icon,
    },
  ],

  // Client links - premium 1:1 coaching features (all clients are premium)
  client: [
    {
      name: "Home",
      href: "/dashboard/dashboard",
      icon: HomeIcon,
    },
    {
      name: "My Workouts",
      href: "/dashboard/workouts/current",
      icon: ClipboardListIcon,
    },
    {
      name: "Premium Dashboard",
      href: "/dashboard/premium",
      icon: SparklesIcon,
    },
    {
      name: "Coach Chat",
      href: "/dashboard/chats",
      icon: MessageSquareIcon,
    },
    {
      name: "Analytics",
      href: "/dashboard/analytics",
      icon: BarChart3Icon,
    },
    {
      name: "Find Trainers",
      href: "/dashboard/trainers",
      icon: DumbbellIcon,
    },
  ],
}

interface MainNavProps {
  userRole: string | null
  featureFlags?: any
}

export function MainNav({
  userRole = "client",
  featureFlags = null
}: MainNavProps) {
  const pathname = usePathname()
  const [effectiveRole, setEffectiveRole] = useState<string | null>(userRole)
  const [effectiveFlags, setEffectiveFlags] = useState(featureFlags || defaultFeatureFlags)
  const [isClient, setIsClient] = useState(false)

  // Check for role override in client component
  useEffect(() => {
    setIsClient(true)

    // Check for overrides in development
    if (process.env.NODE_ENV === 'development') {
      const cookies = document.cookie.split(';')
      const roleCookie = cookies.find(cookie => cookie.trim().startsWith('dev_override_role='))
      const featureCookie = cookies.find(cookie => cookie.trim().startsWith('dev_feature_flags='))
      const actualRoleCookie = cookies.find(cookie => cookie.trim().startsWith('next-auth.session-token='))

      let overrideRole = userRole
      let overrideFlags = featureFlags || defaultFeatureFlags

      // Only allow role override if user is actually an admin
      let isAdmin = false
      if (actualRoleCookie) {
        try {
          const token = actualRoleCookie.split('=')[1]
          const payload = JSON.parse(atob(token.split('.')[1]))
          isAdmin = payload.role === 'admin'
        } catch (e) {
          console.error('Error parsing session token:', e)
        }
      }

      // Check for role override only if user is admin
      if (roleCookie && isAdmin) {
        const cookieRole = roleCookie.split('=')[1]
        if (['admin', 'trainer', 'client'].includes(cookieRole)) {
          overrideRole = cookieRole
        }
      }

      // Check for feature flags override only if user is admin
      if (featureCookie && isAdmin) {
        try {
          const cookieFlags = JSON.parse(decodeURIComponent(featureCookie.split('=')[1]))
          overrideFlags = {
            ...overrideFlags,
            ...cookieFlags
          }
        } catch (e) {
          console.error('Failed to parse feature flags from cookie:', e)
        }
      }

      // Apply overrides
      setEffectiveRole(overrideRole)
      setEffectiveFlags(overrideFlags)
    } else {
      setEffectiveRole(userRole)
      setEffectiveFlags(featureFlags || defaultFeatureFlags)
    }
  }, [userRole, featureFlags])

  // Determine which navigation to use
  const determineNavItems = () => {
    let role = effectiveRole || "client"

    // Get navigation for role
    let navItems = navigation[role] || navigation.client

    // Filter nav items based on feature flags
    navItems = filterNavByFeatureFlags(navItems, effectiveFlags)

    return navItems
  }

  const navItems = determineNavItems()

  if (!isClient) {
    // SSR fallback - shouldn't show a flash on client
    return null
  }

  return (
    <nav className="grid items-start gap-2">
      <div className="mb-2 px-2">
        <div className="text-xs font-medium text-accent/80 uppercase tracking-wider mb-2">Menu</div>
      </div>
      {navItems.map((item, index) => {
        const Icon = item.icon
        return (
          <Link
            key={index}
            href={item.href}
            className={cn(
              "flex items-center gap-x-2 rounded-lg px-3 py-2 text-sm font-medium transition-all relative group",
              pathname === item.href
                ? "bg-primary/10 text-primary dark:bg-primary/20"
                : "text-muted-foreground hover:text-foreground hover:bg-accent/5",
              item.isUpgrade && "bg-gradient-to-r from-primary/5 to-accent/5 border border-primary/20"
            )}
          >
            <div className={cn(
              "flex items-center justify-center w-6 h-6 rounded-md transition-colors",
              pathname === item.href
                ? "text-primary bg-primary/10"
                : "text-muted-foreground group-hover:text-primary bg-background group-hover:bg-primary/5"
            )}>
              <Icon className="h-4 w-4" strokeWidth={1.8} />
            </div>
            <span>{item.name}</span>
            {item.notification && (
              <Badge
                variant="accent"
                className="ml-auto h-5 min-w-5 rounded-full px-1.5 text-[10px] flex items-center justify-center"
              >
                {item.notification}
              </Badge>
            )}
            {item.isUpgrade && (
              <Badge variant="premium" className="ml-auto text-[10px] bg-gradient-to-r from-primary/80 to-accent/80 text-white">
                Upgrade
              </Badge>
            )}
          </Link>
        )
      })}
    </nav>
  )
}