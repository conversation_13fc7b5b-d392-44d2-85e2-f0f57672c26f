import { POST } from '@/app/api/diet-plans/[id]/duplicate/route'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { DietPlanService } from '@/lib/services/diet-plan-service'

// Mock next-auth
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    dietPlan: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    meal: {
      create: jest.fn(),
    },
    food: {
      create: jest.fn(),
    },
  },
}))

// Mock DietPlanService
jest.mock('@/lib/services/diet-plan-service', () => ({
  DietPlanService: {
    findById: jest.fn(),
    duplicate: jest.fn(),
  },
}))

describe('Diet Plan Duplication API', () => {
  const mockUser = {
    id: '1',
    role: 'admin',
  }

  beforeEach(() => {
    ;(getServerSession as jest.Mock).mockResolvedValue({ user: mockUser })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('returns 401 when user is not authenticated', async () => {
    ;(getServerSession as jest.Mock).mockResolvedValue(null)

    const request = new Request('http://localhost:3000/api/diet-plans/1/duplicate', {
      method: 'POST',
    })
    const response = await POST(request, { params: { id: '1' } })
    expect(response?.status).toBe(401)
  })

  it('returns 404 when plan is not found', async () => {
    ;(DietPlanService.findById as jest.Mock).mockResolvedValue(null)

    const request = new Request('http://localhost:3000/api/diet-plans/1/duplicate', {
      method: 'POST',
    })
    const response = await POST(request, { params: { id: '1' } })
    expect(response?.status).toBe(404)
  })

  it('successfully duplicates a diet plan', async () => {
    const mockPlan = {
      id: '1',
      title: 'Original Plan',
      description: 'Original Description',
      athleteId: '1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      meals: [
        {
          name: 'Breakfast',
          description: 'Morning meal',
          calories: 500,
          protein: 30,
          carbs: 60,
          fats: 20,
        },
      ],
    }

    const duplicatedPlan = {
      id: '2',
      title: 'Original Plan (Copy)',
      description: 'Original Description',
      athleteId: '1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      meals: [
        {
          name: 'Breakfast',
          description: 'Morning meal',
          calories: 500,
          protein: 30,
          carbs: 60,
          fats: 20,
        },
      ],
    }

    ;(DietPlanService.findById as jest.Mock).mockResolvedValue(mockPlan)
    ;(DietPlanService.duplicate as jest.Mock).mockResolvedValue(duplicatedPlan)

    const request = new Request('http://localhost:3000/api/diet-plans/1/duplicate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    })
    const response = await POST(request, { params: { id: '1' } })

    expect(response?.status).toBe(200)
    const responseData = await response?.json()
    expect(responseData).toEqual(duplicatedPlan)
  })

  it('handles database errors', async () => {
    const originalPlan = {
      id: '1',
      title: 'Original Plan',
      description: 'Original Description',
      athleteId: '1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      meals: [],
    }

    ;(DietPlanService.findById as jest.Mock).mockResolvedValue(originalPlan)
    ;(DietPlanService.duplicate as jest.Mock).mockRejectedValue(new Error('Database error'))

    const request = new Request('http://localhost:3000/api/diet-plans/1/duplicate', {
      method: 'POST',
    })
    const response = await POST(request, { params: { id: '1' } })

    expect(response?.status).toBe(500)
  })
})