import { prisma } from '../lib/prisma'

async function main() {
  console.log('Starting Prisma debug script...')
  
  try {
    // List all products to check the schema
    const products = await prisma.product.findMany({
      take: 1
    })
    
    console.log('Found products:', products.length)
    if (products.length > 0) {
      console.log('Product schema fields:', Object.keys(products[0]).join(', '))
    }
    
    // Create a test product
    console.log('Creating a test product...')
    const product = await prisma.product.create({
      data: {
        title: 'Test Product', // Try title first
        description: 'Test description',
        price: 9.99,
        athleteId: 'test-user',
        type: 'digital'
      }
    })
    
    console.log('Created product:', product)
  } catch (error) {
    console.error('Error:', error)
    
    // If the first attempt failed, try with 'name' instead
    if (error.message.includes('title')) {
      console.log('Trying with name instead of title...')
      try {
        const product = await prisma.product.create({
          data: {
            name: 'Test Product',
            description: 'Test description',
            price: 9.99,
            athleteId: 'test-user',
            type: 'digital'
          }
        })
        console.log('Created product with name field:', product)
      } catch (retryError) {
        console.error('Retry error:', retryError)
      }
    }
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch(e => {
    console.error(e)
    process.exit(1)
  }) 