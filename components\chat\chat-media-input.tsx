'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Smile, Image as ImageIcon, Paperclip, X, Send } from 'lucide-react';
import Image from 'next/image';

// Define the types for file uploads
interface FileWithPreview extends File {
  preview: string;
  type: string;
}

interface ChatMediaInputProps {
  onSendMessage: (content: string, attachments?: FileWithPreview[]) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

// Common emojis for quick access
const COMMON_EMOJIS = [
  '😀', '😂', '😊', '😍', '🥰', '😎', '🤔', '😢', '😭', '😡',
  '👍', '👎', '👏', '🙌', '🤝', '👋', '✌️', '🤞', '👌', '🤙',
  '❤️', '🔥', '👀', '🎉', '🎂', '🎁', '🏆', '💯', '⭐', '✨',
  '🌈', '🌞', '🌙', '⛅', '🌧️', '⚡', '❄️', '🌸', '🌹', '🍀',
  '🍕', '🍔', '🍟', '🍦', '🍩', '🍺', '🍷', '☕', '🥂', '🍽️'
];

export function ChatMediaInput({
  onSendMessage,
  disabled = false,
  placeholder = 'Type your message...',
  className = '',
}: ChatMediaInputProps) {
  const [message, setMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  // Handle emoji selection
  const onEmojiClick = (emoji: string) => {
    setMessage((prev) => prev + emoji);
    setShowEmojiPicker(false);
    inputRef.current?.focus();
  };

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const newFiles = Array.from(selectedFiles).map((file) =>
      Object.assign(file, {
        preview: URL.createObjectURL(file),
      })
    ) as FileWithPreview[];

    setFiles((prev) => [...prev, ...newFiles]);

    // Reset the input value so the same file can be selected again
    event.target.value = '';
  };

  // Remove a file from the preview
  const removeFile = (index: number) => {
    setFiles((prev) => {
      const newFiles = [...prev];
      URL.revokeObjectURL(newFiles[index].preview);
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  // Handle message submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if ((!message.trim() && files.length === 0) || disabled) return;

    onSendMessage(message, files);
    setMessage('');

    // Clean up file previews
    files.forEach((file) => URL.revokeObjectURL(file.preview));
    setFiles([]);

    inputRef.current?.focus();
  };

  // Handle key press (Enter to send)
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className={`chat-media-input relative ${className}`}>
      {/* Hidden file inputs */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
        multiple
      />
      <input
        type="file"
        ref={imageInputRef}
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
        multiple
      />

      {/* File Previews */}
      {files.length > 0 && (
        <div className="file-previews p-2 flex flex-wrap gap-2 border-t border-border">
          {files.map((file, index) => (
            <div key={index} className="relative group">
              {file.type.startsWith('image/') ? (
                <div className="relative h-16 w-16 rounded overflow-hidden border border-border">
                  <Image
                    src={file.preview}
                    alt={file.name}
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center h-16 w-16 bg-muted rounded border border-border">
                  <Paperclip className="h-6 w-6 text-muted-foreground" />
                </div>
              )}
              <button
                type="button"
                onClick={() => removeFile(index)}
                className="absolute -top-1 -right-1 bg-destructive text-destructive-foreground rounded-full p-0.5 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex w-full items-center">
        <div className="chat-input-container">
          <div className="chat-input-actions">
            {/* Simple Emoji Picker */}
            <Popover open={showEmojiPicker} onOpenChange={setShowEmojiPicker}>
              <PopoverTrigger asChild>
                <Button type="button" variant="ghost" size="icon" className="h-8 w-8 rounded-full">
                  <Smile className="h-5 w-5 text-muted-foreground" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-64 p-2" align="start">
                <div className="grid grid-cols-10 gap-1">
                  {COMMON_EMOJIS.map((emoji, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => onEmojiClick(emoji)}
                      className="w-6 h-6 flex items-center justify-center hover:bg-muted rounded text-lg"
                    >
                      {emoji}
                    </button>
                  ))}
                </div>
              </PopoverContent>
            </Popover>

            {/* Image Upload Button */}
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full"
              onClick={() => imageInputRef.current?.click()}
            >
              <ImageIcon className="h-5 w-5 text-muted-foreground" />
            </Button>

            {/* File Upload Button */}
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full"
              onClick={() => fileInputRef.current?.click()}
            >
              <Paperclip className="h-5 w-5 text-muted-foreground" />
            </Button>
          </div>

          {/* Text Input */}
          <Input
            placeholder={placeholder}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            disabled={disabled}
            ref={inputRef}
            className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 px-0"
            onKeyDown={handleKeyDown}
          />
        </div>

        {/* Send Button */}
        <Button
          type="submit"
          disabled={disabled || (!message.trim() && files.length === 0)}
          size="icon"
          className="send-button bg-primary hover:bg-primary/90"
        >
          <Send className="h-4 w-4" />
          <span className="sr-only">Send</span>
        </Button>
      </form>
    </div>
  );
}
