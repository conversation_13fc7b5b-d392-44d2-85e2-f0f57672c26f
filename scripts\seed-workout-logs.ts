import { db } from "../lib/db"

async function main() {
  try {
    // Find client user
    const clientUser = await db.user.findFirst({
      where: {
        role: "client",
      },
    })

    if (!clientUser) {
      console.log("Client user not found. Please create a client user first.")
      process.exit(1)
    }

    // Get training plans and exercises
    const trainingPlan = await db.trainingPlan.findFirst({
      include: {
        workouts: {
          include: {
            exercises: true,
          },
        },
      },
    })

    if (!trainingPlan) {
      console.log("No training plans found. Please create training plans first.")
      process.exit(1)
    }

    // Generate 10 days of workout logs
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 10)

    // Clear existing workout logs
    await db.exerciseLog.deleteMany()
    await db.workoutLog.deleteMany()

    // Create workout logs
    for (let i = 0; i < 10; i++) {
      const date = new Date(startDate)
      date.setDate(date.getDate() + i)

      // Alternate between workouts
      const workout = trainingPlan.workouts[i % trainingPlan.workouts.length]

      const workoutLog = await db.workoutLog.create({
        data: {
          clientId: clientUser.id,
          workoutId: workout.id,
          date,
          duration: 45 + Math.floor(Math.random() * 30), // 45-75 minutes
          notes: `Completed ${workout.title}. Feeling ${
            Math.random() > 0.5 ? "great" : "tired but accomplished"
          }`,
        },
      })

      // Log exercises
      for (const exercise of workout.exercises) {
        await db.exerciseLog.create({
          data: {
            workoutLogId: workoutLog.id,
            exerciseId: exercise.id,
            sets: exercise.sets,
            reps: exercise.reps,
            weight: 50 + Math.floor(Math.random() * 50), // Random weight between 50-100 lbs
            notes: Math.random() > 0.7 ? "Increased weight from last session" : undefined,
          },
        })
      }
    }

    console.log("Sample workout logs created successfully!")
  } catch (error) {
    console.error("Error seeding workout logs:", error)
    process.exit(1)
  } finally {
    await db.$disconnect()
  }
}

main() 