import { prisma } from "@/lib/prisma"
import { User, Prisma } from "@prisma/client"

export interface IUserRepository {
  findById(id: string): Promise<User | null>
  findByEmail(email: string): Promise<User | null>
  create(data: Prisma.UserCreateInput): Promise<User>
  update(id: string, data: Partial<User>): Promise<User>
  delete(id: string): Promise<User>
  findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.UserWhereInput
    orderBy?: Prisma.UserOrderByWithRelationInput
  }): Promise<User[]>
  count(where?: Prisma.UserWhereInput): Promise<number>
  searchByName(query: string, role?: string): Promise<User[]>
}

export class UserRepository implements IUserRepository {
  async findById(id: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { id }
    })
  }

  async findByEmail(email: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { email }
    })
  }

  async create(data: Prisma.UserCreateInput): Promise<User> {
    return prisma.user.create({
      data
    })
  }

  async update(id: string, data: Partial<User>): Promise<User> {
    return prisma.user.update({
      where: { id },
      data
    })
  }

  async delete(id: string): Promise<User> {
    return prisma.user.delete({
      where: { id }
    })
  }

  async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.UserWhereInput
    orderBy?: Prisma.UserOrderByWithRelationInput
  }): Promise<User[]> {
    return prisma.user.findMany(params)
  }

  async count(where?: Prisma.UserWhereInput): Promise<number> {
    return prisma.user.count({ where })
  }

  async searchByName(query: string, role?: string): Promise<User[]> {
    const whereClause: Prisma.UserWhereInput = {
      name: {
        contains: query,
        mode: 'insensitive'
      }
    }

    if (role) {
      whereClause.role = role
    }

    return prisma.user.findMany({
      where: whereClause,
      take: 10
    })
  }
}
