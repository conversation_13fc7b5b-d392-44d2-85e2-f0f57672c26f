import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await request.json()
    const { title, description, youtubeUrl, thumbnailUrl, duration, workoutId } = body

    if (!title || !youtubeUrl) {
      return new NextResponse("Missing required fields", { status: 400 })
    }

    // Create video
    const video = await prisma.video.create({
      data: {
        title,
        description,
        youtubeUrl,
        thumbnailUrl,
        duration,
        athleteId: session.user.id,
      },
    })

    // If workoutId is provided, associate the video with the workout
    if (workoutId) {
      await prisma.workoutVideo.create({
        data: {
          videoId: video.id,
          workoutId,
        },
      })
    }

    return NextResponse.json(video)
  } catch (error) {
    console.error("[VIDEO_CREATE_ERROR]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const workoutId = searchParams.get("workoutId")

    if (!workoutId) {
      return new NextResponse("Workout ID is required", { status: 400 })
    }

    const videos = await prisma.workoutVideo.findMany({
      where: {
        workoutId,
      },
      include: {
        video: true,
      },
      orderBy: {
        order: "asc",
      },
    })

    return NextResponse.json(videos)
  } catch (error) {
    console.error("[VIDEO_GET_ERROR]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function DELETE(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const videoId = searchParams.get("videoId")

    if (!videoId) {
      return new NextResponse("Video ID is required", { status: 400 })
    }

    // Delete workout video associations first
    await prisma.workoutVideo.deleteMany({
      where: {
        videoId,
      },
    })

    // Delete the video
    await prisma.video.delete({
      where: {
        id: videoId,
      },
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[VIDEO_DELETE_ERROR]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 