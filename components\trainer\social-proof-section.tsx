"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { TrainerTheme } from "@/types/trainer"
import { Star, ChevronLeft, ChevronRight, Users, Award, Trophy, Clock } from "lucide-react"

interface SocialProofProps {
  theme: TrainerTheme & {
    sectionBackground?: string
    sectionTextColor?: string
  }
  statistics?: {
    clients?: number
    experience?: number
    programs?: number
    certifications?: number
  }
  testimonials?: Array<{
    id: string
    name: string
    avatar?: string
    text: string
    rating: number
    program?: string
    date?: string
    verified?: boolean
  }>
  instagramPosts?: Array<{
    id: string
    imageUrl: string
    caption: string
    likes: number
    url: string
  }>
}

export function SocialProofSection({ 
  theme, 
  statistics = {}, 
  testimonials = [],
  instagramPosts = []
}: SocialProofProps) {
  const [currentTestimonial, setCurrentTestimonial] = useState(0)
  
  // Auto-rotate testimonials
  useEffect(() => {
    if (testimonials.length <= 1) return
    
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
    }, 8000)
    
    return () => clearInterval(interval)
  }, [testimonials.length])
  
  // Navigate testimonials
  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => 
      prev === 0 ? testimonials.length - 1 : prev - 1
    )
  }
  
  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => 
      (prev + 1) % testimonials.length
    )
  }
  
  // Animation variants
  const fadeVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5 } },
    exit: { opacity: 0, transition: { duration: 0.5 } }
  }
  
  // Render stars for rating
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, i) => (
      <Star 
        key={i} 
        className={`h-4 w-4 ${i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} 
      />
    ))
  }

  return (
    <section 
      className="py-16"
      style={{ 
        backgroundColor: theme.sectionBackground || 'transparent',
        color: theme.sectionTextColor || 'inherit'
      }}
    >
      <div className="container mx-auto px-4">
        {/* Statistics */}
        {Object.values(statistics).some(val => val) && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            {statistics.clients && (
              <Card className="text-center p-6 border-0 shadow-md bg-gradient-to-br from-primary/5 to-primary/10">
                <CardContent className="p-0 flex flex-col items-center">
                  <Users className="h-10 w-10 text-primary mb-2" />
                  <span className="text-3xl font-bold">{statistics.clients}+</span>
                  <span className="text-sm text-muted-foreground">Happy Clients</span>
                </CardContent>
              </Card>
            )}
            
            {statistics.experience && (
              <Card className="text-center p-6 border-0 shadow-md bg-gradient-to-br from-primary/5 to-primary/10">
                <CardContent className="p-0 flex flex-col items-center">
                  <Clock className="h-10 w-10 text-primary mb-2" />
                  <span className="text-3xl font-bold">{statistics.experience}+</span>
                  <span className="text-sm text-muted-foreground">Years Experience</span>
                </CardContent>
              </Card>
            )}
            
            {statistics.programs && (
              <Card className="text-center p-6 border-0 shadow-md bg-gradient-to-br from-primary/5 to-primary/10">
                <CardContent className="p-0 flex flex-col items-center">
                  <Trophy className="h-10 w-10 text-primary mb-2" />
                  <span className="text-3xl font-bold">{statistics.programs}+</span>
                  <span className="text-sm text-muted-foreground">Programs Created</span>
                </CardContent>
              </Card>
            )}
            
            {statistics.certifications && (
              <Card className="text-center p-6 border-0 shadow-md bg-gradient-to-br from-primary/5 to-primary/10">
                <CardContent className="p-0 flex flex-col items-center">
                  <Award className="h-10 w-10 text-primary mb-2" />
                  <span className="text-3xl font-bold">{statistics.certifications}+</span>
                  <span className="text-sm text-muted-foreground">Certifications</span>
                </CardContent>
              </Card>
            )}
          </div>
        )}
        
        {/* Testimonials */}
        {testimonials.length > 0 && (
          <div className="mb-16">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold mb-2">What My Clients Say</h2>
              <p className="text-muted-foreground">Real results from real people</p>
            </div>
            
            <div className="relative max-w-4xl mx-auto">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentTestimonial}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  variants={fadeVariants}
                  className="relative"
                >
                  <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-950">
                    <CardContent className="pt-10 pb-6 px-6 md:px-10">
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
                        <Avatar className="h-16 w-16 border-4 border-white shadow-md">
                          <AvatarImage src={testimonials[currentTestimonial].avatar || ""} />
                          <AvatarFallback className="bg-primary text-white text-xl">
                            {testimonials[currentTestimonial].name[0]}
                          </AvatarFallback>
                        </Avatar>
                      </div>
                      
                      <div className="flex justify-center mb-4">
                        {renderStars(testimonials[currentTestimonial].rating)}
                      </div>
                      
                      <blockquote className="text-center text-lg md:text-xl italic mb-6">
                        "{testimonials[currentTestimonial].text}"
                      </blockquote>
                      
                      <div className="text-center">
                        <p className="font-semibold">{testimonials[currentTestimonial].name}</p>
                        {testimonials[currentTestimonial].program && (
                          <p className="text-sm text-muted-foreground">
                            {testimonials[currentTestimonial].program}
                          </p>
                        )}
                      </div>
                    </CardContent>
                    
                    {testimonials[currentTestimonial].verified && (
                      <CardFooter className="justify-center pb-6">
                        <div className="flex items-center text-sm text-green-600 dark:text-green-400">
                          <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          Verified Purchase
                        </div>
                      </CardFooter>
                    )}
                  </Card>
                </motion.div>
              </AnimatePresence>
              
              {testimonials.length > 1 && (
                <>
                  <button 
                    onClick={prevTestimonial}
                    className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-5 bg-white dark:bg-gray-800 rounded-full p-2 shadow-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    aria-label="Previous testimonial"
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </button>
                  
                  <button 
                    onClick={nextTestimonial}
                    className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-5 bg-white dark:bg-gray-800 rounded-full p-2 shadow-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    aria-label="Next testimonial"
                  >
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </>
              )}
              
              {/* Testimonial Indicators */}
              {testimonials.length > 1 && (
                <div className="flex justify-center mt-6 space-x-2">
                  {testimonials.map((_, i) => (
                    <button
                      key={i}
                      onClick={() => setCurrentTestimonial(i)}
                      className={`h-2 rounded-full transition-all ${
                        i === currentTestimonial 
                          ? 'w-6 bg-primary' 
                          : 'w-2 bg-gray-300 dark:bg-gray-600'
                      }`}
                      aria-label={`Go to testimonial ${i + 1}`}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* Instagram Feed */}
        {instagramPosts.length > 0 && (
          <div>
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold mb-2">Follow My Journey</h2>
              <p className="text-muted-foreground">Latest updates from Instagram</p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {instagramPosts.map((post, i) => (
                <motion.a
                  key={post.id}
                  href={post.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="relative overflow-hidden rounded-lg aspect-square group"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ 
                    opacity: 1, 
                    y: 0,
                    transition: { delay: i * 0.1, duration: 0.5 }
                  }}
                >
                  <Image
                    src={post.imageUrl}
                    alt={post.caption}
                    fill
                    className="object-cover transition-transform group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div className="text-white text-center p-4">
                      <p className="text-sm line-clamp-3">{post.caption}</p>
                      <div className="mt-2 flex items-center justify-center">
                        <svg className="h-4 w-4 mr-1 fill-current" viewBox="0 0 24 24">
                          <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                        </svg>
                        <span>{post.likes}</span>
                      </div>
                    </div>
                  </div>
                </motion.a>
              ))}
            </div>
          </div>
        )}
      </div>
    </section>
  )
}
