import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DietPlanForm } from '@/components/nutrition/diet-plan-form'

describe('DietPlanForm', () => {
  it('renders empty form by default', () => {
    render(<DietPlanForm />)
    
    expect(screen.getByLabelText(/title/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument()
    expect(screen.getByText(/meals/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /add meal/i })).toBeInTheDocument()
  })

  it('adds a new meal when clicking the add meal button', async () => {
    const user = userEvent.setup()
    render(<DietPlanForm />)
    
    const addMealButton = screen.getByRole('button', { name: /add meal/i })
    await user.click(addMealButton)
    
    // Should find two sets of meal fields since form starts with one meal
    const nameInputs = screen.getAllByLabelText(/name/i)
    const caloriesInputs = screen.getAllByLabelText(/calories/i)
    const proteinInputs = screen.getAllByLabelText(/protein \(g\)/i)
    const carbsInputs = screen.getAllByLabelText(/carbs \(g\)/i)
    const fatsInputs = screen.getAllByLabelText(/fats \(g\)/i)

    expect(nameInputs).toHaveLength(2)
    expect(caloriesInputs).toHaveLength(2)
    expect(proteinInputs).toHaveLength(2)
    expect(carbsInputs).toHaveLength(2)
    expect(fatsInputs).toHaveLength(2)
  })

  it('removes a meal when clicking the remove button', async () => {
    const user = userEvent.setup()
    render(<DietPlanForm />)
    
    // Add a meal first
    await user.click(screen.getByRole('button', { name: /add meal/i }))
    
    // Should have two meals initially
    expect(screen.getAllByLabelText(/name/i)).toHaveLength(2)
    
    // Remove one meal
    await user.click(screen.getAllByRole('button', { name: /remove/i })[0])
    
    // Should have one meal remaining
    expect(screen.getAllByLabelText(/name/i)).toHaveLength(1)
  })

  it('submits the form with valid data', async () => {
    const onSubmit = jest.fn()
    const user = userEvent.setup()
    render(<DietPlanForm onSubmit={onSubmit} />)
    
    // Fill in basic plan details
    await user.type(screen.getByLabelText(/title/i), 'Test Plan')
    await user.type(screen.getByLabelText(/description/i), 'Test Description')
    
    // Fill in meal details (form starts with one meal)
    await user.type(screen.getByLabelText(/name/i), 'Breakfast')
    await user.type(screen.getByLabelText(/calories/i), '500')
    await user.type(screen.getByLabelText(/protein \(g\)/i), '30')
    await user.type(screen.getByLabelText(/carbs \(g\)/i), '60')
    await user.type(screen.getByLabelText(/fats \(g\)/i), '20')
    
    // Submit the form
    await user.click(screen.getByRole('button', { name: /save plan/i }))
    
    expect(onSubmit).toHaveBeenCalledWith({
      title: 'Test Plan',
      description: 'Test Description',
      meals: [
        {
          name: 'Breakfast',
          calories: 500,
          protein: 30,
          carbs: 60,
          fats: 20,
        },
      ],
    })
  })

  it('initializes with existing plan data when provided', () => {
    const initialData = {
      title: 'Existing Plan',
      description: 'Existing Description',
      meals: [
        {
          name: 'Breakfast',
          calories: 500,
          protein: 30,
          carbs: 60,
          fats: 20,
        },
      ],
    }
    
    render(<DietPlanForm initialData={initialData} />)
    
    expect(screen.getByLabelText(/title/i)).toHaveValue('Existing Plan')
    expect(screen.getByLabelText(/description/i)).toHaveValue('Existing Description')
    expect(screen.getByLabelText(/name/i)).toHaveValue('Breakfast')
    expect(screen.getByLabelText(/calories/i)).toHaveValue(500)
    expect(screen.getByLabelText(/protein \(g\)/i)).toHaveValue(30)
    expect(screen.getByLabelText(/carbs \(g\)/i)).toHaveValue(60)
    expect(screen.getByLabelText(/fats \(g\)/i)).toHaveValue(20)
  })
}) 