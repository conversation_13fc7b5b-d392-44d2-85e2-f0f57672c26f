'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import {
  BarChart, Bar, Pie<PERSON>hart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer,
  Line
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';

interface NutritionData {
  date: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  goal: number;
}

interface MacroDistribution {
  name: string;
  value: number;
}

// Formatter functions
const calorieFormatter = (value: number) => [`${value} kcal`, 'Calories'];
const macroFormatter = (value: number) => [`${value}g`, ''];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

export function NutritionAnalytics() {
  const [nutritionData, setNutritionData] = useState<NutritionData[]>([]);
  const [macroDistribution, setMacroDistribution] = useState<MacroDistribution[]>([
    { name: 'Protein', value: 180 },
    { name: 'Carbs', value: 200 },
    { name: 'Fat', value: 65 },
  ]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Fetch nutrition logs
  useEffect(() => {
    const fetchNutritionLogs = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/nutrition-logs');
        if (!response.ok) {
          throw new Error('Failed to fetch nutrition logs');
        }
        
        const logs = await response.json();
        
        // Transform logs to match NutritionData format
        const formattedLogs: NutritionData[] = logs.map((log: any) => ({
          date: format(new Date(log.date), 'MMM dd'),
          calories: log.calories || 0,
          protein: log.protein || 0,
          carbs: log.carbs || 0,
          fat: log.fat || 0,
          goal: 2000, // Default goal
        }));
        
        // Group by date and sum values
        const groupedByDate = formattedLogs.reduce((acc: Record<string, NutritionData>, log) => {
          if (!acc[log.date]) {
            acc[log.date] = { ...log };
          } else {
            acc[log.date].calories += log.calories;
            acc[log.date].protein += log.protein;
            acc[log.date].carbs += log.carbs;
            acc[log.date].fat += log.fat;
          }
          return acc;
        }, {});
        
        // Convert back to array and sort by date
        const result = Object.values(groupedByDate).sort((a, b) => {
          return new Date(a.date).getTime() - new Date(b.date).getTime();
        });
        
        setNutritionData(result);

        // Calculate macro distribution
        if (result.length > 0) {
          // Calculate average macros from the last 7 days or all available data
          const dataToUse = result.slice(-7);
          
          const avgProtein = Math.round(dataToUse.reduce((sum, day) => sum + day.protein, 0) / dataToUse.length);
          const avgCarbs = Math.round(dataToUse.reduce((sum, day) => sum + day.carbs, 0) / dataToUse.length);
          const avgFat = Math.round(dataToUse.reduce((sum, day) => sum + day.fat, 0) / dataToUse.length);
          
          setMacroDistribution([
            { name: 'Protein', value: avgProtein },
            { name: 'Carbs', value: avgCarbs },
            { name: 'Fat', value: avgFat },
          ]);
        }
      } catch (error) {
        console.error('Error fetching nutrition logs:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load nutrition data',
        });
        // Fallback to sample data if fetch fails
        setNutritionData([
          { date: 'Mar 13', calories: 2100, protein: 180, carbs: 200, fat: 70, goal: 2000 },
          { date: 'Mar 14', calories: 2050, protein: 175, carbs: 210, fat: 65, goal: 2000 },
          { date: 'Mar 15', calories: 1950, protein: 185, carbs: 180, fat: 60, goal: 2000 },
          { date: 'Mar 16', calories: 2200, protein: 190, carbs: 220, fat: 75, goal: 2000 },
          { date: 'Mar 17', calories: 1900, protein: 170, carbs: 190, fat: 55, goal: 2000 },
          { date: 'Mar 18', calories: 2000, protein: 180, carbs: 200, fat: 65, goal: 2000 },
          { date: 'Mar 19', calories: 2150, protein: 195, carbs: 210, fat: 70, goal: 2000 },
        ]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchNutritionLogs();
  }, [toast]);

  if (isLoading) {
    return <div className="text-center py-6">Loading nutrition data...</div>;
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Calorie Intake</CardTitle>
          <CardDescription>
            Daily calorie intake vs. goal
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={nutritionData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip formatter={calorieFormatter} />
              <Legend />
              <Bar dataKey="calories" fill="#8884d8" name="Calories" />
              <Line type="monotone" dataKey="goal" stroke="#ff7300" name="Goal" strokeWidth={2} />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Macronutrient Distribution</CardTitle>
          <CardDescription>
            Breakdown of your daily macros
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={macroDistribution}
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
                label={({ name, value, percent }) => `${name}: ${value}g (${(percent * 100).toFixed(0)}%)`}
              >
                {macroDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={macroFormatter} />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Macronutrient Tracking</CardTitle>
          <CardDescription>
            Daily protein, carbs, and fat intake
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={nutritionData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip formatter={macroFormatter} />
              <Legend />
              <Bar dataKey="protein" stackId="a" fill="#8884d8" name="Protein" />
              <Bar dataKey="carbs" stackId="a" fill="#82ca9d" name="Carbs" />
              <Bar dataKey="fat" stackId="a" fill="#ffc658" name="Fat" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}
