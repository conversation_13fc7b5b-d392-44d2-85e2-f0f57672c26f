import { TrainingPlanTemplate } from '@prisma/client';

/**
 * Converts a training plan template to a formatted HTML string for PDF generation
 */
export function trainingPlanToHtml(template: TrainingPlanTemplate): string {
  // Create HTML header with styles
  let html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${template.title}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        h1 {
          color: #2563eb;
          font-size: 24px;
          margin-bottom: 10px;
          border-bottom: 2px solid #2563eb;
          padding-bottom: 5px;
        }
        h2 {
          color: #1e40af;
          font-size: 20px;
          margin-top: 20px;
          margin-bottom: 10px;
        }
        h3 {
          color: #1e3a8a;
          font-size: 18px;
          margin-top: 15px;
          margin-bottom: 5px;
        }
        p {
          margin-bottom: 10px;
        }
        .meta {
          color: #666;
          font-size: 14px;
          margin-bottom: 20px;
        }
        .workout {
          margin-bottom: 20px;
          padding: 15px;
          background-color: #f9fafb;
          border-radius: 5px;
          border-left: 4px solid #3b82f6;
        }
        .exercise {
          margin-bottom: 10px;
          padding-bottom: 10px;
          border-bottom: 1px solid #e5e7eb;
        }
        .exercise:last-child {
          border-bottom: none;
        }
        .exercise-name {
          font-weight: bold;
        }
        .exercise-details {
          color: #4b5563;
          font-size: 14px;
        }
        .footer {
          margin-top: 30px;
          padding-top: 10px;
          border-top: 1px solid #e5e7eb;
          font-size: 12px;
          color: #6b7280;
          text-align: center;
        }
      </style>
    </head>
    <body>
      <h1>${template.title}</h1>
      <div class="meta">
        <p><strong>Difficulty:</strong> ${template.difficulty || 'Not specified'}</p>
        <p><strong>Type:</strong> ${template.type}</p>
        <p><strong>Created:</strong> ${new Date(template.createdAt).toLocaleDateString()}</p>
      </div>
      <p>${template.description || 'No description provided.'}</p>
  `;

  // Parse the weeks data (assuming it's stored as JSON)
  const weeks = typeof template.weeks === 'string' 
    ? JSON.parse(template.weeks) 
    : template.weeks;

  // Format each week
  Object.entries(weeks).forEach(([weekKey, weekData]: [string, any]) => {
    const weekNumber = weekKey.replace('week', '');
    html += `<h2>${weekData.title || `Week ${weekNumber}`}</h2>`;
    
    // Format workouts if they exist
    if (weekData.workouts && Array.isArray(weekData.workouts)) {
      weekData.workouts.forEach((workout: any, index: number) => {
        html += `
          <div class="workout">
            <h3>${workout.title || `Workout ${index + 1}`}</h3>
            <p>${workout.description || ''}</p>
        `;
        
        // Format exercises if they exist
        if (workout.exercises && Array.isArray(workout.exercises)) {
          workout.exercises.forEach((exercise: any) => {
            html += `
              <div class="exercise">
                <div class="exercise-name">${exercise.name}</div>
                <div class="exercise-details">
                  ${exercise.sets ? `${exercise.sets} sets` : ''}
                  ${exercise.reps ? ` × ${exercise.reps} reps` : ''}
                  ${exercise.weight ? ` @ ${exercise.weight}` : ''}
                  ${exercise.rest ? ` (Rest: ${exercise.rest}s)` : ''}
                </div>
                ${exercise.notes ? `<p>${exercise.notes}</p>` : ''}
              </div>
            `;
          });
        }
        
        html += `</div>`;
      });
    } else if (weekData.days && Array.isArray(weekData.days)) {
      // Alternative format with days
      weekData.days.forEach((day: any) => {
        html += `
          <div class="workout">
            <h3>Day ${day.day || 'Unknown'}</h3>
        `;
        
        if (day.workouts && Array.isArray(day.workouts)) {
          day.workouts.forEach((workout: any, wIndex: number) => {
            html += `
              <h4>${workout.name || `Workout ${wIndex + 1}`}</h4>
              <p>${workout.description || ''}</p>
            `;
            
            if (workout.exercises && Array.isArray(workout.exercises)) {
              workout.exercises.forEach((exercise: any) => {
                html += `
                  <div class="exercise">
                    <div class="exercise-name">${exercise.name}</div>
                    <div class="exercise-details">
                      ${exercise.sets ? `${exercise.sets} sets` : ''}
                      ${exercise.reps ? ` × ${exercise.reps} reps` : ''}
                      ${exercise.weight ? ` @ ${exercise.weight}` : ''}
                      ${exercise.restTime ? ` (Rest: ${exercise.restTime}s)` : ''}
                    </div>
                    ${exercise.description ? `<p>${exercise.description}</p>` : ''}
                  </div>
                `;
              });
            }
          });
        }
        
        html += `</div>`;
      });
    }
  });

  // Add footer and close HTML
  html += `
      <div class="footer">
        <p>Generated on ${new Date().toLocaleDateString()} | © ${new Date().getFullYear()} Clear Coach</p>
      </div>
    </body>
    </html>
  `;

  return html;
}

/**
 * Generates a simple text representation of a training plan template
 * This is used as a fallback when PDF generation is not available
 */
export function trainingPlanToText(template: TrainingPlanTemplate): string {
  let text = `TRAINING PLAN: ${template.title}\n`;
  text += `Difficulty: ${template.difficulty || 'Not specified'}\n`;
  text += `Description: ${template.description || 'No description'}\n\n`;

  // Parse the weeks data
  const weeks = typeof template.weeks === 'string' 
    ? JSON.parse(template.weeks) 
    : template.weeks;

  // Format each week
  Object.entries(weeks).forEach(([weekKey, weekData]: [string, any]) => {
    const weekNumber = weekKey.replace('week', '');
    text += `WEEK ${weekNumber}: ${weekData.title || ''}\n`;
    text += `${'='.repeat(40)}\n\n`;
    
    // Format workouts if they exist
    if (weekData.workouts && Array.isArray(weekData.workouts)) {
      weekData.workouts.forEach((workout: any, index: number) => {
        text += `WORKOUT ${index + 1}: ${workout.title || ''}\n`;
        text += `${workout.description || ''}\n\n`;
        
        // Format exercises if they exist
        if (workout.exercises && Array.isArray(workout.exercises)) {
          workout.exercises.forEach((exercise: any, exIndex: number) => {
            text += `${exIndex + 1}. ${exercise.name}\n`;
            text += `   ${exercise.sets || ''} sets × ${exercise.reps || ''} reps`;
            text += `${exercise.weight ? ` @ ${exercise.weight}` : ''}\n`;
            text += `   ${exercise.notes || ''}\n\n`;
          });
        }
        
        text += `\n`;
      });
    } else if (weekData.days && Array.isArray(weekData.days)) {
      // Alternative format with days
      weekData.days.forEach((day: any) => {
        text += `DAY ${day.day || 'Unknown'}\n`;
        text += `${'-'.repeat(20)}\n\n`;
        
        if (day.workouts && Array.isArray(day.workouts)) {
          day.workouts.forEach((workout: any, wIndex: number) => {
            text += `WORKOUT: ${workout.name || `Workout ${wIndex + 1}`}\n`;
            text += `${workout.description || ''}\n\n`;
            
            if (workout.exercises && Array.isArray(workout.exercises)) {
              workout.exercises.forEach((exercise: any, exIndex: number) => {
                text += `${exIndex + 1}. ${exercise.name}\n`;
                text += `   ${exercise.sets || ''} sets × ${exercise.reps || ''} reps`;
                text += `${exercise.restTime ? ` (Rest: ${exercise.restTime}s)` : ''}\n`;
                text += `   ${exercise.description || ''}\n\n`;
              });
            }
            
            text += `\n`;
          });
        }
      });
    }
    
    text += `\n\n`;
  });

  return text;
}
