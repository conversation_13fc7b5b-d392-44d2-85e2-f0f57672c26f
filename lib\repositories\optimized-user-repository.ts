/**
 * Optimized User Repository
 * Implements efficient query patterns for user data
 */

import { Prisma, User } from '@prisma/client';
import { OptimizedRepository } from '../optimized-repository';
import { prisma } from '../prisma';
import { queryCache } from '../cache';

// Cache TTL values (in milliseconds)
const USER_CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const USER_SEARCH_CACHE_TTL = 60 * 1000; // 1 minute

export class OptimizedUserRepository extends OptimizedRepository<User> {
  constructor() {
    super({ name: 'user' }, USER_CACHE_TTL);
  }

  /**
   * Find a user by email with optimized caching
   */
  async findByEmail(
    email: string,
    options: {
      select?: Prisma.UserSelect;
      include?: Prisma.UserInclude;
      useCache?: boolean;
    } = {}
  ): Promise<User | null> {
    const { select, include, useCache = true } = options;
    
    const cacheKey = `user:email:${email}:${JSON.stringify({ select, include })}`;
    
    if (useCache) {
      return queryCache.getOrSet(
        cacheKey,
        () => prisma.user.findUnique({
          where: { email },
          select,
          include,
        }),
        USER_CACHE_TTL
      );
    }
    
    return prisma.user.findUnique({
      where: { email },
      select,
      include,
    });
  }

  /**
   * Search users by name with optimized query and caching
   */
  async searchByName(
    query: string,
    options: {
      role?: string;
      limit?: number;
      useCache?: boolean;
    } = {}
  ): Promise<User[]> {
    const { role, limit = 10, useCache = true } = options;
    
    const whereClause: Prisma.UserWhereInput = {
      name: {
        contains: query,
        mode: 'insensitive'
      }
    };
    
    if (role) {
      whereClause.role = role;
    }
    
    const cacheKey = `user:search:${query}:${role}:${limit}`;
    
    if (useCache) {
      return queryCache.getOrSet(
        cacheKey,
        () => prisma.user.findMany({
          where: whereClause,
          take: limit,
          // Only select fields that are actually needed
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            avatarUrl: true
          }
        }),
        USER_SEARCH_CACHE_TTL
      );
    }
    
    return prisma.user.findMany({
      where: whereClause,
      take: limit,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        avatarUrl: true
      }
    });
  }

  /**
   * Get users with a specific role using efficient pagination
   */
  async findByRole(
    role: string,
    options: {
      page?: number;
      pageSize?: number;
      orderBy?: Prisma.UserOrderByWithRelationInput;
      include?: Prisma.UserInclude;
      useCache?: boolean;
    } = {}
  ): Promise<{ users: User[]; total: number }> {
    const {
      page = 1,
      pageSize = 10,
      orderBy = { createdAt: 'desc' },
      include,
      useCache = true
    } = options;
    
    const skip = (page - 1) * pageSize;
    
    // Use Promise.all to run both queries in parallel
    const [users, total] = await Promise.all([
      this.findMany({
        where: { role },
        orderBy,
        pagination: { page, pageSize },
        include,
        cacheOptions: { useCache, ttl: USER_CACHE_TTL }
      }),
      this.count({ role }, { useCache, ttl: USER_CACHE_TTL })
    ]);
    
    return { users, total };
  }
}
