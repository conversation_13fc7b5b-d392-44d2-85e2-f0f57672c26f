# Clear Coach Development Guide

This guide provides information for developers working on the Clear Coach application.

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL 14+
- npm or yarn
- Git

### Installation

1. Clone the repository
```bash
git clone https://github.com/clear-coach-team/clear-coach-MVP.git
cd clear-coach-MVP
```

2. Install dependencies
```bash
npm install
# or
yarn install
```

3. Set up environment variables
```bash
cp .env.example .env.local
```

Edit the `.env.local` file with your own values.

4. Set up the database
```bash
npx prisma generate
npx prisma db push
```

5. Run the development server
```bash
npm run dev
# or
yarn dev
```

## Project Structure

```
Clear-Coach-app/
├── app/                # Next.js App Router pages
│   ├── api/            # API routes
│   ├── dashboard/      # Dashboard pages
│   ├── auth/           # Authentication pages
│   └── globals.css     # Global styles
├── components/         # React components
│   ├── ui/             # Reusable UI components
│   └── features/       # Feature-specific components
├── lib/                # Utility functions and services
│   ├── prisma.ts       # Prisma client
│   ├── auth.ts         # Auth configuration
│   └── stripe.ts       # Stripe integration
├── prisma/             # Database schema
│   └── schema.prisma   # Database schema
└── tests/              # Test files
    ├── flows/          # Workflow tests
    └── e2e/            # End-to-end tests
```

## Key Concepts

### Authentication

The application uses NextAuth.js for authentication. The configuration is in `lib/auth.ts`.

### Database

We use Prisma ORM with PostgreSQL. The database schema is defined in `prisma/schema.prisma`.

### API Routes

API routes are defined in the `app/api` directory using Next.js App Router.

### Components

UI components are built using Tailwind CSS and shadcn/ui. Feature-specific components are organized by feature.

## Testing

### Running Tests

```bash
# Run all tests
npm test

# Run workflow tests
npm run test:workflows

# Run E2E tests
npm run test:e2e

# Run pre-merge tests
npm run test:pre-merge
```

### Writing Tests

- Unit tests: Use Jest for testing individual functions and components
- Workflow tests: Use Vitest for testing complete workflows
- E2E tests: Use Playwright for end-to-end testing

## Deployment

### Vercel (Recommended)

```bash
npm install -g vercel
vercel
```

### Docker

```bash
docker build -t clear-coach .
docker run -p 3000:3000 clear-coach
```

## Contributing

1. Create a new branch for your changes
2. Make your changes
3. Run tests to ensure everything works
4. Create a pull request

## Security

### File Uploads

File uploads are handled securely using the following measures:

1. File type validation
2. File size limits
3. Secure storage in S3 or local filesystem
4. Unique filenames to prevent collisions

### API Security

API routes are protected using:

1. Authentication checks
2. CORS policy
3. Rate limiting
4. Input validation

## Performance Optimization

### Caching

The application uses Redis for caching API responses and database queries.

### Image Optimization

Images are optimized using:

1. Next.js Image component
2. Cloudinary for transformations
3. Lazy loading

## Accessibility

The application follows WCAG 2.1 AA standards:

1. Keyboard navigation
2. Screen reader support
3. Color contrast
4. Focus management
