'use client';

import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON><PERSON>, ArrowRight, Lock } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useState } from "react";

interface Product {
  id: string;
  title: string;
  description?: string;
  price: number;
  thumbnailUrl?: string;
  isPremiumOnly?: boolean;
  tag?: 'popular' | 'new' | 'featured';
}

interface FeaturedProductsProps {
  products: Product[];
  isPremium: boolean;
}

export function FeaturedProducts({
  products,
  isPremium
}: FeaturedProductsProps) {
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Filter to show only 3 products
  const featuredProducts = products.slice(0, 3);

  // Handle premium product click
  const handleProductClick = (product: Product) => {
    if (product.isPremiumOnly && !isPremium) {
      setSelectedProduct(product);
      return;
    }

    // Navigate to product page
    window.location.href = `/dashboard/shop/product/${product.id}`;
  };

  // Get tag badge
  const getTagBadge = (tag?: string) => {
    switch (tag) {
      case 'popular':
        return <Badge className="bg-red-500">💥 Popular</Badge>;
      case 'new':
        return <Badge className="bg-green-500">🆕 New</Badge>;
      case 'featured':
        return <Badge className="bg-blue-500">🏋️‍♂️ Featured</Badge>;
      default:
        return null;
    }
  };

  return (
    <>
      <Card className="shadow-md hover:shadow-lg transition-all overflow-hidden group">
        <div className="bg-gradient-to-r from-background to-muted/20 px-4 py-3 border-b border-border/30 flex flex-row items-center justify-between group-hover:from-background/80 group-hover:to-muted/30 transition-all">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <ShoppingCart className="h-4 w-4 text-primary" />
            Featured Products
          </CardTitle>

          <Button asChild variant="ghost" size="sm" className="h-8 gap-1 text-xs hover:bg-background/80">
            <Link href="/dashboard/shop">
              View All
              <ArrowRight className="h-3 w-3 ml-1" />
            </Link>
          </Button>
        </div>

        <CardContent className="p-5">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
            {featuredProducts.map((product) => (
              <div
                key={product.id}
                className={`overflow-hidden cursor-pointer rounded-xl border border-border/40 shadow-md hover:shadow-lg hover:-translate-y-1 transition-all group/card ${product.isPremiumOnly && !isPremium ? 'opacity-90' : ''}`}
                onClick={() => handleProductClick(product)}
              >
                <div className="relative h-36 bg-muted">
                  {product.thumbnailUrl ? (
                    <Image
                      src={product.thumbnailUrl}
                      alt={product.title}
                      fill
                      className="object-cover group-hover/card:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-primary/5 to-primary/20">
                      <ShoppingCart className="h-10 w-10 text-primary/50" />
                    </div>
                  )}

                  <div className="absolute top-3 left-3">
                    {getTagBadge(product.tag)}
                  </div>

                  {product.isPremiumOnly && !isPremium && (
                    <div className="absolute inset-0 bg-background/30 backdrop-blur-[1px] flex flex-col items-center justify-center">
                      <div className="h-12 w-12 rounded-full bg-background/80 flex items-center justify-center shadow-md mb-2">
                        <Lock className="h-6 w-6 text-primary" />
                      </div>
                      <span className="text-sm font-medium bg-background/80 px-3 py-1 rounded-full shadow-sm">Premium Only</span>
                    </div>
                  )}
                </div>

                <div className="p-4">
                  <h3 className="font-medium text-base line-clamp-1">{product.title}</h3>
                  <div className="flex justify-between items-center mt-2">
                    <span className="font-bold text-primary text-lg">${product.price.toFixed(2)}</span>
                    <Button size="sm" variant="outline" className="h-8 px-3 text-xs hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors">
                      View Details
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Premium Product Dialog */}
      <Dialog open={!!selectedProduct} onOpenChange={(open) => !open && setSelectedProduct(null)}>
        <DialogContent className="sm:max-w-md">
          <div className="relative overflow-hidden rounded-t-lg -mt-6 -mx-6 mb-6">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10"></div>
            <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>

            <div className="relative p-6 flex flex-col items-center">
              <div className="h-20 w-20 rounded-full bg-background/80 flex items-center justify-center shadow-md mb-4">
                <Lock className="h-10 w-10 text-primary" />
              </div>

              <DialogTitle className="text-2xl font-bold text-center mb-1">
                Premium Content
              </DialogTitle>

              <DialogDescription className="text-center">
                This premium product requires an upgraded subscription
              </DialogDescription>
            </div>
          </div>

          <div className="flex flex-col items-center justify-center p-4 space-y-4">
            <div className="bg-muted/10 p-4 rounded-xl border border-border/20 w-full">
              <h3 className="text-xl font-medium mb-2 text-center">{selectedProduct?.title}</h3>
              <p className="text-center text-muted-foreground">
                {selectedProduct?.description || "Upgrade to Premium to access this and other exclusive products."}
              </p>
            </div>

            <div className="flex items-center justify-center gap-3 w-full">
              <Button asChild variant="outline" className="flex-1" onClick={() => setSelectedProduct(null)}>
                <div>Maybe Later</div>
              </Button>

              <Button asChild className="flex-1 bg-primary hover:bg-primary/90">
                <Link href="/dashboard/upgrade">
                  Upgrade Now
                </Link>
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
