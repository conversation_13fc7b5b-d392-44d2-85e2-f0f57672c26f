import { NextResponse } from "next/server"
import { getAuthSession } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  try {
    const session = await getAuthSession()

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const userId = session.user.id

    // Count unread notifications that are NOT message notifications
    const unreadNotificationsCount = await prisma.notification.count({
      where: {
        userId,
        read: false,
        type: {
          not: "message"
        }
      },
    })

    // Count unread messages
    const unreadMessagesCount = await prisma.message.count({
      where: {
        receiverId: userId,
        read: false,
      },
    })

    // Total count of unread items (non-message notifications + unread messages)
    const totalCount = unreadNotificationsCount + unreadMessagesCount

    return NextResponse.json({
      unreadNotifications: unreadNotificationsCount,
      unreadMessages: unreadMessagesCount,
      totalCount,
    })
  } catch (error) {
    console.error("[NOTIFICATIONS_COUNT_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
