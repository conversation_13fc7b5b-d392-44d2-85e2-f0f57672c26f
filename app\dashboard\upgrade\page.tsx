"use client"

import React, { useState } from 'react'
import { Check, X, ChevronRight, Sparkles, Target, Shield, Zap, <PERSON>Right } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { TierBadge } from "@/components/dashboard/tier-badge"

// Define key features for each tier (more concise list)
const keyFeatures = {
  basic: [
    'Access to workouts',
    'Ability to buy products',
    'Basic workout tracking',
    'Limited history (7 days)'
  ],
  mid: [
    'All Basic features',
    'Workout logs',
    'Training volume tracking',
    'Performance metrics',
    'Habit tracking',
    '30-day history'
  ],
  premium: [
    'All Mid tier features',
    'Advanced analytics',
    'Nutrition tracking',
    'Recovery tools',
    'Unlimited history'
  ],
  coaching: [
    'All Premium features',
    'Private chat with coach',
    'Weekly 1:1 check-ins',
    'Personalized feedback',
    'Custom workout programming'
  ]
};

// Define which features are included in each tier
const tierFeatures = {
  basic: ['basic_workouts', 'purchase_products', 'basic_tracking', 'limited_history'],
  mid: ['all_workouts', 'purchase_products', 'comprehensive_tracking', 'extended_history', 'volume_tracking', 'performance_metrics', 'habit_tracking'],
  premium: ['all_workouts', 'purchase_products', 'comprehensive_tracking', 'unlimited_history', 'volume_tracking', 'performance_metrics', 'habit_tracking', 'advanced_analytics', 'nutrition_tracking', 'recovery_tools', 'premium_content'],
  coaching: ['all_workouts', 'purchase_products', 'comprehensive_tracking', 'unlimited_history', 'volume_tracking', 'performance_metrics', 'habit_tracking', 'advanced_analytics', 'nutrition_tracking', 'recovery_tools', 'premium_content', 'private_chat', 'weekly_checkins', 'personalized_feedback', 'custom_programming'],
};

// Define the tier pricing
const tierPricing = {
  basic: { monthly: 'Free', annually: 'Free' },
  mid: { monthly: '$10', annually: '$99' },
  premium: { monthly: '$20', annually: '$199' },
  coaching: { monthly: 'Custom', annually: 'Custom' },
};

// Define the tier descriptions
const tierDescriptions = {
  basic: 'Get started with basic workout access and tracking.',
  mid: 'Track your progress with comprehensive workout logs and metrics.',
  premium: 'Unlock advanced analytics, nutrition tracking, and recovery tools.',
  coaching: 'Get personalized coaching, feedback, and custom programming.',
};

// Define how to get each tier
const tierAcquisition = {
  basic: 'Free or low-cost ($X/month)',
  mid: '$10/month or included in mid-level subscription',
  premium: '$20/month or included in top-tier subscription',
  coaching: 'High-ticket or custom-priced plan',
};

// Define the tier CTAs
const tierCTAs = {
  basic: 'Upgrade to Mid Tier',
  mid: 'Upgrade to Premium',
  premium: 'Add 1:1 Coaching',
  coaching: 'Contact Coach',
};

// Define the tier icons
const tierIcons = {
  basic: Shield,
  mid: Zap,
  premium: Sparkles,
  coaching: Target,
};

export default function UpgradePage() {
  const router = useRouter()
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'annually'>('monthly')

  // Mock user tier - in a real app, this would come from the user's session
  const userTier = 'basic'

  // Get the next tier based on the user's current tier
  const getNextTier = (currentTier: string) => {
    if (currentTier === 'basic') return 'mid';
    if (currentTier === 'mid') return 'premium';
    if (currentTier === 'premium') return 'coaching';
    return currentTier;
  };

  const nextTier = getNextTier(userTier);

  // Handle subscription upgrade
  const handleUpgrade = (tier: string) => {
    // This would typically redirect to a checkout page or payment flow
    console.log(`Upgrading to ${tier} tier with ${billingPeriod} billing`);
    alert(`This would redirect to a checkout page for the ${tier} tier with ${billingPeriod} billing.`);
  };

  // Function to render the upgrade button for a tier
  const renderUpgradeButton = (tier: string) => {
    if (userTier === tier) {
      return (
        <Button variant="outline" className="w-full" disabled>
          Current Plan
        </Button>
      );
    } else if (getTierLevel(userTier) > getTierLevel(tier)) {
      return (
        <Button variant="secondary" className="w-full" onClick={() => handleUpgrade(tier)}>
          Downgrade
        </Button>
      );
    } else {
      return (
        <Button className="w-full bg-primary hover:bg-primary/90" onClick={() => handleUpgrade(tier)}>
          {tierCTAs[tier as keyof typeof tierCTAs]}
        </Button>
      );
    }
  };

  // Function to render feature list with checkmarks
  const renderFeatureList = (tier: string) => {
    return keyFeatures[tier as keyof typeof keyFeatures].map((feature, index) => (
      <li key={index} className="flex items-start gap-2 py-1.5">
        <Check className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
        <span className="text-sm">{feature}</span>
      </li>
    ));
  };

  return (
    <div className="container py-8 max-w-5xl mx-auto">
      {/* Header Section */}
      <div className="relative overflow-hidden rounded-xl mb-8">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 opacity-70"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <div className="relative p-6 text-center">
          <h1 className="text-2xl md:text-3xl font-bold mb-3">Upgrade Your Fitness Journey</h1>
          <p className="text-sm md:text-base text-muted-foreground max-w-xl mx-auto mb-4">
            Choose the plan that fits your goals and take your training to the next level.
          </p>

          <div className="flex items-center justify-center">
            <div className="bg-background/80 p-1 rounded-full border border-border/40 shadow-sm">
              <div className="flex items-center">
                <div className="px-3 py-1 text-xs font-medium">Current Plan:</div>
                <TierBadge tier={userTier} size="sm" showUpgradeButton={false} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Billing Toggle */}
      <div className="flex justify-center mb-6">
        <Tabs
          defaultValue="monthly"
          value={billingPeriod}
          onValueChange={(value) => setBillingPeriod(value as 'monthly' | 'annually')}
          className="w-[300px]"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="monthly" className="text-xs">Monthly</TabsTrigger>
            <TabsTrigger value="annually" className="text-xs">
              Annual
              <Badge variant="outline" className="ml-1 bg-primary/10 text-primary border-primary/20 text-[10px]">
                Save 15%
              </Badge>
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Premium Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-8">
        {['basic', 'mid', 'premium', 'coaching'].map((tier) => (
          <div
            key={tier}
            className={`relative rounded-xl overflow-hidden transition-all ${userTier === tier ? 'ring-2 ring-primary' : 'hover:shadow-md'}`}
          >
            {/* Tier Card */}
            <div className={`h-full flex flex-col ${tier === 'premium' ? 'bg-gradient-to-b from-primary/5 to-background' : tier === 'coaching' ? 'bg-gradient-to-b from-primary/10 to-background' : 'bg-card'}`}>
              {/* Card Header */}
              <div className="p-4 border-b border-border/30">
                <div className="flex justify-between items-start mb-3">
                  <TierBadge tier={tier} size="md" showUpgradeButton={false} />

                  {userTier === tier && (
                    <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 text-[10px]">
                      Current
                    </Badge>
                  )}
                </div>

                <div className="mb-1">
                  <span className="text-2xl font-bold">
                    {tierPricing[tier as keyof typeof tierPricing][billingPeriod]}
                  </span>
                  {tier !== 'basic' && (
                    <span className="text-xs text-muted-foreground ml-1">
                      /{billingPeriod === 'monthly' ? 'mo' : 'yr'}
                    </span>
                  )}
                </div>

                <p className="text-xs text-muted-foreground">
                  {tierAcquisition[tier as keyof typeof tierAcquisition]}
                </p>
              </div>

              {/* Card Content */}
              <div className="p-4 flex-grow">
                <p className="text-sm mb-3">{tierDescriptions[tier as keyof typeof tierDescriptions]}</p>

                <ul className="space-y-0.5 mb-4">
                  {renderFeatureList(tier)}
                </ul>
              </div>

              {/* Card Footer */}
              <div className="p-4 pt-0 mt-auto">
                {renderUpgradeButton(tier)}
              </div>

              {/* Recommended Badge */}
              {tier === 'premium' && (userTier === 'basic' || userTier === 'mid') && (
                <div className="absolute top-0 right-0">
                  <Badge className="rounded-tl-none rounded-br-none rounded-tr-none bg-primary text-white text-xs">
                    Recommended
                  </Badge>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Upgrade Path Section */}
      {userTier !== 'coaching' && (
        <div className="bg-muted/10 rounded-xl p-4 border border-border/30 mb-6">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-3 md:gap-6">
            <div className="flex items-center">
              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                <TierIcons tier={userTier} />
              </div>
              <div className="mx-2 text-muted-foreground">→</div>
              <div className="h-8 w-8 rounded-full bg-primary/20 flex items-center justify-center">
                <TierIcons tier={nextTier} />
              </div>
            </div>

            <div className="flex-1">
              <p className="text-xs text-muted-foreground mb-1">
                Upgrade to <span className="font-medium text-foreground">{getTierName(nextTier)}</span> to unlock:
              </p>
              <div className="flex flex-wrap gap-2">
                {keyFeatures[nextTier as keyof typeof keyFeatures].slice(0, 3).map((feature, index) => (
                  <Badge key={index} variant="outline" className="bg-primary/5 text-xs font-normal">
                    {feature}
                  </Badge>
                ))}
                {keyFeatures[nextTier as keyof typeof keyFeatures].length > 3 && (
                  <Badge variant="outline" className="bg-primary/5 text-xs font-normal">
                    +{keyFeatures[nextTier as keyof typeof keyFeatures].length - 3} more
                  </Badge>
                )}
              </div>
            </div>

            <Button
              size="sm"
              className="bg-primary hover:bg-primary/90 md:self-center whitespace-nowrap"
              onClick={() => handleUpgrade(nextTier)}
            >
              Upgrade Now
              <ChevronRight className="ml-1 h-3 w-3" />
            </Button>
          </div>
        </div>
      )}

      {/* FAQ Section - Simplified */}
      <div className="rounded-xl border border-border/30 p-4">
        <h3 className="text-sm font-medium mb-3">Frequently Asked Questions</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-xs font-medium mb-1">Can I change my plan later?</p>
            <p className="text-xs text-muted-foreground">Yes, you can upgrade or downgrade anytime. Changes apply at your next billing cycle.</p>
          </div>

          <div>
            <p className="text-xs font-medium mb-1">Can I get premium features without a subscription?</p>
            <p className="text-xs text-muted-foreground">Yes! Purchase training programs or premium bundles for access to premium features.</p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Helper function to get tier level (for comparison)
function getTierLevel(tier: string): number {
  switch (tier) {
    case 'basic': return 1;
    case 'mid': return 2;
    case 'premium': return 3;
    case 'coaching': return 4;
    default: return 0;
  }
}

// Helper function to get tier name
function getTierName(tier: string): string {
  switch (tier) {
    case 'basic': return 'Basic Tier';
    case 'mid': return 'Mid Tier';
    case 'premium': return 'Premium Tier';
    case 'coaching': return '1:1 Coaching';
    default: return tier;
  }
}

// Helper function to get upgrade features
function getUpgradeFeatures(currentTier: string, nextTier: string): string[] {
  if (currentTier === 'basic' && nextTier === 'mid') {
    return [
      'Workout logs',
      'Training volume tracking',
      'Performance metrics',
      'Habit tracking',
      '30-day history'
    ];
  } else if (currentTier === 'mid' && nextTier === 'premium') {
    return [
      'Advanced analytics',
      'Nutrition tracking',
      'Recovery tools',
      'Unlimited history'
    ];
  } else if (currentTier === 'premium' && nextTier === 'coaching') {
    return [
      'Private chat with coach',
      'Weekly 1:1 check-ins',
      'Personalized feedback',
      'Custom workout programming'
    ];
  }
  return [];
}

// Helper component for tier icons
function TierIcons({ tier }: { tier: string }) {
  const Icon = tierIcons[tier as keyof typeof tierIcons] || Shield;
  return <Icon className="h-5 w-5 text-primary" />;
}