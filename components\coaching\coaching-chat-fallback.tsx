'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquare, RefreshCw, ArrowLeft, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

export default function CoachingChatFallback() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSetupChat = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Try to set up the chat using the direct setup endpoint
      const response = await fetch('/api/direct-setup');
      const result = await response.json();

      if (result.success) {
        // Redirect to the dev login page as client with returnTo parameter and userId
        window.location.href = '/api/auth/dev-login?role=client&userId=dev-user-id&returnTo=/dashboard/coaching-chat';
      } else {
        console.error('Failed to set up chat:', result.error);
        setError(result.error || 'Failed to set up chat. Please try again.');
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error setting up chat:', error);
      setError('An unexpected error occurred. Please try again.');
      setIsLoading(false);
    }
  };

  return (
    <div className="container py-10 max-w-4xl mx-auto">
      <Link href="/dashboard">
        <Button variant="outline" className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Dashboard
        </Button>
      </Link>

      <Card className="border-primary/20 shadow-lg bg-gradient-to-b from-background to-background/80 overflow-hidden">
        <CardHeader className="text-center border-b pb-6 bg-gradient-to-r from-primary/5 to-primary/10">
          <CardTitle className="text-2xl flex items-center justify-center gap-2">
            <MessageSquare className="h-6 w-6 text-primary" />
            Premium Coaching Chat
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4 py-8">
          <div className="bg-gradient-to-r from-muted/80 to-muted p-6 rounded-lg border border-muted/50 shadow-sm">
            <h3 className="text-lg font-medium mb-2">No Coaching Conversations Found</h3>
            <p className="text-muted-foreground mb-4">
              You don't have any active coaching conversations yet. This could be because:
            </p>
            <ul className="text-left text-muted-foreground list-disc list-inside space-y-1 mb-6">
              <li>You haven't been assigned to a coach yet</li>
              <li>Your coaching relationship hasn't been activated</li>
              <li>There was an error loading your conversations</li>
            </ul>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col items-center space-y-4">
            <Button
              onClick={handleSetupChat}
              disabled={isLoading}
              className="w-full max-w-md bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-md"
            >
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Setting up chat...
                </>
              ) : (
                <>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Set Up Test Chat
                </>
              )}
            </Button>

            <p className="text-sm text-muted-foreground">
              This will create a test coaching relationship and conversation for demonstration purposes.
              You'll be automatically logged in as a client to test the chat.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center border-t pt-6 bg-gradient-to-r from-primary/5 to-primary/10">
          <div className="space-y-4 w-full max-w-md">
            <p className="text-center text-sm text-muted-foreground">
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-amber-500 to-amber-300 text-amber-900 mb-2">
                Premium Feature
              </span><br />
              Premium coaching is available to subscribers with an active coaching plan.
            </p>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
