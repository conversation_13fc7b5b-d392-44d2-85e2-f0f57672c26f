import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function PATCH(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    
    const { id, field, value } = await request.json()
    
    if (!id || !field) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Update the user
    const updatedUser = await prisma.user.update({
      where: { id },
      data: { [field]: value }
    })
    
    return NextResponse.json(updatedUser)
  } catch (error) {
    console.error("[USER_UPDATE]", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
} 