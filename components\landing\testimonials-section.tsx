import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function TestimonialsSection() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Marathon Runner",
      content:
        "Working with professional athletes has transformed my training. I've improved my marathon time by 15 minutes in just 3 months!",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      name: "<PERSON>",
      role: "Fitness Enthusiast",
      content:
        "The personalized diet plans and direct access to my favorite athlete coach have helped me achieve my fitness goals faster than I ever thought possible.",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      name: "<PERSON>",
      role: "Bodybuilder",
      content:
        "The progress tracking features and custom workout plans have been game-changers for my competition prep. Highly recommended!",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      name: "<PERSON>",
      role: "CrossFit Athlete",
      content:
        "Being able to chat directly with elite athletes and get real-time feedback on my form has improved my performance dramatically.",
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ]

  return (
    <section id="testimonials" className="w-full py-12 md:py-24 lg:py-32 bg-muted/50">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">Success Stories</h2>
            <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              See what our users have achieved with personalized coaching from professional athletes.
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border bg-background">
              <CardHeader className="flex flex-row items-center gap-4 pb-2">
                <Avatar>
                  <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                  <AvatarFallback>{testimonial.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex flex-col">
                  <CardTitle className="text-lg">{testimonial.name}</CardTitle>
                  <CardDescription>{testimonial.role}</CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{testimonial.content}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

