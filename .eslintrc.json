{"extends": "next/core-web-vitals", "plugins": ["unused-imports", "@typescript-eslint"], "rules": {"react/no-unescaped-entities": "off", "@typescript-eslint/no-explicit-any": "off", "import/order": "off", "unused-imports/no-unused-imports": "off", "@typescript-eslint/no-unused-vars": "off", "no-console": "off", "import/no-unresolved": "off", "import/no-duplicates": "off", "react-hooks/rules-of-hooks": "off", "react-hooks/exhaustive-deps": "off", "react/display-name": "off", "@typescript-eslint/no-unused-expressions": "off", "@typescript-eslint/no-require-imports": "off", "@typescript-eslint/no-unsafe-function-type": "off", "no-useless-escape": "off", "jsx-a11y/alt-text": "off", "@next/next/no-img-element": "off"}}