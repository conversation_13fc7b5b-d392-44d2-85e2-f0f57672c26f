import { Workout, Prisma } from "@prisma/client"
import { IWorkoutRepository, WorkoutRepository } from "../repositories/workout-repository"

export class WorkoutService {
  private static repository: IWorkoutRepository = new WorkoutRepository()

  /**
   * Set a custom repository implementation (useful for testing)
   */
  static setRepository(repository: IWorkoutRepository) {
    this.repository = repository
  }

  /**
   * Find a workout by ID
   */
  static async findById(id: string): Promise<Workout | null> {
    return this.repository.findById(id)
  }

  /**
   * Find a workout by ID with exercises
   */
  static async findByIdWithExercises(id: string): Promise<any | null> {
    return this.repository.findByIdWithExercises(id)
  }

  /**
   * Create a new workout
   */
  static async create(data: Prisma.WorkoutCreateInput): Promise<Workout> {
    return this.repository.create(data)
  }

  /**
   * Update a workout
   */
  static async update(id: string, data: Partial<Workout>): Promise<Workout> {
    return this.repository.update(id, data)
  }

  /**
   * Delete a workout
   */
  static async delete(id: string): Promise<Workout> {
    return this.repository.delete(id)
  }

  /**
   * Find multiple workouts
   */
  static async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.WorkoutWhereInput
    orderBy?: Prisma.WorkoutOrderByWithRelationInput
    include?: Prisma.WorkoutInclude
  }): Promise<Workout[]> {
    return this.repository.findMany(params)
  }

  /**
   * Find workouts by trainer ID
   */
  static async findByTrainerId(trainerId: string): Promise<Workout[]> {
    return this.repository.findByTrainerId(trainerId)
  }

  /**
   * Add an exercise to a workout
   */
  static async addExerciseToWorkout(workoutId: string, exerciseData: any): Promise<any> {
    return this.repository.addExerciseToWorkout(workoutId, exerciseData)
  }

  /**
   * Update an exercise in a workout
   */
  static async updateExerciseInWorkout(workoutId: string, exerciseId: string, exerciseData: any): Promise<any> {
    return this.repository.updateExerciseInWorkout(workoutId, exerciseId, exerciseData)
  }

  /**
   * Remove an exercise from a workout
   */
  static async removeExerciseFromWorkout(workoutId: string, exerciseId: string): Promise<any> {
    return this.repository.removeExerciseFromWorkout(workoutId, exerciseId)
  }

  /**
   * Add a video to a workout
   */
  static async addVideoToWorkout(workoutId: string, videoData: any): Promise<any> {
    return this.repository.addVideoToWorkout(workoutId, videoData)
  }
}
