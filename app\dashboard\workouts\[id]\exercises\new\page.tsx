import { notFound, redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { ExerciseForm } from "@/components/forms/exercise-form"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// Define allowed types for the form
type AllowedWorkoutType = "strength" | "cardio" | "flexibility" | "recovery";

// Type guard to check if a string is an allowed workout type
function isAllowedWorkoutType(type: string | null | undefined): type is AllowedWorkoutType {
    return !!type && ["strength", "cardio", "flexibility", "recovery"].includes(type);
}

// Define the expected props structure
interface NewExercisePageProps {
    params: {
        id: string; // This should be workoutId
    }
}

// Use the defined props interface
export default async function NewExercisePage({ params }: NewExercisePageProps) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    redirect("/auth/signin")
  }

  const workoutId = params.id;
  if (!workoutId) {
      console.error("Workout ID missing from params");
      notFound();
  }

  const workout = await prisma.workout.findUnique({
    where: {
      id: workoutId,
    },
    include: {
      trainingPlan: { select: { athleteId: true } },
    },
  })

  if (!workout) {
      console.error(`Workout ${workoutId} not found.`);
      notFound();
  }
  
  if (workout.trainingPlan.athleteId !== session.user.id && session.user.role !== "admin") {
      console.warn(`User ${session.user.id} does not own workout ${workoutId}.`);
      notFound();
  }

  const validatedWorkoutType = isAllowedWorkoutType(workout.type) ? workout.type : 'strength';

  return (
    <div className="container mx-auto py-6 max-w-3xl">
      <h1 className="text-2xl font-bold mb-6">Add New Exercise to Workout</h1>
      <ExerciseForm workoutId={workout.id} workoutType={validatedWorkoutType} />
    </div>
  )
} 