#!/bin/bash

# Wait for the database to be ready
echo "Waiting for database..."
while ! nc -z db 5432; do
  sleep 1
done
echo "Database is ready"
sleep 3

# Run Prisma migrations and seed
echo "Running prisma db push..."
pnpm exec prisma db push --accept-data-loss
echo "Running prisma db seed..."
pnpm exec prisma db seed

# Start Prisma Studio in the background
echo "Starting Prisma Studio..."
pnpm exec prisma studio --port 5555 --hostname 0.0.0.0 &

# Start WebSocket server in the background
echo "Starting WebSocket server..."
node start-server.js &

# Clean up any existing Next.js cache
echo "Cleaning up Next.js cache..."
rm -rf /app/.next

# Ensure node_modules is properly installed
echo "Checking node_modules..."
if [ ! -d "/app/node_modules" ] || [ ! -f "/app/node_modules/.bin/next" ]; then
  echo "Reinstalling dependencies..."
  cd /app
  pnpm install
fi

# Start Next.js
echo "Starting Next.js..."
cd /app
export PATH="$PATH:/app/node_modules/.bin"
echo "PATH: $PATH"

# Use the local Next.js from node_modules
echo "Using pnpm to run Next.js..."
pnpm dev
