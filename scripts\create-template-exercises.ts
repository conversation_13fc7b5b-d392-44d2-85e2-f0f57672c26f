import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const templateExercises = [
  {
    name: "Push-ups",
    description: "Classic bodyweight exercise targeting chest, shoulders, and triceps",
    sets: 3,
    reps: 12,
    duration: 0,
    restTime: 60,
    videoUrl: "https://example.com/pushups.mp4",
    type: "strength",
    difficulty: "beginner",
  },
  {
    name: "Squats",
    description: "Fundamental lower body exercise",
    sets: 4,
    reps: 15,
    duration: 0,
    restTime: 90,
    videoUrl: "https://example.com/squats.mp4",
    type: "strength",
    difficulty: "beginner",
  },
  {
    name: "Pull-ups",
    description: "Upper body compound exercise",
    sets: 3,
    reps: 8,
    duration: 0,
    restTime: 120,
    videoUrl: "https://example.com/pullups.mp4",
    type: "strength",
    difficulty: "intermediate",
  },
  {
    name: "Plank",
    description: "Core stability exercise",
    sets: 3,
    reps: 0,
    duration: 60,
    restTime: 45,
    videoUrl: "https://example.com/plank.mp4",
    type: "core",
    difficulty: "beginner",
  },
  {
    name: "Burpe<PERSON>",
    description: "Full body conditioning exercise",
    sets: 3,
    reps: 10,
    duration: 0,
    restTime: 90,
    videoUrl: "https://example.com/burpees.mp4",
    type: "cardio",
    difficulty: "intermediate",
  },
  {
    name: "Deadlift",
    description: "Compound exercise targeting posterior chain",
    sets: 4,
    reps: 8,
    duration: 0,
    restTime: 120,
    videoUrl: "https://example.com/deadlift.mp4",
    type: "strength",
    difficulty: "advanced",
  },
  {
    name: "Mountain Climbers",
    description: "Dynamic core and cardio exercise",
    sets: 3,
    reps: 0,
    duration: 45,
    restTime: 60,
    videoUrl: "https://example.com/mountain-climbers.mp4",
    type: "cardio",
    difficulty: "intermediate",
  },
  {
    name: "Russian Twists",
    description: "Rotational core exercise",
    sets: 3,
    reps: 20,
    duration: 0,
    restTime: 45,
    videoUrl: "https://example.com/russian-twists.mp4",
    type: "core",
    difficulty: "beginner",
  },
]

async function main() {
  console.log('Creating template exercises...')
  
  for (const exercise of templateExercises) {
    const existing = await prisma.templateExercise.findFirst({
      where: { name: exercise.name }
    })
    
    if (existing) {
      const updated = await prisma.templateExercise.update({
        where: { id: existing.id },
        data: exercise
      })
      console.log(`Updated exercise: ${updated.name}`)
    } else {
      const created = await prisma.templateExercise.create({
        data: exercise
      })
      console.log(`Created exercise: ${created.name}`)
    }
  }

  console.log('Finished creating template exercises')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 