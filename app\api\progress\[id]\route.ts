import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function PUT(
  request: Request,
  context: any
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Safely access params
    const progressId = context?.params?.id;
    if (!progressId) {
        return new NextResponse("Progress entry ID missing in URL", { status: 400 });
    }

    // Check if the request is multipart/form-data
    const contentType = request.headers.get('content-type') || ''
    let weight, bodyFat, measurements, date, notes, photoUrls = {}

    if (contentType.includes('multipart/form-data')) {
      const formData = await request.formData()
      const jsonData = formData.get('data')

      if (jsonData) {
        const parsedData = JSON.parse(jsonData.toString())
        weight = parsedData.weight
        bodyFat = parsedData.bodyFat
        measurements = parsedData.measurements
        date = parsedData.date
        notes = parsedData.notes
      }

      // Handle photo uploads
      const frontPhoto = formData.get('frontPhoto') as File | null
      const sidePhoto = formData.get('sidePhoto') as File | null
      const backPhoto = formData.get('backPhoto') as File | null

      // In a real implementation, you would upload these to a storage service
      if (frontPhoto) {
        // Example: Upload to storage service and get URL
        // const frontPhotoUrl = await uploadToStorage(frontPhoto)
        // photoUrls.front = frontPhotoUrl
        photoUrls.front = URL.createObjectURL(frontPhoto) // This is just for demo purposes
      }

      if (sidePhoto) {
        // const sidePhotoUrl = await uploadToStorage(sidePhoto)
        // photoUrls.side = sidePhotoUrl
        photoUrls.side = URL.createObjectURL(sidePhoto) // This is just for demo purposes
      }

      if (backPhoto) {
        // const backPhotoUrl = await uploadToStorage(backPhoto)
        // photoUrls.back = backPhotoUrl
        photoUrls.back = URL.createObjectURL(backPhoto) // This is just for demo purposes
      }
    } else {
      // Handle regular JSON request
      const body = await request.json()
      weight = body.weight
      bodyFat = body.bodyFat
      measurements = body.measurements
      date = body.date
      notes = body.notes
    }

    // Check if the progress entry exists and belongs to the user
    const progress = await prisma.measurement.findUnique({
      where: {
        id: progressId,
      },
      select: { userId: true }
    })

    if (!progress) {
      return new NextResponse("Progress entry not found", { status: 404 })
    }

    // Check ownership
    if (progress.userId !== session.user.id) {
      return new NextResponse("Forbidden: You do not own this progress entry", { status: 403 })
    }

    // Update the progress entry
    const updatedProgress = await prisma.measurement.update({
      where: {
        id: progressId,
      },
      data: {
        weight: weight ? parseFloat(weight) : undefined,
        bodyFat: bodyFat ? parseFloat(bodyFat) : undefined,
        waist: measurements?.waist ? parseFloat(measurements.waist) : undefined,
        chest: measurements?.chest ? parseFloat(measurements.chest) : undefined,
        arms: measurements?.arms ? parseFloat(measurements.arms) : undefined,
        thighs: measurements?.thighs ? parseFloat(measurements.thighs) : undefined,
        date: date ? new Date(date) : undefined,
        notes: notes !== undefined ? notes : undefined,
        frontPhotoUrl: photoUrls.front || undefined,
        sidePhotoUrl: photoUrls.side || undefined,
        backPhotoUrl: photoUrls.back || undefined,
      },
    })

    return NextResponse.json(updatedProgress)
  } catch (error) {
    console.error("[PROGRESS_UPDATE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function DELETE(
  request: Request,
  context: any // Use 'any' workaround
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Safely access params
    const progressId = context?.params?.id;
    if (!progressId) {
        return new NextResponse("Progress entry ID missing in URL", { status: 400 });
    }

    const progress = await prisma.measurement.findUnique({
      where: {
        id: progressId,
      },
       // Select only the field needed for the ownership check
      select: { userId: true }
    })

    if (!progress) {
      // Idempotent: Already deleted or never existed
      return new NextResponse(null, { status: 204 });
      // return new NextResponse("Progress entry not found", { status: 404 })
    }

    // Check ownership
    if (progress.userId !== session.user.id) {
      return new NextResponse("Forbidden: You do not own this progress entry", { status: 403 })
    }

    // Perform delete
    await prisma.measurement.delete({
      where: {
        id: progressId,
      },
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[PROGRESS_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}