"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { useSession } from "next-auth/react"
import { useRouter, useSearchParams } from "next/navigation"
import { v4 as uuidv4 } from "uuid"
import { MessageSquare, Search, User, ArrowLeft, MoreVertical, PlusCircle, UserPlus, RefreshCw } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { formatDistanceToNow } from "date-fns"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Badge } from "@/components/ui/badge"
import { useSocket } from "@/components/socket-provider"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

// Import custom styles
import "./chat.css"
import "@/styles/chat-image-fix.css" // Import the fix for chat images
import { ChatMediaInput } from "@/components/chat/chat-media-input"
import { ChatMessageAttachments } from "@/components/chat/chat-message-attachments"

interface Client {
  id: string
  name: string
  email: string
  avatarUrl?: string
  clientSubscriptions?: any[]
}

interface Conversation {
  id: string
  user: {
    id: string
    name: string
    avatarUrl?: string
  }
  lastMessage?: {
    content: string
    createdAt: string
  }
  unreadCount: number
}

interface Attachment {
  id: string;
  url: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  mimeType: string;
}

interface Message {
  id: string
  content: string
  senderId: string
  conversationId: string
  createdAt: string
  read: boolean
  isOptimistic?: boolean
  attachments?: Attachment[]
  sender: {
    id: string
    name: string
    avatarUrl?: string
  }
}

export default function ChatsPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const { isConnected, joinConversation, leaveConversation, sendMessage: socketSendMessage } = useSocket()

  const [conversations, setConversations] = useState<Conversation[]>([])
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(true)
  const [sendingMessage, setSendingMessage] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [updatingMessages, setUpdatingMessages] = useState(false)

  // New state variables for client management
  const [clients, setClients] = useState<Client[]>([])
  const [loadingClients, setLoadingClients] = useState(false)
  const [clientSearchQuery, setClientSearchQuery] = useState("")
  const [isNewChatDialogOpen, setIsNewChatDialogOpen] = useState(false)
  const [creatingConversation, setCreatingConversation] = useState(false)

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Filter conversations based on search query
  const filteredConversations = conversations.filter(
    (conv) => conv.user.name?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Separate unread conversations
  const unreadConversations = filteredConversations.filter((conv) => conv.unreadCount > 0)
  const readConversations = filteredConversations.filter((conv) => conv.unreadCount === 0)

  // Filter clients based on search query
  const filteredClients = clients.filter(
    (client) =>
      client.name?.toLowerCase().includes(clientSearchQuery.toLowerCase()) ||
      client.email?.toLowerCase().includes(clientSearchQuery.toLowerCase())
  )

  // We'll use filteredClients directly in the dialog

  // Function to fetch conversations and unread count - EXACTLY like the notification bell
  const fetchUnreadCount = useCallback(async () => {
    if (!session?.user?.id) return;

    console.log("🔄 Fetching conversations and unread count");

    try {
      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/coaching/conversations?t=${timestamp}`, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch conversations: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log("📋 Conversations data received");

      // Transform the data to match our expected format
      const formattedConversations = data.map((conv: any) => ({
        id: conv.id,
        user: conv.user || {
          id: conv.relationship?.clientId || conv.relationship?.trainerId,
          name: conv.user?.name || "User",
          avatarUrl: conv.user?.avatarUrl
        },
        lastMessage: conv.lastMessage || {
          content: "Start a conversation",
          createdAt: new Date().toISOString()
        },
        unreadCount: conv.unreadCount || 0
      }));

      // Update conversations state
      setConversations(formattedConversations);

      // If we have an active conversation, make sure it's updated too
      if (activeConversation) {
        const updatedActiveConversation = formattedConversations.find(
          (c: Conversation) => c.id === activeConversation.id
        );

        if (updatedActiveConversation) {
          setActiveConversation(prev => {
            if (!prev) return prev;
            return {
              ...prev,
              lastMessage: updatedActiveConversation.lastMessage,
              unreadCount: 0 // Always reset unread count for active conversation
            };
          });
        }
      }

      console.log("✅ Conversations and unread count updated successfully");
    } catch (error) {
      console.error("❌ Error fetching conversations:", error);
    }
  }, [session?.user?.id, activeConversation]);

  // Scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messagesEndRef])

  // Mark messages as read with debouncing
  const markMessagesAsRead = useCallback(async (conversationId: string) => {
    // Use a static variable to track if we're already marking messages as read
    if ((markMessagesAsRead as any).isProcessing) {
      console.log('Already marking messages as read, skipping duplicate request');
      return;
    }

    // Set processing flag
    (markMessagesAsRead as any).isProcessing = true;

    try {
      console.log(`Marking messages as read for conversation: ${conversationId}`);

      await fetch(`/api/coaching/messages/mark-read`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ conversationId }),
      });

      // Update unread count in conversations
      setConversations((prev) =>
        prev.map((conv) =>
          conv.id === conversationId ? { ...conv, unreadCount: 0 } : conv
        )
      );

      // No event dispatch here - we only want to update when a message is sent
    } catch (error) {
      console.error("Error marking messages as read:", error);
    } finally {
      // Clear processing flag after a short delay to prevent rapid consecutive calls
      setTimeout(() => {
        (markMessagesAsRead as any).isProcessing = false;
      }, 1000);
    }
  }, []);

  // Fetch messages for a conversation with useCallback and debouncing
  const fetchMessages = useCallback(async (conversationId: string, showLoading = true) => {
    if (!session?.user?.id) return;

    // Use a static variable to track if we're already fetching
    if ((fetchMessages as any).isLoading) {
      console.log('Already fetching messages, skipping duplicate request');
      return;
    }

    // Set loading flag
    (fetchMessages as any).isLoading = true;

    // Always set updating messages to false when this function completes
    if (showLoading) {
      setUpdatingMessages(true);
    }

    try {
      console.log(`Fetching messages for conversation: ${conversationId}`);

      // Add timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/coaching/messages?conversationId=${conversationId}&t=${timestamp}`, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch messages: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // If data is empty or not an array, set empty messages and return
      if (!data || !Array.isArray(data)) {
        console.warn("Received invalid data format for messages:", data);
        setMessages([]);
        return;
      }

      // Preserve optimistic messages that aren't in the fetched data
      const optimisticMessages = messages.filter(m =>
        m.isOptimistic && !data.some(d => d.id === m.id)
      );

      // Get non-optimistic messages from current state for comparison
      const currentRealMessages = messages.filter(m => !m.isOptimistic);

      // Check if there are actual changes before updating state
      const currentMessagesJson = JSON.stringify(currentRealMessages);
      const newMessagesJson = JSON.stringify(data);

      if (currentMessagesJson !== newMessagesJson || optimisticMessages.length > 0) {
        console.log('Messages data has changed, updating state');

        // Combine fetched messages with optimistic ones
        const combinedMessages = [...data, ...optimisticMessages];

        // Sort by creation time
        combinedMessages.sort((a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );

        // Update state with minimal UI disruption
        setMessages(combinedMessages);

        // Scroll to bottom with animation
        setTimeout(() => {
          scrollToBottom();
        }, 100);
      } else {
        console.log('No changes in messages data, skipping update');
      }

      // Mark messages as read
      markMessagesAsRead(conversationId);
    } catch (error) {
      console.error("Error fetching messages:", error);
      // Set empty messages to avoid showing loading forever
      if (showLoading) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load messages. Please try refreshing the page.",
        });
        setMessages([]);
      }
    } finally {
      // Always turn off loading state
      if (showLoading) {
        setUpdatingMessages(false);
      }

      // Clear loading flag after a short delay to prevent rapid consecutive calls
      setTimeout(() => {
        (fetchMessages as any).isLoading = false;
      }, 1000);
    }
  }, [session?.user?.id, markMessagesAsRead, scrollToBottom, toast, messages]);

  // Fetch conversations with useCallback and debouncing
  const fetchConversations = useCallback(async (isInitialLoad = false) => {
    if (!session?.user?.id) return;

    // Use a static variable to track if we're already fetching
    if ((fetchConversations as any).isLoading) {
      console.log('Already fetching conversations, skipping duplicate request');
      return;
    }

    // Set loading flag
    (fetchConversations as any).isLoading = true;

    // Always set loading to false when this function completes, even if there's an error
    if (isInitialLoad) {
      setLoading(true);
    }

    try {
      console.log(`Fetching conversations (isInitialLoad: ${isInitialLoad})`);

      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/coaching/conversations?t=${timestamp}`, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch conversations: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // If data is empty or not an array, set empty conversations and return
      if (!data || !Array.isArray(data)) {
        console.warn("Received invalid data format for conversations:", data);
        setConversations([]);
        return;
      }

      // Transform the data to match our expected format
      const formattedConversations = data.map((conv: any) => ({
        id: conv.id,
        user: conv.user || {
          id: conv.relationship?.clientId || conv.relationship?.trainerId,
          name: conv.user?.name || "User",
          avatarUrl: conv.user?.avatarUrl
        },
        lastMessage: conv.lastMessage || {
          content: "Start a conversation",
          createdAt: new Date().toISOString()
        },
        unreadCount: conv.unreadCount || 0
      }));

      // Check if there are actual changes before updating state
      const currentConversationsJson = JSON.stringify(conversations);
      const newConversationsJson = JSON.stringify(formattedConversations);

      if (currentConversationsJson !== newConversationsJson || isInitialLoad) {
        console.log('Conversations data has changed or initial load, updating state');
        // Set the conversations
        setConversations(formattedConversations);

        // Check if there's a conversationId in the URL (only on initial load)
        if (isInitialLoad && searchParams) {
          const conversationId = searchParams.get("conversationId");
          if (conversationId) {
            const conversation = formattedConversations.find((c: any) => c.id === conversationId);
            if (conversation) {
              setActiveConversation(conversation);
              fetchMessages(conversationId);
            }
          }
        }
      } else {
        console.log('No changes in conversations data, skipping update');
      }
    } catch (error) {
      console.error("Error fetching conversations:", error);
      // Only show error toast on initial load
      if (isInitialLoad) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load conversations. Please try refreshing the page.",
        });

        // Set empty conversations to avoid showing loading forever
        setConversations([]);
      }
    } finally {
      // Always turn off loading state
      if (isInitialLoad) {
        setLoading(false);
      }

      // Clear loading flag after a short delay to prevent rapid consecutive calls
      setTimeout(() => {
        (fetchConversations as any).isLoading = false;
      }, 1000);
    }
  }, [session?.user?.id, searchParams, toast, fetchMessages, conversations]);



  // No manual check for updates - we only update when a message is sent

  // Send a message with improved visual feedback and file attachments
  const sendMessage = async (content: string, files?: File[]) => {
    if (!activeConversation || (!content.trim() && (!files || files.length === 0)) || !session?.user?.id) return

    // Store the message content
    const messageContent = content.trim()

    // Clear input immediately for better UX (handled by ChatMediaInput)

    // Create a temporary message with a temporary ID and optimistic flag
    const tempId = `temp-${Date.now()}`
    const tempMessage: Message = {
      id: tempId,
      content: messageContent,
      senderId: session.user.id,
      conversationId: activeConversation.id,
      createdAt: new Date().toISOString(),
      read: false,
      isOptimistic: true, // Flag to identify this as an optimistic update
      sender: {
        id: session.user.id,
        name: session.user.name || 'You',
        avatarUrl: session.user.image || undefined,
      }
    }

    // Optimistically add the message to the UI
    setMessages((prev) => [...prev, tempMessage])

    // Scroll to bottom immediately
    scrollToBottom()

    // Now start the actual sending process
    setSendingMessage(true)

    try {
      let attachments: Attachment[] = [];

      // Upload files if any
      if (files && files.length > 0) {
        // Upload each file
        const uploadPromises = files.map(async (file) => {
          const formData = new FormData();
          formData.append("file", file);
          formData.append("conversationId", activeConversation.id);

          const uploadResponse = await fetch("/api/chat/upload", {
            method: "POST",
            body: formData,
          });

          if (!uploadResponse.ok) {
            throw new Error(`Failed to upload file: ${file.name}`);
          }

          return await uploadResponse.json();
        });

        // Wait for all uploads to complete
        const uploadResults = await Promise.all(uploadPromises);
        attachments = uploadResults.map((result) => ({
          id: uuidv4(), // Generate a temporary ID
          url: result.url,
          fileName: result.fileName,
          fileSize: result.fileSize,
          fileType: result.fileType,
          mimeType: result.mimeType,
        }));
      }

      // Use the existing coaching messages API endpoint
      const response = await fetch("/api/coaching/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: messageContent,
          conversationId: activeConversation.id,
          attachments: attachments.length > 0 ? attachments : undefined,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to send message")
      }

      const data = await response.json()

      // Replace the temporary message with the real one
      setMessages((prev) =>
        prev.map((msg) => (msg.id === tempId ? { ...data, isOptimistic: false } : msg))
      )

      // Update conversation last message
      setConversations((prev) =>
        prev.map((conv) =>
          conv.id === activeConversation.id
            ? {
                ...conv,
                lastMessage: {
                  content: data.content,
                  createdAt: data.createdAt,
                },
                // Reset unread count for this conversation since we're actively chatting
                unreadCount: 0
              }
            : conv
        )
      )

      // Send the message via WebSocket for real-time updates
      if (isConnected) {
        socketSendMessage({
          ...data,
          receiverId: activeConversation.user.id,
          conversationId: activeConversation.id
        })
      }

      // Prepare the message data for events
      const messageData = {
        ...data,
        receiverId: activeConversation.user.id,
        conversationId: activeConversation.id
      }

      // Dispatch a single message-sent event
      console.log('Message sent successfully, dispatching event')
      window.dispatchEvent(new CustomEvent('message-sent', {
        detail: {
          message: messageData
        }
      }))

      // Dispatch a single force-refresh event after a delay
      // This is enough to ensure other components are updated
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('force-refresh-chat', {
          detail: {
            timestamp: Date.now(),
            message: messageData
          }
        }))
      }, 500)

      // Update the unread count to ensure it's up to date
      setTimeout(() => {
        fetchUnreadCount()
      }, 800)
    } catch (error) {
      console.error("Error sending message:", error)

      // Remove the optimistic message on error
      setMessages((prev) => prev.filter((msg) => msg.id !== tempId))

      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to send message. Please try again.",
      })
    } finally {
      setSendingMessage(false)
      // Focus back on input
      inputRef.current?.focus()
    }
  }





  // Select a conversation
  const selectConversation = (conversation: Conversation) => {
    // Only perform actions if this is a different conversation
    if (activeConversation?.id !== conversation.id) {
      setActiveConversation(conversation);
      fetchMessages(conversation.id);

      // Update URL
      router.push(`/dashboard/chats?conversationId=${conversation.id}`);

      // Note: The join/leave conversation is now handled in the useEffect hook
      // to prevent multiple join/leave cycles
    }
  }

  // Fetch all clients with useCallback and debouncing
  const fetchClients = useCallback(async () => {
    if (!session?.user?.id || session.user.role !== "trainer") return;

    // Use a static variable to track if we're already fetching
    if ((fetchClients as any).isLoading) {
      console.log('Already fetching clients, skipping duplicate request');
      return;
    }

    // Set loading flag
    (fetchClients as any).isLoading = true;

    try {
      console.log('Fetching clients (debounced)');
      setLoadingClients(true);

      const response = await fetch(`/api/trainer/clients?t=${Date.now()}`, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      });

      if (response.ok) {
        const data = await response.json();

        // Check if there are actual changes before updating state
        const currentClientsJson = JSON.stringify(clients);
        const newClientsJson = JSON.stringify(data);

        if (currentClientsJson !== newClientsJson) {
          console.log('Clients data has changed, updating state');
          setClients(data);
        } else {
          console.log('No changes in clients data, skipping update');
        }
      } else {
        console.error("Error fetching clients:", response.statusText);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load clients",
        });
      }
    } catch (error) {
      console.error("Error fetching clients:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load clients",
      });
    } finally {
      setLoadingClients(false);
      // Clear loading flag after a short delay to prevent rapid consecutive calls
      setTimeout(() => {
        (fetchClients as any).isLoading = false;
      }, 1000);
    }
  }, [session?.user?.id, session?.user?.role, clients, toast]);

  // Create a new conversation with a client
  const createConversation = async (clientId: string) => {
    if (!session?.user?.id) return

    try {
      setCreatingConversation(true)

      // Find the client in the list to show in the toast
      const client = clients.find(c => c.id === clientId);

      // Show a loading toast
      toast({
        title: "Creating conversation...",
        description: client ? `Starting a chat with ${client.name}` : "Please wait",
      });

      const response = await fetch("/api/coaching/conversations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ clientId }),
      })

      if (!response.ok) {
        throw new Error("Failed to create conversation")
      }

      const data = await response.json()

      // Close the dialog
      setIsNewChatDialogOpen(false)

      // Update conversations list - use the full list here since we need the new conversation
      await fetchUnreadCount();

      // Select the new conversation
      const newConversation = {
        id: data.id,
        user: data.user,
        unreadCount: 0
      }

      selectConversation(newConversation as Conversation)

      toast({
        title: "Success",
        description: `Started a conversation with ${data.user.name}`,
      })
    } catch (error) {
      console.error("Error creating conversation:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create conversation",
      })
    } finally {
      setCreatingConversation(false)
    }
  }

  // Track if initial fetch has already run
  const initialFetchRef = useRef(false);

  // Initial fetch - ONE TIME ONLY on component mount
  useEffect(() => {
    if (session?.user?.id && !initialFetchRef.current) {
      // Set loading state
      setLoading(true);
      initialFetchRef.current = true;

      console.log("INITIAL FETCH - ONE TIME ONLY");

      // Simple one-time fetch with a small delay to ensure everything is ready
      setTimeout(() => {
        fetchConversations(true)
          .then(() => {
            // Fetch clients if user is a trainer
            if (session.user.role === "trainer") {
              return fetchClients();
            }
          })
          .catch(error => {
            console.error("Error during initial fetch:", error);
          })
          .finally(() => {
            // Always turn off loading state
            setLoading(false);
          });
      }, 100);
    }
  }, [session?.user?.id, fetchConversations, fetchClients]); // Only run when session changes



  // We've merged fetchConversationsList into fetchUnreadCount for simplicity



  // WebSocket event handlers for real-time updates - SUPER AGGRESSIVE VERSION
  useEffect(() => {
    if (!session?.user?.id) return;

    console.log("🔌 Setting up WebSocket event handlers for chat list updates - SUPER AGGRESSIVE VERSION");

    // Function to refresh all chat data - direct API calls
    const refreshAllChatData = async (source: string) => {
      console.log(`🔄 AGGRESSIVE REFRESH triggered by ${source}`);

      toast({
        title: "Refreshing chat data",
        description: `Update triggered by ${source}`,
        duration: 2000,
      });

      // Refresh conversations first
      try {
        console.log('🔄 Aggressively refreshing conversations list');
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/coaching/conversations?t=${timestamp}`, {
          cache: "no-store",
          headers: {
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('📋 Received fresh conversations data:', data.length, 'conversations');

          // Transform the data to match our expected format
          const formattedConversations = data.map((conv: any) => ({
            id: conv.id,
            user: conv.user || {
              id: conv.relationship?.clientId || conv.relationship?.trainerId,
              name: conv.user?.name || "User",
              avatarUrl: conv.user?.avatarUrl
            },
            lastMessage: conv.lastMessage || {
              content: "Start a conversation",
              createdAt: new Date().toISOString()
            },
            unreadCount: conv.unreadCount || 0
          }));

          // Update conversations state
          setConversations(formattedConversations);

          // If we have an active conversation, update it too
          if (activeConversation) {
            const updatedActiveConversation = formattedConversations.find(
              (c: Conversation) => c.id === activeConversation.id
            );

            if (updatedActiveConversation) {
              console.log('📋 Updating active conversation with fresh data');
              setActiveConversation(prev => {
                if (!prev) return prev;
                return {
                  ...prev,
                  lastMessage: updatedActiveConversation.lastMessage,
                  unreadCount: 0 // Always reset unread count for active conversation
                };
              });
            }
          }
        }
      } catch (error) {
        console.error('❌ Error refreshing conversations:', error);
      }

      // Then refresh messages if we have an active conversation
      if (activeConversation?.id) {
        try {
          console.log(`🔄 Aggressively refreshing messages for conversation: ${activeConversation.id}`);
          const timestamp = new Date().getTime();
          const response = await fetch(`/api/coaching/messages?conversationId=${activeConversation.id}&t=${timestamp}`, {
            cache: "no-store",
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              "Pragma": "no-cache",
              "Expires": "0"
            }
          });

          if (response.ok) {
            const data = await response.json();
            console.log('📋 Received fresh messages data:', data.length, 'messages');

            // Update messages state
            setMessages(data);

            // Scroll to bottom
            setTimeout(() => {
              if (messagesEndRef.current) {
                messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
              }
            }, 100);

            // Mark messages as read
            markMessagesAsRead(activeConversation.id);
          }
        } catch (error) {
          console.error('❌ Error refreshing messages:', error);
        }
      }
    };

    // Set up a custom event listener for manually sent messages
    const handleMessageSent = (event: Event) => {
      console.log('📤 Chat list: Message sent event received');
      toast({
        title: "Message sent",
        description: "Updating chat data...",
        duration: 2000,
      });

      try {
        // Get the event detail if available
        const detail = (event as CustomEvent)?.detail;
        const message = detail?.message;

        if (message) {
          console.log('📤 Message sent with details:', JSON.stringify(message));
          // Trigger an aggressive refresh with a delay to ensure the server has processed the message
          setTimeout(() => {
            refreshAllChatData('message-sent event');
          }, 300);
        } else {
          // If no message detail, just refresh everything
          console.log('📤 No message detail, triggering aggressive refresh');
          refreshAllChatData('message-sent event (no details)');
        }
      } catch (error) {
        console.error('❌ Error handling message-sent event:', error);
        // Fallback: refresh everything
        refreshAllChatData('message-sent event (error handler)');
      }
    };

    // Set up a custom event listener for messages received via WebSocket
    const handleMessageReceived = (event: Event) => {
      console.log('📥 Chat list: Message received via WebSocket', event.type);
      toast({
        title: "New message received",
        description: `Event type: ${event.type}`,
        duration: 2000,
      });

      try {
        // Get the event detail if available
        const detail = (event as CustomEvent).detail;
        console.log('📋 Message detail:', JSON.stringify(detail));

        // Extract the message from the event detail - handle different event formats
        let message;

        if (detail?.message) {
          // Standard message-received event
          message = detail.message;
        } else if (detail?.payload && event.type === 'socket-event') {
          // Socket event format
          message = detail.payload;
        } else if (detail?.message && event.type === 'force-refresh-chat') {
          // Force refresh event
          message = detail.message;
        }

        if (!message || !message.id || !message.conversationId) {
          console.log('📋 No valid message in event detail, triggering aggressive refresh');
          refreshAllChatData(`${event.type} event (invalid message)`);
          return;
        }

        console.log('📋 Received message:', JSON.stringify(message));

        // Check if the message is for the active conversation
        if (activeConversation?.id && message.conversationId === activeConversation.id) {
          console.log('📋 Message is for active conversation, updating messages');

          // Create a properly formatted message object to match our Message interface
          const formattedMessage: Message = {
            id: message.id,
            content: message.content,
            senderId: message.senderId,
            conversationId: message.conversationId,
            createdAt: message.createdAt || new Date().toISOString(),
            read: false,
            sender: {
              id: message.senderId,
              name: message.sender?.name || "User",
              avatarUrl: message.sender?.avatarUrl
            }
          };

          console.log('📋 Formatted message:', JSON.stringify(formattedMessage));

          // DIRECT STATE UPDATE - Force a state update with the new message
          setMessages(prevMessages => {
            // Check if the message is already in the array to avoid duplicates
            const messageExists = prevMessages.some(m => m.id === formattedMessage.id);
            if (messageExists) {
              console.log('📋 Message already exists in state, skipping update');
              return prevMessages;
            }

            console.log('📋 Adding new message to state');
            const newMessages = [...prevMessages, formattedMessage];

            // Scroll to bottom after state update
            setTimeout(() => {
              if (messagesEndRef.current) {
                messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
              }
            }, 100);

            return newMessages;
          });

          // Mark the message as read since we're in the conversation
          markMessagesAsRead(message.conversationId);
        }

        // Trigger an aggressive refresh with a delay to ensure the server has processed the message
        setTimeout(() => {
          refreshAllChatData(`${event.type} event`);
        }, 500);

        // Show a toast notification for new messages if they're not from the current user
        if (message.senderId !== session?.user?.id) {
          toast({
            title: 'New Message',
            description: `${message.content.substring(0, 30)}${message.content.length > 30 ? '...' : ''}`,
            duration: 5000,
          });
        }
      } catch (error) {
        console.error('❌ Error handling message-received event:', error);
        // Fallback: refresh everything
        refreshAllChatData(`${event.type} event (error handler)`);
      }
    };

    // Set up a custom event listener for force refresh events
    const handleForceRefresh = (_event: Event) => {
      console.log('🔥 Chat list: Force refresh event received');
      toast({
        title: "Force refresh triggered",
        description: "Updating all chat data...",
        duration: 2000,
      });

      // Trigger an aggressive refresh
      refreshAllChatData('force-refresh-chat event');
    };

    // Add event listeners - for ALL possible events
    window.addEventListener('message-sent', handleMessageSent);
    window.addEventListener('message-received', handleMessageReceived);
    window.addEventListener('socket-event', handleMessageReceived);
    window.addEventListener('force-refresh-chat', handleForceRefresh);

    // Also set up a polling mechanism as a fallback
    const pollingInterval = setInterval(() => {
      console.log('⏰ Polling for updates');
      refreshAllChatData('polling interval');
    }, 10000); // Poll every 10 seconds

    // Force an initial refresh
    setTimeout(() => {
      refreshAllChatData('initial setup');
    }, 1000);

    // Clean up
    return () => {
      console.log("🧹 Removing WebSocket event handlers");
      window.removeEventListener('message-sent', handleMessageSent);
      window.removeEventListener('message-received', handleMessageReceived);
      window.removeEventListener('socket-event', handleMessageReceived);
      window.removeEventListener('force-refresh-chat', handleForceRefresh);
      clearInterval(pollingInterval);
    };
  }, [session?.user?.id, activeConversation?.id]);

  // Track the last active conversation ID
  const lastActiveConversationRef = useRef<string | null>(null);

  // Load messages when active conversation changes - but only when it actually changes
  useEffect(() => {
    // Only fetch messages when active conversation changes to a different conversation
    if (activeConversation?.id && activeConversation.id !== lastActiveConversationRef.current) {
      console.log(`Active conversation changed to: ${activeConversation.id}`);
      lastActiveConversationRef.current = activeConversation.id;

      // Add a small delay to ensure everything is ready
      setTimeout(() => {
        fetchMessages(activeConversation.id);
      }, 100);
    }
  }, [activeConversation?.id, fetchMessages]);

  // Track the last joined conversation to prevent unnecessary join/leave operations
  const lastJoinedConversationRef = useRef<string | null>(null);

  // Handle joining/leaving conversations when the active conversation changes
  useEffect(() => {
    if (isConnected && activeConversation?.id) {
      // Only join if it's a different conversation
      if (activeConversation.id !== lastJoinedConversationRef.current) {
        console.log(`Chat page: Joining conversation ${activeConversation.id}`);

        // If we were in another conversation, leave it first
        if (lastJoinedConversationRef.current) {
          console.log(`Chat page: Leaving previous conversation ${lastJoinedConversationRef.current}`);
          leaveConversation(lastJoinedConversationRef.current);
        }

        // Join the new conversation
        joinConversation(activeConversation.id);
        lastJoinedConversationRef.current = activeConversation.id;
      }

      // Return cleanup function to leave the conversation when unmounting
      return () => {
        if (lastJoinedConversationRef.current) {
          console.log(`Chat page: Leaving conversation ${lastJoinedConversationRef.current} on unmount`);
          leaveConversation(lastJoinedConversationRef.current);
          lastJoinedConversationRef.current = null;
        }
      };
    }
  }, [isConnected, activeConversation?.id, joinConversation, leaveConversation]);

  // Ensure scroll to bottom when conversation changes or messages update
  useEffect(() => {
    // Only scroll if we have an active conversation and messages
    if (activeConversation && messages.length > 0) {
      // Use a debounced scroll to avoid multiple scrolls
      const timer = setTimeout(() => {
        console.log('Scrolling to bottom after conversation/messages change');
        scrollToBottom();
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [activeConversation?.id, messages.length, scrollToBottom]);

  // Get initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
  }

  return (
    <div className="container py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-foreground">Messages</h1>
      </div>

      <div className="chat-container grid grid-cols-1 md:grid-cols-3 gap-0 rounded-xl overflow-hidden shadow-lg border border-primary/20" style={{ height: "calc(100vh - 180px)" }}>
        {/* Conversations Sidebar */}
        <Card className="chat-sidebar md:col-span-1 flex flex-col h-full overflow-hidden border-0 rounded-none">
          <CardHeader className="chat-sidebar-header px-4 py-3 border-b flex-shrink-0 bg-background">
            <div className="chat-search relative w-full">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search messages..."
                className="pl-9 bg-gray-100 border-0 rounded-full text-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </CardHeader>
          <CardContent className="p-0 flex-1 overflow-hidden">
            <Tabs defaultValue="all" className="h-full flex flex-col">
              <TabsList className="chat-sidebar-tabs w-full grid grid-cols-2 rounded-none flex-shrink-0 bg-transparent p-0">
                <TabsTrigger value="all" className="chat-sidebar-tab">
                  General {filteredConversations.length > 0 && <Badge variant="outline" className="ml-2 bg-transparent text-inherit">{filteredConversations.length}</Badge>}
                </TabsTrigger>
                <TabsTrigger value="unread" className="chat-sidebar-tab">
                  Clients {clients.length > 0 && <Badge variant="outline" className="ml-2 bg-transparent text-inherit">{clients.length}</Badge>}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="flex-1 overflow-hidden">
                {loading ? (
                  <div className="flex flex-col justify-center items-center h-full gap-3">
                    <LoadingSpinner className="h-8 w-8 text-primary/60" />
                    <p className="text-sm text-muted-foreground">Loading conversations...</p>
                  </div>
                ) : filteredConversations.length > 0 ? (
                  <div className="h-full overflow-y-auto">
                    <div className="divide-y">
                      <div className="px-4 py-2 text-xs font-medium text-muted-foreground">
                        All Conversations
                      </div>
                      {unreadConversations.length > 0 && (
                        <>
                          <div className="px-4 py-2 text-xs font-medium text-muted-foreground">
                            Unread
                          </div>
                          {unreadConversations.map((conv) => (
                            <div
                              key={conv.id}
                              className={`chat-list-item ${
                                activeConversation?.id === conv.id
                                  ? "active"
                                  : ""
                              } ${conv.unreadCount > 0 ? "unread" : ""}`}
                              onClick={() => selectConversation(conv)}
                            >
                              <Avatar className="chat-list-item-avatar">
                                <AvatarImage src={conv.user.avatarUrl} />
                                <AvatarFallback className="bg-muted text-foreground font-medium">
                                  {getInitials(conv.user.name || "User")}
                                </AvatarFallback>
                              </Avatar>
                              <div className="chat-list-item-content">
                                <div className="chat-list-item-name">
                                  <p className="truncate">{conv.user.name}</p>
                                  <div className="flex items-center gap-2">
                                    {conv.unreadCount > 0 && (
                                      <div className="chat-list-item-badge">
                                        {conv.unreadCount}
                                      </div>
                                    )}
                                    <p className="chat-list-item-time">
                                      {formatDistanceToNow(new Date(conv.lastMessage?.createdAt || Date.now()), { addSuffix: false })}
                                    </p>
                                  </div>
                                </div>
                                {conv.lastMessage && (
                                  <p className="chat-list-item-message">
                                    {(() => {
                                      try {
                                        // Try to parse as JSON to check if it's a message with attachments
                                        const parsedContent = JSON.parse(conv.lastMessage.content);
                                        if (parsedContent.attachments && Array.isArray(parsedContent.attachments)) {
                                          const attachmentCount = parsedContent.attachments.length;
                                          const hasText = parsedContent.text && parsedContent.text.trim().length > 0;

                                          // If there's text, show it with attachment info
                                          if (hasText) {
                                            return `${parsedContent.text} ${attachmentCount > 1 ? `(${attachmentCount} attachments)` : '(1 attachment)'}`;
                                          }

                                          // If no text, just show attachment info
                                          const firstAttachment = parsedContent.attachments[0];
                                          if (firstAttachment.fileType === 'image') {
                                            return `📷 Image${attachmentCount > 1 ? ` (+${attachmentCount - 1} more)` : ''}`;
                                          } else {
                                            return `📎 ${firstAttachment.fileName}${attachmentCount > 1 ? ` (+${attachmentCount - 1} more)` : ''}`;
                                          }
                                        }
                                      } catch (e) {
                                        // Not JSON, treat as regular text
                                      }

                                      // Regular text message
                                      return conv.lastMessage.content;
                                    })()}
                                  </p>
                                )}

                                {!conv.lastMessage && (
                                  <p className="chat-list-item-message text-primary">
                                    Start a conversation
                                  </p>
                                )}
                              </div>
                            </div>
                          ))}
                          <Separator />
                        </>
                      )}
                      {readConversations.map((conv) => (
                        <div
                          key={conv.id}
                          className={`chat-list-item ${
                            activeConversation?.id === conv.id
                              ? "active"
                              : ""
                          } ${conv.unreadCount > 0 ? "unread" : ""}`}
                          onClick={() => selectConversation(conv)}
                        >
                          <Avatar className="chat-list-item-avatar">
                            <AvatarImage src={conv.user.avatarUrl} />
                            <AvatarFallback className="bg-muted text-foreground font-medium">
                              {getInitials(conv.user.name || "User")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="chat-list-item-content">
                            <div className="chat-list-item-name">
                              <p className="truncate">{conv.user.name}</p>
                              <div className="flex items-center gap-2">
                                {conv.unreadCount > 0 && (
                                  <div className="chat-list-item-badge">
                                    {conv.unreadCount}
                                  </div>
                                )}
                                <p className="chat-list-item-time">
                                  {formatDistanceToNow(new Date(conv.lastMessage?.createdAt || Date.now()), { addSuffix: false })}
                                </p>
                              </div>
                            </div>
                            {conv.lastMessage && (
                              <p className="chat-list-item-message">
                                {(() => {
                                  try {
                                    // Try to parse as JSON to check if it's a message with attachments
                                    const parsedContent = JSON.parse(conv.lastMessage.content);
                                    if (parsedContent.attachments && Array.isArray(parsedContent.attachments)) {
                                      const attachmentCount = parsedContent.attachments.length;
                                      const hasText = parsedContent.text && parsedContent.text.trim().length > 0;

                                      // If there's text, show it with attachment info
                                      if (hasText) {
                                        return `${parsedContent.text} ${attachmentCount > 1 ? `(${attachmentCount} attachments)` : '(1 attachment)'}`;
                                      }

                                      // If no text, just show attachment info
                                      const firstAttachment = parsedContent.attachments[0];
                                      if (firstAttachment.fileType === 'image') {
                                        return `📷 Image${attachmentCount > 1 ? ` (+${attachmentCount - 1} more)` : ''}`;
                                      } else {
                                        return `📎 ${firstAttachment.fileName}${attachmentCount > 1 ? ` (+${attachmentCount - 1} more)` : ''}`;
                                      }
                                    }
                                  } catch (e) {
                                    // Not JSON, treat as regular text
                                  }

                                  // Regular text message
                                  return conv.lastMessage.content;
                                })()}
                              </p>
                            )}

                            {!conv.lastMessage && (
                              <p className="chat-list-item-message text-primary">
                                Start a conversation
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full p-6">
                    <div className="bg-muted/20 p-6 rounded-xl border border-border flex flex-col items-center">
                      <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground text-center">No conversations found</p>
                      <p className="text-xs text-muted-foreground/70 text-center mt-1 mb-4">
                        Start a conversation with one of your clients
                      </p>

                      <Button
                        variant="outline"
                        className="mb-4"
                        onClick={() => {
                          fetchUnreadCount();
                          toast({
                            title: "Refreshed",
                            description: "Conversations refreshed",
                          });
                        }}
                      >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh
                      </Button>

                      {session?.user?.role === "trainer" && (
                        <Dialog open={isNewChatDialogOpen} onOpenChange={setIsNewChatDialogOpen}>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              className="mt-2"
                            >
                              <UserPlus className="h-4 w-4 mr-2" />
                              View All Clients
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Start a New Conversation</DialogTitle>
                              <DialogDescription>
                                Select a client to start a new conversation.
                              </DialogDescription>
                            </DialogHeader>
                            <div className="mb-4 relative">
                              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                              <Input
                                placeholder="Search clients..."
                                className="pl-9 bg-gray-100 border-0 rounded-full text-sm"
                                value={clientSearchQuery}
                                onChange={(e) => setClientSearchQuery(e.target.value)}
                              />
                            </div>
                            <div className="max-h-[60vh] overflow-y-auto">
                              {loadingClients ? (
                                <div className="flex flex-col items-center justify-center py-8">
                                  <LoadingSpinner className="h-8 w-8 text-primary/60 mb-2" />
                                  <p className="text-sm text-muted-foreground">Loading clients...</p>
                                </div>
                              ) : filteredClients.length > 0 ? (
                                <div className="divide-y">
                                  {filteredClients.map((client) => {
                                    // Check if there's an existing conversation with this client
                                    const existingConversation = conversations.find(
                                      (conv) => conv.user.id === client.id
                                    );

                                    return (
                                      <div
                                        key={client.id}
                                        className={`p-4 cursor-pointer transition-all duration-200 hover:bg-muted/30 ${
                                          existingConversation ? 'opacity-70' : ''
                                        }`}
                                        onClick={() => {
                                          if (existingConversation) {
                                            // If there's an existing conversation, select it
                                            selectConversation(existingConversation);
                                            setIsNewChatDialogOpen(false);
                                          } else {
                                            // Otherwise, create a new conversation
                                            createConversation(client.id);
                                          }
                                        }}
                                      >
                                        <div className="flex items-center gap-3">
                                          <Avatar className="h-10 w-10 border border-border">
                                            <AvatarImage src={client.avatarUrl} />
                                            <AvatarFallback className="bg-muted text-foreground font-medium">
                                              {getInitials(client.name || "User")}
                                            </AvatarFallback>
                                          </Avatar>
                                          <div className="flex-1 min-w-0">
                                            <div className="flex items-center justify-between">
                                              <p className="font-medium truncate">{client.name}</p>
                                              {existingConversation ? (
                                                <Badge variant="outline" className="text-xs">Chat Active</Badge>
                                              ) : (
                                                <Badge variant={creatingConversation ? "outline" : "secondary"} className="text-xs">
                                                  {creatingConversation ? (
                                                    <span className="flex items-center gap-1">
                                                      <LoadingSpinner className="h-3 w-3" />
                                                      Creating...
                                                    </span>
                                                  ) : "Start Chat"}
                                                </Badge>
                                              )}
                                            </div>
                                            <p className="text-sm text-muted-foreground truncate">
                                              {client.email}
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              ) : (
                                <div className="flex flex-col items-center justify-center py-8">
                                  <User className="h-12 w-12 text-muted-foreground mb-4" />
                                  <p className="text-muted-foreground text-center">No clients found</p>
                                  <p className="text-xs text-muted-foreground/70 text-center mt-1">
                                    You don't have any clients yet
                                  </p>
                                </div>
                              )}
                            </div>
                          </DialogContent>
                        </Dialog>
                      )}
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="unread" className="flex-1 overflow-hidden">
                {loadingClients ? (
                  <div className="flex flex-col justify-center items-center h-full gap-3">
                    <LoadingSpinner className="h-8 w-8 text-primary/60" />
                    <p className="text-sm text-muted-foreground">Loading clients...</p>
                  </div>
                ) : clients.length > 0 ? (
                  <div className="h-full overflow-y-auto">
                    <div className="divide-y">
                      <div className="px-4 py-2 text-xs font-medium text-muted-foreground">
                        All Clients
                      </div>
                      {clients.map((client) => {
                        // Check if there's an existing conversation with this client
                        const existingConversation = conversations.find(
                          (conv) => conv.user.id === client.id
                        );

                        return (
                          <div
                            key={client.id}
                            className={`p-4 cursor-pointer transition-all duration-200 hover:bg-muted/30 border-l-4 border-transparent`}
                            onClick={() => {
                              if (existingConversation) {
                                // If there's an existing conversation, select it
                                selectConversation(existingConversation);
                              } else {
                                // Otherwise, create a new conversation
                                createConversation(client.id);
                              }
                            }}
                          >
                            <div className="flex items-center gap-3">
                              <Avatar className="h-10 w-10 border border-border">
                                <AvatarImage src={client.avatarUrl} />
                                <AvatarFallback className="bg-muted text-foreground font-medium">
                                  {getInitials(client.name || "User")}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <p className="font-medium truncate">{client.name}</p>
                                  {existingConversation ? (
                                    <Badge variant="outline" className="text-xs">Chat Active</Badge>
                                  ) : (
                                    <Badge variant="secondary" className="text-xs">Start Chat</Badge>
                                  )}
                                </div>
                                <p className="text-sm text-muted-foreground truncate">
                                  {client.email}
                                </p>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full p-6">
                    <div className="bg-muted/20 p-6 rounded-xl border border-border flex flex-col items-center">
                      <User className="h-12 w-12 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground text-center">No clients found</p>
                      <p className="text-xs text-muted-foreground/70 text-center mt-1">
                        You don't have any clients yet
                      </p>
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Chat Area */}
        <Card className="md:col-span-2 flex flex-col h-full overflow-hidden border-0 shadow-md">
          {activeConversation ? (
            <>
              <CardHeader className="px-5 py-4 border-b flex-shrink-0 bg-background">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="md:hidden rounded-full hover:bg-muted"
                      onClick={() => setActiveConversation(null)}
                    >
                      <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <Avatar className="h-12 w-12 border border-border">
                      <AvatarImage src={activeConversation.user.avatarUrl} />
                      <AvatarFallback className="bg-muted text-foreground font-medium">
                        {getInitials(activeConversation.user.name || "User")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg font-medium">{activeConversation.user.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">
                        {activeConversation.lastMessage ?
                          `${formatDistanceToNow(new Date(activeConversation.lastMessage.createdAt), { addSuffix: true })}` :
                          'Start a conversation'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon" className="rounded-full h-8 w-8">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <div className="flex-1 overflow-hidden relative" style={{ height: "calc(100% - 140px)" }}>
                {updatingMessages && (
                  <div className="absolute top-2 left-1/2 transform -translate-x-1/2 z-10 bg-background px-3 py-1 rounded-full text-xs text-muted-foreground flex items-center gap-1 shadow-sm border border-border">
                    <LoadingSpinner className="h-3 w-3" />
                    <span>Updating...</span>
                  </div>
                )}
                <div className="chat-messages h-full px-4 py-2 overflow-y-auto">
                  <div className="space-y-6 pb-4">
                    {messages.map((message) => {
                      const isCurrentUser = message.senderId === session?.user?.id
                      const isOptimistic = message.isOptimistic

                      return (
                        <div
                          key={message.id}
                          className={`message ${isCurrentUser ? "outgoing" : "incoming"} ${
                            isOptimistic ? "opacity-80" : ""
                          }`}
                        >
                          <div className={`message-bubble ${isOptimistic ? "optimistic" : ""}`}>
                            {!isCurrentUser && (
                              <Avatar className="h-8 w-8 message-avatar">
                                <AvatarImage src={message.sender.avatarUrl} />
                                <AvatarFallback className="bg-muted text-foreground font-medium">
                                  {getInitials(message.sender.name || "User")}
                                </AvatarFallback>
                              </Avatar>
                            )}

                            <div className="message-content">
                              {(() => {
                                // Check if content is JSON with attachments
                                try {
                                  const parsedContent = JSON.parse(message.content);
                                  if (parsedContent.attachments && Array.isArray(parsedContent.attachments)) {
                                    return (
                                      <>
                                        {parsedContent.text && (
                                          <p className="text-sm whitespace-pre-wrap break-words leading-relaxed mb-2">
                                            {parsedContent.text}
                                          </p>
                                        )}
                                        <ChatMessageAttachments
                                          attachments={parsedContent.attachments}
                                          className="mt-2"
                                        />
                                      </>
                                    );
                                  }
                                } catch (e) {
                                  // Not JSON, treat as regular text
                                }

                                // Regular text message
                                return message.content ? (
                                  <p className="text-sm whitespace-pre-wrap break-words leading-relaxed">
                                    {message.content}
                                  </p>
                                ) : null;
                              })()}

                              {/* Handle optimistic attachments */}
                              {message.attachments && message.attachments.length > 0 && (
                                <ChatMessageAttachments attachments={message.attachments} className="mt-2" />
                              )}
                            </div>

                            {isCurrentUser && (
                              <Avatar className="h-8 w-8 message-avatar">
                                <AvatarImage src={session?.user?.image || undefined} />
                                <AvatarFallback className="bg-muted text-foreground font-medium">
                                  {session?.user?.name ? getInitials(session.user.name) : "Me"}
                                </AvatarFallback>
                              </Avatar>
                            )}
                          </div>
                          <div className="message-time">
                            {isOptimistic ? (
                              <span className="flex items-center gap-1">
                                <span className="w-2 h-2 rounded-full border-2 border-t-transparent border-primary/50 animate-spin"></span>
                                <span>Sending...</span>
                              </span>
                            ) : (
                              <>
                                {formatDistanceToNow(new Date(message.createdAt), {
                                  addSuffix: false,
                                })}
                                {isCurrentUser && message.read && (
                                  <span className="ml-1">• Read</span>
                                )}
                              </>
                            )}
                          </div>
                        </div>
                      )
                    })}
                    <div ref={messagesEndRef} />
                  </div>
                </div>
              </div>

              <div className="chat-input">
                <ChatMediaInput
                  onSendMessage={sendMessage}
                  disabled={sendingMessage}
                  placeholder="Type your message..."
                  className="w-full"
                />
              </div>
            </>
          ) : (
            <div className="chat-empty">
              <MessageSquare className="h-16 w-16 text-blue-500/60" strokeWidth={1.5} />
              <h3 className="text-xl font-semibold mt-4">Select a conversation</h3>
              <p className="mt-2 text-center max-w-md text-gray-500 dark:text-gray-400 text-sm">
                Choose a conversation from the sidebar or start a new one to begin chatting with your clients.
              </p>

              {session?.user?.role === "trainer" && (
                <Dialog open={isNewChatDialogOpen} onOpenChange={setIsNewChatDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      className="mt-6 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-full shadow-sm font-medium"
                    >
                      Start New Conversation
                      <PlusCircle className="h-5 w-5 ml-2" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Start a New Conversation</DialogTitle>
                      <DialogDescription>
                        Select a client to start a new conversation.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="mb-4 relative">
                      <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search clients..."
                        className="pl-9 bg-gray-100 border-0 rounded-full text-sm"
                        value={clientSearchQuery}
                        onChange={(e) => setClientSearchQuery(e.target.value)}
                      />
                    </div>
                    <div className="max-h-[60vh] overflow-y-auto">
                      {loadingClients ? (
                        <div className="flex flex-col items-center justify-center py-8">
                          <LoadingSpinner className="h-8 w-8 text-primary/60 mb-2" />
                          <p className="text-sm text-muted-foreground">Loading clients...</p>
                        </div>
                      ) : filteredClients.length > 0 ? (
                        <div className="divide-y">
                          {filteredClients.map((client) => {
                            // Check if there's an existing conversation with this client
                            const existingConversation = conversations.find(
                              (conv) => conv.user.id === client.id
                            );

                            return (
                              <div
                                key={client.id}
                                className={`p-4 cursor-pointer transition-all duration-200 hover:bg-muted/30 ${
                                  existingConversation ? 'opacity-70' : ''
                                }`}
                                onClick={() => {
                                  if (existingConversation) {
                                    // If there's an existing conversation, select it
                                    selectConversation(existingConversation);
                                    setIsNewChatDialogOpen(false);
                                  } else {
                                    // Otherwise, create a new conversation
                                    createConversation(client.id);
                                  }
                                }}
                              >
                                <div className="flex items-center gap-3">
                                  <Avatar className="h-10 w-10 border border-border">
                                    <AvatarImage src={client.avatarUrl} />
                                    <AvatarFallback className="bg-muted text-foreground font-medium">
                                      {getInitials(client.name || "User")}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                      <p className="font-medium truncate">{client.name}</p>
                                      {existingConversation ? (
                                        <Badge variant="outline" className="text-xs">Chat Active</Badge>
                                      ) : (
                                        <Badge variant={creatingConversation ? "outline" : "secondary"} className="text-xs">
                                          {creatingConversation ? (
                                            <span className="flex items-center gap-1">
                                              <LoadingSpinner className="h-3 w-3" />
                                              Creating...
                                            </span>
                                          ) : "Start Chat"}
                                        </Badge>
                                      )}
                                    </div>
                                    <p className="text-sm text-muted-foreground truncate">
                                      {client.email}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center py-8">
                          <User className="h-12 w-12 text-muted-foreground mb-4" />
                          <p className="text-muted-foreground text-center">No clients found</p>
                          <p className="text-xs text-muted-foreground/70 text-center mt-1">
                            You don't have any clients yet
                          </p>
                        </div>
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              )}

              <div className="w-full max-w-xs mt-8">
                <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/20 rounded-lg border border-gray-200 dark:border-gray-700/30 shadow-sm">
                  <Avatar className="h-10 w-10 border border-gray-200 dark:border-gray-700">
                    <AvatarFallback className="bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 font-medium">
                      ?
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="h-2.5 w-24 bg-gray-200 dark:bg-gray-700 rounded-full mb-2"></div>
                    <div className="h-2 w-32 bg-gray-100 dark:bg-gray-800 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  )
}
