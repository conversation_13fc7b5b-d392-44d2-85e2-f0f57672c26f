import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm, UseFormProps, UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

/**
 * A custom hook to create a form with Zod validation
 * @param schema The Zod schema for validation
 * @param options Optional config for react-hook-form
 * @returns The form methods from react-hook-form
 */
export function useZodForm<TSchema extends z.ZodType<any, any, any>>(
  schema: TSchema,
  options: Omit<UseFormProps<z.infer<TSchema>>, 'resolver'> = {}
): UseFormReturn<z.infer<TSchema>> {
  return useForm<z.infer<TSchema>>({
    resolver: zodResolver(schema),
    ...options,
  });
}

/**
 * A higher-level hook to handle form submission with server-side validation
 * @param schema The Zod schema for validation
 * @param onSubmit The function to call when the form is submitted
 * @param options Optional config for react-hook-form
 * @returns The form methods and submission state
 */
export function useZodFormWithSubmit<TSchema extends z.ZodType<any, any, any>>(
  schema: TSchema,
  onSubmit: (data: z.infer<TSchema>) => Promise<void>,
  options: Omit<UseFormProps<z.infer<TSchema>>, 'resolver'> = {}
) {
  const form = useZodForm(schema, options);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);

  const handleSubmit = async (data: z.infer<TSchema>) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(false);
    
    try {
      await onSubmit(data);
      setSuccess(true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    ...form,
    onSubmit: form.handleSubmit(handleSubmit),
    isSubmitting,
    error,
    success,
  };
} 