/** @type {import('next').NextConfig} */
const userConfig = {
  // Disable static optimization for API routes
  output: 'standalone',
  
  // Configure webpack to handle dynamic imports
  webpack: (config, { isServer }) => {
    // Ignore @mapbox/node-pre-gyp and other problematic modules
    config.resolve.alias = {
      ...config.resolve.alias,
      '@mapbox/node-pre-gyp': false,
    }

    config.module.rules.push({
      test: /\.html$/,
      use: 'ignore-loader'
    })

    return config
  },
  
  // Suppress specific build warnings
  onDemandEntries: {
    // Period (in ms) where the server will keep pages in the buffer
    maxInactiveAge: 25 * 1000,
    // Number of pages that should be kept simultaneously without being disposed
    pagesBufferLength: 5,
  },
}

export default userConfig;
