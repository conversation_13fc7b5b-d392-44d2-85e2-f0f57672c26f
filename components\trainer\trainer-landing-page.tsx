"use client"

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>
} from "lucide-react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input" // Keep Input for modals
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
// Import shared types
import {
  TrainerTheme, 
  SubscriptionTier, 
  DigitalProduct, 
  CoachingService, 
  Trainer,
  SelectedItem
} from "@/types/trainer"
import { CoachingSection } from "./coaching-section"
import { ContactSection } from "./contact-section"
import { ProgramsSection } from "./programs-section"
import { SubscriptionSection } from "./subscription-section"
import { TrainerHeader } from "./trainer-header"

interface TrainerLandingPageProps {
  trainer: Trainer
  theme: TrainerTheme
  subscriptionTiers: SubscriptionTier[]
  digitalProducts: DigitalProduct[]
  coachingServices: CoachingService[]
}

export function TrainerLandingPage({ 
  trainer, 
  theme, 
  subscriptionTiers,
  digitalProducts,
  coachingServices = []
}: TrainerLandingPageProps) {
  const router = useRouter()
  const { toast } = useToast()
  const { data: session, status: sessionStatus } = useSession() // Use the session hook
  
  const [activeTab, setActiveTab] = useState<string>("subscriptions")
  
  // --- Auth State & Modal --- 
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login')
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [name, setName] = useState("")
  const [processingAuth, setProcessingAuth] = useState(false)

  // --- Purchase State & Modal --- 
  const [selectedItem, setSelectedItem] = useState<SelectedItem | null>(null)
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [purchaseComplete, setPurchaseComplete] = useState(false)
  
  // --- Handlers --- 
  const switchToContactTab = () => setActiveTab("contact")

  const handleSelectItem = (item: SelectedItem) => {
    setSelectedItem(item)
    setShowPurchaseModal(true)
  }

  const handleSelectSubscription = (tier: SubscriptionTier) => {
    handleSelectItem({ id: tier.id, type: 'subscription', name: tier.name, price: tier.price })
  }
  
  const handleSelectProduct = (product: DigitalProduct) => {
    handleSelectItem({ id: product.id, type: 'product', name: product.title, price: product.price })
  }
  
  // Handle purchase confirmation (simulated)
  const handlePurchase = () => {
    setProcessingAuth(true)
    // TODO: Add actual payment processing logic here (e.g., Stripe)
    setTimeout(() => {
      toast({ title: "Purchase Successful", description: `Purchased: ${selectedItem?.name}` })
      setPurchaseComplete(true)
      setProcessingAuth(false)
      // TODO: Record purchase in DB, associate with user
    }, 1500)
  }
  
  // Handle clicking the main purchase button in the modal
  const handleInitiatePurchase = () => {
    if (sessionStatus === "authenticated") {
      // If already authenticated, proceed directly to purchase (or payment step)
      handlePurchase(); 
    } else if (sessionStatus === "unauthenticated") {
      // If unauthenticated, show the auth modal
      setShowAuthModal(true);
      setShowPurchaseModal(false); // Hide purchase modal while showing auth
    } 
    // Optionally handle 'loading' state if needed (e.g., disable button)
  }
  
  // Handle auth (login/register) (simulated)
  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    setProcessingAuth(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      // TODO: Replace simulation with actual NextAuth calls (signIn/signUp)
      toast({ title: authMode === 'login' ? "Login Successful" : "Registration Successful" })
      setShowAuthModal(false)
      setEmail("")
      setPassword("")
      setName("")
      // After successful auth, reopen purchase modal to continue
      if (selectedItem) {
        setShowPurchaseModal(true)
        // Optionally directly call handlePurchase() if payment details were already collected
        // or just show the modal again for payment.
        // For now, just showing modal again.
      }
    } catch (error) {
      toast({ title: "Authentication Error", variant: "destructive" })
    } finally {
      setProcessingAuth(false)
    }
  }

  // --- Styles --- 
  const customStyles = {
    "--theme-primary": theme.primaryColor,
    "--theme-secondary": theme.secondaryColor,
    "--theme-font": theme.fontFamily,
  } as React.CSSProperties
  
  // --- Render --- 
  return (
    <div className="flex flex-col min-h-screen" style={customStyles}>
      {/* Custom CSS */}
      <style jsx global>{`
        :root {
          --primary: ${theme.primaryColor};
          --primary-foreground: white;
          --secondary: ${theme.secondaryColor};
        }
        body { font-family: ${theme.fontFamily}, system-ui, sans-serif; }
        .custom-gradient { background: linear-gradient(135deg, ${theme.primaryColor}, ${theme.secondaryColor}); }
        .custom-button { background-color: var(--primary); color: white; transition: all 0.3s ease; }
        .custom-button:hover { background-color: var(--secondary); transform: translateY(-3px); box-shadow: 0 10px 20px rgba(0,0,0,0.1); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-5px); box-shadow: 0 15px 30px rgba(0,0,0,0.1); }
        .feature-icon { transition: all 0.2s ease; }
        .feature-item:hover .feature-icon { transform: scale(1.2); color: var(--primary); }
      `}</style>
      
      <TrainerHeader trainer={trainer} theme={theme} />
      
      <main className="flex-grow bg-background">
        <div className="container mx-auto px-4 py-12">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full max-w-md mx-auto grid-cols-4 mb-8">
              <TabsTrigger value="subscriptions">Memberships</TabsTrigger>
              <TabsTrigger value="coaching">1:1 Coaching</TabsTrigger>
              <TabsTrigger value="programs">Programs</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
            </TabsList>
            
            <SubscriptionSection 
              subscriptionTiers={subscriptionTiers} 
              onSelectSubscription={handleSelectSubscription} 
            />
            <CoachingSection 
              coachingServices={coachingServices} 
              onSwitchToContact={switchToContactTab} 
            />
            <ProgramsSection 
              digitalProducts={digitalProducts} 
              onSelectProduct={handleSelectProduct} 
            />
            <ContactSection 
              trainer={trainer} // Pass only necessary trainer info if possible
            />
          </Tabs>
        </div>
      </main>
      
      {/* Purchase Modal */}
      <Dialog open={showPurchaseModal} onOpenChange={(open) => { 
        setShowPurchaseModal(open); 
        if (!open) setPurchaseComplete(false); // Reset success state on close
      }}>
        <DialogContent className="sm:max-w-md">
          {purchaseComplete ? (
            // Purchase Success View
            <div className="py-6 text-center">
              <div className="mx-auto w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
                <CheckCheck className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold mb-2">Purchase Complete!</h2>
              <p className="text-gray-500 mb-6">Thank you for your purchase</p>
              <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                <h4 className="font-medium text-lg mb-2">{selectedItem?.name}</h4>
                <p className="text-sm text-gray-500 mb-1">
                  {selectedItem?.type === 'subscription' ? 'Monthly Membership' : 
                   selectedItem?.type === 'coaching' ? 'Coaching Service' : 'Digital Product'}
                </p>
                <p className="font-medium">${selectedItem?.price}</p>
              </div>
              <div className="space-y-4">
                <div className="bg-blue-50 rounded-lg p-4 text-left">
                  <h4 className="font-medium text-blue-800 flex items-center">
                    <Lock className="h-4 w-4 mr-2" /> How to Access Your Purchase
                  </h4>
                  <ul className="text-sm text-blue-700 mt-2 space-y-2">
                    <li className="flex items-start">
                      <span className="h-5 w-5 inline-flex items-center justify-center rounded-full bg-blue-100 text-blue-800 text-xs mr-2 mt-0.5">1</span>
                      <span>Log in to your account</span>
                    </li>
                    <li className="flex items-start">
                      <span className="h-5 w-5 inline-flex items-center justify-center rounded-full bg-blue-100 text-blue-800 text-xs mr-2 mt-0.5">2</span>
                      <span>Visit "My Purchases" in your dashboard</span>
                    </li>
                    <li className="flex items-start">
                      <span className="h-5 w-5 inline-flex items-center justify-center rounded-full bg-blue-100 text-blue-800 text-xs mr-2 mt-0.5">3</span>
                      <span>Access your content anytime</span>
                    </li>
                  </ul>
                </div>
                <div className="flex flex-col gap-3">
                  <Button
                    onClick={() => { router.push('/dashboard') }}
                    className="w-full custom-button"
                  >
                    Go to My Dashboard
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => { setShowPurchaseModal(false); setPurchaseComplete(false); setSelectedItem(null); }}
                  >
                    Continue Browsing
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            // Purchase Form View
            <>
              <DialogHeader>
                <DialogTitle className="text-xl">Complete Your Purchase</DialogTitle>
                <DialogDescription>Purchase &quot;{selectedItem?.name}&quot; to get instant access</DialogDescription>
              </DialogHeader>
              <div className="space-y-5 py-4">
                <div className="flex justify-between items-center bg-gray-50 p-4 rounded-lg">
                  <div>
                    <h3 className="font-semibold text-gray-900">{selectedItem?.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {selectedItem?.type === 'subscription' ? 'Monthly Membership' : 
                       selectedItem?.type === 'coaching' ? 'Coaching Service' : 'Digital Product'}
                    </p>
                  </div>
                  <div className="text-lg font-bold">${selectedItem?.price}</div>
                </div>
                <div className="bg-primary/5 rounded-lg p-4 flex items-start">
                  <Lock className="h-5 w-5 text-primary mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Account Required</p>
                    <p className="text-xs text-gray-500 mt-1">Log in or create an account to complete purchase.</p>
                  </div>
                </div>
                <Separator />
                {/* TODO: Replace with actual payment integration (e.g., Stripe Elements) */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="payment-method" className="font-medium">Payment Method</Label>
                    <Select defaultValue="card">
                      <SelectTrigger className="w-full"><SelectValue placeholder="Select method" /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="card">Credit/Debit Card</SelectItem>
                        <SelectItem value="paypal">PayPal</SelectItem>
                        <SelectItem value="apple">Apple Pay</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="card" className="font-medium">Card Information</Label>
                    <Input id="card" placeholder="Card number" className="mt-1 mb-2" />
                    <div className="grid grid-cols-2 gap-2">
                      <Input placeholder="MM/YY" />
                      <Input placeholder="CVC" />
                    </div>
                  </div>
                </div>
                <Button
                  onClick={handleInitiatePurchase}
                  className="w-full custom-button py-6 text-base font-semibold"
                  disabled={processingAuth || sessionStatus === 'loading'}
                >
                  {processingAuth ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                      Processing Payment...
                    </div>
                  ) : "Complete Purchase"}
                </Button>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Auth Modal for Login/Register */}
      <Dialog open={showAuthModal} onOpenChange={setShowAuthModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl">{authMode === 'login' ? 'Log In to Complete Purchase' : 'Create an Account'}</DialogTitle>
            <DialogDescription>
              {authMode === 'login' ? 'Log in to continue purchase' : 'Create account to store purchases'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAuth} className="space-y-4 py-4">
            {authMode === 'register' && (
              <div className="space-y-2">
                <Label htmlFor="auth-name" className="font-medium">Full Name</Label>
                <Input id="auth-name" value={name} onChange={(e) => setName(e.target.value)} placeholder="Your name" required className="border-gray-300 focus:border-primary/70"/>
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="auth-email" className="font-medium">Email</Label>
              <Input id="auth-email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} placeholder="Your email" required className="border-gray-300 focus:border-primary/70"/>
            </div>
            <div className="space-y-2">
              <Label htmlFor="auth-password" className="font-medium">Password</Label>
              <Input id="auth-password" type="password" value={password} onChange={(e) => setPassword(e.target.value)} placeholder="Password" required className="border-gray-300 focus:border-primary/70"/>
            </div>
            {selectedItem && (
              <div className="bg-gray-50 p-3 rounded-md mt-4">
                <p className="text-sm font-medium text-gray-700">Purchasing: {selectedItem.name}</p>
                <p className="text-sm text-gray-500">${selectedItem.price}</p>
              </div>
            )}
            <Button type="submit" className="w-full custom-button py-5 text-base font-semibold" disabled={processingAuth}>
              {processingAuth ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                  Processing...
                </div>
              ) : authMode === 'login' ? 'Log In & Continue' : 'Create Account & Continue'}
            </Button>
            <div className="text-center text-sm">
              <button type="button" className="text-primary hover:underline font-medium" onClick={() => setAuthMode(authMode === 'login' ? 'register' : 'login')}>
                {authMode === 'login' ? 'Don&apos;t have an account? Sign up' : 'Already have an account? Log in'}
              </button>
            </div>
            <Separator />
            <div className="flex flex-col space-y-3">
              <p className="text-xs text-center text-gray-500 mb-1">Or continue with</p>
              {/* TODO: Implement OAuth handlers */}
              <Button variant="outline" type="button" className="w-full">
                <svg className="mr-2 h-4 w-4" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="google" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 488 512"><path fill="currentColor" d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"></path></svg>
                Google
              </Button>
              <Button variant="outline" type="button" className="w-full">
                <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" fill="currentColor"><path d="M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z"/></svg>
                Apple
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
} 