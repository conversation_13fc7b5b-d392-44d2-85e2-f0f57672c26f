import { NextRequest, NextResponse } from "next/server"
import { getServerAuthSession } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// Get progress for a specific product
export async function GET(request: NextRequest) {
  try {
    const session = await getServerAuthSession()

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('productId')

    if (!productId) {
      return new NextResponse("Product ID is required", { status: 400 })
    }

    // Get progress for the specific product
    let progress = null

    try {
      // Try to get progress from the database if the model exists
      progress = await prisma.productProgress.findUnique({
        where: {
          userId_productId: {
            userId: session.user.id,
            productId
          }
        }
      })
    } catch (err) {
      // If the model doesn't exist, check localStorage in development
      console.log('ProductProgress model not available, using fallback')
    }

    if (!progress) {
      return NextResponse.json({
        progress: {
          productId,
          progressPercentage: 0,
          completedSections: null
        }
      })
    }

    return NextResponse.json({ progress })
  } catch (error) {
    console.error("Error fetching product progress:", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

// Update progress for a product
export async function POST(request: NextRequest) {
  try {
    const session = await getServerAuthSession()

    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const body = await request.json()
    const { productId, progressPercentage, completedSections } = body

    if (!productId) {
      return new NextResponse("Product ID is required", { status: 400 })
    }

    if (typeof progressPercentage !== 'number' || progressPercentage < 0 || progressPercentage > 100) {
      return new NextResponse("Progress percentage must be a number between 0 and 100", { status: 400 })
    }

    // Check if the user has purchased this product
    const hasAccess = await prisma.orderItem.findFirst({
      where: {
        productId,
        order: {
          userId: session.user.id,
          status: "completed"
        }
      }
    })

    // Check if this is a dev premium user
    const cookies = request.headers.get('cookie') || ''
    const isPremiumDev = process.env.NODE_ENV === 'development' &&
                        cookies.includes('dev_premium_status=true')

    if (!hasAccess && !isPremiumDev) {
      return new NextResponse("You don't have access to this product", { status: 403 })
    }

    // Update or create progress record
    let progress = null

    try {
      // Try to update progress in the database if the model exists
      progress = await prisma.productProgress.upsert({
        where: {
          userId_productId: {
            userId: session.user.id,
            productId
          }
        },
        update: {
          progressPercentage,
          completedSections: completedSections || undefined,
          lastAccessedAt: new Date()
        },
        create: {
          userId: session.user.id,
          productId,
          progressPercentage,
          completedSections: completedSections || undefined
        }
      })
    } catch (err) {
      // If the model doesn't exist, use a fallback
      console.log('ProductProgress model not available, using fallback')
      progress = {
        userId: session.user.id,
        productId,
        progressPercentage,
        completedSections: completedSections || null,
        lastAccessedAt: new Date()
      }
    }

    return NextResponse.json({ progress })
  } catch (error) {
    console.error("Error updating product progress:", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
