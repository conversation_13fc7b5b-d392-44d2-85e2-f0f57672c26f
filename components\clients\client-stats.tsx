import { Users, <PERSON><PERSON><PERSON>, TrendingUp } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"

interface ClientStatsProps {
  clients: any[]
}

export function ClientStats({ clients }: ClientStatsProps) {
  // Calculate stats
  const totalClients = clients.length
  const activeClients = clients.filter((client) => client.status === "active").length

  // Calculate monthly recurring revenue
  const monthlyRevenue = clients
    .filter((client) => client.status === "active")
    .reduce((total, client) => total + (client.subscription_tiers?.price || 0), 0)

  return (
    <div className="grid gap-4 md:grid-cols-3">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalClients}</div>
          <p className="text-xs text-muted-foreground">{activeClients} active clients</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
          <CreditCard className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${monthlyRevenue.toFixed(2)}</div>
          <p className="text-xs text-muted-foreground">From active subscriptions</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Client Retention</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {totalClients > 0 ? Math.round((activeClients / totalClients) * 100) : 0}%
          </div>
          <p className="text-xs text-muted-foreground">Active vs. total clients</p>
        </CardContent>
      </Card>
    </div>
  )
}

