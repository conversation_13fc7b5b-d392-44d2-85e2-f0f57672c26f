'use client';

import { useEffect } from 'react';
import { useSession } from 'next-auth/react';

export default function SSEListener() {
  const { data: session } = useSession();

  useEffect(() => {
    if (!session?.user?.id) {
      console.log('No user session, not connecting to SSE');
      return;
    }

    console.log(`Setting up SSE for user ${session.user.id}`);

    let eventSource: EventSource | null = null;
    let connectionAttempts = 0;
    const maxConnectionAttempts = 5;

    const connectSSE = () => {
      if (connectionAttempts >= maxConnectionAttempts) {
        console.log(`Maximum connection attempts (${maxConnectionAttempts}) reached, giving up`);
        return;
      }

      connectionAttempts++;
      console.log(`SSE connection attempt ${connectionAttempts} for user ${session.user.id}`);

      // Close any existing connection
      if (eventSource) {
        console.log('Closing existing SSE connection');
        eventSource.close();
      }

      try {
        // Create a new SSE connection
        console.log(`Creating new SSE connection for user ${session.user.id}`);
        eventSource = new EventSource('/api/sse');

        // Handle connection open
        eventSource.onopen = () => {
          console.log(`SSE connection established for user ${session.user.id}`);
          connectionAttempts = 0; // Reset connection attempts on successful connection

          // Force a test message to verify the connection
          setTimeout(() => {
            console.log('Sending test message to verify SSE connection');
            window.dispatchEvent(new CustomEvent('message-received', {
              detail: { type: 'test', message: 'Connection test' }
            }));
          }, 2000);
        };

        // Handle messages
        eventSource.onmessage = (event) => {
          try {
            console.log(`SSE message received for user ${session.user.id}:`, event.data);

            if (event.data === 'connected') {
              console.log(`SSE connected successfully for user ${session.user.id}`);
              return;
            }

            const data = JSON.parse(event.data);
            console.log('Parsed SSE data:', data);

            if (data.type === 'new-message') {
              console.log('New message received via SSE, dispatching message-received event');

              // Dispatch a custom event that other components can listen for
              window.dispatchEvent(new CustomEvent('message-received', {
                detail: data
              }));

              // Also try dispatching a message-sent event as a fallback
              console.log('Also dispatching message-sent event as fallback');
              window.dispatchEvent(new CustomEvent('message-sent'));
            }
          } catch (error) {
            console.error(`Error processing SSE message for user ${session.user.id}:`, error);
          }
        };

        // Handle errors
        eventSource.onerror = (error) => {
          console.error(`SSE connection error for user ${session.user.id}:`, error);

          // Close the connection on error
          eventSource?.close();

          // Try to reconnect after a delay
          console.log(`Will attempt to reconnect in 5 seconds (attempt ${connectionAttempts}/${maxConnectionAttempts})`);
          setTimeout(connectSSE, 5000);
        };
      } catch (error) {
        console.error(`Error creating SSE connection for user ${session.user.id}:`, error);
        setTimeout(connectSSE, 5000);
      }
    };

    // Initial connection
    connectSSE();

    // Set up a periodic check to ensure the connection is still active
    const connectionCheckInterval = setInterval(() => {
      if (!eventSource || eventSource.readyState === EventSource.CLOSED) {
        console.log('SSE connection check: Connection is closed, reconnecting...');
        connectSSE();
      } else {
        console.log(`SSE connection check: Connection is ${eventSource.readyState === EventSource.OPEN ? 'open' : 'connecting'}`);
      }
    }, 30000); // Check every 30 seconds

    // Clean up on unmount
    return () => {
      if (eventSource) {
        console.log(`Closing SSE connection for user ${session.user.id}`);
        eventSource.close();
      }

      clearInterval(connectionCheckInterval);
    };
  }, [session?.user?.id]);

  // This component doesn't render anything
  return null;
}
