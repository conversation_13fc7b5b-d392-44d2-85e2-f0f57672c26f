import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { 
  createTestPremiumClient,
  cleanupTestData,
  disconnectDatabase
} from './helpers/user-roles'

// Create a Prisma client for testing
const prisma = new PrismaClient()

describe('Premium Client Workflows', () => {
  let premiumClient: any
  let trainerId: string
  let trainingPlan: any
  let dietPlan: any
  
  beforeAll(async () => {
    // Create a premium client (with trainer and subscription)
    const result = await createTestPremiumClient()
    premiumClient = {
      user: result.user,
      clientProfile: result.clientProfile
    }
    trainerId = result.trainerId
    
    // Create a training plan
    trainingPlan = await prisma.trainingPlan.create({
      data: {
        title: 'Premium Training Plan',
        description: 'A training plan for premium clients',
        type: 'strength',
        difficulty: 'intermediate',
        trainerId
      }
    })
    
    // Create a diet plan
    dietPlan = await prisma.dietPlan.create({
      data: {
        title: 'Premium Diet Plan',
        description: 'A diet plan for premium clients',
        trainerId
      }
    })
    
    // Assign training plan to client
    await prisma.clientPlan.create({
      data: {
        clientProfileId: premiumClient.clientProfile.id,
        trainingPlanId: trainingPlan.id,
        assignedAt: new Date()
      }
    })
    
    // Assign diet plan to client
    await prisma.clientDietPlan.create({
      data: {
        clientProfileId: premiumClient.clientProfile.id,
        dietPlanId: dietPlan.id,
        assignedAt: new Date()
      }
    })
  })
  
  afterAll(async () => {
    // Clean up test data
    await prisma.clientPlan.deleteMany({
      where: {
        clientProfileId: premiumClient.clientProfile.id
      }
    })
    
    await prisma.clientDietPlan.deleteMany({
      where: {
        clientProfileId: premiumClient.clientProfile.id
      }
    })
    
    await prisma.trainingPlan.delete({
      where: {
        id: trainingPlan.id
      }
    })
    
    await prisma.dietPlan.delete({
      where: {
        id: dietPlan.id
      }
    })
    
    await cleanupTestData(premiumClient.user.id)
    
    await disconnectDatabase()
  })
  
  describe('Subscription Status', () => {
    it('should have an active subscription', async () => {
      const subscriptions = await prisma.subscription.findMany({
        where: {
          clientProfileId: premiumClient.clientProfile.id,
          status: 'active'
        },
        include: {
          tier: true
        }
      })
      
      expect(subscriptions).toBeDefined()
      expect(subscriptions.length).toBeGreaterThan(0)
      expect(subscriptions[0].status).toBe('active')
    })
  })
  
  describe('Accessing Training Plans', () => {
    it('should have access to assigned training plans', async () => {
      const clientPlans = await prisma.clientPlan.findMany({
        where: {
          clientProfileId: premiumClient.clientProfile.id
        },
        include: {
          trainingPlan: true
        }
      })
      
      expect(clientPlans).toBeDefined()
      expect(clientPlans.length).toBeGreaterThan(0)
      expect(clientPlans.some(plan => plan.trainingPlanId === trainingPlan.id)).toBe(true)
    })
  })
  
  describe('Accessing Diet Plans', () => {
    it('should have access to assigned diet plans', async () => {
      const clientDietPlans = await prisma.clientDietPlan.findMany({
        where: {
          clientProfileId: premiumClient.clientProfile.id
        },
        include: {
          dietPlan: true
        }
      })
      
      expect(clientDietPlans).toBeDefined()
      expect(clientDietPlans.length).toBeGreaterThan(0)
      expect(clientDietPlans.some(plan => plan.dietPlanId === dietPlan.id)).toBe(true)
    })
  })
  
  describe('Tracking Progress', () => {
    it('should be able to log workouts', async () => {
      // Create a workout log
      const workoutLog = await prisma.workoutLog.create({
        data: {
          userId: premiumClient.user.id,
          date: new Date(),
          duration: 60,
          notes: 'Test workout log',
          exercises: {
            create: [
              {
                name: 'Push-ups',
                sets: 3,
                reps: 10,
                weight: 0
              },
              {
                name: 'Squats',
                sets: 3,
                reps: 15,
                weight: 0
              }
            ]
          }
        }
      })
      
      expect(workoutLog).toBeDefined()
      expect(workoutLog.userId).toBe(premiumClient.user.id)
      
      // Get workout logs
      const workoutLogs = await prisma.workoutLog.findMany({
        where: {
          userId: premiumClient.user.id
        },
        include: {
          exercises: true
        }
      })
      
      expect(workoutLogs).toBeDefined()
      expect(workoutLogs.length).toBeGreaterThan(0)
      expect(workoutLogs[0].exercises.length).toBe(2)
      
      // Clean up
      await prisma.exerciseLog.deleteMany({
        where: {
          workoutLogId: workoutLog.id
        }
      })
      
      await prisma.workoutLog.delete({
        where: {
          id: workoutLog.id
        }
      })
    })
    
    it('should be able to log measurements', async () => {
      // Create a measurement log
      const measurementLog = await prisma.progress.create({
        data: {
          userId: premiumClient.user.id,
          date: new Date(),
          weight: 80,
          bodyFat: 15,
          measurements: {
            chest: 100,
            waist: 80,
            hips: 95
          }
        }
      })
      
      expect(measurementLog).toBeDefined()
      expect(measurementLog.userId).toBe(premiumClient.user.id)
      
      // Get measurement logs
      const measurementLogs = await prisma.progress.findMany({
        where: {
          userId: premiumClient.user.id
        }
      })
      
      expect(measurementLogs).toBeDefined()
      expect(measurementLogs.length).toBeGreaterThan(0)
      
      // Clean up
      await prisma.progress.delete({
        where: {
          id: measurementLog.id
        }
      })
    })
  })
})
