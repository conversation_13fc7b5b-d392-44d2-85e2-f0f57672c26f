// This script sets up a trainer and client with a 1:1 coaching relationship
const { PrismaClient } = require('@prisma/client');
const { hash } = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting setup of trainer and client with 1:1 relationship...');
    
    // Create a hashed password for test users
    const hashedPassword = await hash('password123', 10);
    
    // 1. Create or find a trainer user
    console.log('Setting up trainer user...');
    let trainer = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
      },
    });
    
    if (!trainer) {
      console.log('Creating new trainer user...');
      trainer = await prisma.user.create({
        data: {
          name: '<PERSON>',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'trainer',
          emailVerified: new Date(),
        },
      });
      console.log('Created trainer user:', trainer.id);
    } else {
      console.log('Found existing trainer user:', trainer.id);
      
      // Update the role to ensure it's a trainer
      if (trainer.role !== 'trainer') {
        trainer = await prisma.user.update({
          where: { id: trainer.id },
          data: { role: 'trainer' },
        });
        console.log('Updated user to trainer role');
      }
    }
    
    // 2. Create or find a client user
    console.log('Setting up client user...');
    let client = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
      },
    });
    
    if (!client) {
      console.log('Creating new client user...');
      client = await prisma.user.create({
        data: {
          name: 'Sarah Client',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'client',
          emailVerified: new Date(),
        },
      });
      console.log('Created client user:', client.id);
    } else {
      console.log('Found existing client user:', client.id);
      
      // Update the role to ensure it's a client
      if (client.role !== 'client') {
        client = await prisma.user.update({
          where: { id: client.id },
          data: { role: 'client' },
        });
        console.log('Updated user to client role');
      }
    }
    
    // 3. Create trainer profile if it doesn't exist
    console.log('Setting up trainer profile...');
    let trainerProfile = await prisma.trainerProfile.findUnique({
      where: {
        userId: trainer.id,
      },
    });
    
    if (!trainerProfile) {
      console.log('Creating new trainer profile...');
      trainerProfile = await prisma.trainerProfile.create({
        data: {
          userId: trainer.id,
        },
      });
      console.log('Created trainer profile:', trainerProfile.id);
    } else {
      console.log('Found existing trainer profile:', trainerProfile.id);
    }
    
    // 4. Create client profile if it doesn't exist
    console.log('Setting up client profile...');
    let clientProfile = await prisma.clientProfile.findUnique({
      where: {
        userId: client.id,
      },
    });
    
    if (!clientProfile) {
      console.log('Creating new client profile...');
      clientProfile = await prisma.clientProfile.create({
        data: {
          userId: client.id,
          assignedTrainerId: trainerProfile.id,
        },
      });
      console.log('Created client profile:', clientProfile.id);
    } else {
      console.log('Found existing client profile:', clientProfile.id);
      
      // Update the assigned trainer
      if (clientProfile.assignedTrainerId !== trainerProfile.id) {
        clientProfile = await prisma.clientProfile.update({
          where: { id: clientProfile.id },
          data: { assignedTrainerId: trainerProfile.id },
        });
        console.log('Updated client profile with assigned trainer');
      }
    }
    
    // 5. Create a coaching relationship if it doesn't exist
    console.log('Setting up coaching relationship...');
    let coachingRelationship = await prisma.coachingRelationship.findFirst({
      where: {
        trainerId: trainer.id,
        clientId: client.id,
        status: 'active',
      },
    });
    
    if (!coachingRelationship) {
      console.log('Creating new coaching relationship...');
      try {
        coachingRelationship = await prisma.coachingRelationship.create({
          data: {
            trainerId: trainer.id,
            clientId: client.id,
            status: 'active',
            plan: 'Premium Coaching',
            notes: 'Test coaching relationship for chat testing',
          },
        });
        console.log('Created coaching relationship:', coachingRelationship.id);
      } catch (error) {
        console.error('Error creating coaching relationship:', error);
        
        // If there's an error, try to find any existing relationship and update it
        const existingRelationship = await prisma.coachingRelationship.findFirst({
          where: {
            trainerId: trainer.id,
            clientId: client.id,
          },
        });
        
        if (existingRelationship) {
          coachingRelationship = await prisma.coachingRelationship.update({
            where: { id: existingRelationship.id },
            data: { status: 'active' },
          });
          console.log('Updated existing relationship to active:', coachingRelationship.id);
        } else {
          throw error; // Re-throw if we couldn't find an existing relationship
        }
      }
    } else {
      console.log('Found existing coaching relationship:', coachingRelationship.id);
    }
    
    // 6. Create a conversation if it doesn't exist
    console.log('Setting up conversation...');
    let conversation = await prisma.conversation.findFirst({
      where: {
        OR: [
          { user1Id: trainer.id, user2Id: client.id },
          { user1Id: client.id, user2Id: trainer.id },
        ],
      },
    });
    
    if (!conversation) {
      console.log('Creating new conversation...');
      conversation = await prisma.conversation.create({
        data: {
          user1Id: trainer.id,
          user2Id: client.id,
        },
      });
      console.log('Created conversation:', conversation.id);
    } else {
      console.log('Found existing conversation:', conversation.id);
    }
    
    // 7. Create a test message if there are no messages
    console.log('Checking for existing messages...');
    const messageCount = await prisma.message.count({
      where: {
        conversationId: conversation.id,
      },
    });
    
    let message;
    if (messageCount === 0) {
      console.log('Creating test message...');
      message = await prisma.message.create({
        data: {
          content: 'Hello! This is a test message to start the conversation.',
          senderId: trainer.id,
          receiverId: client.id,
          conversationId: conversation.id,
        },
      });
      console.log('Created test message:', message.id);
      
      // Update the conversation's lastMessageAt
      console.log('Updating conversation lastMessageAt...');
      await prisma.conversation.update({
        where: {
          id: conversation.id,
        },
        data: {
          lastMessageAt: new Date(),
        },
      });
    } else {
      console.log(`Found ${messageCount} existing messages in the conversation`);
      message = await prisma.message.findFirst({
        where: {
          conversationId: conversation.id,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    }
    
    console.log('Setup complete!');
    console.log('Summary:');
    console.log('- Trainer:', trainer.name, `(${trainer.id})`);
    console.log('- Client:', client.name, `(${client.id})`);
    console.log('- Coaching Relationship:', coachingRelationship.id);
    console.log('- Conversation:', conversation.id);
    console.log('- Test Message:', message ? message.id : 'None');
    
    console.log('\nTo test the chat functionality:');
    console.log('1. Login as trainer: http://localhost:3000/api/auth/dev-login?role=trainer');
    console.log('2. Access the chat: http://localhost:3000/dashboard/coaching-chat');
    console.log('3. Login as client: http://localhost:3000/api/auth/dev-login?role=client');
    console.log('4. Access the chat: http://localhost:3000/dashboard/coaching-chat');
    
  } catch (error) {
    console.error('Error setting up coaching users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
