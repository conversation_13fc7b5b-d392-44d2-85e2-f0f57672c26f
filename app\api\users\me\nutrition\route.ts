import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const json = await request.json()
    const { date, mealType, name, calories, protein, carbs, fat, notes } = json

    try {
      const nutritionLog = await prisma.nutritionLog.create({
        data: {
          clientId: session.user.id,
          date: new Date(date),
          mealType,
          name,
          calories: calories ? parseInt(calories.toString()) : null,
          protein: protein ? parseFloat(protein.toString()) : null,
          carbs: carbs ? parseFloat(carbs.toString()) : null,
          fat: fat ? parseFloat(fat.toString()) : null,
          notes: notes || null,
        },
      })

      // Return the full list of nutrition logs after creating a new one
      const nutritionLogs = await prisma.nutritionLog.findMany({
        where: {
          clientId: session.user.id,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 10,
      })

      return NextResponse.json(nutritionLogs)
    } catch (err) {
      console.error("[NUTRITION_POST] Database error:", err)
      return new NextResponse("Failed to create nutrition log", { status: 500 })
    }
  } catch (error) {
    console.error("[NUTRITION_POST]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const userId = session.user.id

    // Get user's nutrition data
    const nutritionData = await prisma.nutritionLog.findMany({
      where: {
        clientId: userId,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
    })

    // Get user's nutrition targets
    const userProfile = await prisma.clientProfile.findUnique({
      where: {
        userId: userId,
      },
      select: {
        calorieTarget: true,
        proteinTarget: true,
        carbTarget: true,
        fatTarget: true,
      },
    })

    // Calculate current day's nutrition totals
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const todayNutrition = await prisma.nutritionLog.findMany({
      where: {
        clientId: userId,
        createdAt: {
          gte: today,
        },
      },
    })

    const currentProtein = todayNutrition.reduce((sum, item) => sum + (item.protein || 0), 0)
    const currentCarbs = todayNutrition.reduce((sum, item) => sum + (item.carbs || 0), 0)
    const currentFat = todayNutrition.reduce((sum, item) => sum + (item.fat || 0), 0)
    const currentCalories = todayNutrition.reduce((sum, item) => sum + (item.calories || 0), 0)

    // Create meal plan from nutrition logs
    const mealPlan = todayNutrition.map(meal => ({
      id: meal.id,
      name: meal.mealType || "Meal",
      description: meal.description || "",
      macros: {
        protein: meal.protein || 0,
        carbs: meal.carbs || 0,
        fat: meal.fat || 0,
      },
      time: meal.createdAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      calories: meal.calories || 0,
      date: meal.date || meal.createdAt,
    }))

    // If no meals logged today, provide default structure
    if (mealPlan.length === 0) {
      mealPlan.push(
        {
          name: "No meals logged yet",
          description: "Log your first meal of the day",
          macros: { protein: 0, carbs: 0, fat: 0 },
          time: "Today",
          calories: 0,
        }
      )
    }

    // Default targets if not set
    const targets = {
      protein: userProfile?.proteinTarget || 180,
      carbs: userProfile?.carbTarget || 200,
      fat: userProfile?.fatTarget || 75,
      calories: userProfile?.calorieTarget || 2500,
    }

    // Calculate percentages
    const percentages = {
      protein: Math.round((currentProtein / targets.protein) * 100) || 0,
      carbs: Math.round((currentCarbs / targets.carbs) * 100) || 0,
      fat: Math.round((currentFat / targets.fat) * 100) || 0,
      calories: Math.round((currentCalories / targets.calories) * 100) || 0,
    }

    return NextResponse.json({
      logs: nutritionData, // Include the actual logs
      mealPlan,
      current: {
        protein: currentProtein,
        carbs: currentCarbs,
        fat: currentFat,
        calories: currentCalories,
      },
      targets,
      percentages,
    })
  } catch (error) {
    console.error("[NUTRITION_GET]", error)
    return new NextResponse("Internal Error", { status: 500 })
  }
}
