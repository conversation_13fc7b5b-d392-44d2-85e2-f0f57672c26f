'use client';

import { useState } from 'react';
import { Edit, Plus, ChevronDown, ChevronRight, Calendar, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { PremiumExerciseGrid } from './premium-exercise-grid';

interface Exercise {
  id: string;
  name: string;
  sets: number;
  reps: number;
  notes?: string;
  weight?: number;
  category?: string;
  difficulty?: string;
  targetMuscles?: string[];
  video?: string;
  description?: string;
}

interface DailyWorkout {
  id: string;
  day: string;
  exercises: Exercise[];
}

interface Week {
  id: string;
  weekNumber: number;
  dailyWorkouts: DailyWorkout[];
}

interface TrainingPlan {
  id: string;
  title: string;
  description?: string;
  weeks?: Week[];
}

interface PremiumTrainingPlanProps {
  plan: TrainingPlan;
  onDisassociate: () => void;
  onEdit: () => void;
  onExerciseAdd: (weekIndex: number, dayId: string, exercise: Exercise) => void;
  onExerciseEdit: (weekIndex: number, dayId: string, exerciseId: string, updatedExercise: Exercise) => void;
  onExerciseDelete: (weekIndex: number, dayId: string, exerciseId: string) => void;
}

export function PremiumTrainingPlan({
  plan,
  onDisassociate,
  onEdit,
  onExerciseAdd,
  onExerciseEdit,
  onExerciseDelete
}: PremiumTrainingPlanProps) {
  const [selectedWeek, setSelectedWeek] = useState(1);
  const [selectedDay, setSelectedDay] = useState('Monday');

  // Get the weeks array
  const weeksArray = Array.isArray(plan.weeks)
    ? plan.weeks
    : (typeof plan.weeks === 'object' && plan.weeks !== null
      ? Object.values(plan.weeks)
      : []);

  // Get unique week numbers
  const weekNumbers = weeksArray.length > 0
    ? [...new Set(weeksArray.map(week => {
        if (week.id && typeof week.id === 'string' && week.id.startsWith('week-')) {
          return parseInt(week.id.split('-')[1]);
        } else if (week.weekNumber) {
          return typeof week.weekNumber === 'string'
            ? parseInt(week.weekNumber)
            : week.weekNumber;
        } else {
          return 1;
        }
      }))].sort((a, b) => a - b)
    : [1, 2, 3];

  // Find selected week data
  const selectedWeekData = weeksArray.find(w => {
    if (!w) return false;

    if (w.id && typeof w.id === 'string' && w.id.startsWith('week-')) {
      const weekNum = parseInt(w.id.split('-')[1]);
      return weekNum === selectedWeek;
    }

    const weekNum = typeof w.weekNumber === 'string'
      ? parseInt(w.weekNumber)
      : (typeof w.weekNumber === 'number' ? w.weekNumber : 0);

    return weekNum === selectedWeek;
  }) || (weeksArray.length >= selectedWeek ? weeksArray[selectedWeek - 1] : null);

  // Find selected day data
  const selectedDayData = selectedWeekData?.dailyWorkouts?.find(
    (d) => d.day === selectedDay
  ) || (selectedWeekData?.dailyWorkouts?.length > 0 ? selectedWeekData.dailyWorkouts[0] : null);

  // Days of the week
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  return (
    <div className="space-y-6">
      {/* Premium Header */}
      <div className="relative overflow-hidden rounded-xl border bg-gradient-to-r from-background via-background to-muted/20 p-6">
        <div className="absolute inset-0 opacity-[0.03]" style={{
          backgroundImage: `linear-gradient(to right, currentColor 1px, transparent 1px), linear-gradient(to bottom, currentColor 1px, transparent 1px)`,
          backgroundSize: '20px 20px'
        }}></div>
        <div className="relative z-10">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20 font-medium">
                  Training Plan
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {weekNumbers.length} {weekNumbers.length === 1 ? 'week' : 'weeks'}
                </span>
              </div>
              <h1 className="text-2xl font-bold tracking-tight">{plan.title}</h1>
              {plan.description && (
                <p className="text-muted-foreground mt-1 max-w-2xl">{plan.description}</p>
              )}
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                className="border-destructive/30 text-destructive hover:bg-destructive/10 hover:text-destructive hover:border-destructive/50"
                onClick={onDisassociate}
              >
                Disassociate Plan
              </Button>
              <Button onClick={onEdit} className="gap-1.5">
                <Edit className="h-4 w-4" />
                Edit Plan
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Week Selector */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-medium text-muted-foreground">Select Week</h2>
          <div className="h-px flex-1 bg-border mx-4"></div>
          <span className="text-xs text-muted-foreground">Week {selectedWeek} of {weekNumbers.length}</span>
        </div>
        <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
          {weekNumbers.map((weekNumber) => (
            <Button
              key={`week-${weekNumber}`}
              variant={selectedWeek === weekNumber ? "default" : "outline"}
              className={cn(
                "w-full transition-all",
                selectedWeek === weekNumber
                  ? "bg-primary text-primary-foreground shadow-md"
                  : "hover:bg-muted/50"
              )}
              onClick={() => {
                setSelectedWeek(weekNumber);
              }}
            >
              Week {weekNumber}
            </Button>
          ))}
        </div>
      </div>

      {/* Day Selector */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-medium text-muted-foreground">Select Day</h2>
          <div className="h-px flex-1 bg-border mx-4"></div>
          <span className="text-xs text-muted-foreground">{selectedDay}</span>
        </div>
        <div className="grid grid-cols-7 gap-2">
          {days.map(day => (
            <Button
              key={day}
              variant={selectedDay === day ? "default" : "outline"}
              className={cn(
                "w-full transition-all",
                selectedDay === day
                  ? "bg-primary text-primary-foreground shadow-md"
                  : "hover:bg-muted/50"
              )}
              onClick={() => setSelectedDay(day)}
            >
              {day.substring(0, 3)}
            </Button>
          ))}
        </div>
      </div>

      {/* Exercise Grid */}
      <Card className="overflow-hidden border-muted/50">
        <CardContent className="p-6">
          <PremiumExerciseGrid
            selectedWeek={selectedWeek}
            selectedDay={selectedDay}
            weekData={selectedWeekData}
            dayData={selectedDayData}
            onExerciseAdd={onExerciseAdd}
            onExerciseEdit={onExerciseEdit}
            onExerciseDelete={onExerciseDelete}
          />
        </CardContent>
      </Card>
    </div>
  );
}
