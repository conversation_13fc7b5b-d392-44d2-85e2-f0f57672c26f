import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    
    // Get query parameters
    const url = new URL(request.url)
    const timeRange = url.searchParams.get("timeRange") || "month"
    
    // Calculate date range based on timeRange
    const now = new Date()
    let startDate = new Date()
    
    switch (timeRange) {
      case "week":
        startDate.setDate(now.getDate() - 7)
        break
      case "month":
        startDate.setMonth(now.getMonth() - 1)
        break
      case "quarter":
        startDate.setMonth(now.getMonth() - 3)
        break
      case "year":
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setMonth(now.getMonth() - 1) // Default to month
    }
    
    // Get user ID
    const userId = session.user.id
    
    // Fetch workout logs for the user
    const workoutLogs = await prisma.workoutLog.findMany({
      where: {
        clientId: userId,
        date: {
          gte: startDate
        }
      },
      include: {
        exercises: true
      },
      orderBy: {
        date: "asc"
      }
    })
    
    // Fetch measurements for the user
    const measurements = await prisma.measurement.findMany({
      where: {
        userId,
        date: {
          gte: startDate
        }
      },
      orderBy: {
        date: "asc"
      }
    })
    
    // Calculate training metrics
    const trainingMetrics = {
      totalWorkouts: workoutLogs.length,
      totalVolume: workoutLogs.reduce((sum, log) => {
        // Calculate volume for each workout (weight * sets * reps)
        const workoutVolume = log.exercises.reduce((exSum, ex) => {
          return exSum + ((ex.weight || 0) * (ex.sets || 0) * (ex.reps || 0))
        }, 0)
        return sum + workoutVolume
      }, 0),
      avgDuration: workoutLogs.length > 0 
        ? Math.round(workoutLogs.reduce((sum, log) => sum + (log.duration || 0), 0) / workoutLogs.length) 
        : 0,
      // Add more metrics as needed
    }
    
    // Format data for charts
    const weightData = measurements.map(m => ({
      date: m.date.toISOString().split('T')[0],
      weight: m.weight || 0,
      bodyFat: m.bodyFat || 0
    }))
    
    // Calculate training volume by week
    const trainingVolumeByWeek = []
    const weekMap = new Map()
    
    workoutLogs.forEach(log => {
      const date = new Date(log.date)
      const weekStart = new Date(date)
      weekStart.setDate(date.getDate() - date.getDay()) // Start of week (Sunday)
      const weekKey = weekStart.toISOString().split('T')[0]
      
      const workoutVolume = log.exercises.reduce((sum, ex) => {
        return sum + ((ex.weight || 0) * (ex.sets || 0) * (ex.reps || 0))
      }, 0)
      
      if (weekMap.has(weekKey)) {
        weekMap.set(weekKey, weekMap.get(weekKey) + workoutVolume)
      } else {
        weekMap.set(weekKey, workoutVolume)
      }
    })
    
    weekMap.forEach((volume, date) => {
      trainingVolumeByWeek.push({
        date,
        volume
      })
    })
    
    // Return analytics data
    return NextResponse.json({
      metrics: trainingMetrics,
      weightData,
      trainingData: trainingVolumeByWeek,
      // Add more data as needed
    })
    
  } catch (error) {
    console.error("[USER_ANALYTICS_GET]", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
