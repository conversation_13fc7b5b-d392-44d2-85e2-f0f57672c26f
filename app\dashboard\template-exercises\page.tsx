import Link from "next/link"
import { notFound } from "next/navigation"
import { getServerSession } from "next-auth"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export default async function TemplateExercisesPage() {
  const session = await getServerSession(authOptions)
  if (!session?.user || session.user.role !== "admin") {
    return notFound()
  }

  const exercises = await prisma.templateExercise.findMany({
    orderBy: {
      name: "asc",
    },
  })

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Template Exercises</h1>
        <Button asChild>
          <Link href="/dashboard/template-exercises/new">Add Template Exercise</Link>
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Difficulty</TableHead>
              <TableHead>Details</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {exercises.map((exercise) => (
              <TableRow key={exercise.id}>
                <TableCell className="font-medium">{exercise.name}</TableCell>
                <TableCell className="capitalize">{exercise.type}</TableCell>
                <TableCell className="capitalize">{exercise.difficulty}</TableCell>
                <TableCell>
                  <div className="text-sm text-muted-foreground">
                    {exercise.type === "strength" && (
                      <>
                        {exercise.sets && <span>Sets: {exercise.sets} </span>}
                        {exercise.reps && <span>Reps: {exercise.reps}</span>}
                      </>
                    )}
                    {(exercise.type === "cardio" || exercise.type === "flexibility") && (
                      exercise.duration && <span>Duration: {exercise.duration} min</span>
                    )}
                    {exercise.restTime && <span>Rest: {exercise.restTime}s</span>}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button asChild variant="outline" size="sm">
                      <Link href={`/dashboard/template-exercises/${exercise.id}/edit`}>
                        Edit
                      </Link>
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={async () => {
                        const response = await fetch(
                          `/api/template-exercises/${exercise.id}`,
                          {
                            method: "DELETE",
                          }
                        )
                        if (response.ok) {
                          window.location.reload()
                        }
                      }}
                    >
                      Delete
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
} 