import { test, expect } from 'vitest';
import { appRouter } from '@/server/api/trpc';
import { createInnerTRPCContext } from '@/server/api/trpc';
import { hash } from 'bcryptjs';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  name: 'Test User',
};

test('Authentication flow - register, login, session', async () => {
  await prisma.user.deleteMany({
    where: { email: testUser.email },
  });
  
  const caller = appRouter.createCaller(
    createInnerTRPCContext({ session: null })
  );
  
  const registeredUser = await caller.auth.register({
    email: testUser.email,
    password: testUser.password,
    name: testUser.name,
  });
  
  expect(registeredUser).toBeDefined();
  expect(registeredUser.email).toBe(testUser.email);
  expect(registeredUser.name).toBe(testUser.name);
  
  const loginResult = await caller.auth.login({
    email: testUser.email,
    password: testUser.password,
  });
  
  expect(loginResult).toBeDefined();
  expect(loginResult.user).toBeDefined();
  expect(loginResult.user.email).toBe(testUser.email);
  
  const authedCaller = appRouter.createCaller(
    createInnerTRPCContext({ 
      session: { 
        user: loginResult.user, 
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() 
      } 
    })
  );
  
  const profile = await authedCaller.user.getProfile();
  
  expect(profile).toBeDefined();
  expect(profile.email).toBe(testUser.email);
  
  await prisma.user.deleteMany({
    where: { email: testUser.email },
  });
});
