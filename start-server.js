// This is a WebSocket server script for real-time chat
const { createServer } = require('http');
const { WebSocketServer } = require('ws');
const url = require('url');

console.log('Starting WebSocket server...');

// Create a separate HTTP server for WebSocket
const socketPort = parseInt(process.env.SOCKET_PORT || '3001', 10);
const httpServer = createServer();

// Initialize WebSocket Server
const wss = new WebSocketServer({ server: httpServer });

// Store active connections by user ID and conversation ID
const userConnections = new Map();
const conversationRooms = new Map();

// WebSocket connection handler
wss.on('connection', (ws, req) => {
  // Parse the URL to get the userId query parameter
  const parsedUrl = url.parse(req.url, true);
  const userId = parsedUrl.query.userId;

  if (!userId) {
    console.log('WebSocket connection without userId, disconnecting');
    ws.close(1008, 'UserId is required');
    return;
  }

  console.log(`WebSocket connected for user ${userId}`);

  // Store the connection by user ID
  if (!userConnections.has(userId)) {
    userConnections.set(userId, new Set());
  }
  userConnections.get(userId).add(ws);

  // Set up connection properties
  ws.userId = userId;
  ws.isAlive = true;
  ws.conversations = new Set();

  // Handle pings to keep connection alive
  ws.on('pong', () => {
    ws.isAlive = true;
  });

  // Handle messages
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      console.log(`Received message from user ${userId}:`, data);

      if (data.event === 'join-conversation') {
        const conversationId = data.payload;
        console.log(`User ${userId} joining conversation ${conversationId}`);

        // Add user to conversation room
        if (!conversationRooms.has(conversationId)) {
          conversationRooms.set(conversationId, new Set());
        }
        conversationRooms.get(conversationId).add(ws);
        ws.conversations.add(conversationId);

      } else if (data.event === 'leave-conversation') {
        const conversationId = data.payload;
        console.log(`User ${userId} leaving conversation ${conversationId}`);

        // Remove user from conversation room
        if (conversationRooms.has(conversationId)) {
          conversationRooms.get(conversationId).delete(ws);
        }
        ws.conversations.delete(conversationId);

      } else if (data.event === 'send-message') {
        const message = data.payload;
        console.log(`New message from user ${userId} in conversation ${message.conversationId}`);

        // Broadcast to the conversation room
        broadcastToConversation(message.conversationId, {
          event: 'new-message',
          payload: message
        });

        // Also send to the receiver's user connections
        if (message.receiverId) {
          sendToUser(message.receiverId, {
            event: 'notification',
            payload: {
              type: 'new-message',
              message: message
            }
          });
        }
      }
    } catch (error) {
      console.error(`Error processing message from user ${userId}:`, error);
    }
  });

  // Handle disconnection
  ws.on('close', () => {
    console.log(`WebSocket disconnected for user ${userId}`);

    // Remove from user connections
    if (userConnections.has(userId)) {
      userConnections.get(userId).delete(ws);
      if (userConnections.get(userId).size === 0) {
        userConnections.delete(userId);
      }
    }

    // Remove from all conversation rooms
    ws.conversations.forEach(conversationId => {
      if (conversationRooms.has(conversationId)) {
        conversationRooms.get(conversationId).delete(ws);
        if (conversationRooms.get(conversationId).size === 0) {
          conversationRooms.delete(conversationId);
        }
      }
    });
  });

  // Send a welcome message
  sendToConnection(ws, {
    event: 'connect',
    payload: { userId }
  });
});

// Broadcast a message to all connections in a conversation
function broadcastToConversation(conversationId, data) {
  if (conversationRooms.has(conversationId)) {
    const connections = conversationRooms.get(conversationId);
    connections.forEach(connection => {
      sendToConnection(connection, data);
    });
  }
}

// Send a message to all connections for a user
function sendToUser(userId, data) {
  if (userConnections.has(userId)) {
    const connections = userConnections.get(userId);
    connections.forEach(connection => {
      sendToConnection(connection, data);
    });
  }
}

// Send a message to a specific connection
function sendToConnection(connection, data) {
  if (connection.readyState === connection.OPEN) {
    connection.send(JSON.stringify(data));
  }
}

// Set up a heartbeat interval to detect dead connections
const interval = setInterval(() => {
  wss.clients.forEach(ws => {
    if (ws.isAlive === false) {
      console.log(`Terminating dead connection for user ${ws.userId}`);
      return ws.terminate();
    }

    ws.isAlive = false;
    ws.ping();
  });
}, 30000);

// Clean up on WebSocket server close
wss.on('close', () => {
  clearInterval(interval);
});

// Start WebSocket server
// Listen on all interfaces (0.0.0.0) to make it accessible from outside the container
httpServer.listen(socketPort, '0.0.0.0', () => {
  console.log(`WebSocket server running on port ${socketPort} (0.0.0.0)`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Shutting down WebSocket server...');
  httpServer.close(() => {
    console.log('WebSocket server closed');
    process.exit(0);
  });
});
