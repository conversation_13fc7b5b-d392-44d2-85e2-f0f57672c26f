# API Routes in Next.js

## Dynamic Server Usage Warnings

When building the application, you may see warnings like:

```
[ERROR] Dynamic server usage: Route /api/auth/verify-email couldn't be rendered statically because it used `request.url`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error
```

These warnings are **expected** and **do not prevent the build from completing**. They are informational messages that indicate certain API routes are using dynamic features that cannot be statically generated at build time.

## Why These Warnings Occur

Next.js tries to pre-render as many pages as possible during the build process to improve performance. However, API routes that use dynamic server features like:

- `headers`
- `cookies`
- `request.url`
- `searchParams`

Cannot be statically generated because they need access to request-specific information that's only available at runtime.

## How We've Handled These Warnings

We've configured the application to properly handle these dynamic routes:

1. **Route Configuration Files**: We've added `route.config.js` files to API routes that use dynamic features, explicitly marking them as dynamic with `export const dynamic = 'force-dynamic'`.

2. **Global API Configuration**: We've added a global configuration for all API routes in `app/api/config.js` to mark them as dynamic by default.

3. **Middleware Configuration**: We've updated the middleware to add a custom header to API responses that helps Next.js understand these routes are dynamic.

4. **Next.js Configuration**: We've updated the Next.js configuration to properly handle dynamic routes and suppress warnings.

## What This Means for Development

These warnings don't affect the functionality of your application. They're just informing you about how Next.js will handle these routes at runtime:

- Static routes are pre-rendered at build time
- Dynamic routes are server-rendered at request time

## Best Practices for API Routes

When working with API routes:

1. Use `export const dynamic = 'force-dynamic'` at the top of your route file if it uses dynamic features
2. Be aware that dynamic routes have slightly higher latency than static routes
3. Consider caching strategies for frequently accessed dynamic routes

## Further Reading

- [Next.js Dynamic Routes Documentation](https://nextjs.org/docs/app/building-your-application/routing/dynamic-routes)
- [Next.js API Routes Documentation](https://nextjs.org/docs/api-routes/introduction)
- [Next.js Dynamic Server Usage Error](https://nextjs.org/docs/messages/dynamic-server-error)
