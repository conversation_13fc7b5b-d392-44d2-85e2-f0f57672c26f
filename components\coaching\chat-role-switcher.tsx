'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { UserCog, User, UserCheck } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useToast } from '@/components/ui/use-toast';

export function ChatRoleSwitcher() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const currentRole = session?.user?.role || 'client';
  const isTrainer = currentRole === 'trainer';

  const switchToRole = async (role: string) => {
    setIsLoading(true);
    try {
      // Redirect to the dev login endpoint with the selected role and the correct userId
      // Use specific trainer and client IDs that exist in the database
      const userId = role === 'trainer' ? '1' : '2';
      // Set premium status to true for both roles to ensure access to premium features
      window.location.href = `/api/auth/dev-login?role=${role}&userId=${userId}&premium=true&returnTo=${window.location.pathname}`;

      toast({
        title: 'Switching roles',
        description: `Switching to ${role} view...`,
      });
    } catch (error) {
      console.error('Error switching role:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to switch roles. Please try again.',
      });
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center gap-2 mb-4">
      <Button
        variant={isTrainer ? "default" : "outline"}
        size="sm"
        onClick={() => switchToRole('trainer')}
        disabled={isLoading || isTrainer}
        className="flex items-center gap-2"
      >
        <UserCog className="h-4 w-4" />
        <span>Trainer View</span>
        {isTrainer && <UserCheck className="h-4 w-4 ml-1" />}
      </Button>

      <Button
        variant={!isTrainer ? "default" : "outline"}
        size="sm"
        onClick={() => switchToRole('client')}
        disabled={isLoading || !isTrainer}
        className="flex items-center gap-2"
      >
        <User className="h-4 w-4" />
        <span>Client View</span>
        {!isTrainer && <UserCheck className="h-4 w-4 ml-1" />}
      </Button>
    </div>
  );
}
