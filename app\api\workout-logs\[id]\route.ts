import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: Request,
  context: any
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Safely access params
    const logId = context?.params?.id;
    if (!logId) {
        return new NextResponse("Workout log ID missing in URL", { status: 400 });
    }

    // Check if the workout log exists and belongs to the user
    const log = await prisma.workoutLog.findUnique({
      where: {
        id: logId,
      },
      include: {
        exercises: true
      }
    })

    if (!log) {
      return new NextResponse("Workout log not found", { status: 404 })
    }

    // Check ownership
    if (log.clientId !== session.user.id) {
      return new NextResponse("Forbidden: You do not own this workout log", { status: 403 })
    }

    return NextResponse.json(log)
  } catch (error) {
    console.error("[WORKOUT_LOG_GET]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function PUT(
  request: Request,
  context: any
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Safely access params
    const logId = context?.params?.id;
    if (!logId) {
        return new NextResponse("Workout log ID missing in URL", { status: 400 });
    }

    const body = await request.json()
    const { date, duration, notes, exercises } = body

    // Check if the workout log exists and belongs to the user
    const log = await prisma.workoutLog.findUnique({
      where: {
        id: logId,
      },
      select: { clientId: true }
    })

    if (!log) {
      return new NextResponse("Workout log not found", { status: 404 })
    }

    // Check ownership
    if (log.clientId !== session.user.id) {
      return new NextResponse("Forbidden: You do not own this workout log", { status: 403 })
    }

    // Update the workout log
    const updatedLog = await prisma.workoutLog.update({
      where: {
        id: logId,
      },
      data: {
        date: date ? new Date(date) : undefined,
        duration: duration !== undefined ? duration : undefined,
        notes: notes !== undefined ? notes : undefined,
      },
      include: {
        exercises: true
      }
    })

    // Update exercises if provided
    if (exercises && exercises.length > 0) {
      // Delete existing exercises
      await prisma.exerciseLog.deleteMany({
        where: {
          workoutLogId: logId
        }
      })

      // Create new exercises
      await Promise.all(exercises.map(async (exercise: any) => {
        await prisma.exerciseLog.create({
          data: {
            workoutLogId: logId,
            exerciseName: exercise.name,
            sets: exercise.sets,
            reps: exercise.reps,
            weight: exercise.weight,
            duration: exercise.duration,
            notes: exercise.notes,
            rpe: exercise.rpe,
            rir: exercise.rir,
            completed: exercise.completed || true
          }
        })
      }))

      // Fetch the updated workout log with exercises
      const refreshedLog = await prisma.workoutLog.findUnique({
        where: {
          id: logId
        },
        include: {
          exercises: true
        }
      })

      return NextResponse.json(refreshedLog)
    }

    return NextResponse.json(updatedLog)
  } catch (error) {
    console.error("[WORKOUT_LOG_UPDATE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function DELETE(
  request: Request,
  context: any
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Safely access params
    const logId = context?.params?.id;
    if (!logId) {
        return new NextResponse("Workout log ID missing in URL", { status: 400 });
    }

    // Check if the workout log exists and belongs to the user
    const log = await prisma.workoutLog.findUnique({
      where: {
        id: logId,
      },
      select: { clientId: true }
    })

    if (!log) {
      // Idempotent: Already deleted or never existed
      return new NextResponse(null, { status: 204 });
    }

    // Check ownership
    if (log.clientId !== session.user.id) {
      return new NextResponse("Forbidden: You do not own this workout log", { status: 403 })
    }

    // Delete the workout log (exercises will be deleted automatically due to onDelete: Cascade)
    await prisma.workoutLog.delete({
      where: {
        id: logId,
      },
    })

    return new NextResponse(null, { status: 204 })
  } catch (error) {
    console.error("[WORKOUT_LOG_DELETE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}
