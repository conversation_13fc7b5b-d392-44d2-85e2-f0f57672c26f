import { NextResponse } from "next/server"
import { checkAndUpdateExpiredRelationships } from "@/app/actions/coaching"

export const dynamic = "force-dynamic"
export const revalidate = 0

export async function GET(request: Request) {
  try {
    // Verify that this is a cron job request (you might want to add authentication)
    const authHeader = request.headers.get("authorization")
    if (!authHeader || authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Check and update expired relationships
    const expiredRelationships = await checkAndUpdateExpiredRelationships()

    return NextResponse.json({
      success: true,
      expiredCount: expiredRelationships.length,
    })
  } catch (error) {
    console.error("Error in subscription check cron:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 