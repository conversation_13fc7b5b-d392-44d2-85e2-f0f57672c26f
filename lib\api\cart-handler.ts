import { NextResponse } from "next/server"
import { BaseApiHandler } from "./base-api-handler"
import { CartService } from "../services/cart-service"

export class CartHandler extends BaseApiHandler {
  /**
   * Get the user's cart
   */
  protected async get(req: Request, userId: string): Promise<NextResponse> {
    const cartItems = await CartService.getCart(userId)
    const formattedCart = CartService.formatCartData(cartItems)
    
    return NextResponse.json(formattedCart)
  }

  /**
   * Add an item to the cart
   */
  protected async post(req: Request, userId: string): Promise<NextResponse> {
    const { productId, quantity = 1 } = await req.json()
    
    if (!productId) {
      return NextResponse.json({ error: "Product ID is required" }, { status: 400 })
    }
    
    const cartItem = await CartService.addItem(userId, productId, quantity)
    
    // Get the updated cart
    const cartItems = await CartService.getCart(userId)
    const formattedCart = CartService.formatCartData(cartItems)
    
    return NextResponse.json({
      message: "Item added to cart",
      cart: formattedCart
    })
  }

  /**
   * Clear the cart
   */
  protected async delete(req: Request, userId: string): Promise<NextResponse> {
    await CartService.clearCart(userId)
    
    return NextResponse.json({
      message: "Cart cleared",
      cart: {
        items: [],
        total: 0,
        count: 0
      }
    })
  }

  /**
   * Update the cart (not implemented)
   */
  protected async put(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}

export class CartItemHandler extends BaseApiHandler {
  /**
   * Get a cart item (not implemented)
   */
  protected async get(req: Request, userId: string, params: { itemId: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Create a cart item (not implemented in [id] route)
   */
  protected async post(req: Request, userId: string, params: { itemId: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Update a cart item
   */
  protected async put(req: Request, userId: string, params: { itemId: string }): Promise<NextResponse> {
    const { quantity } = await req.json()
    
    if (quantity === undefined) {
      return NextResponse.json({ error: "Quantity is required" }, { status: 400 })
    }
    
    const cartItem = await CartService.updateItem(params.itemId, quantity)
    
    // Get the updated cart
    const cartItems = await CartService.getCart(userId)
    const formattedCart = CartService.formatCartData(cartItems)
    
    return NextResponse.json({
      message: "Cart item updated",
      cart: formattedCart
    })
  }

  /**
   * Remove a cart item
   */
  protected async delete(req: Request, userId: string, params: { itemId: string }): Promise<NextResponse> {
    await CartService.removeItem(params.itemId)
    
    // Get the updated cart
    const cartItems = await CartService.getCart(userId)
    const formattedCart = CartService.formatCartData(cartItems)
    
    return NextResponse.json({
      message: "Item removed from cart",
      cart: formattedCart
    })
  }
}
