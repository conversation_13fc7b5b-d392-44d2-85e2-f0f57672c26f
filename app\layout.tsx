import "@/app/globals.css"
import { Inter } from "next/font/google"
import { RootLayoutClient } from "@/components/layout/root-layout-client"

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "Clear Coach App",
  description: "Connect with fitness trainers and athletes for personalized training and coaching.",
  icons: {
    icon: '/logo/brand-logo.png',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="h-full" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
        <meta name="description" content="Athlete and trainer Coaching Platform" />
        <meta name="theme-color" content="#ffffff" />
        <link rel="icon" href="/logo/brand-logo.png" />

        {/* DNS prefetching for external resources */}
        <link rel="dns-prefetch" href="https://res.cloudinary.com" />
        <link rel="dns-prefetch" href="https://js.stripe.com" />

        {/* PDF.js for PDF thumbnail generation */}
        <script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.min.js" async></script>
        <script src="/js/pdf-thumbnail-generator.js" async></script>

        {/* Preconnect to critical origins */}
        <link rel="preconnect" href="https://res.cloudinary.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://js.stripe.com" crossOrigin="anonymous" />
        
        {/* PWA manifest */}
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className={`${inter.className} min-h-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800`} suppressHydrationWarning>
        <RootLayoutClient>
          <a 
            href="#main-content" 
            className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground py-2 px-4 rounded"
          >
            Skip to content
          </a>
          <main id="main-content">
            {children}
          </main>
        </RootLayoutClient>
        
        {/* Register Service Worker */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js').then(
                    function(registration) {
                      console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    },
                    function(err) {
                      console.log('ServiceWorker registration failed: ', err);
                    }
                  );
                });
              }
            `,
          }}
        />
      </body>
    </html>
  )
}
