import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(
  request: Request,
  context: any // Use 'any' workaround
) {
  try {
    const session = await getServerSession(authOptions)
    // Authorization: Check if user is logged in and owns the workout or is admin
    if (!session?.user?.id) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Safely access params
    const workoutId = context?.params?.id; // Assuming the param is named 'id' based on folder
    if (!workoutId) {
        return new NextResponse("Workout ID missing in URL", { status: 400 });
    }

    const body = await request.json()
    const { exerciseIds } = body // Expecting an array of TEMPLATE exercise IDs

    if (!exerciseIds || !Array.isArray(exerciseIds) || exerciseIds.length === 0) {
      return new NextResponse("Invalid request body: exerciseIds array is required", { status: 400 })
    }
    if (!exerciseIds.every(id => typeof id === 'string')) {
         return new NextResponse("Invalid exerciseIds: all IDs must be strings", { status: 400 });
    }

    // Verify workout exists and check ownership
    const workout = await prisma.workout.findUnique({
        where: { id: workoutId },
        include: { trainingPlan: { select: { athleteId: true } } }
    });

    if (!workout) {
        return new NextResponse("Workout not found", { status: 404 });
    }
    if (workout.trainingPlan.athleteId !== session.user.id && session.user.role !== "admin") {
        return new NextResponse("Forbidden: You do not own this workout", { status: 403 });
    }

    // Get the template exercises to be added
    const templateExercises = await prisma.exercise.findMany({
      where: {
        id: {
          in: exerciseIds,
        },
        isTemplate: true, // Ensure we only fetch actual templates
      },
    })

    // Check if all requested template IDs were found
    if (templateExercises.length !== exerciseIds.length) {
        const foundIds = new Set(templateExercises.map(t => t.id));
        const notFoundIds = exerciseIds.filter(id => !foundIds.has(id));
        console.warn(`Could not find template exercises with IDs: ${notFoundIds.join(', ')}`);
        // Decide whether to proceed with found ones or return an error
        // Returning error for clarity:
        return new NextResponse(`Could not find all requested template exercises. Missing: ${notFoundIds.join(', ')}`, { status: 404 });
    }

    // Determine the starting order for the new exercises
     const lastExercise = await prisma.exercise.findFirst({
        where: { workoutId: workoutId },
        orderBy: { order: 'desc' },
        select: { order: true }
    });
    let currentOrder = (lastExercise?.order ?? -1) + 1;

    // Create new exercises from templates
    const newExercisesData = templateExercises.map((template) => ({
        name: template.name,
        description: template.description,
        sets: template.sets,
        reps: template.reps,
        duration: template.duration,
        restTime: template.restTime,
        videoUrl: template.videoUrl,
        muscleGroup: template.muscleGroup,
        type: template.type,
        thumbnailUrl: template.thumbnailUrl,
        calories: template.calories,
        difficulty: template.difficulty,
        equipment: template.equipment,
        isTemplate: false, // The new instance is not a template
        order: currentOrder++, // Assign sequential order
        workoutId: workoutId, // Connect to the workout
        createdBy: session.user?.id // Link to the user adding it
    }));

    // Use createMany for efficiency
    await prisma.exercise.createMany({ data: newExercisesData });

    // Fetch the newly created exercises to return them (optional)
    const createdExercises = await prisma.exercise.findMany({
        where: {
            workoutId: workoutId,
            id: { in: newExercisesData.map(ex => ex.name + ex.order) } // This won't work reliably to get IDs
            // A better way would be to fetch all exercises for the workout after creation
            // OR if createMany returned IDs (which it doesn't reliably across DBs)
        },
         orderBy: { order: 'asc' }
    });

    // For simplicity, let's refetch all exercises for the workout
    const allWorkoutExercises = await prisma.exercise.findMany({
        where: { workoutId: workoutId },
        orderBy: { order: 'asc' }
    });

    return NextResponse.json(allWorkoutExercises);
    // return new NextResponse(null, { status: 204 }) // Or just return success

  } catch (error) {
    console.error("[WORKOUT_EXERCISES_ADD]", error)
    return new NextResponse("Internal error adding exercises", { status: 500 })
  }
} 