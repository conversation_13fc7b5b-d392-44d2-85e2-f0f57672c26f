/**
 * Format a price value into a currency string
 * @param price The price to format
 * @param locale The locale to use for formatting (default: en-US)
 * @param currency The currency to use (default: USD)
 * @returns Formatted price string
 */
export function formatPrice(
  price: number,
  locale = "en-US",
  currency = "USD"
): string {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
  }).format(price);
}

/**
 * Format a date string to a more readable format
 * @param dateString The date string to format
 * @param locale The locale to use for formatting (default: en-US)
 * @returns Formatted date string
 */
export function formatDate(
  dateString: string | Date,
  locale = "en-US"
): string {
  const date = typeof dateString === "string" 
    ? new Date(dateString) 
    : dateString;
  
  return new Intl.DateTimeFormat(locale, {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(date);
}

/**
 * Format a number with thousands separators
 * @param value The number to format
 * @param locale The locale to use for formatting (default: en-US)
 * @returns Formatted number string
 */
export function formatNumber(
  value: number, 
  locale = "en-US"
): string {
  return new Intl.NumberFormat(locale).format(value);
}

/**
 * Format file size in bytes to a human-readable format
 * @param bytes The file size in bytes
 * @param decimals Number of decimal places (default: 2)
 * @returns Formatted file size string
 */
export function formatFileSize(bytes: number, decimals = 2): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return (
    parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + " " + sizes[i]
  );
} 