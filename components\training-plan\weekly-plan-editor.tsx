"use client"

import {
  Dnd<PERSON>ontext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  KeyboardSensor,
  useSensor,
  useSensors,
  closestCenter,
  pointerWithin,
  useDroppable
} from "@dnd-kit/core"
import { SortableContext, arrayMove, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import {
  Copy,
  Trash2,
  GripVertical,
  Plus,
  Pencil,
  Activity,
  Dumbbell,
  Timer,
  Heart,
  Bike,
  Award,
  ChevronsRight,
  Calendar
} from "lucide-react"
import React, { useState, useEffect, MouseEvent, FormEvent } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ExerciseForm, ExerciseFormValues } from "@/components/training/exercise-form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { getExerciseIcon } from "@/lib/utils"

interface Exercise {
  id: string
  name: string
  sets: number
  reps: number
  weight: number
  category?: string
  iconName?: string
  video?: string
  description?: string
  targetMuscles?: string[]
  difficulty?: "Beginner" | "Intermediate" | "Advanced"
  duration?: string
  parentExerciseId?: string
  isAlternative?: boolean
  hasAlternatives?: boolean
}

interface DailyWorkout {
  id: string
  day: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday"
  exercises: Exercise[]
}

interface WeekPlan {
  id: string
  dailyWorkouts: DailyWorkout[]
}

interface Plan {
  id: string
  title: string
  description: string
  type: string
  weeks: WeekPlan[]
  [key: string]: any
}

interface WeeklyPlanEditorProps {
  plan: Plan
  onPlanUpdate: (updatedPlan: Plan) => void
  availableExercises: Exercise[]
  isEditMode?: boolean
  onFormSubmit?: (e: FormEvent) => void
  onExerciseCreated?: (exercise: Exercise) => void
}

// Add YouTube helper functions
function getYouTubeVideoId(url: string): string | null {
  if (!url) return null;
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
  const match = url.match(regExp)
  return match && match[2].length === 11 ? match[2] : null
}

function getYouTubeEmbedUrl(url: string): string | null {
  const videoId = getYouTubeVideoId(url)
  return videoId ? `https://www.youtube-nocookie.com/embed/${videoId}` : null
}

function renderExerciseIcon(iconName: string) {
  switch (iconName) {
    case "Dumbbell":
      return <Dumbbell className="h-5 w-5 text-blue-400" />
    case "Timer":
      return <Timer className="h-5 w-5 text-red-400" />
    case "Activity":
      return <Activity className="h-5 w-5 text-green-400" />
    case "Heart":
      return <Heart className="h-5 w-5 text-purple-400" />
    case "Bike":
      return <Bike className="h-5 w-5 text-yellow-400" />
    default:
      return <Award className="h-5 w-5 text-gray-400" />
  }
}

function DraggableExerciseItem({ exercise, onAdd, primaryExercises, availableDays }: {
  exercise: Exercise
  onAdd: (exercise: Exercise, targetDay?: string) => void
  primaryExercises?: Exercise[]
  availableDays?: { day: string; exercises?: Exercise[] }[]
}) {
  const { toast } = useToast()
  const [open, setOpen] = useState(false)
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useSortable({
    id: exercise.id,
    data: {
      type: 'available',
      exercise
    }
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: 'transform 200ms ease',
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className="shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all cursor-grab active:cursor-grabbing"
      {...attributes}
      {...listeners}
    >
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <div className="p-2 rounded-full bg-primary/10">
            {renderExerciseIcon(getExerciseIcon(exercise.category))}
          </div>
          <div className="flex-1">
            <p className="font-medium">{exercise.name}</p>
            <p className="text-sm text-muted-foreground">{exercise.category}</p>
          </div>
          <DropdownMenu open={open} onOpenChange={setOpen}>
            <DropdownMenuTrigger asChild>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="hover:bg-accent/10"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {/* First level: Select Day */}
              {availableDays && availableDays.length > 0 ? (
                availableDays.map(dayItem => (
                  <DropdownMenuSub key={dayItem.day}>
                    <DropdownMenuSubTrigger>
                      <Calendar className="h-4 w-4 mr-2" />
                      {dayItem.day}
                    </DropdownMenuSubTrigger>
                    <DropdownMenuSubContent>
                      {/* Second level: Choose Principal or Alternative */}
                      <DropdownMenuItem onClick={(e: React.MouseEvent) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onAdd(
                          { ...exercise, id: `${exercise.id}-${Date.now()}` },
                          dayItem.day
                        )
                        toast({
                          title: "Exercise Added",
                          description: `Added ${exercise.name} as principal exercise to ${dayItem.day}`
                        });
                        setOpen(false);
                      }}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add as Principal Exercise
                      </DropdownMenuItem>

                      {/* Alternative option - only if there are primary exercises in this day */}
                      {primaryExercises && primaryExercises.length > 0 && (
                        <DropdownMenuSub>
                          <DropdownMenuSubTrigger>
                            <ChevronsRight className="h-4 w-4 mr-2" />
                            Add as Alternative to...
                          </DropdownMenuSubTrigger>
                          <DropdownMenuSubContent>
                            <DropdownMenuLabel>Select Primary Exercise</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <div className="max-h-[200px] overflow-y-auto">
                              {/* Filter primary exercises for this specific day */}
                              {(() => {
                                const dayPrimaryExercises = primaryExercises.filter(pe => {
                                  // Find the workout this exercise belongs to
                                  const workout = availableDays.find(d => {
                                    return d.exercises && d.exercises.some(e => e.id === pe.id);
                                  });
                                  return workout && workout.day === dayItem.day;
                                });

                                return dayPrimaryExercises.length > 0 ? (
                                  dayPrimaryExercises.map(primaryExercise => (
                                    <DropdownMenuItem key={primaryExercise.id} onClick={(e: React.MouseEvent) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      const alternativeExercise = {
                                        ...exercise,
                                        id: `${exercise.id}-${Date.now()}`,
                                        parentExerciseId: primaryExercise.id,
                                        isAlternative: true
                                      };
                                      console.log('Adding as alternative via dropdown:', alternativeExercise);
                                      onAdd(alternativeExercise, dayItem.day)
                                      toast({
                                        title: "Alternative Added",
                                        description: `Added ${exercise.name} as alternative to ${primaryExercise.name} in ${dayItem.day}`
                                      });
                                      setOpen(false);
                                    }}>
                                      {primaryExercise.name}
                                    </DropdownMenuItem>
                                  ))
                                ) : (
                                  <DropdownMenuItem disabled>
                                    No primary exercises in {dayItem.day}
                                  </DropdownMenuItem>
                                );
                              })()}
                            </div>
                          </DropdownMenuSubContent>
                        </DropdownMenuSub>
                      )}
                    </DropdownMenuSubContent>
                  </DropdownMenuSub>
                ))
              ) : (
                <DropdownMenuItem disabled>
                  No days available
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={(e: React.MouseEvent) => {
                e.preventDefault();
                e.stopPropagation();
                const firstDay = availableDays && availableDays.length > 0 ? availableDays[0].day : null;
                if (firstDay) {
                  onAdd({ ...exercise, id: `${exercise.id}-${Date.now()}` }, firstDay);
                  toast({
                    title: "Exercise Added",
                    description: `Added ${exercise.name} to ${firstDay}`
                  });
                  setOpen(false);
                }
              }}>
                <Plus className="h-4 w-4 mr-2" />
                Quick Add to First Day
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
}

function SortableExerciseItem({ exercise, onUpdate, onRemove }: {
  exercise: Exercise
  onUpdate: (exercise: Exercise) => void
  onRemove: () => void
}) {
  const [isOver, setIsOver] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
    over,
  } = useSortable({
    id: exercise.id,
    data: {
      type: 'exercise',
      exercise,
    }
  })

  // Check if this is an alternative exercise
  const isAlternative = Boolean(exercise.isAlternative)

  // Check if this exercise has alternatives
  const hasAlternatives = Boolean(exercise.hasAlternatives)

  // Check if something is being dragged over this exercise
  useEffect(() => {
    if (over && over.id !== exercise.id) {
      setIsOver(true);
    } else {
      setIsOver(false);
    }
  }, [over, exercise.id]);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: 'transform 200ms ease, opacity 200ms ease, box-shadow 200ms ease, background-color 200ms ease',
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 10 : (isOver ? 5 : 1)
  }

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all relative
        ${isDragging ? 'ring-2 ring-primary' : ''}
        ${isAlternative ? 'bg-secondary/20 border-l-4 border-l-secondary' : ''}
        ${hasAlternatives ? 'border-l-4 border-l-primary' : ''}
        ${isOver ? 'ring-2 ring-secondary bg-secondary/10 shadow-lg' : ''}
      `}
    >
      {/* Alternative indicator overlay */}
      {isOver && (
        <div className="absolute inset-0 flex items-center justify-center bg-secondary/5 rounded-lg z-10 pointer-events-none">
          <div className="bg-secondary text-white px-3 py-1 rounded-full text-xs font-medium shadow-md">
            Will be added as alternative
          </div>
        </div>
      )}
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <div className="p-2 rounded-full bg-primary/10">
            {renderExerciseIcon(getExerciseIcon(exercise.category))}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <p className="font-medium">{exercise.name}</p>
              {isAlternative && (
                <span className="inline-flex items-center rounded-full bg-secondary/10 px-2 py-0.5 text-xs font-medium text-secondary">
                  Alternative
                </span>
              )}
              {hasAlternatives && (
                <span className="inline-flex items-center rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary">
                  Has Alternatives
                </span>
              )}
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>{exercise.category}</span>
              {exercise.sets && exercise.reps && (
                <>
                  <span>•</span>
                  <span>{exercise.sets} sets × {exercise.reps} reps</span>
                </>
              )}
              {exercise.duration && (
                <>
                  <span>•</span>
                  <span>{exercise.duration}</span>
                </>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => onUpdate(exercise)}
              className="hover:bg-accent/10"
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={onRemove}
              className="hover:bg-destructive/10 hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
            <div className="cursor-grab active:cursor-grabbing">
              <GripVertical className="h-4 w-4 text-muted-foreground" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function DailyWorkoutEditor({
  workout,
  onUpdate,
  availableExercises,
  onCopyDay,
  weekIndex
}: {
  workout: DailyWorkout
  onUpdate: (workout: DailyWorkout) => void
  availableExercises: Exercise[]
  onCopyDay: (sourceDay: string, targetDay: string) => void
  weekIndex: number
}) {
  const { toast } = useToast()
  const [editExercise, setEditExercise] = useState<Exercise | null>(null)
  const [isDroppableOver, setIsDroppableOver] = useState(false);
  const { setNodeRef: setDroppableRef, over } = useDroppable({
    id: `workout-${weekIndex}-${workout.day.toLowerCase()}`,
    data: {
      type: 'workout',
      weekIndex,
      day: workout.day
    }
  })

  // Check if something is being dragged over the main drop area
  useEffect(() => {
    if (over && over.id === `workout-${weekIndex}-${workout.day.toLowerCase()}`) {
      setIsDroppableOver(true);
    } else {
      setIsDroppableOver(false);
    }
  }, [over, weekIndex, workout.day]);

  const addExercise = (exercise: Exercise) => {
    onUpdate({
      ...workout,
      exercises: [...workout.exercises, exercise]
    })

    // Scroll the container to the bottom after adding an exercise
    setTimeout(() => {
      // Find the exercise container
      const container = document.querySelector(`#workout-${weekIndex}-${workout.day.toLowerCase()} .exercise-container`);
      if (container) {
        // Add extra padding to ensure there's always space for drag and drop
        container.scrollTop = container.scrollHeight + 200;
        console.log('Scrolled exercise container to bottom');
      } else {
        console.log('Could not find exercise container');
      }
    }, 100);
  }

  const removeExercise = (id: string) => {
    onUpdate({
      ...workout,
      exercises: workout.exercises.filter(item => item.id !== id)
    })
  }

  const updateExercise = (updatedExercise: Exercise) => {
    onUpdate({
      ...workout,
      exercises: workout.exercises.map(item =>
        item.id === updatedExercise.id ? updatedExercise : item
      )
    })
  }

  const handleEditClick = (exercise: Exercise) => {
    setEditExercise({...exercise})
  }

  const handleEditExercise = (updatedExercise: Exercise) => {
    updateExercise(updatedExercise)
    setEditExercise(null)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-medium">{workout.day}</h4>
        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => onCopyDay(workout.day, "")}
          >
            <Copy className="h-4 w-4 mr-2" />
            Copy Day
          </Button>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Day's Exercises ({workout.exercises.length})</h3>
        <div
          ref={setDroppableRef}
          className={`relative h-[500px] border-2 border-dashed rounded-lg p-4 overflow-y-auto bg-card/50 transition-all duration-200
            ${isDroppableOver ? 'border-primary bg-primary/5 shadow-lg' : 'border-gray-200'}
          `}
          style={{ display: 'flex', flexDirection: 'column' }}
          id={`workout-${weekIndex}-${workout.day.toLowerCase()}`}
        >
          {/* New exercise indicator - only shown when dragging over the container */}
          {isDroppableOver && (
            <div className="absolute top-2 right-2 bg-primary text-white px-3 py-1 rounded-full text-xs font-medium shadow-md z-10">
              Add as new exercise
            </div>
          )}
          {workout.exercises.length > 0 && (
            <div className="mb-4 p-3 bg-primary/5 border border-primary/20 rounded-md text-sm">
              <div className="flex items-center gap-2">
                <div className="p-1 rounded-full bg-primary/10">
                  <svg className="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p><span className="font-medium">Pro Tip:</span> Drag an exercise over another exercise to add it as an alternative option</p>
              </div>
            </div>
          )}
          <SortableContext
            items={workout.exercises.map(e => e.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-3 h-[400px] overflow-y-auto pr-2 custom-scrollbar exercise-container" style={{ overflowY: 'auto', paddingBottom: '100px', scrollbarWidth: 'thin' }}>
              {/* Group exercises by parent/alternative relationship */}
              {(() => {
                // First, filter out primary exercises (those that are not alternatives)
                const primaryExercises = workout.exercises.filter(exercise =>
                  // An exercise is primary if it's not marked as an alternative
                  !exercise.isAlternative &&
                  // AND it doesn't have a parent exercise ID
                  !exercise.parentExerciseId
                );

                console.log('Primary exercises:', primaryExercises);

                // Then, for each primary exercise, find its alternatives
                return primaryExercises.map(exercise => {
                  // Find any alternatives for this exercise
                  const alternatives = workout.exercises.filter(
                    alt => alt.isAlternative && alt.parentExerciseId === exercise.id
                  );

                  console.log(`Alternatives for ${exercise.name}:`, alternatives);

                  return (
                    <div key={exercise.id} className="space-y-1">
                      <SortableExerciseItem
                        exercise={{...exercise, hasAlternatives: alternatives.length > 0}}
                        onUpdate={handleEditClick}
                        onRemove={() => removeExercise(exercise.id)}
                      />

                      {/* Show alternatives indented */}
                      {alternatives.length > 0 && (
                        <div className="pl-4 space-y-1 border-l-2 border-secondary ml-4 mt-1">
                          {alternatives.map(alt => (
                            <SortableExerciseItem
                              key={alt.id}
                              exercise={alt}
                              onUpdate={handleEditClick}
                              onRemove={() => removeExercise(alt.id)}
                            />
                          ))}
                        </div>
                      )}
                    </div>
                  );
                });
              })()}

              {/* If there are any orphaned exercises (alternatives without parents), display them too */}
              {(() => {
                const orphanedExercises = workout.exercises.filter(exercise =>
                  // An exercise is orphaned if:
                  // 1. It's marked as an alternative
                  exercise.isAlternative &&
                  // 2. It has a parent exercise ID
                  exercise.parentExerciseId &&
                  // 3. The parent exercise doesn't exist in this workout
                  !workout.exercises.some(e => e.id === exercise.parentExerciseId)
                );

                console.log('Orphaned exercises:', orphanedExercises);

                return orphanedExercises.map(exercise => (
                  <div key={exercise.id} className="space-y-1">
                    <SortableExerciseItem
                      exercise={exercise}
                      onUpdate={handleEditClick}
                      onRemove={() => removeExercise(exercise.id)}
                    />
                  </div>
                ));
              })()}

              {/* If there are no exercises at all, show empty state */}
              {workout.exercises.length === 0 ? (
                <div className="flex flex-col items-center justify-center text-center h-[300px]">
                  <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                    <svg className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium mb-2">No Exercises Added</h3>
                  <p className="text-muted-foreground mb-6 max-w-xs">Drag exercises from the left panel or click + to add</p>

                  {/* Keep the drop zone but make it invisible for functionality */}
                  <div className="w-full max-w-sm p-4 mt-4 border-2 border-dashed border-transparent rounded-lg bg-transparent">
                    {/* Empty div to maintain drop functionality */}
                  </div>
                </div>
              ) : (
                /* Drop zone indicator at the bottom - only show when exercises exist */
                <div className={`mt-6 p-6 border-2 border-dashed rounded-lg text-center transition-all duration-200
                  ${isDroppableOver ? 'border-primary bg-primary/10 shadow-lg' : 'border-primary/30 bg-primary/5'}
                `}>
                  <div className="flex flex-col items-center justify-center py-2">
                    <svg className="h-6 w-6 text-primary mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    <p className="text-base font-medium text-primary/80">Drop exercises here to add more</p>

                    {/* Visual indicator when dragging over */}
                    {isDroppableOver && (
                      <span className="mt-2 inline-flex items-center rounded-full bg-primary px-2.5 py-0.5 text-xs font-medium text-white">
                        Add as new exercise
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          </SortableContext>
        </div>
      </div>

      {/* Exercise edit dialog */}
      {editExercise && (
        <Dialog open={!!editExercise} onOpenChange={() => setEditExercise(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="text-xl font-semibold">Edit Exercise</DialogTitle>
              <DialogDescription>
                Customize the exercise details for your training plan
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label>
                  Name <span className="text-destructive">*</span>
                </Label>
                <Input
                  value={editExercise.name}
                  onChange={(e) => setEditExercise({ ...editExercise, name: e.target.value })}
                  placeholder="Exercise name"
                  required
                />
              </div>

              <div>
                <Label>YouTube Video URL</Label>
                <div className="flex gap-2">
                  <Input
                    value={editExercise.video || ""}
                    onChange={(e) => setEditExercise({ ...editExercise, video: e.target.value })}
                    placeholder="https://youtube.com/watch?v=..."
                  />
                </div>
              </div>

              <div>
                <Label>Description</Label>
                <Textarea
                  value={editExercise.description || ""}
                  onChange={(e) => setEditExercise({ ...editExercise, description: e.target.value })}
                  placeholder="Describe how to perform this exercise"
                  className="min-h-[100px]"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Muscle Group</Label>
                  <Select
                    value={editExercise.category || ""}
                    onValueChange={(value) => setEditExercise({ ...editExercise, category: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select group" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="chest">Chest</SelectItem>
                      <SelectItem value="back">Back</SelectItem>
                      <SelectItem value="legs">Legs</SelectItem>
                      <SelectItem value="shoulders">Shoulders</SelectItem>
                      <SelectItem value="arms">Arms</SelectItem>
                      <SelectItem value="core">Core</SelectItem>
                      <SelectItem value="cardio">Cardio</SelectItem>
                      <SelectItem value="strength">Strength</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Difficulty</Label>
                  <Select
                    value={editExercise.difficulty || "Beginner"}
                    onValueChange={(value) => setEditExercise({
                      ...editExercise,
                      difficulty: value as "Beginner" | "Intermediate" | "Advanced"
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Beginner">Beginner</SelectItem>
                      <SelectItem value="Intermediate">Intermediate</SelectItem>
                      <SelectItem value="Advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Sets</Label>
                  <Input
                    type="number"
                    value={editExercise.sets}
                    onChange={(e) => setEditExercise({
                      ...editExercise,
                      sets: parseInt(e.target.value) || 0
                    })}
                    placeholder="e.g. 3"
                    min={1}
                  />
                </div>
                <div>
                  <Label>Reps</Label>
                  <Input
                    type="number"
                    value={editExercise.reps}
                    onChange={(e) => setEditExercise({
                      ...editExercise,
                      reps: parseInt(e.target.value) || 0
                    })}
                    placeholder="e.g. 12"
                    min={1}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Weight (kg)</Label>
                  <Input
                    type="number"
                    value={editExercise.weight}
                    onChange={(e) => setEditExercise({
                      ...editExercise,
                      weight: parseInt(e.target.value) || 0
                    })}
                    placeholder="e.g. 50"
                    min={0}
                  />
                </div>
                <div>
                  <Label>Duration (optional)</Label>
                  <Input
                    type="text"
                    value={editExercise.duration || ""}
                    onChange={(e) => setEditExercise({
                      ...editExercise,
                      duration: e.target.value
                    })}
                    placeholder="e.g. 30 min"
                  />
                </div>
              </div>

              <div>
                <Label>Target Muscles</Label>
                <Input
                  value={editExercise.targetMuscles?.join(", ") || ""}
                  onChange={(e) => setEditExercise({
                    ...editExercise,
                    targetMuscles: e.target.value
                      .split(",")
                      .map((m) => m.trim())
                      .filter(Boolean),
                  })}
                  placeholder="e.g. Chest, Triceps, Shoulders"
                />
              </div>
            </div>

            <div className="flex justify-end gap-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setEditExercise(null)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleEditExercise(editExercise)}
                variant="default"
              >
                Save Changes
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

function WeeklyExercises({
  week,
  weekIndex,
  onUpdate,
  availableExercises,
  onCopyWeek,
  onExerciseCreated
}: {
  week: WeekPlan
  weekIndex: number
  onUpdate: (weekIndex: number, updatedWeek: WeekPlan) => void
  availableExercises: Exercise[]
  onCopyWeek: (weekIndex: number, event: MouseEvent) => void
  onExerciseCreated?: () => void
}) {
  const { toast } = useToast()
  const [showCopyDayModal, setShowCopyDayModal] = useState(false)
  const [copySourceDay, setCopySourceDay] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isCreatingExercise, setIsCreatingExercise] = useState(false)

  const updateWorkout = (day: DailyWorkout['day'], updatedWorkout: DailyWorkout) => {
    const updatedWorkouts = week.dailyWorkouts.map(w =>
      w.day === day ? updatedWorkout : w
    )

    onUpdate(weekIndex, {
      ...week,
      dailyWorkouts: updatedWorkouts
    })
  }

  const handleCreateExercise = async (data: ExerciseFormValues) => {
    console.log('handleCreateExercise called with data:', data);
    setIsCreatingExercise(true);
    try {
      // Create the exercise via API
      console.log('Sending POST request to /api/exercises');
      const response = await fetch('/api/exercises', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...data,
          isTemplate: false, // Mark as custom exercise
          category: data.type, // Use type as category for icon display
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create exercise");
      }

      const newExercise = await response.json();

      // Add the new exercise to the available exercises list
      // This is a temporary solution that will work within this component
      // The exercise will be available until the page is refreshed

      // Create a copy of the exercise with the right structure for the UI
      const exerciseForUI = {
        ...newExercise,
        id: newExercise.id,
        name: newExercise.name,
        sets: newExercise.sets || 3,
        reps: newExercise.reps || 10,
        weight: newExercise.weight || 0,
        category: newExercise.type || "strength",
        difficulty: newExercise.difficulty || "beginner",
        description: newExercise.description || ""
      };

      // Show success message
      toast({
        title: "Success",
        description: "Custom exercise created successfully"
      });

      // Call the callback to refresh exercises
      if (onExerciseCreated) {
        console.log('Calling onExerciseCreated to refresh exercises');
        onExerciseCreated();
      } else {
        console.warn('onExerciseCreated callback is not defined');
      }

      setIsCreateDialogOpen(false);
    } catch (error: any) {
      console.error("Error creating exercise:", error);
      toast({
        title: "Error",
        description: `Failed to create exercise: ${error.message}`,
        variant: "destructive"
      });
    } finally {
      setIsCreatingExercise(false);
    }
  };

  const handleCopyDay = (sourceDay: string, targetDay: string) => {
    if (!targetDay) {
      setCopySourceDay(sourceDay)
      setShowCopyDayModal(true)
      return
    }

    const sourceWorkout = week.dailyWorkouts.find(w => w.day === sourceDay)
    if (!sourceWorkout) return

    const targetWorkout = week.dailyWorkouts.find(w => w.day === targetDay)
    if (!targetWorkout) return

    // Create a map of old IDs to new IDs to maintain parent-child relationships
    const idMap = new Map<string, string>()

    // First pass: create new IDs for all exercises
    sourceWorkout.exercises.forEach(exercise => {
      const newId = `${exercise.id}-copy-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
      idMap.set(exercise.id, newId)
    })

    // Second pass: deep copy exercises with new IDs and update parent references
    const copiedExercises = sourceWorkout.exercises.map(exercise => {
      const newId = idMap.get(exercise.id) || `${exercise.id}-copy-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

      // If this is an alternative exercise, update its parent reference
      let newParentId = exercise.parentExerciseId
      if (exercise.parentExerciseId && idMap.has(exercise.parentExerciseId)) {
        newParentId = idMap.get(exercise.parentExerciseId) || exercise.parentExerciseId
      }

      return {
        ...exercise,
        id: newId,
        parentExerciseId: newParentId
      }
    })

    updateWorkout(targetDay as DailyWorkout['day'], {
      ...targetWorkout,
      exercises: copiedExercises
    })

    toast({
      title: "Day copied",
      description: `Exercises from ${sourceDay} copied to ${targetDay}`,
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold">Week {weekIndex + 1} Exercises</h3>
        <Button
          type="button"
          variant="outline"
          onClick={(e) => onCopyWeek(weekIndex, e)}
        >
          <Copy className="h-4 w-4 mr-2" />
          Copy Week
        </Button>
      </div>

      <div className="space-y-4">
        {/* Two-column layout with fixed available exercises and scrollable days */}
        <div className="grid grid-cols-1 lg:grid-cols-[350px_1fr] gap-4">
          {/* Available Exercises Column - Fixed */}
          <div className="w-full">
            <div className="sticky top-4 border border-gray-200 dark:border-gray-800 rounded-lg p-4 shadow-sm">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-medium">Available Exercises</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    console.log('Create Exercise button clicked');
                    setIsCreateDialogOpen(true);
                  }}
                  className="whitespace-nowrap"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Exercise
                </Button>
              </div>

              {/* Search Bar */}
              <div className="relative mb-4">
                <input
                  type="text"
                  placeholder="Search exercises..."
                  className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <svg
                  className="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>

              <SortableContext
                items={availableExercises.map(e => e.id)}
                strategy={verticalListSortingStrategy}
              >
                <div className="space-y-3 max-h-[70vh] overflow-y-auto pr-2 custom-scrollbar">
                  {availableExercises
                    .filter(exercise =>
                      exercise.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      (exercise.category && exercise.category.toLowerCase().includes(searchQuery.toLowerCase()))
                    )
                    .map(exercise => (
                    <DraggableExerciseItem
                      key={exercise.id}
                      exercise={exercise}
                      onAdd={(exercise, targetDay) => {
                        // If targetDay is specified, add to that day
                        // Otherwise, add to the first day (default behavior)
                        if (targetDay) {
                          const targetWorkout = week.dailyWorkouts.find(w => w.day === targetDay);
                          if (targetWorkout) {
                            updateWorkout(targetWorkout.day, {
                              ...targetWorkout,
                              exercises: [...targetWorkout.exercises, exercise]
                            });
                            toast({
                              title: "Exercise Added",
                              description: `Added ${exercise.name} to ${targetDay}`
                            });
                          }
                        } else {
                          // Default behavior - add to first day
                          const firstWorkout = week.dailyWorkouts[0];
                          if (firstWorkout) {
                            updateWorkout(firstWorkout.day, {
                              ...firstWorkout,
                              exercises: [...firstWorkout.exercises, exercise]
                            });
                            toast({
                              title: "Exercise Added",
                              description: `Added ${exercise.name} to ${firstWorkout.day}`
                            });
                          }
                        }
                      }}
                      primaryExercises={week.dailyWorkouts.flatMap(w => w.exercises.filter(e => !e.isAlternative))}
                      availableDays={week.dailyWorkouts}
                    />
                  ))}
                </div>
              </SortableContext>
            </div>
          </div>

          {/* Days Columns - Scrollable */}
          <div className="relative overflow-hidden border border-gray-200 dark:border-gray-800 rounded-lg p-4 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium">Daily Workout Schedule</h3>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Scroll horizontally to view all days</span>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="animate-bounce-x">
                  <path d="M9 5L15 12L9 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
            <div className="overflow-x-auto pb-4 custom-scrollbar daily-workout-scroll-container">
              <div className="flex space-x-4" style={{ width: 'calc(100% + 120px)' }}>
                {week.dailyWorkouts.map((workout) => (
                  <div key={workout.day} className="w-[calc(100%-120px)] flex-shrink-0 bg-card border border-border rounded-lg p-3 shadow-sm hover:shadow-md transition-all">
                    <DailyWorkoutEditor
                      workout={workout}
                      onUpdate={(updatedWorkout) => updateWorkout(workout.day, updatedWorkout)}
                      availableExercises={availableExercises}
                      onCopyDay={handleCopyDay}
                      weekIndex={weekIndex}
                    />
                  </div>
                ))}
              </div>
            </div>
            <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-background to-transparent pointer-events-none"></div>
            <div className="absolute right-0 top-1/2 -translate-y-1/2 bg-gradient-to-l from-background to-transparent w-16 h-40 pointer-events-none"></div>
            <style jsx global>{`
              @keyframes bounce-x {
                0%, 100% { transform: translateX(0); }
                50% { transform: translateX(3px); }
              }
              .animate-bounce-x {
                animation: bounce-x 1s infinite;
              }
            `}</style>
          </div>
        </div>
      </div>

      {/* Copy Day Modal */}
      {showCopyDayModal && copySourceDay && (
        <Dialog open={showCopyDayModal} onOpenChange={setShowCopyDayModal}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Copy {copySourceDay} To</DialogTitle>
              <DialogDescription>
                Select which day you want to copy the exercises to.
              </DialogDescription>
            </DialogHeader>

            <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 py-4">
              {week.dailyWorkouts.map((workout) => {
                if (workout.day !== copySourceDay) {
                  return (
                    <Button
                      type="button"
                      key={workout.day}
                      variant="outline"
                      onClick={() => {
                        handleCopyDay(copySourceDay, workout.day)
                        setShowCopyDayModal(false)
                        setCopySourceDay(null)
                      }}
                    >
                      {workout.day}
                    </Button>
                  )
                }
                return null
              })}
            </div>

            <div className="flex justify-end">
              <Button
                type="button"
                variant="ghost"
                onClick={() => {
                  setShowCopyDayModal(false)
                  setCopySourceDay(null)
                }}
              >
                Cancel
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Create Exercise Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create Custom Exercise</DialogTitle>
          </DialogHeader>
          <ExerciseForm
            defaultValues={{
              name: "",
              description: "",
              type: "strength",
              difficulty: "beginner",
              sets: 3,
              reps: 10,
            }}
            onSubmit={handleCreateExercise}
            onCancel={() => setIsCreateDialogOpen(false)}
            isSubmitting={isCreatingExercise}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}

export function WeeklyPlanEditor({
  plan,
  onPlanUpdate,
  availableExercises: initialAvailableExercises,
  isEditMode = false,
  onFormSubmit
}: WeeklyPlanEditorProps) {
  const { toast } = useToast()
  const [activeWeekTab, setActiveWeekTab] = useState("0") // Default to first week
  const [copySourceWeek, setCopySourceWeek] = useState<number | null>(null)
  const [showCopyWeekModal, setShowCopyWeekModal] = useState(false)
  const [activeId, setActiveId] = useState<string | null>(null)
  const [availableExercises, setAvailableExercises] = useState<Exercise[]>(initialAvailableExercises)

  // Function to fetch exercises
  const fetchExercises = async () => {
    try {
      console.log('Fetching exercises in WeeklyPlanEditor');
      const response = await fetch('/api/exercises?templateOnly=false');
      if (response.ok) {
        const data = await response.json();
        console.log('Fetched exercises:', data);
        setAvailableExercises(data);
      } else {
        console.error('Failed to fetch exercises:', await response.text());
      }
    } catch (error) {
      console.error('Error fetching exercises:', error);
    }
  };

  // Fetch exercises when component mounts
  useEffect(() => {
    fetchExercises();
  }, []);

  // Setup proper sensor configuration for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Increased from 5 to 8 for better control
        tolerance: 5, // Added tolerance for more precise activation
        delay: 50 // Added small delay to prevent accidental drags
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Add week management functions
  const updatePlanDuration = (duration: number) => {
    if (duration < plan.weeks.length) {
      // Reduce weeks
      const updatedPlan = {
        ...plan,
        weeks: plan.weeks.slice(0, duration)
      };

      onPlanUpdate(updatedPlan);

      // If currently viewing a week that will be removed, switch to the last available week
      if (parseInt(activeWeekTab) >= duration) {
        setActiveWeekTab((duration - 1).toString())
      }
    } else if (duration > plan.weeks.length) {
      // Add more weeks
      const newWeeks = [...plan.weeks]
      const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]

      for (let i = plan.weeks.length; i < duration; i++) {
        newWeeks.push({
          id: `week-${i+1}`,
          dailyWorkouts: daysOfWeek.map(day => ({
            id: `week-${i+1}-${day.toLowerCase()}`,
            day: day as DailyWorkout['day'],
            exercises: []
          }))
        })
      }

      const updatedPlan = {
        ...plan,
        weeks: newWeeks
      };

      onPlanUpdate(updatedPlan);
    }
  }

  const updateWeek = (weekIndex: number, updatedWeek: WeekPlan) => {
    const updatedWeeks = [...plan.weeks]
    updatedWeeks[weekIndex] = updatedWeek

    const updatedPlan = {
      ...plan,
      weeks: updatedWeeks
    };

    onPlanUpdate(updatedPlan);
  }

  const handleCopyWeek = (sourceWeekIndex: number, event: MouseEvent) => {
    // Prevent form submission
    event.preventDefault();
    event.stopPropagation();

    setCopySourceWeek(sourceWeekIndex)
    setShowCopyWeekModal(true)
  }

  const copyWeek = (targetWeekIndex: number) => {
    if (
      copySourceWeek !== null &&
      copySourceWeek >= 0 &&
      copySourceWeek < plan.weeks.length &&
      targetWeekIndex >= 0 &&
      targetWeekIndex < plan.weeks.length &&
      copySourceWeek !== targetWeekIndex
    ) {
      const sourceWeek = plan.weeks[copySourceWeek]

      // Create a map of old IDs to new IDs for each day's exercises
      const idMaps = sourceWeek.dailyWorkouts.map(() => new Map<string, string>())

      // First pass: create new IDs for all exercises in each day
      sourceWeek.dailyWorkouts.forEach((workout, dayIndex) => {
        workout.exercises.forEach(exercise => {
          const newId = `${exercise.id}-copy-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
          idMaps[dayIndex].set(exercise.id, newId)
        })
      })

      // Deep copy exercises with new IDs to ensure uniqueness
      const copiedExercises = sourceWeek.dailyWorkouts.map((item, dayIndex) => {
        const idMap = idMaps[dayIndex]

        return {
          ...item,
          id: `${item.id}-copy-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          exercises: item.exercises.map(exercise => {
            const newId = idMap.get(exercise.id) || `${exercise.id}-copy-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

            // If this is an alternative exercise, update its parent reference
            let newParentId = exercise.parentExerciseId
            if (exercise.parentExerciseId && idMap.has(exercise.parentExerciseId)) {
              newParentId = idMap.get(exercise.parentExerciseId) || exercise.parentExerciseId
            }

            return {
              ...exercise,
              id: newId,
              parentExerciseId: newParentId
            }
          })
        }
      })

      const updatedWeeks = [...plan.weeks];
      updatedWeeks[targetWeekIndex] = {
        ...plan.weeks[targetWeekIndex],
        dailyWorkouts: copiedExercises
      };

      const updatedPlan = {
        ...plan,
        weeks: updatedWeeks
      };

      onPlanUpdate(updatedPlan);

      toast({
        title: "Week copied",
        description: `Week ${copySourceWeek + 1} workouts copied to Week ${targetWeekIndex + 1}`,
      })
    }
    setCopySourceWeek(null)
    setShowCopyWeekModal(false)
  }

  // Handle drag events
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over) return;

    const activeId = active.id;
    const overId = over.id;

    console.log('Drag end event:', {
      activeId,
      overId,
      activeData: active.data.current,
      overData: over.data.current
    });

    if (activeId === overId) return;

    // Check if we're dropping onto an exercise (for alternatives)
    const isOverExercise = over.data?.current?.type === 'exercise';

    if (isOverExercise) {
      // We're dropping onto another exercise - create an alternative
      console.log('Creating alternative exercise');

      // Find the target exercise
      let targetExercise: Exercise | null = null;
      let targetWorkoutIndex = -1;
      let weekIndex = -1;

      // Search through all weeks and workouts to find the target exercise
      for (let wi = 0; wi < plan.weeks.length; wi++) {
        const week = plan.weeks[wi];
        for (let di = 0; di < week.dailyWorkouts.length; di++) {
          const workout = week.dailyWorkouts[di];
          const exercise = workout.exercises.find(e => e.id === overId);
          if (exercise) {
            targetExercise = exercise;
            targetWorkoutIndex = di;
            weekIndex = wi;
            break;
          }
        }
        if (targetExercise) break;
      }

      if (!targetExercise || targetWorkoutIndex === -1 || weekIndex === -1) return;

      // Get the active exercise data
      let exerciseToMove: Exercise | null = null;
      let sourceWorkoutIndex = -1;
      let sourceWeekIndex = -1;

      // If dragging from available exercises
      const activeData = active.data.current;
      if (activeData && activeData.type === 'available') {
        const exercise = activeData.exercise as Exercise;

        // Create a copy of the exercise with a unique ID
        exerciseToMove = {
          ...exercise,
          id: `${exercise.id}-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
          parentExerciseId: targetExercise.id,
          isAlternative: true
        };
      } else {
        // Find the source exercise in the plan
        for (let wi = 0; wi < plan.weeks.length; wi++) {
          const week = plan.weeks[wi];
          for (let di = 0; di < week.dailyWorkouts.length; di++) {
            const workout = week.dailyWorkouts[di];
            const exercise = workout.exercises.find(e => e.id === activeId);
            if (exercise) {
              exerciseToMove = {
                ...exercise,
                parentExerciseId: targetExercise.id,
                isAlternative: true
              };
              sourceWorkoutIndex = di;
              sourceWeekIndex = wi;
              break;
            }
          }
          if (exerciseToMove) break;
        }
      }

      if (!exerciseToMove) return;

      // Create a deep copy of the plan
      const updatedPlan = JSON.parse(JSON.stringify(plan));

      // If moving from within the plan, remove from source
      if (sourceWorkoutIndex !== -1 && sourceWeekIndex !== -1) {
        updatedPlan.weeks[sourceWeekIndex].dailyWorkouts[sourceWorkoutIndex].exercises =
          updatedPlan.weeks[sourceWeekIndex].dailyWorkouts[sourceWorkoutIndex].exercises.filter(
            (e: Exercise) => e.id !== activeId
          );
      }

      // Make sure the exercise is properly marked as an alternative
      const finalExerciseToMove = {
        ...exerciseToMove,
        isAlternative: true,
        parentExerciseId: targetExercise.id
      };

      console.log('Adding as alternative:', finalExerciseToMove);

      // Add as alternative to target
      updatedPlan.weeks[weekIndex].dailyWorkouts[targetWorkoutIndex].exercises.push(finalExerciseToMove);

      // Update the plan
      onPlanUpdate(updatedPlan);
      toast({
        title: "Alternative Exercise Added",
        description: `Added ${exerciseToMove.name} as an alternative to ${targetExercise.name}`,
      });

      // Scroll the container to the bottom after adding an alternative exercise
      setTimeout(() => {
        // Find the exercise container
        const container = document.querySelector(`#workout-${weekIndex}-${week.dailyWorkouts[targetWorkoutIndex].day.toLowerCase()} .exercise-container`);
        if (container) {
          // Add extra padding to ensure there's always space for drag and drop
          container.scrollTop = container.scrollHeight + 200;
          console.log('Scrolled exercise container to bottom after adding alternative');
        } else {
          console.log('Could not find exercise container');
        }
      }, 100);

      return;
    }

    // Get the target week and day from the droppable area data
    const overData = over.data.current as { type: string; weekIndex: number; day: string } | undefined;
    if (!overData || overData.type !== 'workout') return;

    const weekIndex = overData.weekIndex;
    const week = plan.weeks[weekIndex];

    // Check if dragging from available exercises
    const activeData = active.data.current;
    if (activeData && activeData.type === 'available') {
      const exercise = activeData.exercise as Exercise;

      // Create a copy of the exercise with a unique ID
      const newExercise = {
        ...exercise,
        id: `${exercise.id}-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`
      };

      // Find the target workout
      const targetWorkoutIndex = week.dailyWorkouts.findIndex(w =>
        w.day.toLowerCase() === overData.day.toLowerCase()
      );

      if (targetWorkoutIndex !== -1) {
        console.log('Adding exercise to day:', {
          exercise: newExercise,
          targetDay: overData.day,
          targetWorkoutIndex
        });

        // Create a deep copy of the plan to ensure proper state updates
        const updatedPlan = JSON.parse(JSON.stringify(plan));

        // Add to the specific daily workout in the target week
        updatedPlan.weeks[weekIndex].dailyWorkouts[targetWorkoutIndex].exercises.push(newExercise);

        // Update the plan
        onPlanUpdate(updatedPlan);

        toast({
          title: "Exercise Added",
          description: `Added ${newExercise.name} to ${overData.day}`
        });

        // Scroll the container to the bottom after adding an exercise
        setTimeout(() => {
          // Find the exercise container
          const container = document.querySelector(`#workout-${weekIndex}-${overData.day.toLowerCase()} .exercise-container`);
          if (container) {
            // Add extra padding to ensure there's always space for drag and drop
            container.scrollTop = container.scrollHeight + 200;
            console.log('Scrolled exercise container to bottom after drag');
          } else {
            console.log('Could not find exercise container');
          }
        }, 100);
      }
    } else {
      // Find the source and target daily workouts in the current week
      const sourceWorkoutIndex = week.dailyWorkouts.findIndex(w =>
        w.exercises.some(e => e.id === activeId)
      );

      const targetWorkoutIndex = week.dailyWorkouts.findIndex(w =>
        w.day.toLowerCase() === overData.day.toLowerCase()
      );

      if (sourceWorkoutIndex !== -1 && targetWorkoutIndex !== -1) {
        console.log('Moving exercise between workouts:', {
          sourceWorkoutIndex,
          targetWorkoutIndex,
          activeId,
          overId
        });

        // Create a deep copy of the plan to ensure proper state updates
        const updatedPlan = JSON.parse(JSON.stringify(plan));

        // Moving within the same daily workout
        if (sourceWorkoutIndex === targetWorkoutIndex) {
          const workout = updatedPlan.weeks[weekIndex].dailyWorkouts[sourceWorkoutIndex];
          const oldIndex = workout.exercises.findIndex(e => e.id === activeId);
          const newIndex = workout.exercises.findIndex(e => e.id === overId);

          if (oldIndex !== -1 && newIndex !== -1) {
            // Use array move to reorder the exercises
            workout.exercises = arrayMove([...workout.exercises], oldIndex, newIndex);

            // Update the plan
            onPlanUpdate(updatedPlan);

            toast({
              title: "Exercise Reordered",
              description: `Reordered exercises in ${workout.day}`
            });
          }
        } else {
          // Moving between different daily workouts in the current week
          const sourceWorkout = updatedPlan.weeks[weekIndex].dailyWorkouts[sourceWorkoutIndex];
          const targetWorkout = updatedPlan.weeks[weekIndex].dailyWorkouts[targetWorkoutIndex];

          // Find the exercise to move
          const exerciseIndex = sourceWorkout.exercises.findIndex(e => e.id === activeId);
          if (exerciseIndex === -1) return;

          // Get the exercise and remove it from source
          const exerciseToMove = sourceWorkout.exercises[exerciseIndex];
          sourceWorkout.exercises.splice(exerciseIndex, 1);

          // Reset alternative status when moving between workouts
          const updatedExerciseToMove = {
            ...exerciseToMove,
            parentExerciseId: undefined,
            isAlternative: false
          };

          // Add to target
          targetWorkout.exercises.push(updatedExerciseToMove);

          // Update the plan
          onPlanUpdate(updatedPlan);

          toast({
            title: "Exercise Moved",
            description: `Moved ${updatedExerciseToMove.name} from ${sourceWorkout.day} to ${targetWorkout.day}`
          });
        }
      }
    }

  setActiveId(null);
}

  // Intercept form submission when in copy mode
  const handleFormSubmitIntercept = (e: FormEvent) => {
    if (showCopyWeekModal) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }

    // Pass to parent handler if provided
    if (onFormSubmit) {
      onFormSubmit(e);
    }
  };

  return (
    <form onSubmit={handleFormSubmitIntercept} className="space-y-8" data-weekly-plan-editor>
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-md mb-4">
        <h3 className="font-medium text-blue-800 mb-1">Pro Tip: Alternative Exercises</h3>
        <p className="text-sm text-blue-700">
          You can now create alternative exercises by dragging one exercise on top of another.
          This allows you to provide options for your clients when they can't perform a specific exercise.
        </p>
      </div>
      <div className="grid gap-4">
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle>Weekly Exercise Plan</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <Label htmlFor="duration">Plan Duration (weeks)</Label>
                  <Select
                    value={plan.weeks.length.toString()}
                    onValueChange={(value) => updatePlanDuration(parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => i + 1).map((weeks) => (
                        <SelectItem key={`weeks-${weeks}`} value={weeks.toString()}>
                          {weeks} {weeks === 1 ? 'week' : 'weeks'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <DndContext
          sensors={sensors}
          collisionDetection={pointerWithin}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <Tabs
            value={`week-${parseInt(activeWeekTab) + 1}`}
            onValueChange={(value) => setActiveWeekTab((parseInt(value.split('-')[1]) - 1).toString())}
            className="w-full"
          >
            <TabsList className="w-full justify-start">
              {plan.weeks.map((week, index) => (
                <TabsTrigger key={`week-tab-${index + 1}`} value={`week-${index + 1}`}>
                  Week {index + 1}
                </TabsTrigger>
              ))}
            </TabsList>
            {plan.weeks.map((week, index) => (
              <TabsContent key={`week-content-${index + 1}`} value={`week-${index + 1}`}>
                <WeeklyExercises
                  week={week}
                  weekIndex={index}
                  onUpdate={updateWeek}
                  availableExercises={availableExercises}
                  onCopyWeek={handleCopyWeek}
                  onExerciseCreated={fetchExercises}
                />
              </TabsContent>
            ))}
          </Tabs>
          <DragOverlay>
            {activeId ? (
              <Card className="shadow-lg">
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <div className="p-2 rounded-full bg-primary/10">
                      {(() => {
                        // First check available exercises
                        let exercise = availableExercises.find(e => e.id === activeId);

                        // If not found, check the current week
                        if (!exercise) {
                          const weekIndex = parseInt(activeWeekTab);
                          const week = plan.weeks[weekIndex];
                          exercise = week?.dailyWorkouts[0]?.exercises[0];
                        }

                        return renderExerciseIcon(getExerciseIcon(exercise?.category))
                      })()}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">
                        {(() => {
                          // First check available exercises
                          let exercise = availableExercises.find(e => e.id === activeId);

                          // If not found, check the current week
                          if (!exercise) {
                            const weekIndex = parseInt(activeWeekTab);
                            const week = plan.weeks[weekIndex];
                            exercise = week?.dailyWorkouts[0]?.exercises[0];
                          }

                          return exercise?.name || 'Exercise';
                        })()}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {(() => {
                          // First check available exercises
                          let exercise = availableExercises.find(e => e.id === activeId);

                          // If not found, check the current week
                          if (!exercise) {
                            const weekIndex = parseInt(activeWeekTab);
                            const week = plan.weeks[weekIndex];
                            exercise = week?.dailyWorkouts[0]?.exercises[0];
                          }

                          return exercise?.category || 'No category';
                        })()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : null}
          </DragOverlay>
        </DndContext>
      </div>

      {/* Copy Week Modal */}
      {showCopyWeekModal && copySourceWeek !== null && (
        <Dialog open={showCopyWeekModal} onOpenChange={setShowCopyWeekModal}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Copy Week {copySourceWeek + 1} To</DialogTitle>
              <DialogDescription>
                Select which week you want to copy the workouts to.
              </DialogDescription>
            </DialogHeader>

            <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 py-4">
              {plan.weeks.map((_, index) => {
                if (index !== copySourceWeek) {
                  return (
                    <Button
                      type="button"
                      key={`copy-week-btn-${index}`}
                      variant="outline"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        copyWeek(index);
                      }}
                    >
                      Week {index + 1}
                    </Button>
                  )
                }
                return null
              })}
            </div>

            <div className="flex justify-end">
              <Button
                type="button"
                variant="ghost"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setCopySourceWeek(null)
                  setShowCopyWeekModal(false)
                }}
              >
                Cancel
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </form>
  )
}