import { NextResponse } from "next/server"
import { Resend } from "resend"
import crypto from "crypto"
import { prisma } from "@/lib/prisma"
import { resetPasswordRequestSchema } from "@/lib/validations/auth"
import logger from "@/lib/logger"

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY)
const FROM_EMAIL = process.env.EMAIL_FROM || "<EMAIL>"
const APP_BASE_URL = process.env.NEXTAUTH_URL || "http://localhost:3000"

// --- Helpers (can be shared in a util file) ---
function generateResetToken() {
  return crypto.randomBytes(32).toString("hex")
}

function hashToken(token: string) {
  return crypto.createHash("sha256").update(token).digest("hex")
}
// --- End Helpers ---

export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // --- Zod <PERSON>idation ---
    const validationResult = resetPasswordRequestSchema.safeParse(body)
    if (!validationResult.success) {
      logger.warn({ 
        validationErrors: validationResult.error.flatten().fieldErrors, 
        receivedEmail: body.email
      }, "Invalid password reset request input.")
      return NextResponse.json({ message: "If an account exists for this email, a password reset link has been sent." }, { status: 200 })
    }
    const { email } = validationResult.data
    // --- End Zod Validation ---

    // 1. Find the user
    const user = await prisma.user.findUnique({
      where: { email },
    })

    // SECURITY: Always return a success-like response even if user doesn't exist or isn't verified
    // This prevents email enumeration attacks.
    if (!user || !user.emailVerified) {
      logger.info({ email }, `Password reset requested for non-existent or unverified email.`)
      return NextResponse.json({ message: "If an account exists for this email, a password reset link has been sent." }, { status: 200 })
    }

    // 2. Generate and store reset token
    const resetToken = generateResetToken()
    const hashedResetToken = hashToken(resetToken)
    const expires = new Date(Date.now() + 1 * 60 * 60 * 1000) // 1 hour from now

    // Delete any existing reset tokens for this user first
    await (prisma as any).passwordResetToken.deleteMany({
      where: { identifier: email },
    })
    
    // Create the new token
    await (prisma as any).passwordResetToken.create({
      data: {
        identifier: email,
        token: hashedResetToken,
        expires,
      },
    })

    // 3. Send password reset email
    const resetLink = `${APP_BASE_URL}/reset-password?token=${resetToken}`

    try {
      await resend.emails.send({
        from: FROM_EMAIL,
        to: email,
        subject: "Reset Your Password",
        html: `
          <h1>Reset Your Password</h1>
          <p>You requested a password reset. Click the link below to set a new password:</p>
          <p><a href="${resetLink}" target="_blank">Reset Password</a></p>
          <p>This link will expire in 1 hour.</p>
          <p>If you didn't request this, please ignore this email.</p>
        `,
      })
      logger.info({ email }, `Password reset email sent`)
    } catch (emailError) {
      logger.error({ email, error: emailError }, `Failed to send password reset email`)
      // If email fails, the token is still in the DB, but user won't get the link.
      // Return a generic server error.
      return NextResponse.json({ message: "Failed to send reset email. Please try again later." }, { status: 500 })
    }

    // 4. Return success response (same as user not found for security)
    return NextResponse.json({ message: "If an account exists for this email, a password reset link has been sent." }, { status: 200 })

  } catch (error) {
    logger.error({ error }, "Password Reset Request process failed")
    return NextResponse.json(
      { message: "An internal server error occurred." },
      { status: 500 },
    )
  }
}