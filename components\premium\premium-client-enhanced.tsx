"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON><PERSON>,
  CalendarD<PERSON>,
  <PERSON><PERSON><PERSON>,
  MessageSquare,
  Activity,
  Sparkles,
  Trophy,
  TrendingUp,
  Users,
  Clock,
  Target,
  Heart,
  ArrowUpRight,
  Check,
  Plus,
  Calendar
} from "lucide-react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { PremiumTabsEnhanced } from "@/components/premium/premium-tabs-enhanced"
import { DailyFlowMockup } from "@/components/daily-flow/daily-flow-mockup"
import { PremiumTraining } from "@/components/premium/premium-training"
import { PremiumCalendar } from "@/components/premium/premium-calendar"
import { PremiumMessages } from "@/components/premium/premium-messages"

export function PremiumClientEnhanced() {
  const { data: session } = useSession()
  const [activeTab, setActiveTab] = useState("overview")
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    workoutCompletionRate: 65,
    completedWorkouts: 13,
    totalPlannedWorkouts: 20,
    streakDays: 7,
    nextSession: {
      date: new Date(Date.now() + 86400000), // Tomorrow
      time: "10:00 AM",
      title: "Strength Training",
      coach: "John Coach"
    },
    nutritionProgress: {
      protein: 75,
      carbs: 60,
      fat: 80,
      calories: 70
    },
    recentAchievements: [
      { id: 1, title: "7-Day Streak", icon: "🔥", date: "Today" },
      { id: 2, title: "Completed 10 Workouts", icon: "💪", date: "Yesterday" },
      { id: 3, title: "Logged All Meals", icon: "🥗", date: "3 days ago" }
    ]
  })

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
  }

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        // Fetch data from API endpoints
        // This would be replaced with actual API calls

        // Simulate API delay
        setTimeout(() => {
          setLoading(false)
        }, 1000)
      } catch (error) {
        console.error("Error fetching data:", error)
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  return (
    <div className="space-y-6">
      {/* Premium Tabs */}
      <PremiumTabsEnhanced activeTab={activeTab} onTabChange={handleTabChange} />

      {/* Overview Tab */}
      {activeTab === "overview" && (
        <div className="space-y-6">
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="premium-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Dumbbell className="h-4 w-4 text-primary" />
                  Workout Completion
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.completedWorkouts}/{stats.totalPlannedWorkouts}</div>
                <div className="mt-2">
                  <Progress className="premium-progress h-2" value={stats.workoutCompletionRate}>
                    <div
                      className="premium-progress-indicator"
                      style={{ width: `${stats.workoutCompletionRate}%` }}
                    />
                  </Progress>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  {stats.workoutCompletionRate}% complete this month
                </p>
              </CardContent>
            </Card>

            <Card className="premium-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Activity className="h-4 w-4 text-primary" />
                  Current Streak
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.streakDays} days</div>
                <div className="flex items-center gap-1 mt-2">
                  {[...Array(7)].map((_, i) => (
                    <div
                      key={i}
                      className={`h-2 flex-1 rounded-full ${i < stats.streakDays % 7 ? 'bg-primary' : 'bg-primary/20'}`}
                    />
                  ))}
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Keep it up! You're on fire 🔥
                </p>
              </CardContent>
            </Card>

            <Card className="premium-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <CalendarDays className="h-4 w-4 text-primary" />
                  Next Session
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.nextSession.time}</div>
                <div className="flex items-center gap-2 mt-2">
                  <div className="w-1 h-8 bg-primary rounded-full"></div>
                  <div>
                    <p className="font-medium text-sm">{stats.nextSession.title}</p>
                    <p className="text-xs text-muted-foreground">with {stats.nextSession.coach}</p>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  {stats.nextSession.date.toLocaleDateString()}
                </p>
              </CardContent>
            </Card>

            <Card className="premium-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Trophy className="h-4 w-4 text-primary" />
                  Achievements
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.recentAchievements.length}</div>
                <div className="flex items-center gap-1 mt-2">
                  {stats.recentAchievements.slice(0, 3).map((achievement) => (
                    <div key={achievement.id} className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-sm">
                      {achievement.icon}
                    </div>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  {stats.recentAchievements[0].title} - {stats.recentAchievements[0].date}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Nutrition & Progress */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="premium-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Heart className="h-5 w-5 text-primary" />
                    Nutrition Progress
                  </CardTitle>
                  <Badge variant="outline" className="bg-primary/10 hover:bg-primary/20 transition-colors">
                    Today
                  </Badge>
                </div>
                <CardDescription>Track your daily nutrition goals</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-primary"></div>
                        <span className="text-sm font-medium">Protein</span>
                      </div>
                      <span className="text-sm">{stats.nutritionProgress.protein}%</span>
                    </div>
                    <Progress className="premium-progress h-2" value={stats.nutritionProgress.protein}>
                      <div
                        className="premium-progress-indicator"
                        style={{ width: `${stats.nutritionProgress.protein}%` }}
                      />
                    </Progress>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                        <span className="text-sm font-medium">Carbs</span>
                      </div>
                      <span className="text-sm">{stats.nutritionProgress.carbs}%</span>
                    </div>
                    <Progress className="h-2 bg-primary/10" value={stats.nutritionProgress.carbs}>
                      <div
                        className="h-full bg-amber-500 rounded-full"
                        style={{ width: `${stats.nutritionProgress.carbs}%` }}
                      />
                    </Progress>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                        <span className="text-sm font-medium">Fat</span>
                      </div>
                      <span className="text-sm">{stats.nutritionProgress.fat}%</span>
                    </div>
                    <Progress className="h-2 bg-primary/10" value={stats.nutritionProgress.fat}>
                      <div
                        className="h-full bg-blue-500 rounded-full"
                        style={{ width: `${stats.nutritionProgress.fat}%` }}
                      />
                    </Progress>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <span className="text-sm font-medium">Calories</span>
                      </div>
                      <span className="text-sm">{stats.nutritionProgress.calories}%</span>
                    </div>
                    <Progress className="h-2 bg-primary/10" value={stats.nutritionProgress.calories}>
                      <div
                        className="h-full bg-green-500 rounded-full"
                        style={{ width: `${stats.nutritionProgress.calories}%` }}
                      />
                    </Progress>
                  </div>
                </div>

                <div className="mt-6 flex justify-end">
                  <Button
                    size="sm"
                    className="bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 text-white border-none"
                    onClick={() => handleTabChange("daily-flow")}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Log Nutrition
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="premium-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-primary" />
                    Weekly Progress
                  </CardTitle>
                  <Badge variant="outline" className="bg-primary/10 hover:bg-primary/20 transition-colors">
                    This Week
                  </Badge>
                </div>
                <CardDescription>Your fitness journey at a glance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-primary/5 rounded-lg p-4 border border-primary/10">
                      <div className="flex items-center gap-2 mb-2">
                        <Target className="h-5 w-5 text-primary" />
                        <span className="font-medium">Goal Progress</span>
                      </div>
                      <div className="text-2xl font-bold">75%</div>
                      <p className="text-xs text-muted-foreground mt-1">Ahead of schedule</p>
                    </div>

                    <div className="bg-primary/5 rounded-lg p-4 border border-primary/10">
                      <div className="flex items-center gap-2 mb-2">
                        <Users className="h-5 w-5 text-primary" />
                        <span className="font-medium">Coach Sessions</span>
                      </div>
                      <div className="text-2xl font-bold">2/4</div>
                      <p className="text-xs text-muted-foreground mt-1">This month</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Recent Achievements</h4>
                    <div className="space-y-2">
                      {stats.recentAchievements.map((achievement) => (
                        <div key={achievement.id} className="flex items-center justify-between p-2 rounded-md bg-background/60 border border-primary/10">
                          <div className="flex items-center gap-2">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-sm">
                              {achievement.icon}
                            </div>
                            <span className="font-medium text-sm">{achievement.title}</span>
                          </div>
                          <span className="text-xs text-muted-foreground">{achievement.date}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="mt-2 flex justify-end">
                    <Button
                      size="sm"
                      variant="outline"
                      className="border-primary/20 hover:bg-primary/5 hover:text-primary hover:border-primary/30 transition-all duration-200"
                      onClick={() => handleTabChange("achievements")}
                    >
                      <Trophy className="h-4 w-4 mr-2" />
                      View All Achievements
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Upcoming Sessions */}
          <Card className="premium-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-primary" />
                  Upcoming Sessions
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-primary/20 hover:bg-primary/5 hover:text-primary hover:border-primary/30 transition-all duration-200"
                  onClick={() => handleTabChange("calendar")}
                >
                  View Calendar
                </Button>
              </div>
              <CardDescription>Your scheduled training and coaching sessions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 rounded-lg bg-primary/5 border border-primary/10">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
                      <Dumbbell className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-medium">Strength Training</h4>
                      <p className="text-xs text-muted-foreground">with John Coach</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">Tomorrow</div>
                    <div className="text-xs text-muted-foreground">10:00 AM</div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-primary/5 border border-primary/10">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
                      <MessageSquare className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-medium">Coaching Call</h4>
                      <p className="text-xs text-muted-foreground">with John Coach</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">Friday</div>
                    <div className="text-xs text-muted-foreground">2:00 PM</div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-primary/5 border border-primary/10">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
                      <Activity className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-medium">HIIT Workout</h4>
                      <p className="text-xs text-muted-foreground">with Sarah Trainer</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">Next Monday</div>
                    <div className="text-xs text-muted-foreground">6:00 PM</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Training Tab */}
      {activeTab === "training" && (
        <div className="space-y-6">
          <Card className="premium-card p-8 flex flex-col items-center justify-center text-center">
            <CardTitle className="text-2xl mb-4">Training Programs</CardTitle>
            <CardDescription className="max-w-md mb-6">
              View and manage your premium training programs and workouts.
            </CardDescription>
            <PremiumTraining />
          </Card>
        </div>
      )}

      {/* Daily Flow Tab */}
      {activeTab === "daily-flow" && (
        <div className="space-y-6">
          <Card className="premium-card p-8">
            <DailyFlowMockup />
          </Card>
        </div>
      )}

      {/* Calendar Tab */}
      {activeTab === "calendar" && (
        <div className="space-y-6">
          <Card className="premium-card p-8 flex flex-col items-center justify-center text-center">
            <CardTitle className="text-2xl mb-4">Coaching Calendar</CardTitle>
            <CardDescription className="max-w-md mb-6">
              Schedule and manage your 1:1 coaching sessions.
            </CardDescription>
            <PremiumCalendar />
          </Card>
        </div>
      )}

      {/* Messages Tab */}
      {activeTab === "messages" && (
        <div className="space-y-6">
          <Card className="premium-card p-8 flex flex-col items-center justify-center text-center">
            <CardTitle className="text-2xl mb-4">Coach Messages</CardTitle>
            <CardDescription className="max-w-md mb-6">
              Communicate with your coach and get personalized guidance.
            </CardDescription>
            <PremiumMessages />
          </Card>
        </div>
      )}

      {/* Achievements Tab */}
      {activeTab === "achievements" && (
        <div className="space-y-6">
          <Card className="premium-card">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Trophy className="h-5 w-5 text-primary" />
                Your Achievements
              </CardTitle>
              <CardDescription>Track your progress and celebrate your wins</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-primary/5 rounded-lg p-4 border border-primary/10 flex flex-col items-center text-center">
                    <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center text-2xl mb-3">
                      {["🔥", "💪", "🥗", "🏆", "⚡", "🌟"][i % 6]}
                    </div>
                    <h4 className="font-medium mb-1">{["7-Day Streak", "Completed 10 Workouts", "Logged All Meals", "First Milestone", "Quick Progress", "Premium Member"][i % 6]}</h4>
                    <p className="text-xs text-muted-foreground">Earned {["Today", "Yesterday", "3 days ago", "Last week", "2 weeks ago", "1 month ago"][i % 6]}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
