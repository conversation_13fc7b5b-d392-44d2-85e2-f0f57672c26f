'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import { Loader2, ArrowLeft, Calendar, Dumbbell, CheckCircle } from 'lucide-react'
import Link from 'next/link'

export default function WorkoutPlanPage({ params }: { params: { planId: string } }) {
  const { planId } = params
  const router = useRouter()
  const { toast } = useToast()

  const [plan, setPlan] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [selectedWeek, setSelectedWeek] = useState(0)
  const [selectedDay, setSelectedDay] = useState(0)

  // Fetch the training plan
  useEffect(() => {
    const fetchPlan = async () => {
      try {
        setLoading(true)

        // Fetch the training plan details
        const response = await fetch(`/api/training-plan-templates/${planId}`)

        if (!response.ok) {
          throw new Error('Failed to fetch training plan')
        }

        const data = await response.json()
        console.log('Fetched plan:', data)
        setPlan(data)

      } catch (error) {
        console.error('Error fetching plan:', error)
        toast({
          title: 'Error',
          description: 'Failed to load workout plan. Please try again.',
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
      }
    }

    fetchPlan()
  }, [planId, toast])

  // Parse weeks data
  const getWeeks = () => {
    if (!plan?.weeks) return []

    try {
      // If weeks is a string, parse it
      const weeksData = typeof plan.weeks === 'string'
        ? JSON.parse(plan.weeks)
        : plan.weeks

      // Handle different weeks data structures
      if (Array.isArray(weeksData)) {
        // New format with array of weeks
        return weeksData.map(week => ({
          ...week,
          days: week.dailyWorkouts || []
        }))
      } else if (weeksData.week1) {
        // Old format with week1, week2, etc.
        // Convert to array format
        const weekKeys = Object.keys(weeksData).filter(key => key.startsWith('week'))
        return weekKeys.map(key => {
          const weekData = weeksData[key]
          return {
            id: key,
            title: weekData.title || `Week ${key.replace('week', '')}`,
            days: weekData.workouts || []
          }
        })
      }

      return []
    } catch (error) {
      console.error('Error parsing weeks data:', error)
      return []
    }
  }

  const weeks = getWeeks()

  // Get the current week's workouts
  const currentWeekWorkouts = weeks[selectedWeek]?.days || []

  // Get the selected day's workout
  const selectedDayWorkout = currentWeekWorkouts[selectedDay] || null

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Loading workout plan...</p>
      </div>
    )
  }

  if (!plan) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-bold">Workout Plan Not Found</h2>
          <p className="text-muted-foreground">The workout plan you're looking for doesn't exist or you don't have access to it.</p>
          <Button asChild>
            <Link href="/dashboard/workouts/current">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to My Workouts
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/workouts/current">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to My Workouts
            </Link>
          </Button>
          <h1 className="text-3xl font-bold mt-2">{plan.title}</h1>
          <p className="text-muted-foreground">{plan.description}</p>
        </div>
        <div>
          <Button
            onClick={() => router.push(`/dashboard/workout-session/${planId}`)}
            className="bg-primary hover:bg-primary/90"
            size="lg"
          >
            <Dumbbell className="mr-2 h-5 w-5" />
            Start Workout
          </Button>
        </div>
      </div>

      <Separator />

      <div className="grid md:grid-cols-4 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5" />
                Weeks
              </CardTitle>
              <CardDescription>
                Select a week to view workouts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {weeks.map((week, index) => (
                  <Button
                    key={index}
                    variant={selectedWeek === index ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => {
                      setSelectedWeek(index)
                      setSelectedDay(0) // Reset day selection when changing weeks
                    }}
                  >
                    Week {index + 1}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Dumbbell className="mr-2 h-5 w-5" />
                Week {selectedWeek + 1} Workouts
              </CardTitle>
              <CardDescription>
                Select a day to view workout details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="0" onValueChange={(value) => setSelectedDay(parseInt(value))}>
                <TabsList className="grid grid-cols-7">
                  {currentWeekWorkouts.map((day, index) => (
                    <TabsTrigger key={index} value={index.toString()}>
                      Day {index + 1}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {currentWeekWorkouts.map((day, index) => (
                  <TabsContent key={index} value={index.toString()}>
                    <Card>
                      <CardHeader>
                        <CardTitle>{day.name || `Day ${index + 1}`}</CardTitle>
                        <CardDescription>{day.description || 'No description available'}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        {day.exercises && day.exercises.length > 0 ? (
                          <div className="space-y-4">
                            {day.exercises.map((exercise, exIndex) => (
                              <div key={exIndex} className="border rounded-lg p-4">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <h4 className="font-medium">{exercise.name}</h4>
                                    <p className="text-sm text-muted-foreground">
                                      {exercise.sets} sets × {exercise.reps} reps
                                    </p>
                                    {exercise.notes && (
                                      <p className="text-sm mt-2">{exercise.notes}</p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-muted-foreground">No exercises for this day</p>
                        )}
                      </CardContent>
                      <CardFooter>
                        <Button className="w-full">
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Start Workout
                        </Button>
                      </CardFooter>
                    </Card>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
