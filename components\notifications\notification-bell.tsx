"use client"

import { useState, useEffect, useCallback } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Bell, MessageSquare, User, CalendarClock, CheckCircle2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/components/ui/use-toast"
import { formatDistanceToNow } from "date-fns"
import { useSocket } from "@/components/socket-provider"

interface NotificationBellProps {
  userId: string
}

interface Notification {
  id: string
  title: string
  message: string
  type: string
  read: boolean
  createdAt: string
  actionLink?: string | null
  sourceId?: string | null
  sourceType?: string | null
  senderAvatar?: string | null
  senderName?: string | null
}

export function NotificationBell({ userId }: NotificationBellProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const router = useRouter()
  const { toast } = useToast()
  const { isConnected } = useSocket()

  // Fetch notification count (only when needed, not on a timer)
  const fetchNotificationCount = useCallback(async () => {
    try {
      const response = await fetch("/api/notifications/count", {
        // Add cache control to prevent frequent requests
        cache: "no-cache",
        headers: {
          "Cache-Control": "no-cache"
        }
      })
      if (response.ok) {
        const data = await response.json()
        setUnreadCount(data.totalCount)
      }
    } catch (error) {
      console.error("Error fetching notification count:", error)
    }
  }, [])

  // Fetch full notifications (heavier operation)
  const fetchNotifications = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/notifications")
      if (response.ok) {
        const data = await response.json()

        // For message notifications, fetch sender details
        const enhancedNotifications = await Promise.all(
          data.map(async (notification: Notification) => {
            if (notification.type === "message" && notification.sourceId) {
              try {
                // Try to fetch message details to get sender info
                const messageResponse = await fetch(`/api/messages/${notification.sourceId}`)
                if (messageResponse.ok) {
                  const messageData = await messageResponse.json()
                  return {
                    ...notification,
                    senderAvatar: messageData.sender?.avatarUrl,
                    senderName: messageData.sender?.name
                  }
                }
              } catch (error) {
                console.error("Error fetching message details:", error)
              }
            }
            return notification
          })
        )

        setNotifications(enhancedNotifications)
      }
    } catch (error) {
      console.error("Error fetching notifications:", error)
    } finally {
      setLoading(false)
    }
  }

  // Mark a notification as read
  const markAsRead = async (id: string) => {
    try {
      const response = await fetch("/api/notifications/mark-read", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id }),
      })

      if (response.ok) {
        // Update local state
        setNotifications((prev) =>
          prev.map((notification) =>
            notification.id === id ? { ...notification, read: true } : notification
          )
        )
        // Update unread count
        setUnreadCount((prev) => Math.max(0, prev - 1))
      }
    } catch (error) {
      console.error("Error marking notification as read:", error)
    }
  }

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const response = await fetch("/api/notifications/mark-read", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ all: true }),
      })

      if (response.ok) {
        // Update local state
        setNotifications((prev) =>
          prev.map((notification) => ({ ...notification, read: true }))
        )
        // Update unread count
        setUnreadCount(0)

        toast({
          title: "Success",
          description: "All notifications marked as read",
        })
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error)
    }
  }

  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    // Mark as read
    if (!notification.read) {
      markAsRead(notification.id)
    }

    // Navigate to the action link if provided
    if (notification.actionLink) {
      setOpen(false)

      // If it's a message notification, ensure it points to the new chat page
      if (notification.type === "message" && notification.sourceId && notification.sourceType === "message") {
        // Extract conversation ID from the action link if possible
        const conversationIdMatch = notification.actionLink.match(/conversationId=([^&]+)/)
        if (conversationIdMatch && conversationIdMatch[1]) {
          router.push(`/dashboard/chats?conversationId=${conversationIdMatch[1]}`)
        } else {
          router.push("/dashboard/chats")
        }
      } else {
        router.push(notification.actionLink)
      }
    }
  }

  // Initial fetch and set up event listeners for message events
  useEffect(() => {
    // Initial fetch once when component mounts
    fetchNotificationCount()

    // Set up a custom event listener for manually sent messages
    const handleMessageSent = () => {
      console.log('Notification bell: Message sent event received');
      fetchNotificationCount();
    }

    // Set up a custom event listener for messages received via WebSocket
    const handleMessageReceived = (event: Event) => {
      console.log('Notification bell: Message received via WebSocket');

      try {
        // Get the event detail if available
        const detail = (event as CustomEvent).detail;
        console.log('Message detail:', detail);

        // Update notification count
        console.log('Updating notification count');
        fetchNotificationCount();

        // Show a toast notification for new messages
        if (detail.type === 'new-message' && detail.message.receiverId === userId) {
          toast({
            title: 'New Message',
            description: `${detail.message.content.substring(0, 30)}${detail.message.content.length > 30 ? '...' : ''}`,
            duration: 5000,
          });
        }
      } catch (error) {
        console.error('Error handling message-received event:', error);

        // Fallback: update notification count anyway
        fetchNotificationCount();
      }
    }

    // Set up a custom event listener for notifications received via WebSocket
    const handleNotificationReceived = (event: Event) => {
      console.log('Notification bell: Notification received via WebSocket');

      try {
        // Get the event detail if available
        const detail = (event as CustomEvent).detail;
        console.log('Notification detail:', detail);

        // Update notification count
        console.log('Updating notification count');
        fetchNotificationCount();
      } catch (error) {
        console.error('Error handling notification-received event:', error);

        // Fallback: update notification count anyway
        fetchNotificationCount();
      }
    }

    // Add event listeners
    window.addEventListener('message-sent', handleMessageSent);
    window.addEventListener('message-received', handleMessageReceived);
    window.addEventListener('notification-received', handleNotificationReceived);
    window.addEventListener('socket-event', (event: Event) => {
      const { detail } = event as CustomEvent;
      if (detail.event === 'new-message' || detail.event === 'notification') {
        console.log('Socket event received:', detail);
        fetchNotificationCount();
      }
    });



    // Clean up
    return () => {
      window.removeEventListener('message-sent', handleMessageSent);
      window.removeEventListener('message-received', handleMessageReceived);
      window.removeEventListener('notification-received', handleNotificationReceived);
      window.removeEventListener('socket-event', (event: Event) => {
        const { detail } = event as CustomEvent;
        if (detail.event === 'new-message' || detail.event === 'notification') {
          console.log('Socket event received:', detail);
          fetchNotificationCount();
        }
      });

    }
  }, [userId, fetchNotificationCount, toast])

  // Refresh notifications when the component is clicked
  useEffect(() => {
    if (open) {
      fetchNotifications()
      fetchNotificationCount()
    }
  }, [open])

  // Get icon based on notification type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "message":
        return <MessageSquare className="h-4 w-4" />
      case "subscription":
        return <CalendarClock className="h-4 w-4" />
      case "system":
        return <CheckCircle2 className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative h-8 w-8 rounded-full"
          onClick={() => setOpen(true)}
        >
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <span className="absolute -right-1 -top-1 h-4 w-4 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
              {unreadCount}
            </span>
          )}
          <span className="sr-only">Notifications</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-medium text-sm">Notifications</h3>
          {notifications.some(n => !n.read) && (
            <Button variant="ghost" size="sm" onClick={markAllAsRead}>
              Mark all as read
            </Button>
          )}
        </div>
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="w-full grid grid-cols-2 rounded-none border-b">
            <TabsTrigger value="all" className="rounded-none">
              All
            </TabsTrigger>
            <TabsTrigger value="unread" className="rounded-none">
              Unread
              {unreadCount > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {unreadCount}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
          <TabsContent value="all" className="max-h-[300px] overflow-hidden">
            <ScrollArea className="h-[300px]">
              {loading ? (
                <div className="flex justify-center items-center h-[300px]">
                  <span className="text-sm text-muted-foreground">Loading...</span>
                </div>
              ) : notifications.length > 0 ? (
                <div className="divide-y">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-3 cursor-pointer hover:bg-muted/50 transition-colors ${
                        !notification.read ? "bg-muted/20" : ""
                      }`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex items-start gap-3">
                        {notification.type === "message" && notification.senderAvatar ? (
                          <Avatar className="h-8 w-8 mt-1">
                            <AvatarImage src={notification.senderAvatar} />
                            <AvatarFallback>
                              {notification.senderName ? notification.senderName.charAt(0).toUpperCase() : "U"}
                            </AvatarFallback>
                          </Avatar>
                        ) : (
                          <div className={`mt-1 p-1 rounded-full ${!notification.read ? "bg-primary/10 text-primary" : "bg-muted text-muted-foreground"}`}>
                            {getNotificationIcon(notification.type)}
                          </div>
                        )}
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium">
                            {notification.type === "message" && notification.senderName
                              ? `Message from ${notification.senderName}`
                              : notification.title}
                          </p>
                          <p className="text-xs text-muted-foreground">{notification.message}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-[300px] p-4">
                  <Bell className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground text-center">No notifications yet</p>
                </div>
              )}
            </ScrollArea>
          </TabsContent>
          <TabsContent value="unread" className="max-h-[300px] overflow-hidden">
            <ScrollArea className="h-[300px]">
              {loading ? (
                <div className="flex justify-center items-center h-[300px]">
                  <span className="text-sm text-muted-foreground">Loading...</span>
                </div>
              ) : notifications.filter(n => !n.read).length > 0 ? (
                <div className="divide-y">
                  {notifications
                    .filter(n => !n.read)
                    .map((notification) => (
                      <div
                        key={notification.id}
                        className="p-3 cursor-pointer hover:bg-muted/50 transition-colors bg-muted/20"
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="flex items-start gap-3">
                          {notification.type === "message" && notification.senderAvatar ? (
                            <Avatar className="h-8 w-8 mt-1">
                              <AvatarImage src={notification.senderAvatar} />
                              <AvatarFallback>
                                {notification.senderName ? notification.senderName.charAt(0).toUpperCase() : "U"}
                              </AvatarFallback>
                            </Avatar>
                          ) : (
                            <div className="mt-1 p-1 rounded-full bg-primary/10 text-primary">
                              {getNotificationIcon(notification.type)}
                            </div>
                          )}
                          <div className="flex-1 space-y-1">
                            <p className="text-sm font-medium">
                              {notification.type === "message" && notification.senderName
                                ? `Message from ${notification.senderName}`
                                : notification.title}
                            </p>
                            <p className="text-xs text-muted-foreground">{notification.message}</p>
                            <p className="text-xs text-muted-foreground">
                              {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-[300px] p-4">
                  <CheckCircle2 className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground text-center">No unread notifications</p>
                </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </PopoverContent>
    </Popover>
  )
}
