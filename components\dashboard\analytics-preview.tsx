'use client';

import Link from "next/link";
import { <PERSON><PERSON><PERSON>, ArrowUpRight, ArrowDownRight, Activity, Weight, Dumbbell, Lock } from "lucide-react";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface AnalyticsPreviewProps {
  isPremium: boolean;
  currentWeight?: number;
  weightChange?: number;
  trainingVolume?: number;
  volumeChange?: number;
  workoutsCompleted?: number;
  workoutsChange?: number;
}

export function AnalyticsPreview({
  isPremium,
  currentWeight = 0,
  weightChange = 0,
  trainingVolume = 0,
  volumeChange = 0,
  workoutsCompleted = 0,
  workoutsChange = 0
}: AnalyticsPreviewProps) {
  return (
    <Card className="shadow-md hover:shadow-lg transition-all overflow-hidden group">
      <div className="bg-gradient-to-r from-background to-muted/20 px-4 py-3 border-b border-border/30 flex flex-row items-center justify-between group-hover:from-background/80 group-hover:to-muted/30 transition-all">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <BarChart className="h-4 w-4 text-primary" />
          Training Metrics
        </CardTitle>

        <Button asChild variant="ghost" size="sm" className="h-8 gap-1 text-xs hover:bg-background/80">
          <Link href="/dashboard/analytics">
            View All
            <ArrowUpRight className="h-3 w-3 ml-1" />
          </Link>
        </Button>
      </div>

      <CardContent className="p-5">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-muted/10 rounded-xl p-4 border border-border/20 hover:border-primary/20 hover:bg-muted/20 transition-all">
            <div className="flex items-center gap-2 mb-2">
              <div className="h-8 w-8 rounded-full bg-blue-500/10 flex items-center justify-center">
                <Weight className="h-4 w-4 text-blue-500" />
              </div>
              <div className="text-sm font-medium">Current Weight</div>
            </div>
            <div className="flex items-end justify-between">
              <div className="text-2xl font-bold">{currentWeight} <span className="text-sm font-normal text-muted-foreground">lbs</span></div>
              <div className="text-sm flex items-center">
                {weightChange < 0 ? (
                  <span className="text-green-500 flex items-center">
                    <ArrowDownRight className="h-4 w-4 mr-1" />
                    {Math.abs(weightChange)} lbs
                  </span>
                ) : (
                  <span className="text-red-500 flex items-center">
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                    {weightChange} lbs
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="bg-muted/10 rounded-xl p-4 border border-border/20 hover:border-primary/20 hover:bg-muted/20 transition-all">
            <div className="flex items-center gap-2 mb-2">
              <div className="h-8 w-8 rounded-full bg-green-500/10 flex items-center justify-center">
                <Activity className="h-4 w-4 text-green-500" />
              </div>
              <div className="text-sm font-medium">Training Volume</div>
            </div>
            <div className="flex items-end justify-between">
              <div className="text-2xl font-bold">{trainingVolume.toLocaleString()} <span className="text-sm font-normal text-muted-foreground">kg</span></div>
              <div className="text-sm flex items-center">
                <span className={volumeChange >= 0 ? "text-green-500 flex items-center" : "text-red-500 flex items-center"}>
                  {volumeChange >= 0 ? (
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-4 w-4 mr-1" />
                  )}
                  {Math.abs(volumeChange)}%
                </span>
              </div>
            </div>
          </div>

          <div className="bg-muted/10 rounded-xl p-4 border border-border/20 hover:border-primary/20 hover:bg-muted/20 transition-all">
            <div className="flex items-center gap-2 mb-2">
              <div className="h-8 w-8 rounded-full bg-amber-500/10 flex items-center justify-center">
                <Dumbbell className="h-4 w-4 text-amber-500" />
              </div>
              <div className="text-sm font-medium">Workouts</div>
            </div>
            <div className="flex items-end justify-between">
              <div className="text-2xl font-bold">{workoutsCompleted} <span className="text-sm font-normal text-muted-foreground">sessions</span></div>
              <div className="text-sm flex items-center">
                <span className={workoutsChange >= 0 ? "text-green-500 flex items-center" : "text-red-500 flex items-center"}>
                  {workoutsChange >= 0 ? (
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-4 w-4 mr-1" />
                  )}
                  {Math.abs(workoutsChange)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {!isPremium && (
          <div className="mt-5 bg-gradient-to-r from-primary/5 to-primary/10 p-4 rounded-xl border border-primary/10 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-background/80 flex items-center justify-center shadow-sm">
                <Lock className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="text-sm font-medium">Unlock Full Analytics</p>
                <p className="text-xs text-muted-foreground">Upgrade to see your complete history and detailed insights.</p>
              </div>
            </div>

            <Button asChild size="sm" className="bg-primary hover:bg-primary/90">
              <Link href="/dashboard/upgrade">
                Upgrade Now
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
