import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Youtube } from "lucide-react"

export interface Exercise {
  id: string;
  name: string;
  sets: number;
  reps: number;
  notes?: string;
  weight?: number | string | null; // Handle different weight formats
  category?: string;
  difficulty?: string;
  targetMuscles?: string[];
  description?: string;
  video?: string;
  videoUrl?: string; // Added for compatibility with database
  type?: string;
}

interface EditExerciseDialogProps {
  exercise: Exercise
  onSave: (updatedExercise: Exercise) => void
  trigger?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function EditExerciseDialog({ exercise, onSave, trigger, open: controlledOpen, onOpenChange }: EditExerciseDialogProps) {
  const [editedExercise, setEditedExercise] = useState<Exercise>(exercise)
  const [open, setOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("basic")

  // Update edited exercise when the exercise prop changes
  useEffect(() => {
    setEditedExercise(exercise)
  }, [exercise])

  // Use controlled open state if provided
  const isOpen = controlledOpen !== undefined ? controlledOpen : open

  // Handle open state changes
  const handleOpenChange = (newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen)
    } else {
      setOpen(newOpen)
    }
  }

  const handleSave = () => {
    onSave(editedExercise)
    handleOpenChange(false)
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      {trigger && (
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Exercise</DialogTitle>
          <DialogDescription>
            Make changes to the exercise details here.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4 py-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Exercise Name</Label>
                <Input
                  id="name"
                  value={editedExercise.name}
                  onChange={(e) => setEditedExercise({ ...editedExercise, name: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="type">Exercise Type</Label>
                  <Select
                    value={editedExercise.type || "strength"}
                    onValueChange={(value) => setEditedExercise({ ...editedExercise, type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="strength">Strength</SelectItem>
                      <SelectItem value="cardio">Cardio</SelectItem>
                      <SelectItem value="flexibility">Flexibility</SelectItem>
                      <SelectItem value="balance">Balance</SelectItem>
                      <SelectItem value="plyometric">Plyometric</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="difficulty">Difficulty</Label>
                  <Select
                    value={editedExercise.difficulty || "beginner"}
                    onValueChange={(value) => setEditedExercise({ ...editedExercise, difficulty: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="sets">Sets</Label>
                  <Input
                    id="sets"
                    type="number"
                    value={editedExercise.sets}
                    onChange={(e) => setEditedExercise({ ...editedExercise, sets: parseInt(e.target.value) })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="reps">Reps</Label>
                  <Input
                    id="reps"
                    type="number"
                    value={editedExercise.reps}
                    onChange={(e) => setEditedExercise({ ...editedExercise, reps: parseInt(e.target.value) })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="weight">Weight (kg)</Label>
                  <Input
                    id="weight"
                    type="number"
                    value={typeof editedExercise.weight === 'string' ? parseFloat(editedExercise.weight) : (editedExercise.weight ?? 60)}
                    onChange={(e) => {
                      const weightValue = e.target.value === '' ? null : parseFloat(e.target.value);
                      setEditedExercise({ ...editedExercise, weight: weightValue });
                    }}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4 py-4">
            <div className="grid gap-4">
              <div>
                <Label>YouTube Video URL</Label>
                <div className="flex gap-2">
                  <Input
                    value={editedExercise.video || editedExercise.videoUrl || ""}
                    onChange={(e) => setEditedExercise({
                      ...editedExercise,
                      video: e.target.value,
                      videoUrl: e.target.value // Update both fields for compatibility
                    })}
                    placeholder="https://youtube.com/watch?v=..."
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    className="shrink-0"
                  >
                    <Youtube className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="grid gap-2">
                <Label>Description</Label>
                <Textarea
                  value={editedExercise.description || ""}
                  onChange={(e) => setEditedExercise({ ...editedExercise, description: e.target.value })}
                  placeholder="Describe how to perform this exercise"
                  className="min-h-[100px]"
                />
              </div>

              <div className="grid gap-2">
                <Label>Notes</Label>
                <Input
                  value={editedExercise.notes || ""}
                  onChange={(e) => setEditedExercise({ ...editedExercise, notes: e.target.value })}
                  placeholder="Additional notes"
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}