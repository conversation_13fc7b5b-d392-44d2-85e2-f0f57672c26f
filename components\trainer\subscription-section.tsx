"use client"

import { <PERSON><PERSON><PERSON>, CheckCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { TabsContent } from "@/components/ui/tabs"
import { SubscriptionTier } from "@/types/trainer"

interface SubscriptionSectionProps {
  subscriptionTiers: SubscriptionTier[]
  onSelectSubscription: (tier: SubscriptionTier) => void
}

export function SubscriptionSection({
  subscriptionTiers,
  onSelectSubscription
}: SubscriptionSectionProps) {
  return (
    <TabsContent value="subscriptions" className="space-y-4">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-2">Membership Plans</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Subscribe to get access to premium training programs and content
        </p>
      </div>

      {subscriptionTiers.length > 0 ? (
        <div className="grid md:grid-cols-3 gap-6">
          {subscriptionTiers.map((tier) => (
            <Card key={tier.id} className="overflow-hidden border-2 hover:border-primary/50 transition-all card-hover">
              <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 pb-4">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-xl font-bold">{tier.name}</CardTitle>
                    <CardDescription className="mt-1">{tier.description}</CardDescription>
                  </div>
                  <div className="text-right">
                    <span className="text-2xl font-bold">${tier.price}</span>
                    <p className="text-sm text-muted-foreground">/month</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-2">
                  {tier.features.map((feature, index) => (
                    <div key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-primary mr-3 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="bg-muted/30 pt-4">
                <Button
                  className="w-full"
                  onClick={() => onSelectSubscription(tier)}
                >
                  Subscribe Now
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center p-10 border rounded-lg bg-muted/40">
          <h3 className="text-xl font-medium mb-2">Subscription Plans Coming Soon</h3>
          <p className="text-muted-foreground mb-6">
            This trainer doesn't have any subscription plans available yet.
            Check back later for updates.
          </p>
        </div>
      )}
    </TabsContent>
  )
}