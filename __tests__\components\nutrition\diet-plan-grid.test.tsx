import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DietPlanGrid } from '@/components/nutrition/diet-plan-grid'
import { useToast } from '@/components/ui/use-toast'
import { useRouter } from 'next/navigation'
import '@testing-library/jest-dom'

// Mock the hooks
jest.mock('@/components/ui/use-toast')
jest.mock('next/navigation')

const mockToast = jest.fn()
const mockRouter = {
  push: jest.fn(),
}

const mockPlans = [
  {
    id: '1',
    title: 'Test Plan 1',
    description: 'Test Description 1',
    is_template: true,
    calories_per_day: 2000,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    meals: [
      {
        name: 'Breakfast',
        calories: 500,
        protein: 30,
        carbs: 60,
        fats: 20,
      },
    ],
  },
  {
    id: '2',
    title: 'Test Plan 2',
    description: 'Test Description 2',
    is_template: false,
    calories_per_day: 2500,
    createdAt: '2024-01-02T00:00:00.000Z',
    updatedAt: '2024-01-02T00:00:00.000Z',
    meals: [
      {
        name: 'Lunch',
        calories: 800,
        protein: 40,
        carbs: 80,
        fats: 30,
      },
    ],
  },
]

describe('DietPlanGrid', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(useToast as jest.Mock).mockReturnValue({ toast: mockToast })
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
  })

  it('renders all plans by default', () => {
    render(<DietPlanGrid plans={mockPlans} />)
    
    expect(screen.getByText('Test Plan 1')).toBeInTheDocument()
    expect(screen.getByText('Test Plan 2')).toBeInTheDocument()
    expect(screen.getByText('2000 calories/day')).toBeInTheDocument()
    expect(screen.getByText('2500 calories/day')).toBeInTheDocument()
  })

  it('filters plans by template', async () => {
    render(<DietPlanGrid plans={mockPlans} />)
    
    const templatesTab = screen.getByRole('tab', { name: /templates/i })
    await userEvent.click(templatesTab)
    
    expect(screen.getByText('Test Plan 1')).toBeInTheDocument()
    expect(screen.queryByText('Test Plan 2')).not.toBeInTheDocument()
  })

  it('filters plans by custom', async () => {
    render(<DietPlanGrid plans={mockPlans} />)
    
    const customTab = screen.getByRole('tab', { name: /custom plans/i })
    await userEvent.click(customTab)
    
    expect(screen.queryByText('Test Plan 1')).not.toBeInTheDocument()
    expect(screen.getByText('Test Plan 2')).toBeInTheDocument()
  })

  it('handles plan duplication', async () => {
    const onDuplicatePlan = jest.fn()
    render(<DietPlanGrid plans={mockPlans} onDuplicatePlan={onDuplicatePlan} />)
    
    const duplicateButton = screen.getAllByRole('button', { name: /duplicate/i })[0]
    await userEvent.click(duplicateButton)
    
    expect(onDuplicatePlan).toHaveBeenCalledWith('1')
    expect(mockToast).toHaveBeenCalledWith({
      title: 'Success',
      description: 'Plan duplicated successfully',
    })
  })

  it('handles plan deletion', async () => {
    const onDeletePlan = jest.fn()
    render(<DietPlanGrid plans={mockPlans} onDeletePlan={onDeletePlan} />)
    
    const deleteButton = screen.getAllByRole('button', { name: /delete/i })[0]
    await userEvent.click(deleteButton)
    
    expect(onDeletePlan).toHaveBeenCalledWith('1')
    expect(mockToast).toHaveBeenCalledWith({
      title: 'Success',
      description: 'Plan deleted successfully',
    })
  })

  it('shows empty state when no plans exist', () => {
    render(<DietPlanGrid plans={[]} />)
    
    expect(screen.getByText('No diet plans found')).toBeInTheDocument()
  })

  it('shows empty state when no plans match filter', async () => {
    const customPlans = mockPlans.filter(plan => plan.is_template)
    render(<DietPlanGrid plans={customPlans} />)
    
    const customTab = screen.getByRole('tab', { name: /custom plans/i })
    await userEvent.click(customTab)
    
    expect(screen.getByText('No custom plans found')).toBeInTheDocument()
  })

  it('navigates to plan details when clicking a plan', async () => {
    render(<DietPlanGrid plans={mockPlans} />)
    
    const planCard = screen.getByText('Test Plan 1').closest('.cursor-pointer')
    await userEvent.click(planCard!)
    
    expect(mockRouter.push).toHaveBeenCalledWith('/dashboard/diet-plans/1')
  })

  it('displays meal information correctly', () => {
    render(<DietPlanGrid plans={mockPlans} />)
    
    const breakfastText = screen.getByText('Breakfast')
    const breakfastCalories = screen.getByText('- 500 calories')
    const lunchText = screen.getByText('Lunch')
    const lunchCalories = screen.getByText('- 800 calories')
    
    expect(breakfastText).toBeInTheDocument()
    expect(breakfastCalories).toBeInTheDocument()
    expect(lunchText).toBeInTheDocument()
    expect(lunchCalories).toBeInTheDocument()
  })
}) 