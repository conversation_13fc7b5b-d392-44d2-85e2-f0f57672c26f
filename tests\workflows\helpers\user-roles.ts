import { PrismaClient, User, Client<PERSON><PERSON><PERSON>le, TrainerProfile } from '@prisma/client'
import { v4 as uuidv4 } from 'uuid'
import bcrypt from 'bcryptjs'

// Create a Prisma client for testing
const prisma = new PrismaClient()

/**
 * Create a test user with the specified role
 */
export async function createTestUser(role: 'admin' | 'trainer' | 'client' = 'client'): Promise<User> {
  const userId = uuidv4()
  const email = `test-${role}-${userId}@example.com`
  const password = await bcrypt.hash('password123', 10)

  const user = await prisma.user.create({
    data: {
      name: `Test ${role.charAt(0).toUpperCase() + role.slice(1)}`,
      email,
      password,
      role,
      emailVerified: new Date()
    }
  })

  return user
}

/**
 * Create a test client profile for a user
 */
export async function createTestClientProfile(userId: string): Promise<ClientProfile> {
  return prisma.clientProfile.create({
    data: {
      user: {
        connect: {
          id: userId
        }
      }
    }
  })
}

/**
 * Create a test trainer profile for a user
 */
export async function createTestTrainerProfile(userId: string): Promise<TrainerProfile> {
  return prisma.trainerProfile.create({
    data: {
      user: {
        connect: {
          id: userId
        }
      }
    }
  })
}

/**
 * Create a test premium client (client with active subscription)
 */
export async function createTestPremiumClient(): Promise<{user: User, clientProfile: ClientProfile, trainerId: string}> {
  // Create a trainer first
  const trainerUser = await createTestUser('trainer')
  const trainerProfile = await createTestTrainerProfile(trainerUser.id)

  // Create a client
  const clientUser = await createTestUser('client')
  const clientProfile = await createTestClientProfile(clientUser.id)

  // Create a subscription tier
  const tier = await prisma.subscriptionTier.create({
    data: {
      name: 'Premium Test Tier',
      price: 29.99,
      description: 'Test premium tier',
      features: ['Feature 1', 'Feature 2'],
      trainerId: trainerProfile.id
    }
  })

  // Create an active subscription
  await prisma.subscription.create({
    data: {
      clientProfile: {
        connect: {
          id: clientProfile.id
        }
      },
      tier: {
        connect: {
          id: tier.id
        }
      },
      status: 'active',
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    }
  })

  return {
    user: clientUser,
    clientProfile,
    trainerId: trainerProfile.id
  }
}

/**
 * Clean up test data
 */
export async function cleanupTestData(userId: string): Promise<void> {
  try {
    // Find profiles
    const clientProfile = await prisma.clientProfile.findFirst({
      where: { userId }
    })

    const trainerProfile = await prisma.trainerProfile.findFirst({
      where: { userId }
    })

    // Delete client-related data
    if (clientProfile) {
      // Delete subscriptions
      await prisma.subscription.deleteMany({
        where: { clientId: userId }
      })

      // Delete personalized training plans
      await prisma.trainingPlanTemplate.deleteMany({
        where: { clientId: userId, type: "personalized" }
      })

      // Delete client profile
      await prisma.clientProfile.delete({
        where: { id: clientProfile.id }
      })
    }

    // Delete trainer-related data
    if (trainerProfile) {
      // Delete subscription tiers
      await prisma.subscriptionTier.deleteMany({
        where: { trainerId: trainerProfile.id }
      })

      // Delete trainer profile
      await prisma.trainerProfile.delete({
        where: { id: trainerProfile.id }
      })
    }

    // Delete user
    await prisma.user.delete({
      where: { id: userId }
    })
  } catch (error) {
    console.error(`Error cleaning up test data for user ${userId}:`, error)
  }
}

/**
 * Disconnect from the database
 */
export async function disconnectDatabase(): Promise<void> {
  await prisma.$disconnect()
}
