/**
 * Utility functions for generating PDF thumbnails
 */

/**
 * Generates a thumbnail from a PDF file URL
 * This function uses the browser's built-in PDF rendering capabilities
 * 
 * @param pdfUrl URL of the PDF file
 * @returns Promise that resolves to a data URL of the thumbnail
 */
export async function generatePdfThumbnail(pdfUrl: string): Promise<string> {
  try {
    // Create an iframe to load the PDF
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    document.body.appendChild(iframe);
    
    // Set the source to the PDF URL
    iframe.src = pdfUrl;
    
    // Wait for the iframe to load
    await new Promise<void>((resolve) => {
      iframe.onload = () => resolve();
    });
    
    // Create a canvas to render the PDF
    const canvas = document.createElement('canvas');
    canvas.width = 400;
    canvas.height = 500;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Could not get canvas context');
    }
    
    // Draw a placeholder background
    ctx.fillStyle = '#f0f4f8';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Try to capture the iframe content
    try {
      // This might fail due to CORS restrictions
      ctx.drawImage(iframe, 0, 0, canvas.width, canvas.height);
    } catch (e) {
      console.error('Could not draw iframe content to canvas:', e);
      
      // Draw a PDF icon as fallback
      ctx.fillStyle = '#e2e8f0';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Draw PDF text
      ctx.font = 'bold 24px Arial';
      ctx.fillStyle = '#4a5568';
      ctx.textAlign = 'center';
      ctx.fillText('PDF', canvas.width / 2, canvas.height / 2);
      
      // Draw a document icon outline
      ctx.strokeStyle = '#3182ce';
      ctx.lineWidth = 3;
      const iconSize = 100;
      const x = (canvas.width - iconSize) / 2;
      const y = (canvas.height - iconSize) / 2 - 30;
      
      // Document outline
      ctx.beginPath();
      ctx.moveTo(x, y);
      ctx.lineTo(x, y + iconSize);
      ctx.lineTo(x + iconSize, y + iconSize);
      ctx.lineTo(x + iconSize, y + 20);
      ctx.lineTo(x + iconSize - 20, y);
      ctx.closePath();
      ctx.stroke();
      
      // Folded corner
      ctx.beginPath();
      ctx.moveTo(x + iconSize - 20, y);
      ctx.lineTo(x + iconSize - 20, y + 20);
      ctx.lineTo(x + iconSize, y + 20);
      ctx.stroke();
      
      // Lines representing text
      ctx.beginPath();
      ctx.moveTo(x + 20, y + 40);
      ctx.lineTo(x + iconSize - 20, y + 40);
      ctx.stroke();
      
      ctx.beginPath();
      ctx.moveTo(x + 20, y + 60);
      ctx.lineTo(x + iconSize - 20, y + 60);
      ctx.stroke();
      
      ctx.beginPath();
      ctx.moveTo(x + 20, y + 80);
      ctx.lineTo(x + iconSize - 40, y + 80);
      ctx.stroke();
    }
    
    // Clean up
    document.body.removeChild(iframe);
    
    // Return the canvas as a data URL
    return canvas.toDataURL('image/png');
  } catch (error) {
    console.error('Error generating PDF thumbnail:', error);
    return '';
  }
}

/**
 * Server-side function to generate a thumbnail URL for a PDF
 * This is a placeholder that would normally use a library like pdf.js
 * 
 * @param pdfPath Path to the PDF file
 * @returns Path to the generated thumbnail
 */
export function generatePdfThumbnailPath(pdfPath: string): string {
  if (!pdfPath) return '';
  
  // Extract the filename without extension
  const filename = pdfPath.split('/').pop()?.split('.')[0] || '';
  
  // Create a thumbnail path
  return `/uploads/thumbnails/${filename}-thumbnail.png`;
}
