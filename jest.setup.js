// Learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn(),
  })),
  usePathname: jest.fn(),
  useSearchParams: jest.fn(() => ({
    get: jest.fn(),
  })),
}))

// Mock next-auth
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(() => Promise.resolve({ user: { id: '1', email: '<EMAIL>' } })),
}))

// Mock next-auth/next
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(() => Promise.resolve({ user: { id: '1', email: '<EMAIL>' } })),
}))

// Mock next/server
jest.mock('next/server', () => {
  class MockResponse {
    constructor(body, init = {}) {
      this.body = body
      this.status = init.status || 200
      this.headers = new Headers(init.headers || {})
    }

    json() {
      return Promise.resolve(JSON.parse(this.body))
    }
  }

  class NextResponse extends MockResponse {
    static json(data, init = {}) {
      return new MockResponse(JSON.stringify(data), {
        ...init,
        headers: {
          'Content-Type': 'application/json',
          ...init.headers,
        },
      })
    }
  }

  return {
    NextResponse,
  }
})

// Mock Request and Response
global.Headers = class Headers {
  constructor(init = {}) {
    this._headers = new Map()
    for (const [key, value] of Object.entries(init)) {
      this._headers.set(key.toLowerCase(), value)
    }
  }

  get(name) {
    return this._headers.get(name.toLowerCase()) || null
  }

  set(name, value) {
    this._headers.set(name.toLowerCase(), value)
  }

  has(name) {
    return this._headers.has(name.toLowerCase())
  }

  delete(name) {
    this._headers.delete(name.toLowerCase())
  }
}

global.Request = class Request {
  constructor(url, init = {}) {
    this.url = url
    this.method = init.method || 'GET'
    this.headers = new Headers(init.headers)
    this.body = init.body
  }
}

global.Response = class Response {
  constructor(body, init = {}) {
    this.body = body
    this.init = init
    this.status = init.status || 200
    this.ok = this.status >= 200 && this.status < 300
    this.headers = new Headers(init.headers)
  }

  async json() {
    return JSON.parse(this.body)
  }
}

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    status: 200,
    statusText: 'OK',
  })
)

// Mock useToast
jest.mock('@/components/ui/use-toast', () => ({
  useToast: jest.fn(() => ({
    toast: jest.fn(),
  })),
}))

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    dietPlan: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
    },
  },
}))

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Set up environment variables for testing
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000/api';
process.env.NODE_ENV = 'test';

// Mock next/router
jest.mock('next/navigation', () => {
  const push = jest.fn();
  const replace = jest.fn();
  const prefetch = jest.fn();
  const back = jest.fn();

  return {
    useRouter: jest.fn(() => ({
      push,
      replace,
      prefetch,
      back,
      pathname: '/',
      query: {},
    })),
    usePathname: jest.fn(() => '/'),
    useSearchParams: jest.fn(() => ({ get: jest.fn() })),
  };
});

// Initialize error tracking in test mode
jest.mock('@/lib/errorTracking', () => ({
  errorTracking: {
    init: jest.fn(),
    captureException: jest.fn(),
    captureMessage: jest.fn(),
    addBreadcrumb: jest.fn(),
    setUser: jest.fn(),
  },
  captureException: jest.fn(),
  captureMessage: jest.fn(),
  addBreadcrumb: jest.fn(),
  setUser: jest.fn(),
  withErrorTracking: jest.fn(fn => fn),
}));

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
});