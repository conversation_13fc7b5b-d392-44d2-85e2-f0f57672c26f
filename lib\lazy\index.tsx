'use client'

import { ComponentType, lazy, ReactNode, Suspense } from 'react'

// Custom lazy loading implementation with retries for network resilience
export function lazyLoad<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback: ReactNode = null,
  retries = 3
) {
  const LazyComponent = lazy(() => {
    let attempts = 0

    // Create a wrapper that retries the import on failure
    const retryImport = async (): Promise<{ default: T }> => {
      try {
        return await importFn()
      } catch (error) {
        attempts++
        if (attempts >= retries) {
          throw error
        }
        // Exponential backoff for retries
        const delay = Math.min(2 ** attempts * 100, 3000)
        return new Promise(resolve => {
          setTimeout(() => resolve(retryImport()), delay)
        })
      }
    }

    return retryImport()
  })

  // Return a component that wraps the lazy component with Suspense
  return function LazyLoadedComponent(props: any) {
    return (
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    )
  }
}

// Helper function to prefetch components
export function prefetchComponent(importFn: () => Promise<any>) {
  // Use requestIdleCallback if available, otherwise use setTimeout
  const scheduleLoad = 
    typeof window !== 'undefined' && 'requestIdleCallback' in window
      ? (window as any).requestIdleCallback
      : (cb: Function) => setTimeout(cb, 1)

  scheduleLoad(() => {
    importFn().catch(() => {
      // Silent catch - we don't need to handle prefetch errors
    })
  })
}

// Create a batch prefetcher for multiple components
export function prefetchComponents(importFns: Array<() => Promise<any>>) {
  importFns.forEach(prefetchComponent)
} 