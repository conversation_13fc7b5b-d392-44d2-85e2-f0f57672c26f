'use client'

import Image, { ImageProps } from 'next/image'
import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'

export interface OptimizedImageProps extends Omit<ImageProps, 'onLoad' | 'onError'> {
  fallbackSrc?: string
  lowQualitySrc?: string
  preloadPriority?: boolean
  fadeIn?: boolean
  blur?: boolean
  aspectRatio?: string
  containerClassName?: string
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  quality = 85,
  priority = false,
  loading = 'lazy',
  fallbackSrc = '/images/placeholder.jpg',
  lowQualitySrc,
  preloadPriority = false,
  fadeIn = true,
  blur = true, 
  className,
  aspectRatio,
  containerClassName,
  ...props
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [error, setError] = useState(false)
  const [currentSrc, setCurrentSrc] = useState(lowQualitySrc || src)

  // Preload image for critical images
  useEffect(() => {
    if (preloadPriority && typeof src === 'string') {
      const img = new window.Image()
      img.src = src as string
    }
  }, [preloadPriority, src])

  const handleLoad = () => {
    setIsLoaded(true)
    if (lowQualitySrc) {
      setCurrentSrc(src)
    }
  }

  const handleError = () => {
    setError(true)
    setCurrentSrc(fallbackSrc)
  }

  // Calculate appropriate sizes if not provided
  useEffect(() => {
    if (!priority && typeof window !== 'undefined' && typeof IntersectionObserver !== 'undefined') {
      const imgElement = document.querySelector(`[data-img-src="${src}"]`) as HTMLImageElement
      
      if (imgElement) {
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                // Start loading the high quality image when near viewport
                if (lowQualitySrc) {
                  setCurrentSrc(src)
                }
                observer.disconnect()
              }
            })
          },
          { rootMargin: '200px' } // Start loading when within 200px of viewport
        )
        
        observer.observe(imgElement)
        return () => observer.disconnect()
      }
    }
  }, [src, lowQualitySrc, priority])

  const imageStyle = {
    opacity: fadeIn ? (isLoaded ? 1 : 0) : 1,
    transition: fadeIn ? 'opacity 0.3s ease-in-out' : 'none',
    objectFit: props.objectFit || 'cover',
  } as React.CSSProperties

  const containerStyle = {
    aspectRatio: aspectRatio,
    position: 'relative' as const,
    overflow: 'hidden',
  }

  return (
    <div 
      className={cn('relative overflow-hidden bg-muted/20', containerClassName)}
      style={aspectRatio ? containerStyle : undefined}
    >
      <Image
        src={error ? fallbackSrc : currentSrc}
        alt={alt}
        width={width}
        height={height}
        sizes={sizes}
        quality={quality}
        priority={priority}
        loading={loading}
        className={cn(
          'transition-opacity duration-300',
          blur && !isLoaded && 'blur-sm scale-105',
          isLoaded && 'blur-0 scale-100',
          className
        )}
        style={imageStyle}
        data-img-src={src as string}
        onLoadingComplete={handleLoad}
        onError={handleError}
        {...props}
      />
    </div>
  )
} 