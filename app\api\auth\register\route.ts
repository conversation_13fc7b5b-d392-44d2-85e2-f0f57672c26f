import { NextResponse } from "next/server"
import { hash } from "bcryptjs"
import { Resend } from "resend"
import crypto from "crypto"
import { prisma } from "@/lib/prisma"
import { registerSchema } from "@/lib/validations/auth" // Import Zod schema
import logger from "@/lib/logger" // Import logger

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY)
const FROM_EMAIL = process.env.EMAIL_FROM || "<EMAIL>" // Replace with your verified Resend domain/email
const APP_BASE_URL = process.env.NEXTAUTH_URL || "http://localhost:3000"

// --- Helper: Generate secure token --- 
function generateVerificationToken() {
  return crypto.randomBytes(32).toString("hex")
}

// --- Helper: Hash token for storage --- 
function hashToken(token: string) {
  return crypto.createHash("sha256").update(token).digest("hex")
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // --- Zod Validation ---
    const validationResult = registerSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Invalid input.", errors: validationResult.error.flatten().fieldErrors },
        { status: 400 },
      )
    }
    const { email, password, fullName } = validationResult.data
    // --- End Zod Validation ---

    const existingUser = await prisma.user.findUnique({
      where: { email },
    })

    if (existingUser) {
      logger.warn({ email: existingUser.email }, "Registration attempt for existing user.");
      return NextResponse.json(
        { message: "User already exists" },
        { status: 409 }, // Conflict
      )
    }

    // 1. Create the user
    const hashedPassword = await hash(password, 10)
    const user = await prisma.user.create({
      data: {
        email,
        name: fullName,
        password: hashedPassword,
        role: "client", 
        // emailVerified remains null initially
      },
    })

    // 2. Generate and store verification token
    const verificationToken = generateVerificationToken()
    const hashedVerificationToken = hashToken(verificationToken)
    const expires = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now

    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token: hashedVerificationToken,
        expires,
      },
    })

    // 3. Send verification email
    const verificationLink = `${APP_BASE_URL}/verify-email?token=${verificationToken}`

    try {
      await resend.emails.send({
        from: FROM_EMAIL,
        to: user.email,
        subject: "Verify Your Email Address - Clear-Coach",
        html: `
          <h1>Welcome to Clear-Coach!</h1>
          <p>Thank you for signing up. Please click the link below to verify your email address and activate your account:</p>
          <p><a href="${verificationLink}" target="_blank" style="display:inline-block; background-color:#4F46E5; color:white; padding:10px 20px; text-decoration:none; border-radius:5px; font-weight:bold;">Verify Email Address</a></p>
          <p>Or copy and paste this link in your browser:</p>
          <p>${verificationLink}</p>
          <p>This link will expire in 24 hours.</p>
          <p>If you didn't request this, please ignore this email.</p>
          <p>Best regards,<br>The Clear-Coach Team</p>
        `,
      })
      logger.info({ email: user.email }, `Verification email sent`);
    } catch (emailError) {
      logger.error({ email: user.email, error: emailError }, `Failed to send verification email`);
      // Decide if this should be a fatal error for the request
    }

    // 4. Return success response (even if email failed, for now)
    return NextResponse.json(
      { 
        message: "Registration successful. Please check your email to verify your account.",
        userId: user.id 
      }, 
      { status: 201 }
    )

  } catch (error) {
    logger.error({ error }, "Registration process failed");
    // Check for Prisma unique constraint violation if findUnique failed somehow
    if (error instanceof Error && error.message.includes('Unique constraint failed')) {
        return NextResponse.json(
          { message: "User already exists" },
          { status: 409 }
        )
    }
    return NextResponse.json(
      { message: "An internal server error occurred during registration." },
      { status: 500 },
    )
  }
} 