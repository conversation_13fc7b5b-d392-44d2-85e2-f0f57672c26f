// Simple script to start Next.js directly
const { spawn } = require('child_process');
const path = require('path');

console.log('Starting Next.js development server...');

// Find the next binary in node_modules
const nextBinPath = path.resolve(__dirname, 'node_modules', '.bin', 'next');

// Start Next.js directly using the binary path
const nextProcess = spawn(nextBinPath, ['dev'], {
  stdio: 'inherit',
  shell: true,
  env: {
    ...process.env,
    NODE_ENV: 'development',
    PORT: process.env.PORT || '3000'
  }
});

// Handle Next.js process events
nextProcess.on('error', (err) => {
  console.error('Failed to start Next.js process:', err);
  process.exit(1);
});

nextProcess.on('close', (code) => {
  console.log(`Next.js process exited with code ${code}`);
  process.exit(code);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Shutting down Next.js...');
  nextProcess.kill('SIGINT');
  process.exit(0);
});
