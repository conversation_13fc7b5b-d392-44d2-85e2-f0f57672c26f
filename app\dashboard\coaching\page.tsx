import { redirect } from "next/navigation"
import { getServerAuthSession } from "@/lib/auth"
import { getTrainerClients } from "@/app/actions/coaching"
import { CoachingPageClient } from "./coaching-page-client"

export default async function CoachingPage() {
  const session = await getServerAuthSession()
  
  if (!session?.user || session.user.role !== "trainer") {
    redirect("/dashboard")
  }

  // Fetch real client data from the database
  const clients = await getTrainerClients()

  // Mock data for inquiries - this would need to be implemented in the database
  const inquiries = [
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      date: "2023-11-01",
      goals: "Looking to build muscle and improve overall fitness",
    },
    {
      id: "2",
      name: "<PERSON>",
      email: "<EMAIL>",
      date: "2023-10-28",
      goals: "Training for a marathon and need specialized coaching",
    },
  ]

  return <CoachingPageClient clients={clients} inquiries={inquiries} />
} 