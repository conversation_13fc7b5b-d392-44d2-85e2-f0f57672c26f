// User types
export type UserRole = 'client' | 'athlete' | 'admin';

export interface User {
  id: string;
  name?: string;
  email: string;
  fullName: string;
  role: UserRole;
  bio?: string;
  avatarUrl?: string;
  socialLinks?: Record<string, string>;
  createdAt: string;
  updatedAt: string;
  slug?: string;
  themeSettings?: string;
}

// Workout and Exercise types
export type WorkoutType = 'strength' | 'cardio' | 'flexibility' | 'recovery' | 'hybrid';
export type MuscleGroup = 'chest' | 'back' | 'legs' | 'shoulders' | 'arms' | 'core' | 'full_body';
export type ExerciseDifficulty = 'beginner' | 'intermediate' | 'advanced';

export interface Exercise {
  id: string;
  name: string;
  description?: string;
  thumbnailUrl?: string;
  videoUrl?: string;
  type?: string;
  muscleGroup?: string;
  equipment?: string;
  difficulty?: string;
  sets?: number;
  reps?: number;
  duration?: number;
  restTime?: number;
  tempo?: string;
  notes?: string;
  order: number;
  isTemplate: boolean;
  createdAt: string;
  updatedAt: string;
  workoutId: string;
}

export interface TemplateExercise {
  id: string;
  name: string;
  description?: string;
  sets?: number;
  reps?: number;
  duration?: number;
  restTime?: number;
  videoUrl?: string;
  type: string;
  difficulty: string;
  createdAt: string;
  updatedAt: string;
}

export interface Workout {
  id: string;
  title: string;
  description?: string;
  type: string;
  order: number;
  isTemplate: boolean;
  createdAt: string;
  updatedAt: string;
  weekId: string;
  trainingPlanId: string;
  exercises: Exercise[];
  scheduledDate?: string;
  isCompleted: boolean;
  completedDate?: string;
}

export interface Week {
  id: string;
  weekNumber: number;
  order: number;
  trainingPlanId: string;
  workouts: Workout[];
  createdAt: string;
  updatedAt: string;
}

export interface TrainingPlan {
  id: string;
  title: string;
  description?: string;
  difficulty: string;
  is_template: boolean;
  createdAt: string;
  updatedAt: string;
  athleteId: string;
  weeks: Week[];
  workouts: Workout[];
}

// Subscription types
export interface SubscriptionTier {
  id: string;
  name: string;
  description: string;
  price: number;
  stripePriceId?: string;
  features: string[];
  createdAt: string;
  updatedAt: string;
}

export type SubscriptionStatus = 'active' | 'paused' | 'canceled' | 'trial' | 'past_due';

export interface Subscription {
  id: string;
  athleteId: string;
  clientId: string;
  tierId: string;
  stripeSubscriptionId?: string;
  stripeCustomerId?: string;
  status: SubscriptionStatus;
  startDate: string;
  endDate?: string;
  currentPeriodStart?: string;
  currentPeriodEnd?: string;
  createdAt: string;
  updatedAt: string;
  tier?: SubscriptionTier;
  athlete?: User;
}

// Nutrition types
export interface Meal {
  id: string;
  name: string;
  description: string;
  calories: number;
  protein: number;
  carbs: number;
  fats: number;
  dietPlanId: string;
  createdAt: string;
  updatedAt: string;
}

export interface DietPlan {
  id: string;
  title: string;
  description: string;
  athleteId: string;
  createdAt: string;
  updatedAt: string;
  meals: Meal[];
}

// Product types
export type ProductType = 'digital' | 'physical' | 'subscription';

export interface Product {
  id: string;
  title: string;
  description: string;
  price: number;
  thumbnailUrl?: string;
  fileUrl?: string;
  productType: ProductType;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  athleteId: string;
}

export interface CartItem {
  id: string;
  title: string;
  price: number;
  quantity: number;
  thumbnailUrl?: string;
  productType: ProductType;
}

export interface PurchasedProduct {
  id: string;
  title: string;
  description: string;
  price: number;
  thumbnailUrl?: string;
  fileUrl?: string;
  productType: ProductType;
  purchaseDate: Date;
}

// Progress types
export interface Progress {
  id: string;
  clientId: string;
  date: string;
  weight: number;
  bodyFat?: number;
  measurements?: Record<string, number>;
  createdAt: string;
  updatedAt: string;
}

// Coaching types
export type CoachingInquiryStatus = 'pending' | 'accepted' | 'declined';

export interface CoachingInquiry {
  id: string;
  clientName: string;
  clientEmail: string;
  message: string;
  status: CoachingInquiryStatus;
  createdAt: string;
  updatedAt: string;
  athleteId: string;
  athlete?: User;
}

export interface CoachingRelationship {
  id: string;
  trainerId: string;
  clientId: string;
  status: string;
  startDate: string;
  endDate?: string;
  createdAt: string;
  updatedAt: string;
  trainer?: User;
  client?: User;
  monthlyFee: number;
  plan?: string;
  notes?: string;
}

// Form submission types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
} 