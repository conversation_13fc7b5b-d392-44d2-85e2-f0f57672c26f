import { NextResponse } from "next/server";

// Valid roles for the application
const validRoles = ["admin", "trainer", "client"];

// DEV ONLY: Simple endpoint to switch roles without authentication
export async function GET(request: Request) {
  // Only available in development mode
  if (process.env.NODE_ENV !== "development") {
    return new NextResponse("Not available in production", { status: 403 });
  }

  const { searchParams } = new URL(request.url);
  const role = searchParams.get("role");
  const returnTo = searchParams.get("returnTo") || "/dashboard";
  
  // Validate role parameter
  if (!role || !validRoles.includes(role)) {
    return new NextResponse(
      `Invalid role. Use one of: ${validRoles.join(", ")}`, 
      { status: 400 }
    );
  }
  
  // Create response that redirects to the dashboard or specified page
  const response = NextResponse.redirect(new URL(returnTo, request.url));
  
  // Set the cookie for role override
  response.cookies.set("dev_override_role", role, { 
    path: "/",
    httpOnly: false, // Needs to be accessible from JavaScript
    maxAge: 60 * 60 * 24 * 7, // 1 week
  });
  
  console.log(`[Dev] Role switched to: ${role}`);
  return response;
} 