"use client"

import { motion } from "framer-motion"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ChevronRight,
  <PERSON>rkles,
  <PERSON>,
  Clock,
  Target,
  MessageSquare
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useEffect, useState } from "react"


import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { RequestCoachingDialog } from "@/components/coaching/request-coaching-dialog"

// Mock subscriptions data - in a real app this would come from an API
const userSubscriptions = [
  {
    id: "sub-1",
    trainerId: "1",
    trainerName: "<PERSON>",
    trainerTitle: "Professional Bodybuilder",
    trainerImage: "/placeholder.svg",
    planName: "Strength Builder",
    active: true,
    weeks: [
      { id: "week-march-18", title: "Week of March 18, 2024", current: true },
      { id: "week-march-11", title: "Week of March 11, 2024", current: false },
      { id: "week-march-04", title: "Week of March 4, 2024", current: false },
      { id: "week-feb-26", title: "Week of February 26, 2024", current: false },
    ]
  },
  {
    id: "sub-2",
    trainerId: "2",
    trainerName: "Sarah Johnson",
    trainerTitle: "CrossFit Expert",
    trainerImage: "/placeholder.svg",
    planName: "HIIT Performance",
    active: false,
    weeks: []
  }
];

// Mock premium plans data - these would be the personalized plans from the coach
const premiumPlans = [
  {
    id: "premium-1",
    coachName: "Coach Michael",
    coachTitle: "Your Personal Coach",
    coachImage: "/placeholder.svg",
    planName: "Customized Strength Program",
    planDescription: "Personalized program designed specifically for your goals",
    lastUpdated: "Updated 2 days ago",
    weeks: [
      { id: "premium-week-1", title: "Week 1: Foundation", current: false, completed: true },
      { id: "premium-week-2", title: "Week 2: Progression", current: false, completed: true },
      { id: "premium-week-3", title: "Week 3: Intensity", current: true, completed: false },
      { id: "premium-week-4", title: "Week 4: Recovery", current: false, completed: false },
    ]
  }
];

export default function WorkoutSelectionPage() {
  // Client-side state for premium status detection
  const [session, setSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isPremiumUser, setIsPremiumUser] = useState(false);
  const [hasActiveSubscriptions, setHasActiveSubscriptions] = useState(false);
  const [lastActiveProgram, setLastActiveProgram] = useState<string | null>(null);

  useEffect(() => {
    async function loadSession() {
      try {
        // Check if there's a real premium status from the server
        const response = await fetch('/api/auth/session');
        const data = await response.json();
        setSession(data);

        // Determine real premium status from session data
        const isPremiumFromSession = data?.user?.subscriptionTier === 'premium';

        // Check for dev cookies in development
        let isPremiumFromCookie = false;
        let trackerEnabled = false;
        let hasSubscription = false;
        let subscriptionTier = '';

        if (process.env.NODE_ENV === 'development') {
          const cookies = document.cookie.split(';');

          // Check premium status
          const premiumCookie = cookies.find(cookie => cookie.trim().startsWith('dev_premium_status='));
          if (premiumCookie) {
            isPremiumFromCookie = premiumCookie.split('=')[1] === 'true';
          }

          // Also check for explicit premiumClient role
          const roleCookie = cookies.find(cookie => cookie.trim().startsWith('dev_override_role='));
          if (roleCookie) {
            const role = roleCookie.split('=')[1];
            if (role === 'premiumClient') {
              isPremiumFromCookie = true;
            }
          }

          // Check if tracker is enabled (from $5 add-on or premium subscription)
          const trackerCookie = cookies.find(cookie => cookie.trim().startsWith('dev_tracker_enabled='));
          if (trackerCookie) {
            trackerEnabled = trackerCookie.split('=')[1] === 'true';
          }

          // Check if user has an active subscription
          const subscriptionCookie = cookies.find(cookie => cookie.trim().startsWith('dev_subscription_active='));
          if (subscriptionCookie) {
            hasSubscription = subscriptionCookie.split('=')[1] === 'true';
          }

          // Check subscription tier
          const tierCookie = cookies.find(cookie => cookie.trim().startsWith('dev_subscription_tier='));
          if (tierCookie) {
            subscriptionTier = tierCookie.split('=')[1];
          }

          console.log('Subscription status:', { hasSubscription, subscriptionTier });
        }

        // Final premium status determination
        const finalPremiumStatus = process.env.NODE_ENV === 'development'
          ? (isPremiumFromCookie || trackerEnabled)
          : isPremiumFromSession;

        setIsPremiumUser(finalPremiumStatus);

        // Check subscriptions - use cookie value in development, mock data otherwise
        setHasActiveSubscriptions(
          process.env.NODE_ENV === 'development'
            ? hasSubscription
            : userSubscriptions.some(sub => sub.active)
        );

        // Check for last active program in localStorage
        const lastActive = localStorage.getItem('lastActiveProgram');
        if (lastActive) {
          setLastActiveProgram(lastActive);
        } else if (userSubscriptions.some(sub => sub.active)) {
          // If no last active program but there are active subscriptions,
          // set the first active subscription as the last active program
          const firstActive = userSubscriptions.find(sub => sub.active);
          if (firstActive) {
            setLastActiveProgram(firstActive.id);
            localStorage.setItem('lastActiveProgram', firstActive.id);
          }
        }

        console.log('Workout page status:', {
          fromSession: isPremiumFromSession,
          fromCookie: isPremiumFromCookie,
          trackerEnabled,
          finalStatus: finalPremiumStatus,
          lastActiveProgram: lastActive
        });

        setLoading(false);
      } catch (error) {
        console.error('Error loading session:', error);
        setLoading(false);
      }
    }

    loadSession();
  }, []);

  // Show loading state
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
      </div>
    );
  }

  // Redirect if not logged in
  if (!session?.user) {
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
    return null;
  }

  return (
    <div className="container py-8 max-w-5xl mx-auto space-y-8">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-4"
      >
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 text-transparent bg-clip-text">
          My Workouts
        </h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Track your progress and stay consistent with your fitness journey
        </p>
      </motion.div>

      {/* Stats Overview */}
      {(hasActiveSubscriptions || isPremiumUser) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-4"
        >
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-primary/10">
                  <Trophy className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <div className="text-2xl font-bold">12</div>
                  <div className="text-sm text-muted-foreground">Workouts Completed</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-primary/10">
                  <Clock className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <div className="text-2xl font-bold">5.5 hrs</div>
                  <div className="text-sm text-muted-foreground">Total Training Time</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-primary/10">
                  <Target className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <div className="text-2xl font-bold">85%</div>
                  <div className="text-sm text-muted-foreground">Goal Progress</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {!hasActiveSubscriptions && !isPremiumUser ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="text-center py-16 px-4"
        >
          <div className="mx-auto w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mb-6">
            <Dumbbell className="h-10 w-10 text-primary" />
          </div>
          <h2 className="text-2xl font-bold mb-4">No Active Subscriptions</h2>
          <p className="text-muted-foreground mb-8 max-w-md mx-auto text-lg">
            Start your fitness journey by subscribing to a trainer's program tailored to your goals.
          </p>
          <Button size="lg" asChild className="animate-pulse">
            <Link href="/dashboard/trainers" className="gap-2">
              Browse Trainers
              <ArrowRight className="h-5 w-5" />
            </Link>
          </Button>
        </motion.div>
      ) : (
        <Tabs defaultValue={isPremiumUser ? "premium-plans" : "subscriptions"} className="space-y-8">
          <TabsList className="mb-6 p-1 gap-2 bg-muted/30">
            {isPremiumUser && (
              <TabsTrigger value="premium-plans" className="gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                <Sparkles className="h-4 w-4" />
                Premium Plans
              </TabsTrigger>
            )}
            <TabsTrigger value="subscriptions" className="gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              <Dumbbell className="h-4 w-4" />
              My Subscriptions
            </TabsTrigger>
          </TabsList>

          {/* Premium Plans Tab */}
          {isPremiumUser && (
            <TabsContent value="premium-plans" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <div className="flex items-center gap-3 mb-6">
                  <Sparkles className="h-5 w-5 text-primary" />
                  <h2 className="text-xl font-semibold">My Personalized Training Plan</h2>
                </div>

                {premiumPlans.map((plan) => (
                  <motion.div
                    key={plan.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <Card className="overflow-hidden border-primary/20 transition-all hover:border-primary/40 hover:shadow-lg">
                      <CardHeader className="bg-primary/5 pb-4">
                        <div className="flex justify-between items-center">
                          <CardTitle className="text-xl flex items-center gap-2">
                            <Sparkles className="h-5 w-5 text-primary" />
                            {plan.planName}
                          </CardTitle>
                          <Badge variant="secondary" className="text-xs">
                            {plan.lastUpdated}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="p-6 space-y-6">
                        <div className="flex items-start gap-6">
                          <div className="relative h-20 w-20 rounded-full overflow-hidden flex-shrink-0 border-2 border-primary/20">
                            <Image
                              src={plan.coachImage}
                              alt={plan.coachName}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between items-start">
                              <div>
                                <h3 className="text-lg font-semibold">{plan.coachName}</h3>
                                <p className="text-muted-foreground">{plan.coachTitle}</p>
                                <p className="mt-2 text-sm">{plan.planDescription}</p>
                              </div>
                              <RequestCoachingDialog
                                trainerId="premium-1"
                                trainerName={plan.coachName}
                                requestType="program"
                                programId={plan.id}
                                programName={plan.planName}
                                trigger={
                                  <Button size="sm" variant="outline">
                                    <MessageSquare className="h-4 w-4 mr-2" />
                                    Ask Coach
                                  </Button>
                                }
                              />
                            </div>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-medium">Program Progress</div>
                            <div className="text-sm text-muted-foreground">
                              {plan.weeks.filter(w => w.completed).length} / {plan.weeks.length} weeks
                            </div>
                          </div>
                          <Progress value={
                            (plan.weeks.filter(w => w.completed).length / plan.weeks.length) * 100
                          } className="h-2" />
                        </div>

                        <div className="space-y-3">
                          <div className="text-sm font-medium">Program Weeks</div>
                          <div className="space-y-2 rounded-lg border bg-card">
                            {plan.weeks.map((week, index) => (
                              <Link
                                key={week.id}
                                href={`/dashboard/training-plans/${week.id}`}
                                className="flex items-center justify-between p-4 hover:bg-muted/50 transition-all border-b last:border-b-0 group"
                                onClick={() => {
                                  // Save this as the last active program
                                  localStorage.setItem('lastActiveProgram', plan.id);
                                }}
                              >
                                <div className="flex items-center gap-3">
                                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                    week.completed
                                      ? 'bg-primary/20 text-primary'
                                      : week.current
                                        ? 'bg-primary text-primary-foreground'
                                        : 'bg-muted text-muted-foreground'
                                  }`}>
                                    {index + 1}
                                  </div>
                                  <div>
                                    <div className="font-medium">{week.title}</div>
                                    <div className="flex items-center gap-2 mt-1">
                                      {week.current && (
                                        <Badge className="bg-primary/10 text-primary">Current</Badge>
                                      )}
                                      {week.completed && (
                                        <Badge variant="outline" className="gap-1">
                                          <Trophy className="h-3 w-3" />
                                          Completed
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <ChevronRight className="h-5 w-5 text-muted-foreground transition-transform group-hover:translate-x-1" />
                              </Link>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            </TabsContent>
          )}

          {/* Subscriptions Tab */}
          <TabsContent value="subscriptions" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* Sort subscriptions to show last active first */}
              {userSubscriptions
                .filter(subscription => subscription.active)
                .sort((a, b) => {
                  // If lastActiveProgram matches a subscription id, put it first
                  if (lastActiveProgram === a.id) return -1;
                  if (lastActiveProgram === b.id) return 1;
                  return 0;
                })
                .map((subscription) => (
                  <Card
                    key={subscription.id}
                    className={`overflow-hidden transition-all hover:shadow-lg ${lastActiveProgram === subscription.id
                      ? 'border-primary border-2'
                      : 'border-primary/20 hover:border-primary/40'}`}
                  >
                    <CardHeader className="bg-primary/5 pb-4">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-xl">{subscription.planName}</CardTitle>
                        <div className="flex gap-2">
                          {lastActiveProgram === subscription.id && (
                            <Badge className="bg-primary/20 text-primary text-xs">Last Active</Badge>
                          )}
                          <Badge variant="outline" className="text-xs">Active Plan</Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-6 space-y-6">
                      <div className="flex items-start gap-6">
                        <div className="relative h-20 w-20 rounded-full overflow-hidden flex-shrink-0 border-2 border-primary/20">
                          <Image
                            src={subscription.trainerImage}
                            alt={subscription.trainerName}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold">{subscription.trainerName}</h3>
                          <p className="text-muted-foreground">{subscription.trainerTitle}</p>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="text-sm font-medium">Available Weeks</div>
                        <div className="space-y-2 rounded-lg border bg-card">
                          {subscription.weeks.map((week, index) => (
                            <Link
                              key={week.id}
                              href={`/dashboard/workout/${week.id}`}
                              className="flex items-center justify-between p-4 hover:bg-muted/50 transition-all border-b last:border-b-0 group"
                              onClick={() => {
                                // Save this as the last active program
                                localStorage.setItem('lastActiveProgram', subscription.id);
                              }}
                            >
                              <div className="flex items-center gap-3">
                                <Calendar className="h-5 w-5 text-muted-foreground" />
                                <div className="font-medium">{week.title}</div>
                                {week.current && (
                                  <Badge className="bg-primary/10 text-primary">Current Week</Badge>
                                )}
                              </div>
                              <ChevronRight className="h-5 w-5 text-muted-foreground transition-transform group-hover:translate-x-1" />
                            </Link>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              ))}
            </motion.div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}