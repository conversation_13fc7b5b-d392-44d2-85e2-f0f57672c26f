import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(req: Request) {
  // Only allow in development mode
  if (process.env.NODE_ENV !== "development") {
    return new NextResponse("Not available in production", { status: 403 })
  }

  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }
  
  try {
    const clients = await prisma.user.findMany({
      where: {
        role: "client",
        clientCoachingRelationships: {
          some: {
            trainerId: "dev-user-id"
          }
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        avatarUrl: true,
        clientProfile: true,
        clientCoachingRelationships: {
          where: {
            trainerId: "dev-user-id"
          },
          select: {
            id: true,
            status: true,
            startDate: true,
            monthlyFee: true,
            plan: true
          }
        }
      }
    })
    
    console.log(`Found ${clients.length} clients for trainer ${session.user.id}`)
    
    return NextResponse.json(clients)
  } catch (error) {
    console.error("Error fetching test clients:", error)
    return NextResponse.json({ error: "Failed to fetch test clients" }, { status: 500 })
  }
}
