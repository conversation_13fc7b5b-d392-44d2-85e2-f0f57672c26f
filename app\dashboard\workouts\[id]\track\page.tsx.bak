"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import {
  ArrowLeft,
  CheckCircle,
  Clock,
  <PERSON><PERSON><PERSON>,
  Info,
  Loader2,
  Youtube
} from "lucide-react"
import { VideoPlayer } from "@/components/video/video-player"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { Slider } from "@/components/ui/slider"
import { toast } from "sonner"

interface SetData {
  setNumber: number;
  weight: number;
  reps: number;
  rpe?: number; // Rate of Perceived Exertion
  rir?: number; // Reps In Reserve
  completed: boolean;
}

interface ExerciseState {
  id: string;
  name: string;
  completed: boolean;
  sets?: number;
  reps?: number;
  weight?: number;
  restTime?: number;
  notes?: string;
  videoUrl?: string;
  setData?: SetData[];
  actualRestTime?: number; // For custom rest time during workout
}

type WorkoutData = {
  id: string;
  title: string;
  description?: string;
  type?: string;
  duration?: number;
  exercises: ExerciseState[];
  source?: string;
  completed?: boolean;
};

export default function WorkoutTrackingPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [workout, setWorkout] = useState<WorkoutData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [completionNote, setCompletionNote] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [exercises, setExercises] = useState<ExerciseState[]>([])

  const workoutId = params.id

  useEffect(() => {
    if (!workoutId) {
      console.error("Workout ID is missing from params")
      setIsLoading(false)
      return
    }

    const fetchWorkout = async () => {
      try {
        // In a real app, you would fetch this from an API
        // For now, we'll use mock data from localStorage or hardcoded data

        // Mock data for premium workouts
        const mockUserWorkouts = [
          {
            id: "workout-premium-1",
            title: "Upper Body Strength",
            description: "Focus on chest, shoulders, and triceps",
            type: "strength",
            duration: 60,
            exercises: [
              { id: "ex1", name: "Bench Press", sets: 4, reps: 10, weight: 135, restTime: 90, notes: "Focus on form", videoUrl: "https://www.youtube.com/watch?v=rT7DgCr-3pg" },
              { id: "ex2", name: "Shoulder Press", sets: 3, reps: 12, weight: 95, restTime: 60, notes: "Keep core tight", videoUrl: "https://www.youtube.com/watch?v=qEwKCR5JCog" },
              { id: "ex3", name: "Tricep Pushdowns", sets: 3, reps: 15, weight: 50, restTime: 45, videoUrl: "https://www.youtube.com/watch?v=2-LAMcpzODU" },
              { id: "ex4", name: "Incline Dumbbell Press", sets: 3, reps: 10, weight: 60, restTime: 60, videoUrl: "https://www.youtube.com/watch?v=0G2_XV7slIg" },
              { id: "ex5", name: "Lateral Raises", sets: 3, reps: 15, weight: 15, restTime: 45, videoUrl: "https://www.youtube.com/watch?v=3VcKaXpzqRo" }
            ],
            source: "1:1 coaching"
          },
          {
            id: "workout-premium-2",
            title: "Lower Body Power",
            description: "Focus on legs and glutes with power movements",
            type: "strength",
            duration: 60,
            exercises: [
              { id: "ex6", name: "Barbell Squats", sets: 4, reps: 8, weight: 185, restTime: 120, notes: "Drive through heels", videoUrl: "https://www.youtube.com/watch?v=ultWZbUMPL8" },
              { id: "ex7", name: "Romanian Deadlifts", sets: 3, reps: 10, weight: 135, restTime: 90, notes: "Keep back flat", videoUrl: "https://www.youtube.com/watch?v=JCXUYuzwNrM" },
              { id: "ex8", name: "Walking Lunges", sets: 3, reps: 12, weight: 40, restTime: 60, videoUrl: "https://www.youtube.com/watch?v=L8fvypPrzzs" },
              { id: "ex9", name: "Leg Press", sets: 3, reps: 12, weight: 225, restTime: 90, videoUrl: "https://www.youtube.com/watch?v=IZxyjW7MPJQ" },
              { id: "ex10", name: "Calf Raises", sets: 4, reps: 15, weight: 100, restTime: 45, videoUrl: "https://www.youtube.com/watch?v=gwLzBJYoWlI" }
            ],
            source: "1:1 coaching"
          }
        ]

        // Mock data for purchased workouts
        const mockPurchasedWorkouts = [
          {
            id: "workout-purchased-1",
            title: "Full Body Conditioning",
            description: "Total body workout using just your bodyweight",
            type: "conditioning",
            duration: 45,
            exercises: [
              { id: "ex11", name: "Burpees", sets: 3, reps: 15, restTime: 45, videoUrl: "https://www.youtube.com/watch?v=TU8QYVW0gDU" },
              { id: "ex12", name: "Mountain Climbers", sets: 3, reps: 30, restTime: 30, videoUrl: "https://www.youtube.com/watch?v=nmwgirgXLYM" },
              { id: "ex13", name: "Jumping Jacks", sets: 3, reps: 50, restTime: 30, videoUrl: "https://www.youtube.com/watch?v=c4DAnQ6DtF8" },
              { id: "ex14", name: "Bodyweight Squats", sets: 3, reps: 20, restTime: 45, videoUrl: "https://www.youtube.com/watch?v=YaXPRqUwItQ" },
              { id: "ex15", name: "Push-ups", sets: 3, reps: 15, restTime: 45, videoUrl: "https://www.youtube.com/watch?v=IODxDxX7oi4" }
            ],
            source: "purchased"
          }
        ]

        // Mock data for subscription workouts
        const mockSubscriptionWorkouts = [
          {
            id: "workout-subscription-1",
            title: "Functional Movement",
            description: "Focus on the seven fundamental movement patterns",
            type: "functional",
            duration: 50,
            exercises: [
              { id: "ex16", name: "Goblet Squats", sets: 3, reps: 12, weight: 35, restTime: 60, notes: "Keep chest up", videoUrl: "https://www.youtube.com/watch?v=MeIiIdhvXT4" },
              { id: "ex17", name: "Kettlebell Swings", sets: 3, reps: 15, weight: 35, restTime: 45, videoUrl: "https://www.youtube.com/watch?v=YSxHifyI6s8" },
              { id: "ex18", name: "TRX Rows", sets: 3, reps: 12, restTime: 60, videoUrl: "https://www.youtube.com/watch?v=XZV9IwluPjw" },
              { id: "ex19", name: "Medicine Ball Slams", sets: 3, reps: 10, weight: 20, restTime: 45, videoUrl: "https://www.youtube.com/watch?v=CUo9WHiRLr8" },
              { id: "ex20", name: "Plank with Shoulder Taps", sets: 3, reps: 20, restTime: 45, videoUrl: "https://www.youtube.com/watch?v=QOCn3_iOAro" }
            ],
            source: "subscription"
          }
        ]

        // Combine all workouts
        const allWorkouts = [
          ...mockUserWorkouts,
          ...mockPurchasedWorkouts,
          ...mockSubscriptionWorkouts
        ]

        // Find the workout by ID
        const foundWorkout = allWorkouts.find(w => w.id === workoutId)

        if (foundWorkout) {
          setWorkout(foundWorkout)

          // Initialize exercises with setData for tracking individual sets
          const initializedExercises = foundWorkout.exercises.map(ex => {
            // Create setData array for each exercise
            const setData = Array.from({ length: ex.sets || 0 }, (_, i) => ({
              setNumber: i + 1,
              weight: ex.weight || 0,
              reps: ex.reps || 0,
              rpe: 7, // Default RPE value
              rir: 2, // Default RIR value
              completed: false
            }))

            return {
              ...ex,
              completed: false,
              setData,
              actualRestTime: ex.restTime // Initialize with default rest time
            }
          })

          setExercises(initializedExercises)
        } else {
          console.error(`Workout with ID ${workoutId} not found`)
          toast.error("Workout not found")
        }
      } catch (error) {
        console.error("Failed to fetch workout:", error)
        toast.error("Failed to load workout")
      } finally {
        setIsLoading(false)
      }
    }

    fetchWorkout()
  }, [workoutId])

  // State for rest timer
  const [activeTimer, setActiveTimer] = useState<{exerciseId: string, timeLeft: number, initialTime: number} | null>(null)
  const [timerRunning, setTimerRunning] = useState(false)

  // Handle updating set data (weight, reps, rpe, rir, completion)
  const handleSetDataUpdate = (exerciseId: string, setNumber: number, field: 'weight' | 'reps' | 'rpe' | 'rir' | 'completed', value: number | boolean) => {
    setExercises(prev =>
      prev.map(ex => {
        if (ex.id !== exerciseId) return ex

        const updatedSetData = ex.setData?.map(set => {
          if (set.setNumber !== setNumber) return set
          return { ...set, [field]: value }
        }) || []

        // Check if all sets are completed to mark the exercise as completed
        const allSetsCompleted = updatedSetData.every(set => set.completed)

        return {
          ...ex,
          setData: updatedSetData,
          completed: allSetsCompleted
        }
      })
    )
  }

  // Handle updating rest time
  const handleRestTimeUpdate = (exerciseId: string, restTime: number) => {
    setExercises(prev =>
      prev.map(ex =>
        ex.id === exerciseId ? { ...ex, actualRestTime: restTime } : ex
      )
    )
  }

  // Start rest timer
  const startRestTimer = (exerciseId: string) => {
    const exercise = exercises.find(ex => ex.id === exerciseId)
    if (!exercise || !exercise.actualRestTime) return

    setActiveTimer({
      exerciseId,
      timeLeft: exercise.actualRestTime,
      initialTime: exercise.actualRestTime
    })
    setTimerRunning(true)
  }
  
  // Pause the timer
  const pauseTimer = () => {
    setTimerRunning(false)
  }
  
  // Resume the timer
  const resumeTimer = () => {
    if (activeTimer) {
      setTimerRunning(true)
    }
  }
  
  // Reset the timer
  const resetTimer = () => {
    if (activeTimer) {
      setActiveTimer({
        ...activeTimer,
        timeLeft: activeTimer.initialTime
      })
      setTimerRunning(false)
    }
  }


  // Handle exercise completion toggle
  const handleExerciseToggle = (id: string) => {
    setExercises(prev =>
      prev.map(ex => {
        if (ex.id !== id) return ex

        // If toggling to completed, mark all sets as completed
        const updatedSetData = ex.setData?.map(set => ({
          ...set,
          completed: !ex.completed
        })) || []

        return {
          ...ex,
          completed: !ex.completed,
          setData: updatedSetData
        }
      })
    )
  }

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (timerRunning && activeTimer) {
      interval = setInterval(() => {
        setActiveTimer(prev => {
          if (!prev) return null

          const newTimeLeft = prev.timeLeft - 1

          if (newTimeLeft <= 0) {
            setTimerRunning(false)
            toast.success("Rest time completed!")
            return null
          }

          return { ...prev, timeLeft: newTimeLeft }
        })
      }, 1000)
    }

    return () => clearInterval(interval)
  }, [timerRunning, activeTimer])

  const allExercisesCompleted = exercises.length > 0 && exercises.every(ex => ex.completed)

  const handleSubmitCompletion = async () => {
    if (!workout) return
    setIsSubmitting(true)
    try {
      // Prepare workout data for saving
      const workoutData = {
        id: workout.id,
        title: workout.title,
        completedAt: new Date().toISOString(),
        note: completionNote,
        exercises: exercises.map(ex => ({
          id: ex.id,
          name: ex.name,
          completed: ex.completed,
          sets: ex.setData?.map(set => ({
            setNumber: set.setNumber,
            weight: set.weight,
            reps: set.reps,
            rpe: set.rpe || 7,
            rir: set.rir || 2,
            completed: set.completed
          })) || [],
          restTime: ex.actualRestTime
        }))
      }

      console.log("Saving workout data:", workoutData)

      // In a real app, you would send this to an API
      // This would save the data for analytics and progress tracking
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Save to localStorage for demo purposes
      const savedWorkouts = JSON.parse(localStorage.getItem('completedWorkouts') || '[]')
      savedWorkouts.push(workoutData)
      localStorage.setItem('completedWorkouts', JSON.stringify(savedWorkouts))

      // Update local state
      setWorkout(prev => prev ? { ...prev, completed: true } : null)

      toast.success("Workout completed successfully!")

      // Redirect back to workouts page after a short delay
      setTimeout(() => {
        router.push("/dashboard/workouts/current")
      }, 1500)
    } catch (error) {
      console.error("Error completing workout:", error)
      toast.error("Failed to save workout completion")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container py-8 flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading workout...</p>
        </div>
      </div>
    )
  }

  if (!workout) {
    return (
      <div className="container py-8">
        <Button variant="outline" asChild className="mb-8">
          <Link href="/dashboard/workouts/current">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Workouts
          </Link>
        </Button>

        <Card className="mx-auto max-w-md text-center p-6">
          <CardContent className="pt-6">
            <h2 className="text-2xl font-bold mb-2">Workout Not Found</h2>
            <p className="text-muted-foreground mb-6">
              The workout you're looking for doesn't exist or you don't have access to it.
            </p>
            <Button asChild>
              <Link href="/dashboard/workouts/current">
                View My Workouts
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Main component render
  return (
    <div className="container max-w-4xl py-8">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-8">
        <Button variant="outline" asChild>
          <Link href="/dashboard/workouts/current">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Workouts
          </Link>
        </Button>

        {allExercisesCompleted ? (
          <Button
            className="bg-green-600 hover:bg-green-700"
            onClick={handleSubmitCompletion}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Complete Workout
              </>
            )}
          </Button>
        ) : (
          <div className="text-sm text-muted-foreground">
            {exercises.filter(ex => ex.completed).length} of {exercises.length} exercises completed
          </div>
        )}
      </div>

      {/* Workout Info */}
      <Card className="mb-8">
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">{workout.title}</CardTitle>
              {workout.description && (
                <CardDescription className="mt-1">{workout.description}</CardDescription>
              )}
            </div>
            {workout.source && (
              <Badge variant={workout.source === '1:1 coaching' ? 'default' : 'outline'} className="capitalize">
                {workout.source}
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="pb-4">
          <div className="flex items-center gap-4 text-sm">
            {workout.duration && (
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>{workout.duration} min</span>
              </div>
            )}
            {workout.type && (
              <div className="flex items-center gap-1">
                <Dumbbell className="h-4 w-4 text-muted-foreground" />
                <span className="capitalize">{workout.type}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Exercise List */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">Exercises</h2>

        {exercises.map((exercise, index) => (
          <Card key={exercise.id} className={`transition-all ${exercise.completed ? 'border-green-500 bg-green-50 dark:bg-green-950/20' : ''}`}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <CardTitle className="text-lg">{exercise.name}</CardTitle>
                </div>
                <Checkbox
                  id={`exercise-${exercise.id}`}
                  checked={exercise.completed}
                  onCheckedChange={() => handleExerciseToggle(exercise.id)}
                  className="h-6 w-6"
                />
              </div>
            </CardHeader>
            <CardContent className="pb-4">
              {/* Exercise Overview */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                {exercise.sets && (
                  <div className="bg-muted rounded-md p-3 text-center">
                    <div className="text-lg font-bold">{exercise.sets}</div>
                    <div className="text-xs text-muted-foreground">Sets</div>
                  </div>
                )}
                {exercise.reps && (
                  <div className="bg-muted rounded-md p-3 text-center">
                    <div className="text-lg font-bold">{exercise.reps}</div>
                    <div className="text-xs text-muted-foreground">Reps</div>
                  </div>
                )}

                {/* Rest Timer Section */}
                <div className="bg-muted rounded-md p-3 text-center relative">
                  <div className="flex justify-center items-center space-x-2">
                    <Input
                      type="number"
                      className="w-16 text-center"
                      value={exercise.actualRestTime || 0}
                      onChange={(e) => handleRestTimeUpdate(exercise.id, parseInt(e.target.value) || 0)}
                    />
                    <span className="text-xs">sec</span>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">Rest Time</div>

                  {/* Timer Display */}
                  {activeTimer && activeTimer.exerciseId === exercise.id ? (
                    <div className="mt-2">
                      <div className="text-xl font-bold mb-2">{activeTimer.timeLeft}s</div>
                      <div className="flex justify-between gap-1">
                        {timerRunning ? (
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex-1"
                            onClick={pauseTimer}
                          >
                            Pause
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex-1"
                            onClick={resumeTimer}
                          >
                            Resume
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1"
                          onClick={resetTimer}
                        >
                          Reset
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      className="mt-2 w-full"
                      onClick={() => startRestTimer(exercise.id)}
                      disabled={timerRunning}
                    >
                      Start Timer
                    </Button>
                  )}
                </div>
              </div>

              {/* Set Tracking */}
              {exercise.setData && exercise.setData.length > 0 && (
                <div className="mt-6 space-y-4">
                  <h3 className="text-sm font-medium">Track Your Sets</h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-12 gap-2 text-xs font-medium text-muted-foreground">
                      <div className="col-span-1">Set</div>
                      <div className="col-span-3">Weight (lbs)</div>
                      <div className="col-span-2">Reps</div>
                      <div className="col-span-2">RPE</div>
                      <div className="col-span-2">RIR</div>
                      <div className="col-span-2 text-center">Done</div>
                    </div>

                    {exercise.setData.map((set) => (
                      <div key={set.setNumber} className="grid grid-cols-12 gap-2 items-center mb-3 pb-3 border-b border-muted last:border-0">
                        <div className="col-span-1 font-medium">{set.setNumber}</div>
                        <div className="col-span-3">
                          <Input
                            type="number"
                            value={set.weight}
                            onChange={(e) => handleSetDataUpdate(
                              exercise.id,
                              set.setNumber,
                              'weight',
                              parseInt(e.target.value) || 0
                            )}
                          />
                        </div>
                        <div className="col-span-2">
                          <Input
                            type="number"
                            value={set.reps}
                            onChange={(e) => handleSetDataUpdate(
                              exercise.id,
                              set.setNumber,
                              'reps',
                              parseInt(e.target.value) || 0
                            )}
                          />
                        </div>
                        <div className="col-span-2">
                          <Input
                            type="number"
                            min="1"
                            max="10"
                            value={set.rpe || 7}
                            onChange={(e) => handleSetDataUpdate(
                              exercise.id,
                              set.setNumber,
                              'rpe',
                              parseInt(e.target.value) || 7
                            )}
                          />
                        </div>
                        <div className="col-span-2">
                          <Input
                            type="number"
                            min="0"
                            max="5"
                            value={set.rir || 2}
                            onChange={(e) => handleSetDataUpdate(
                              exercise.id,
                              set.setNumber,
                              'rir',
                              parseInt(e.target.value) || 2
                            )}
                          />
                        </div>
                        <div className="col-span-2 flex justify-center">
                          <Checkbox
                            checked={set.completed}
                            onCheckedChange={(checked) => handleSetDataUpdate(
                              exercise.id,
                              set.setNumber,
                              'completed',
                              !!checked
                            )}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {exercise.notes && (
                <div className="bg-primary/10 p-3 rounded-md mb-4 border border-primary/20">
                  <div className="flex items-center gap-2 mb-1">
                    <Info className="h-4 w-4 text-primary" />
                    <span className="text-sm font-medium">Trainer Comments</span>
                  </div>
                  <p className="text-sm">{exercise.notes}</p>
                </div>
              )}

              {/* Video Section */}
              {exercise.videoUrl && (
                <div className="mt-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Youtube className="h-4 w-4 text-red-500" />
                    <span className="text-sm font-medium">Exercise Video</span>
                  </div>
                  <VideoPlayer
                    videoId={exercise.videoUrl}
                    title={`${exercise.name} demonstration`}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Completion Notes */}
      {allExercisesCompleted && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Workout Notes</CardTitle>
            <CardDescription>Add any notes about your workout performance</CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="How did this workout feel? Any achievements or challenges?"
              value={completionNote}
              onChange={(e) => setCompletionNote(e.target.value)}
              rows={4}
            />
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleSubmitCompletion}
              className="w-full bg-green-600 hover:bg-green-700"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving Workout...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Complete Workout
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      )}
    </div>
  )
}
