"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { WeeklyPlanEditor } from "./weekly-plan-editor"
import { TrainingPlanTemplate } from "@prisma/client"

interface TrainingPlanEditorProps {
  initialData: TrainingPlanTemplate
  mode: "create" | "edit"
}

export function TrainingPlanEditor({ initialData, mode }: TrainingPlanEditorProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [plan, setPlan] = useState(() => {
    const weeks = initialData?.weeks 
      ? (typeof initialData.weeks === 'string' ? JSON.parse(initialData.weeks) : initialData.weeks)
      : []

    return {
      id: initialData?.id || "",
      title: initialData?.title || "",
      description: initialData?.description || "",
      type: "template",
      weeks
    }
  })

  const handlePlanUpdate = (updatedPlan: any) => {
    setPlan(updatedPlan)
  }

  const handleSave = async () => {
    try {
      setIsLoading(true)

      if (!plan.title) {
        toast({
          title: "Error",
          description: "Title is required",
          variant: "destructive",
        })
        return
      }

      const endpoint = mode === "create" 
        ? "/api/training-plan-templates"
        : `/api/training-plan-templates/${initialData.id}`

      const method = mode === "create" ? "POST" : "PUT"

      const response = await fetch(endpoint, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(plan),
      })

      if (!response.ok) {
        throw new Error("Failed to save training plan")
      }

      toast({
        title: "Success",
        description: mode === "create" 
          ? "Training plan created successfully" 
          : "Training plan updated successfully",
      })

      router.push("/dashboard/training-plans")
      router.refresh()
    } catch (error) {
      console.error("Error saving training plan:", error)
      toast({
        title: "Error",
        description: "Failed to save training plan",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div>
          <label htmlFor="title" className="block text-sm font-medium mb-2">
            Title
          </label>
          <Input
            id="title"
            value={plan.title}
            onChange={(e) => setPlan({ ...plan, title: e.target.value })}
            placeholder="Enter plan title"
            disabled={isLoading}
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium mb-2">
            Description
          </label>
          <Textarea
            id="description"
            value={plan.description}
            onChange={(e) => setPlan({ ...plan, description: e.target.value })}
            placeholder="Enter plan description"
            disabled={isLoading}
          />
        </div>
      </div>

      <WeeklyPlanEditor
        plan={plan}
        onPlanUpdate={handlePlanUpdate}
        availableExercises={[]}
        isEditMode={mode === "edit"}
      />

      <div className="flex justify-end space-x-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          disabled={isLoading}
        >
          {isLoading ? "Saving..." : mode === "create" ? "Create Plan" : "Save Changes"}
        </Button>
      </div>
    </div>
  )
} 