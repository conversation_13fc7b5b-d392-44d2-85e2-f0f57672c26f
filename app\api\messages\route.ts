import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: Request) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return new NextResponse("Unauthorized", { status: 401 })
  }
  const userId = session.user.id;

  const { searchParams } = new URL(request.url)
  const otherUserId = searchParams.get("otherUserId");

  try {
    const conversations = await prisma.conversation.findMany({
      where: {
        OR: [
          { user1Id: userId },
          { user2Id: userId }
        ],
        ...(otherUserId ? {
            AND: [
                { OR: [{ user1Id: userId }, { user2Id: userId }] },
                { OR: [{ user1Id: otherUserId }, { user2Id: otherUserId }] }
            ]
        } : {})
      },
      select: { id: true }
    });
    const conversationIds = conversations.map(c => c.id);

    if (conversationIds.length === 0) {
        return NextResponse.json([]);
    }

    const messages = await prisma.message.findMany({
      where: {
        conversationId: {
          in: conversationIds
        }
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
          },
        },
        receiver: {
           select: {
             id: true,
             name: true,
             avatarUrl: true,
           }
        }
      },
      orderBy: {
        createdAt: "asc",
      },
    })

    return NextResponse.json(messages)
  } catch (error) {
    console.error("Error fetching messages:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return new NextResponse("Unauthorized", { status: 401 })
  }
  const userId = session.user.id;

  try {
    const body = await request.json();
    const { content, receiverId } = body;

    if (!content || !receiverId) {
        return new NextResponse("Missing content or receiverId", { status: 400 });
    }

    if (userId === receiverId) {
        return new NextResponse("Cannot send message to yourself", { status: 400 });
    }

    let conversation = await prisma.conversation.findFirst({
      where: {
        OR: [
          { user1Id: userId, user2Id: receiverId },
          { user1Id: receiverId, user2Id: userId },
        ],
      },
    });

    if (!conversation) {
      try {
        conversation = await prisma.conversation.create({
          data: {
            user1Id: userId,
            user2Id: receiverId,
          },
        });
      } catch (e: any) {
         if (e.code === 'P2002') {
            conversation = await prisma.conversation.findFirst({
               where: {
                 OR: [
                   { user1Id: userId, user2Id: receiverId },
                   { user1Id: receiverId, user2Id: userId },
                 ],
               },
            });
            if (!conversation) throw new Error("Failed to find or create conversation after P2002 error.");
         } else {
            throw e;
         }
      }
    }

    const newMessage = await prisma.message.create({
      data: {
        content: content,
        senderId: userId,
        receiverId: receiverId,
        conversationId: conversation.id,
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
          },
        },
         receiver: {
           select: {
             id: true,
             name: true,
             avatarUrl: true,
           }
         }
      },
    })

    // Create a notification for the receiver
    await prisma.notification.create({
      data: {
        userId: receiverId,
        title: "New Message",
        message: `You have a new message from ${newMessage.sender.name || 'a user'}`,
        type: "message",
        actionLink: `/dashboard/chats?conversationId=${conversation.id}`,
        sourceId: newMessage.id,
        sourceType: "message",
      },
    })

    return NextResponse.json(newMessage)
  } catch (error) {
    console.error("Error creating message:", error)
    if (error instanceof Error && 'code' in error && error.code === 'P2003') {
         return new NextResponse("Receiver user not found", { status: 404 });
    }
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}