'use client';

import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Ruler, 
  Scale, 
  Percent, 
  ChevronRight, 
  ChevronLeft,
  Info
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface MeasurementGuideProps {
  className?: string;
}

export function MeasurementGuide({ className }: MeasurementGuideProps) {
  const [activeTab, setActiveTab] = useState('weight');
  
  const guides = [
    {
      id: 'weight',
      title: 'Weight',
      icon: <Scale className="h-5 w-5" />,
      image: '/images/measurement-weight.svg',
      instructions: [
        'Weigh yourself first thing in the morning',
        'Before eating or drinking',
        'Wear minimal or no clothing',
        'Use the same scale each time',
        'Stand still with weight evenly distributed'
      ]
    },
    {
      id: 'bodyFat',
      title: 'Body Fat',
      icon: <Percent className="h-5 w-5" />,
      image: '/images/measurement-bodyfat.svg',
      instructions: [
        'Use calipers at the same locations each time',
        'Take measurements in a relaxed state',
        'For bioelectrical impedance scales, maintain consistent hydration',
        'Take multiple measurements and average them',
        'Consider professional DEXA scans for most accurate results'
      ]
    },
    {
      id: 'chest',
      title: 'Chest',
      icon: <Ruler className="h-5 w-5" />,
      image: '/images/measurement-chest.svg',
      instructions: [
        'Wrap tape measure around the fullest part of your chest',
        'Keep the tape parallel to the floor',
        'Breathe normally and measure at mid-breath',
        'Don\'t flex your chest muscles',
        'Keep the tape snug but not tight'
      ]
    },
    {
      id: 'waist',
      title: 'Waist',
      icon: <Ruler className="h-5 w-5" />,
      image: '/images/measurement-waist.svg',
      instructions: [
        'Measure at the narrowest part of your waist',
        'Usually just above the belly button',
        'Keep the tape parallel to the floor',
        'Breathe normally and measure at the end of a normal exhale',
        'Don\'t suck in your stomach'
      ]
    },
    {
      id: 'hips',
      title: 'Hips',
      icon: <Ruler className="h-5 w-5" />,
      image: '/images/measurement-hips.svg',
      instructions: [
        'Measure around the widest part of your hips/buttocks',
        'Keep the tape parallel to the floor',
        'Stand with feet together',
        'Keep the tape snug but not tight',
        'Measure over thin clothing'
      ]
    },
    {
      id: 'arms',
      title: 'Arms',
      icon: <Ruler className="h-5 w-5" />,
      image: '/images/measurement-arms.svg',
      instructions: [
        'Measure around the fullest part of your upper arm',
        'Keep your arm relaxed at your side',
        'For consistency, always measure the same arm',
        'Keep the tape perpendicular to your arm',
        'Don\'t flex your bicep'
      ]
    },
    {
      id: 'thighs',
      title: 'Thighs',
      icon: <Ruler className="h-5 w-5" />,
      image: '/images/measurement-thighs.svg',
      instructions: [
        'Measure around the fullest part of your thigh',
        'Usually just below where the thigh meets the buttocks',
        'Keep your weight evenly distributed on both feet',
        'Keep the tape parallel to the floor',
        'For consistency, always measure the same thigh'
      ]
    }
  ];

  const currentGuideIndex = guides.findIndex(guide => guide.id === activeTab);
  
  const handlePrevious = () => {
    const newIndex = (currentGuideIndex - 1 + guides.length) % guides.length;
    setActiveTab(guides[newIndex].id);
  };
  
  const handleNext = () => {
    const newIndex = (currentGuideIndex + 1) % guides.length;
    setActiveTab(guides[newIndex].id);
  };

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex items-center justify-between bg-muted p-2">
            <Button variant="ghost" size="icon" onClick={handlePrevious}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <TabsList className="grid grid-flow-col auto-cols-fr">
              {guides.map(guide => (
                <TabsTrigger 
                  key={guide.id} 
                  value={guide.id}
                  className="flex items-center gap-2 px-3 py-1.5"
                >
                  {guide.icon}
                  <span className="hidden sm:inline">{guide.title}</span>
                </TabsTrigger>
              ))}
            </TabsList>
            <Button variant="ghost" size="icon" onClick={handleNext}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          
          {guides.map(guide => (
            <TabsContent key={guide.id} value={guide.id} className="m-0">
              <div className="grid md:grid-cols-2 gap-4 p-4">
                <div className="flex flex-col justify-center items-center">
                  {/* Placeholder for measurement figure image */}
                  <div className="w-full aspect-square bg-muted/50 rounded-lg flex items-center justify-center">
                    {guide.image ? (
                      <img 
                        src={guide.image} 
                        alt={`How to measure ${guide.title}`} 
                        className="max-h-full max-w-full object-contain"
                      />
                    ) : (
                      <div className="text-center p-4">
                        <Info className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                        <p className="text-muted-foreground">Measurement guide illustration</p>
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
                    {guide.icon}
                    How to Measure: {guide.title}
                  </h3>
                  <ul className="space-y-2">
                    {guide.instructions.map((instruction, i) => (
                      <li key={i} className="flex items-start gap-2">
                        <span className="bg-primary/10 text-primary rounded-full w-5 h-5 flex items-center justify-center text-xs mt-0.5">
                          {i + 1}
                        </span>
                        <span>{instruction}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  );
}
