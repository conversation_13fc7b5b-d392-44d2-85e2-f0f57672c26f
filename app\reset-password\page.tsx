"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2 } from "lucide-react"

// Type for Zod flattened errors
type ResetFieldErrors = {
  password?: string[];
  token?: string[]; // Although less likely for token errors to be field-specific here
};

function ResetPasswordContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const token = searchParams.get("token")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [status, setStatus] = useState<"idle" | "loading" | "success" | "error">("idle")
  const [feedbackMessage, setFeedbackMessage] = useState("")
  const [error, setError] = useState<string | null>(null); // Re-add generic error state if needed
  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null)
  const [fieldErrors, setFieldErrors] = useState<ResetFieldErrors>({}) // Field-specific errors

  // Basic check if token exists on mount
  useEffect(() => {
    if (!token) {
      setStatus("error")
      setFeedbackMessage("Password reset token is missing or invalid.")
      setIsTokenValid(false)
    } else {
      setIsTokenValid(true) // Assume valid initially, API will confirm
    }
  }, [token])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError(null) // Clear generic error
    setFieldErrors({}) // Clear previous field errors

    // Frontend validation checks remain useful for immediate feedback
    if (password !== confirmPassword) {
        setStatus("error")
        setFeedbackMessage("Passwords do not match.")
        return
    }
    if (password.length < 8) {
        setStatus("error")
        setFeedbackMessage("Password must be at least 8 characters long.")
        return
    }

    setStatus("loading")
    setFeedbackMessage("")

    try {
      const response = await fetch("/api/auth/password-reset/reset", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token, password }),
      })

      const data = await response.json()

      if (!response.ok) {
         if (response.status === 400 && data.errors) {
          // Handle Zod validation errors
          setFieldErrors(data.errors);
          setFeedbackMessage("Please fix the errors in the form."); // Set a generic message
        } else {
          // Handle other errors (e.g., invalid/expired token, 500)
          setFeedbackMessage(data.message || "Failed to reset password.")
          setFieldErrors({});
           if (response.status === 400 || response.status === 410) {
                setIsTokenValid(false) // Mark token as invalid if API confirms
           }
        }
        throw new Error(data.message || "Failed to reset password.") // Ensure catch block runs for errors
      }

      // Success
      setStatus("success")
      setFieldErrors({});
      setFeedbackMessage(data.message + " You can now log in with your new password.")
      // Optionally redirect to login after delay
       setTimeout(() => {
          router.push("/login?reset=success")
        }, 3000)

    } catch (error) {
      setStatus("error")
      // Feedback message is likely already set if it came from !response.ok block
      if (!feedbackMessage) {
          setFeedbackMessage(error instanceof Error ? error.message : "An unexpected error occurred.")
      }
      setError(error instanceof Error ? error.message : "An error occurred.") // Set generic error too
      // Already handle setIsTokenValid inside the !response.ok block for specific errors
    }
  }

  if (isTokenValid === false) {
      // Show simplified error if token was invalid from the start or became invalid
      return (
          <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8 text-center">
                 <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">Invalid Link</h2>
                 <p className="mt-2 text-center text-sm text-red-600">
                     {feedbackMessage || "This password reset link is invalid or has expired."} Please request a new one.
                 </p>
                 <div className="mt-6">
                    <Link href="/forgot-password" passHref>
                      <Button variant="outline">Request New Link</Button>
                    </Link>
                    <Link href="/login" passHref className="ml-4">
                      <Button>Go to Login</Button>
                    </Link>
                 </div>
            </div>
        </div>
      )
  }

  if (isTokenValid === null) {
       // Still checking token validity (or missing token initial state)
        return (
            <div className="min-h-screen flex items-center justify-center">
                 <Loader2 className="h-12 w-12 animate-spin" />
            </div>
        )
  }

  // Render form if token seems valid initially
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Reset Your Password
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Enter your new password below.
          </p>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm space-y-4">
             <div>
              <Label htmlFor="password">New Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                required
                className={`appearance-none relative block w-full px-3 py-2 border ${fieldErrors.password ? 'border-red-500' : 'border-gray-300'} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm`}
                placeholder="Enter new password (min 8 chars, Aa, 1#, 1$)"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={status === "loading" || status === "success"}
              />
              {fieldErrors.password && (
                <p className="mt-1 text-xs text-red-600">{fieldErrors.password.join(", ")}</p>
              )}
            </div>
             <div>
              <Label htmlFor="confirmPassword">Confirm New Password</Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                required
                className={`appearance-none relative block w-full px-3 py-2 border ${password !== confirmPassword && confirmPassword ? 'border-red-500' : 'border-gray-300'} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm`}
                placeholder="Confirm new password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                disabled={status === "loading" || status === "success"}
              />
               {password !== confirmPassword && confirmPassword && (
                 <p className="mt-1 text-xs text-red-600">Passwords do not match.</p>
               )}
            </div>
          </div>

          {feedbackMessage && (
            <div className={`text-sm text-center ${status === "success" ? "text-green-600" : status === "error" ? "text-red-600" : "text-gray-600"}`}>
              {feedbackMessage}
            </div>
          )}

          <div>
            <Button
              type="submit"
              disabled={status === "loading" || status === "success"}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {status === "loading" ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Resetting...</>
              ) : (
                "Reset Password"
              )}
            </Button>
          </div>
        </form>
         {status === "success" && (
            <div className="text-center mt-4">
              <Link
                href="/login"
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                Proceed to Login
              </Link>
            </div>
         )}
      </div>
    </div>
  )
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading reset password page...</div>}>
      <ResetPasswordContent />
    </Suspense>
  )
}