"use client"

import "./daily-flow.css"
import { useState, useEffect } from "react"
import {
  Moon,
  Coffee,
  Zap,
  Flame,
  Plus,
  Utensils,
  Scale,
  Smile,
  Frown,
  Meh,
  Pencil,
  Trash2,
  TrendingUp,
  TrendingDown,
  User,
  Calendar
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"

export function DailyFlowMockup() {
  console.log("DailyFlowMockup component rendered")

  // Initialize with sleep category
  const [activeCategory, setActiveCategory] = useState("sleep")

  // State for tracking metrics
  const [isLoading, setIsLoading] = useState(true)
  const [streaks, setStreaks] = useState<any[]>([])
  const [measurements, setMeasurements] = useState<any[]>([])
  const [nutritionLogs, setNutritionLogs] = useState<any[]>([])

  // Track which categories have been logged today
  const [loggedCategories, setLoggedCategories] = useState({
    sleep: false,
    stress: false,
    coffee: false,
    nutrition: false,
    progress: false
  })

  // Form values for metrics
  const [sleepHours, setSleepHours] = useState<number>(7.5)
  const [sleepQuality, setSleepQuality] = useState<string>("good")
  const [stressLevel, setStressLevel] = useState<number>(3)
  const [stressFactors, setStressFactors] = useState<string[]>([])
  const [coffeeCount, setCoffeeCount] = useState<number>(2)
  const [coffeeType, setCoffeeType] = useState<string>("espresso")
  const [timeOfDay, setTimeOfDay] = useState<string>("morning")
  const [weight, setWeight] = useState<number>(175.5)
  const [bodyFat, setBodyFat] = useState<number>(18.5)
  const [waist, setWaist] = useState<number>(34.5)
  const [chest, setChest] = useState<number>(42.0)

  // Nutrition form values
  const [mealType, setMealType] = useState("breakfast")
  const [mealName, setMealName] = useState("")
  const [calories, setCalories] = useState<number>(0)
  const [protein, setProtein] = useState<number>(0)
  const [carbs, setCarbs] = useState<number>(0)
  const [fat, setFat] = useState<number>(0)
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())

  // Handle category changes
  const handleCategoryChange = (value: string) => {
    console.log("Changing category to:", value)
    setActiveCategory(value)
  }

  // Track which category was just logged for celebration effect
  const [justLogged, setJustLogged] = useState<string | null>(null)

  // State for editing logs
  const [editingLog, setEditingLog] = useState<any>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<string>("")

  // State for history modals
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false)
  const [historyCategory, setHistoryCategory] = useState<string>("")

  // Fetch data from API
  useEffect(() => {
    // Initial data fetch
    const fetchInitialData = async () => {
      try {
        setIsLoading(true)

        // Fetch streaks
        const streaksResponse = await fetch("/api/users/me/streaks")
        if (streaksResponse.ok) {
          const streaksData = await streaksResponse.json()
          setStreaks(streaksData)
        }

        // Fetch measurements
        const measurementsResponse = await fetch("/api/users/me/progress")
        if (measurementsResponse.ok) {
          const measurementsData = await measurementsResponse.json()
          // Extract the measurements from the response
          if (measurementsData.measurements && Array.isArray(measurementsData.measurements)) {
            setMeasurements(measurementsData.measurements)
          } else {
            setMeasurements([])
          }
        }

        // Fetch nutrition logs
        const nutritionResponse = await fetch("/api/users/me/nutrition")
        if (nutritionResponse.ok) {
          const nutritionData = await nutritionResponse.json()
          // Extract the logs from the response
          if (nutritionData.logs && Array.isArray(nutritionData.logs)) {
            setNutritionLogs(nutritionData.logs)
          } else {
            setNutritionLogs([])
          }
        }

        // Check which categories have been logged today
        const today = new Date().toDateString()
        const loggedToday = {
          sleep: false,
          stress: false,
          coffee: false,
          nutrition: false,
          progress: false
        }

        // Check if any measurements were logged today
        if (measurementsResponse.ok) {
          const measurementsData = await measurementsResponse.json()
          if (measurementsData.measurements && Array.isArray(measurementsData.measurements)) {
            const todayMeasurements = measurementsData.measurements.filter((m: any) =>
              new Date(m.date).toDateString() === today
            )

            if (todayMeasurements.length > 0) {
              // Check which metrics were logged today
              todayMeasurements.forEach((m: any) => {
                if (m.sleepHours !== null && m.sleepHours !== undefined) loggedToday.sleep = true
                if (m.stressLevel !== null && m.stressLevel !== undefined) loggedToday.stress = true
                if (m.coffeeCount !== null && m.coffeeCount !== undefined) loggedToday.coffee = true
                if (m.weight !== null || m.bodyFat !== null || m.waist !== null || m.chest !== null) {
                  loggedToday.progress = true
                }
              })
            }
          }
        }

        // Check if any nutrition logs were logged today
        if (nutritionResponse.ok) {
          const nutritionData = await nutritionResponse.json()
          if (nutritionData.logs && Array.isArray(nutritionData.logs)) {
            const todayNutrition = nutritionData.logs.filter((n: any) =>
              new Date(n.date).toDateString() === today
            )

            if (todayNutrition.length > 0) {
              loggedToday.nutrition = true
            }
          }
        }

        // Update logged categories state
        setLoggedCategories(loggedToday)
      } catch (error) {
        console.error("Error fetching initial data:", error)
        toast({
          title: "Error",
          description: "Failed to fetch data. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchInitialData()
  }, [])

  // Function to refresh all data
  const refreshData = async () => {
    setIsLoading(true)
    try {
      // Fetch streaks
      const streaksResponse = await fetch("/api/users/me/streaks")
      if (streaksResponse.ok) {
        const streaksData = await streaksResponse.json()
        setStreaks(streaksData)

        // Check which categories have been logged today
        const today = new Date().toDateString()
        const loggedToday = {
          sleep: false,
          stress: false,
          coffee: false,
          nutrition: false,
          progress: false
        }

        // Check streaks with lastLoggedAt today
        streaksData.forEach((streak: any) => {
          if (streak.lastLoggedAt) {
            const lastLogged = new Date(streak.lastLoggedAt).toDateString()
            if (lastLogged === today) {
              loggedToday[streak.type as keyof typeof loggedToday] = true
            }
          }
        })

        // Fetch recent measurements
        const measurementsResponse = await fetch("/api/users/me/progress")
        if (measurementsResponse.ok) {
          const measurementsData = await measurementsResponse.json()
          console.log("Measurements data:", measurementsData)

          // Extract the measurements from the response
          if (measurementsData.measurements && Array.isArray(measurementsData.measurements)) {
            setMeasurements(measurementsData.measurements)
          } else {
            setMeasurements([])
          }

          // Check if any measurements were logged today
          const todayMeasurements = measurementsData.measurements && Array.isArray(measurementsData.measurements) ?
            measurementsData.measurements.filter((m: any) => new Date(m.date).toDateString() === today) :
            []

          if (todayMeasurements.length > 0) {
            // Check which metrics were logged today
            todayMeasurements.forEach((m: any) => {
              if (m.sleepHours !== null && m.sleepHours !== undefined) loggedToday.sleep = true
              if (m.stressLevel !== null && m.stressLevel !== undefined) loggedToday.stress = true
              if (m.coffeeCount !== null && m.coffeeCount !== undefined) loggedToday.coffee = true
              if (m.weight !== null || m.bodyFat !== null || m.waist !== null || m.chest !== null) {
                loggedToday.progress = true
              }
            })

            // Set form values from most recent measurement
            const latest = todayMeasurements[0]
            if (latest.sleepHours !== null && latest.sleepHours !== undefined) setSleepHours(latest.sleepHours)
            if (latest.stressLevel !== null && latest.stressLevel !== undefined) setStressLevel(latest.stressLevel)
            if (latest.coffeeCount !== null && latest.coffeeCount !== undefined) setCoffeeCount(latest.coffeeCount)
            if (latest.weight !== null) setWeight(latest.weight)
            if (latest.bodyFat !== null) setBodyFat(latest.bodyFat)
            if (latest.waist !== null) setWaist(latest.waist)
            if (latest.chest !== null) setChest(latest.chest)
          }
        }

        // Fetch nutrition logs
        const nutritionResponse = await fetch("/api/users/me/nutrition")
        if (nutritionResponse.ok) {
          const nutritionData = await nutritionResponse.json()
          console.log("Nutrition data:", nutritionData)

          // Extract the logs from the response
          if (nutritionData.logs && Array.isArray(nutritionData.logs)) {
            setNutritionLogs(nutritionData.logs)

            // Check if any nutrition logs were logged today
            const todayNutrition = nutritionData.logs.filter((n: any) =>
              new Date(n.date).toDateString() === today
            )

            if (todayNutrition.length > 0) {
              loggedToday.nutrition = true
            }
          } else {
            setNutritionLogs([])
          }
        }

        // Update logged categories state
        setLoggedCategories(loggedToday)
      }
    } catch (error) {
      console.error("Error fetching data:", error)
      toast({
        title: "Error",
        description: "Failed to fetch data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Function to log a category
  const logCategory = async (category: string) => {
    const wasAlreadyLogged = loggedCategories[category as keyof typeof loggedCategories]

    try {
      setIsLoading(true)

      // Log the appropriate metric based on category
      let result;
      switch(category) {
        case "sleep":
          result = await logSleep()
          break
        case "stress":
          result = await logStress()
          break
        case "coffee":
          result = await logCoffee()
          break
        case "nutrition":
          result = await logNutrition()
          break
        case "progress":
          result = await logProgress()
          break
      }

      // Update logged categories
      setLoggedCategories(prev => ({
        ...prev,
        [category]: true
      }))

      // Show celebration only if this is the first time logging this category today
      if (!wasAlreadyLogged) {
        setJustLogged(category)

        // Show toast notification
        toast({
          title: "Great job!",
          description: `You've logged your ${category} for today! 🎉`,
          variant: "default",
        })

        // Clear celebration effect after 2 seconds
        setTimeout(() => {
          setJustLogged(null)
        }, 2000)
      } else {
        // Show update toast
        toast({
          title: "Updated!",
          description: `Your ${category} log has been updated.`,
          variant: "default",
        })
      }

      // No need to refresh data as the API responses already contain the updated data
    } catch (error) {
      console.error(`Error logging ${category}:`, error)
      toast({
        title: "Error",
        description: `Failed to log your ${category}. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }



  // Function to log sleep
  const logSleep = async () => {
    const response = await fetch("/api/users/me/progress", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        weight: weight, // Required by the API
        sleepHours,
        notes: `Sleep logged: ${sleepHours} hours, Quality: ${sleepQuality}`,
        sleepQuality,
      }),
    })

    if (!response.ok) {
      throw new Error("Failed to log sleep")
    }

    // The response already contains the updated measurements
    const measurementsData = await response.json()

    // Extract the measurements from the response
    if (measurementsData.measurements && Array.isArray(measurementsData.measurements)) {
      setMeasurements(measurementsData.measurements)
    } else if (Array.isArray(measurementsData)) {
      // Handle the case where the API directly returns an array of measurements
      setMeasurements(measurementsData)
    } else {
      setMeasurements([])
    }

    return measurementsData
  }

  // Function to log stress
  const logStress = async () => {
    const response = await fetch("/api/users/me/progress", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        weight: weight, // Required by the API
        stressLevel,
        notes: `Stress logged: Level ${stressLevel}, Factors: ${stressFactors.join(', ')}`,
        stressFactors: stressFactors.length > 0 ? JSON.stringify(stressFactors) : null,
      }),
    })

    if (!response.ok) {
      throw new Error("Failed to log stress")
    }

    // The response already contains the updated measurements
    const measurementsData = await response.json()

    // Extract the measurements from the response
    if (measurementsData.measurements && Array.isArray(measurementsData.measurements)) {
      setMeasurements(measurementsData.measurements)
    } else if (Array.isArray(measurementsData)) {
      // Handle the case where the API directly returns an array of measurements
      setMeasurements(measurementsData)
    } else {
      setMeasurements([])
    }

    return measurementsData
  }

  // Function to log coffee
  const logCoffee = async () => {
    const response = await fetch("/api/users/me/progress", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        weight: weight, // Required by the API
        coffeeCount,
        notes: `Coffee logged: ${coffeeCount} cups, Type: ${coffeeType}, Time: ${timeOfDay}`,
        coffeeType,
        timeOfDay,
      }),
    })

    if (!response.ok) {
      throw new Error("Failed to log coffee")
    }

    // The response already contains the updated measurements
    const measurementsData = await response.json()

    // Extract the measurements from the response
    if (measurementsData.measurements && Array.isArray(measurementsData.measurements)) {
      setMeasurements(measurementsData.measurements)
    } else if (Array.isArray(measurementsData)) {
      // Handle the case where the API directly returns an array of measurements
      setMeasurements(measurementsData)
    } else {
      setMeasurements([])
    }

    return measurementsData
  }

  // Function to log nutrition
  const logNutrition = async () => {
    // Default meal name if not provided
    const name = mealName || `${mealType.charAt(0).toUpperCase() + mealType.slice(1)} meal`

    const response = await fetch("/api/users/me/nutrition", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        date: selectedDate.toISOString(),
        mealType,
        name,
        calories: calories || (protein * 4 + carbs * 4 + fat * 9),
        protein,
        carbs,
        fat,
      }),
    })

    if (!response.ok) {
      throw new Error("Failed to log nutrition")
    }

    // The response already contains the updated nutrition logs
    const nutritionData = await response.json()

    // Extract the logs from the response
    if (nutritionData.logs && Array.isArray(nutritionData.logs)) {
      setNutritionLogs(nutritionData.logs)
    } else if (Array.isArray(nutritionData)) {
      // Handle the case where the API directly returns an array of logs
      setNutritionLogs(nutritionData)
    } else {
      setNutritionLogs([])
    }

    return nutritionData
  }

  // Function to log progress
  const logProgress = async () => {
    const response = await fetch("/api/users/me/progress", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        weight,
        bodyFat,
        measurements: {
          waist,
          chest,
        },
        notes: `Progress logged: Weight ${weight} lbs, Body Fat ${bodyFat}%`,
      }),
    })

    if (!response.ok) {
      throw new Error("Failed to log progress")
    }

    // The response already contains the updated measurements
    const measurementsData = await response.json()

    // Extract the measurements from the response
    if (measurementsData.measurements && Array.isArray(measurementsData.measurements)) {
      setMeasurements(measurementsData.measurements)
    } else if (Array.isArray(measurementsData)) {
      // Handle the case where the API directly returns an array of measurements
      setMeasurements(measurementsData)
    } else {
      setMeasurements([])
    }

    return measurementsData
  }

  // Get the appropriate icon and color for celebration
  const getCelebrationDetails = (category: string) => {
    switch(category) {
      case "sleep":
        return { icon: <Moon className="h-5 w-5" />, color: "bg-indigo-500" }
      case "stress":
        return { icon: <Zap className="h-5 w-5" />, color: "bg-amber-500" }
      case "coffee":
        return { icon: <Coffee className="h-5 w-5" />, color: "bg-amber-700" }
      case "nutrition":
        return { icon: <Utensils className="h-5 w-5" />, color: "bg-green-500" }
      case "progress":
        return { icon: <Scale className="h-5 w-5" />, color: "bg-blue-500" }
      default:
        return { icon: <Flame className="h-5 w-5" />, color: "bg-primary" }
    }
  }

  // Get streak count for a specific type
  const getStreakCount = (type: string) => {
    const streak = streaks.find((s: any) => s.type === type)
    return streak ? streak.currentCount : 0
  }

  // Function to open history modal
  const openHistoryModal = (category: string) => {
    setHistoryCategory(category)
    setIsHistoryModalOpen(true)
  }

  // Function to delete a measurement log
  const deleteMeasurement = async (id: string) => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/progress/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error("Failed to delete measurement")
      }

      // Since the DELETE endpoint returns 204 No Content, we need to fetch the updated measurements
      const measurementsResponse = await fetch("/api/users/me/progress")
      if (measurementsResponse.ok) {
        const measurementsData = await measurementsResponse.json()

        // Extract the measurements from the response
        if (measurementsData.measurements && Array.isArray(measurementsData.measurements)) {
          setMeasurements(measurementsData.measurements)
        } else if (Array.isArray(measurementsData)) {
          // Handle the case where the API directly returns an array of measurements
          setMeasurements(measurementsData)
        } else {
          setMeasurements([])
        }
      }

      toast({
        title: "Success",
        description: "Log deleted successfully",
        variant: "default",
      })
    } catch (error) {
      console.error("Error deleting measurement:", error)
      toast({
        title: "Error",
        description: "Failed to delete log. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Function to delete a nutrition log
  const deleteNutritionLog = async (id: string) => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/users/me/nutrition/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error("Failed to delete nutrition log")
      }

      // The response already contains the updated nutrition logs
      const nutritionData = await response.json()
      setNutritionLogs(nutritionData)

      toast({
        title: "Success",
        description: "Nutrition log deleted successfully",
        variant: "default",
      })
    } catch (error) {
      console.error("Error deleting nutrition log:", error)
      toast({
        title: "Error",
        description: "Failed to delete nutrition log. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Function to edit a measurement
  const editMeasurement = async (id: string, data: any) => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/progress/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error("Failed to update measurement")
      }

      // The response already contains the updated measurements
      const measurementsData = await response.json()

      // Extract the measurements from the response
      if (measurementsData.measurements && Array.isArray(measurementsData.measurements)) {
        setMeasurements(measurementsData.measurements)
      } else if (Array.isArray(measurementsData)) {
        // Handle the case where the API directly returns an array of measurements
        setMeasurements(measurementsData)
      } else {
        setMeasurements([])
      }

      toast({
        title: "Success",
        description: "Log updated successfully",
        variant: "default",
      })
    } catch (error) {
      console.error("Error updating measurement:", error)
      toast({
        title: "Error",
        description: "Failed to update log. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Function to edit a nutrition log
  const editNutritionLog = async (id: string, data: any) => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/users/me/nutrition/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error("Failed to update nutrition log")
      }

      // The response already contains the updated nutrition logs
      const nutritionData = await response.json()
      setNutritionLogs(nutritionData)

      toast({
        title: "Success",
        description: "Nutrition log updated successfully",
        variant: "default",
      })
    } catch (error) {
      console.error("Error updating nutrition log:", error)
      toast({
        title: "Error",
        description: "Failed to update nutrition log. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6 relative">
      {/* Celebration overlay when a category is logged */}
      {justLogged && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-black/20 rounded-lg animate-fade-in">
          <div className={`${getCelebrationDetails(justLogged).color} text-white p-6 rounded-full animate-bounce-in shadow-lg flex items-center gap-3`}>
            {getCelebrationDetails(justLogged).icon}
            <span className="font-bold">Logged!</span>
          </div>
        </div>
      )}
      {/* Category subtabs for Daily Flow */}
      <div className="mt-4 mb-6 bg-primary/5 p-6 rounded-lg border border-primary/20 shadow-md">
        <h3 className="text-xl font-semibold mb-4 text-primary">Select Tracking Category</h3>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3">
          <button
            onClick={() => setActiveCategory("sleep")}
            className={`relative flex flex-col items-center justify-center p-4 rounded-lg border
              ${activeCategory === "sleep" ? "bg-indigo-500 text-white border-indigo-600" :
                loggedCategories.sleep ? "bg-indigo-100 border-indigo-300 hover:bg-indigo-200" :
                "bg-background border-primary/20 hover:bg-primary/5"}`}
          >
            {loggedCategories.sleep && (
              <div className={`absolute top-1 right-1 w-3 h-3 bg-green-500 rounded-full border border-white ${justLogged === "sleep" ? "animate-pulse" : ""}`}></div>
            )}
            <Moon className={`h-6 w-6 mb-2 ${loggedCategories.sleep && activeCategory !== "sleep" ? "text-indigo-500" : ""}`} />
            <span>Sleep</span>
          </button>
          <button
            onClick={() => setActiveCategory("stress")}
            className={`relative flex flex-col items-center justify-center p-4 rounded-lg border
              ${activeCategory === "stress" ? "bg-amber-500 text-white border-amber-600" :
                loggedCategories.stress ? "bg-amber-100 border-amber-300 hover:bg-amber-200" :
                "bg-background border-primary/20 hover:bg-primary/5"}`}
          >
            {loggedCategories.stress && (
              <div className={`absolute top-1 right-1 w-3 h-3 bg-green-500 rounded-full border border-white ${justLogged === "stress" ? "animate-pulse" : ""}`}></div>
            )}
            <Zap className={`h-6 w-6 mb-2 ${loggedCategories.stress && activeCategory !== "stress" ? "text-amber-500" : ""}`} />
            <span>Stress</span>
          </button>
          <button
            onClick={() => setActiveCategory("coffee")}
            className={`relative flex flex-col items-center justify-center p-4 rounded-lg border
              ${activeCategory === "coffee" ? "bg-amber-700 text-white border-amber-800" :
                loggedCategories.coffee ? "bg-amber-100 border-amber-300 hover:bg-amber-200" :
                "bg-background border-primary/20 hover:bg-primary/5"}`}
          >
            {loggedCategories.coffee && (
              <div className={`absolute top-1 right-1 w-3 h-3 bg-green-500 rounded-full border border-white ${justLogged === "coffee" ? "animate-pulse" : ""}`}></div>
            )}
            <Coffee className={`h-6 w-6 mb-2 ${loggedCategories.coffee && activeCategory !== "coffee" ? "text-amber-700" : ""}`} />
            <span>Coffee</span>
          </button>
          <button
            onClick={() => setActiveCategory("nutrition")}
            className={`relative flex flex-col items-center justify-center p-4 rounded-lg border
              ${activeCategory === "nutrition" ? "bg-green-500 text-white border-green-600" :
                loggedCategories.nutrition ? "bg-green-100 border-green-300 hover:bg-green-200" :
                "bg-background border-primary/20 hover:bg-primary/5"}`}
          >
            {loggedCategories.nutrition && (
              <div className={`absolute top-1 right-1 w-3 h-3 bg-green-500 rounded-full border border-white ${justLogged === "nutrition" ? "animate-pulse" : ""}`}></div>
            )}
            <Utensils className={`h-6 w-6 mb-2 ${loggedCategories.nutrition && activeCategory !== "nutrition" ? "text-green-500" : ""}`} />
            <span>Nutrition</span>
          </button>
          <button
            onClick={() => setActiveCategory("progress")}
            className={`relative flex flex-col items-center justify-center p-4 rounded-lg border
              ${activeCategory === "progress" ? "bg-blue-500 text-white border-blue-600" :
                loggedCategories.progress ? "bg-blue-100 border-blue-300 hover:bg-blue-200" :
                "bg-background border-primary/20 hover:bg-primary/5"}`}
          >
            {loggedCategories.progress && (
              <div className={`absolute top-1 right-1 w-3 h-3 bg-green-500 rounded-full border border-white ${justLogged === "progress" ? "animate-pulse" : ""}`}></div>
            )}
            <Scale className={`h-6 w-6 mb-2 ${loggedCategories.progress && activeCategory !== "progress" ? "text-blue-500" : ""}`} />
            <span>Progress</span>
          </button>
        </div>
      </div>

      {/* Coffee Category Content */}
      {activeCategory === "coffee" && (
        <div className="space-y-6">
          <Card className="premium-card shadow-md border border-primary/20 overflow-hidden">
            <div className="absolute inset-0 bg-grid-pattern opacity-5 transition-opacity"></div>
            <CardHeader className="relative z-10 pb-2 border-b border-primary/10">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center">
                    <Coffee className="h-5 w-5 text-amber-700" />
                  </div>
                  <div>
                    <CardTitle>Coffee Tracking</CardTitle>
                    <CardDescription>Monitor your daily caffeine intake</CardDescription>
                  </div>
                </div>
                <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-800">
                  <Flame className="h-3 w-3 mr-1" />
                  {getStreakCount("coffee")} day streak
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="relative z-10 pt-6">
              <div className="flex flex-col md:flex-row gap-8 items-center">
                <div className="flex-1 flex flex-col items-center justify-center">
                  <div className="text-6xl font-bold text-amber-700 mb-2">2-3</div>
                  <div className="text-sm text-muted-foreground">Recommended cups/day</div>

                  <div className="mt-6 w-full max-w-xs">
                    <Label htmlFor="coffee-cups" className="mb-2 block">Cups of coffee today</Label>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="icon"
                        className="rounded-r-none h-10 w-10"
                        onClick={() => setCoffeeCount(Math.max(0, coffeeCount - 1))}
                        type="button"
                      >
                        -
                      </Button>
                      <Input
                        id="coffee-cups"
                        className="rounded-none text-center h-10 border-x-0"
                        value={coffeeCount}
                        onChange={(e) => setCoffeeCount(parseInt(e.target.value) || 0)}
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        className="rounded-l-none h-10 w-10"
                        onClick={() => setCoffeeCount(coffeeCount + 1)}
                        type="button"
                      >
                        +
                      </Button>
                      <span className="ml-2">cups</span>
                    </div>
                  </div>
                </div>

                <div className="flex-1 space-y-4">
                  <div className="space-y-2">
                    <Label className="mb-1 block">Coffee Type</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        className={`justify-start py-3 ${coffeeType === "espresso" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setCoffeeType("espresso")}
                      >
                        <span className="mr-2">☕</span>
                        Espresso
                      </Button>
                      <Button
                        variant="outline"
                        className={`justify-start py-3 ${coffeeType === "americano" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setCoffeeType("americano")}
                      >
                        <span className="mr-2">🍵</span>
                        Americano
                      </Button>
                      <Button
                        variant="outline"
                        className={`justify-start py-3 ${coffeeType === "latte" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setCoffeeType("latte")}
                      >
                        <span className="mr-2">🫖</span>
                        Latte
                      </Button>
                      <Button
                        variant="outline"
                        className={`justify-start py-3 ${coffeeType === "other" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setCoffeeType("other")}
                      >
                        <span className="mr-2">☕</span>
                        Other
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="mb-1 block">Time of Day</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        className={`justify-start py-3 ${timeOfDay === "morning" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setTimeOfDay("morning")}
                      >
                        <span className="mr-2">🌅</span>
                        Morning
                      </Button>
                      <Button
                        variant="outline"
                        className={`justify-start py-3 ${timeOfDay === "afternoon" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setTimeOfDay("afternoon")}
                      >
                        <span className="mr-2">🌞</span>
                        Afternoon
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="relative z-10 pt-4 border-t border-primary/10 flex justify-end gap-2">
              <Button variant="outline" onClick={() => openHistoryModal("coffee")}>
                View History
              </Button>
              <Button
                onClick={() => logCategory("coffee")}
                className="bg-amber-700 hover:bg-amber-800 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                {loggedCategories.coffee ? "Update Coffee" : "Log Coffee"}
              </Button>
            </CardFooter>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="premium-card shadow-sm border border-primary/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Recent Coffee Logs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {isLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : measurements && measurements.length > 0 ? measurements.filter(m => m.coffeeCount !== null && m.coffeeCount !== undefined).slice(0, 5).map((log) => (

                    <div key={log.id} className="flex items-center justify-between p-2 rounded-md bg-background/60 border border-primary/10">
                      <div className="flex items-center gap-2">
                        <Coffee className="h-4 w-4 text-amber-700" />
                        <span>{new Date(log.date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="font-medium flex items-center gap-2">
                          {log.coffeeCount} {log.coffeeCount === 1 ? 'cup' : 'cups'}
                        </span>
                        <div className="flex">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              setEditingLog(log)
                              setEditingCategory("coffee")
                              setCoffeeCount(log.coffeeCount)

                              // Set coffee type and time from notes or default values
                              if (log.notes) {
                                // Extract coffee type
                                if (log.notes.includes("Type:")) {
                                  const typeMatch = log.notes.match(/Type: (\w+)/)
                                  if (typeMatch && typeMatch[1]) {
                                    setCoffeeType(typeMatch[1].toLowerCase())
                                  } else {
                                    setCoffeeType("espresso")
                                  }
                                } else {
                                  setCoffeeType("espresso")
                                }

                                // Extract time of day
                                if (log.notes.includes("Time:")) {
                                  const timeMatch = log.notes.match(/Time: (\w+)/)
                                  if (timeMatch && timeMatch[1]) {
                                    setTimeOfDay(timeMatch[1].toLowerCase())
                                  } else {
                                    setTimeOfDay("morning")
                                  }
                                } else {
                                  setTimeOfDay("morning")
                                }
                              } else {
                                setCoffeeType("espresso")
                                setTimeOfDay("morning")
                              }

                              setIsEditDialogOpen(true)
                            }}
                          >
                            <Pencil className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive"
                            onClick={() => deleteMeasurement(log.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )) : (
                    <div className="p-4 text-center text-muted-foreground">
                      <p>No coffee logs found. Log your first coffee intake!</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="premium-card shadow-sm border border-primary/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Caffeine Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 rounded-md bg-amber-50 border border-amber-100 dark:bg-amber-950/30 dark:border-amber-900/50">
                    <div className="font-medium text-amber-800 dark:text-amber-300 mb-1">Weekly Average</div>
                    <p className="text-sm text-amber-700 dark:text-amber-400">You're averaging 2.3 cups of coffee per day this week.</p>
                  </div>

                  <div className="p-3 rounded-md bg-blue-50 border border-blue-100 dark:bg-blue-950/30 dark:border-blue-900/50">
                    <div className="font-medium text-blue-800 dark:text-blue-300 mb-1">Recommendation</div>
                    <p className="text-sm text-blue-700 dark:text-blue-400">Try to avoid coffee after 2pm to improve sleep quality.</p>
                  </div>

                  <div className="p-3 rounded-md bg-green-50 border border-green-100 dark:bg-green-950/30 dark:border-green-900/50">
                    <div className="font-medium text-green-800 dark:text-green-300 mb-1">Hydration Reminder</div>
                    <p className="text-sm text-green-700 dark:text-green-400">Remember to drink water alongside your coffee to stay hydrated.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Sleep Category Content */}
      {activeCategory === "sleep" && (
        <div className="space-y-6">
          <Card className="premium-card shadow-md border border-primary/20 overflow-hidden">
            <div className="absolute inset-0 bg-grid-pattern opacity-5 transition-opacity"></div>
            <CardHeader className="relative z-10 pb-2 border-b border-primary/10">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center">
                    <Moon className="h-5 w-5 text-indigo-500" />
                  </div>
                  <div>
                    <CardTitle>Sleep Tracking</CardTitle>
                    <CardDescription>Log your sleep hours and quality</CardDescription>
                  </div>
                </div>
                <Badge variant="outline" className="bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-400 dark:border-indigo-800">
                  <Flame className="h-3 w-3 mr-1" />
                  {getStreakCount("sleep")} day streak
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="relative z-10 pt-6">
              <div className="flex flex-col md:flex-row gap-8 items-center">
                <div className="flex-1 flex flex-col items-center justify-center">
                  <div className="text-6xl font-bold text-indigo-500 mb-2">7-9</div>
                  <div className="text-sm text-muted-foreground">Recommended hours</div>

                  <div className="mt-6 w-full max-w-xs">
                    <Label htmlFor="sleep-hours" className="mb-2 block">Hours slept last night</Label>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="icon"
                        className="rounded-r-none h-10 w-10"
                        onClick={() => setSleepHours(Math.max(0, sleepHours - 0.5))}
                      >
                        -
                      </Button>
                      <Input
                        id="sleep-hours"
                        className="rounded-none text-center h-10"
                        value={sleepHours}
                        onChange={(e) => setSleepHours(parseFloat(e.target.value) || 0)}
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        className="rounded-l-none h-10 w-10"
                        onClick={() => setSleepHours(sleepHours + 0.5)}
                      >
                        +
                      </Button>
                      <span className="ml-2">hours</span>
                    </div>
                  </div>
                </div>

                <div className="flex-1 space-y-4">
                  <div className="space-y-2">
                    <Label className="mb-1 block">Sleep Quality</Label>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        className={`flex-1 py-6 flex flex-col gap-2 ${sleepQuality === "poor" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setSleepQuality("poor")}
                      >
                        <Frown className="h-6 w-6 text-red-500" />
                        <span>Poor</span>
                      </Button>
                      <Button
                        variant="outline"
                        className={`flex-1 py-6 flex flex-col gap-2 ${sleepQuality === "fair" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setSleepQuality("fair")}
                      >
                        <Meh className="h-6 w-6 text-amber-500" />
                        <span>Fair</span>
                      </Button>
                      <Button
                        variant="outline"
                        className={`flex-1 py-6 flex flex-col gap-2 ${sleepQuality === "good" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setSleepQuality("good")}
                      >
                        <Smile className="h-6 w-6 text-green-500" />
                        <span>Good</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="relative z-10 pt-4 border-t border-primary/10 flex justify-end gap-2">
              <Button variant="outline" onClick={() => openHistoryModal("sleep")}>
                View History
              </Button>
              <Button
                onClick={() => logCategory("sleep")}
                className="bg-indigo-500 hover:bg-indigo-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                {loggedCategories.sleep ? "Update Sleep" : "Log Sleep"}
              </Button>
            </CardFooter>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="premium-card shadow-sm border border-primary/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Recent Sleep Logs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {isLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : measurements && measurements.length > 0 ? measurements.filter(m => m.sleepHours !== null && m.sleepHours !== undefined).slice(0, 5).map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-2 rounded-md bg-background/60 border border-primary/10">
                      <div className="flex items-center gap-2">
                        <Moon className="h-4 w-4 text-indigo-400" />
                        <span>{new Date(log.date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="font-medium">{log.sleepHours} hrs</span>
                        <div className="flex">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              setEditingLog(log)
                              setEditingCategory("sleep")
                              setSleepHours(log.sleepHours)
                              // Set sleep quality from notes or default to "good"
                              if (log.notes && log.notes.includes("Quality:")) {
                                const qualityMatch = log.notes.match(/Quality: (\w+)/)
                                if (qualityMatch && qualityMatch[1]) {
                                  setSleepQuality(qualityMatch[1].toLowerCase())
                                } else {
                                  setSleepQuality("good")
                                }
                              } else {
                                setSleepQuality("good")
                              }
                              setIsEditDialogOpen(true)
                            }}
                          >
                            <Pencil className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive"
                            onClick={() => deleteMeasurement(log.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )) : (
                    <div className="p-4 text-center text-muted-foreground">
                      <p>No sleep logs found. Log your first sleep!</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="premium-card shadow-sm border border-primary/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Sleep Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 rounded-md bg-indigo-50 border border-indigo-100 dark:bg-indigo-950/30 dark:border-indigo-900/50">
                    <div className="font-medium text-indigo-800 dark:text-indigo-300 mb-1">Sleep Pattern</div>
                    <p className="text-sm text-indigo-700 dark:text-indigo-400">Your sleep has been consistent this week. Keep it up!</p>
                  </div>

                  <div className="p-3 rounded-md bg-amber-50 border border-amber-100 dark:bg-amber-950/30 dark:border-amber-900/50">
                    <div className="font-medium text-amber-800 dark:text-amber-300 mb-1">Recommendation</div>
                    <p className="text-sm text-amber-700 dark:text-amber-400">Try to go to bed 30 minutes earlier to improve recovery.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Stress Category Content */}
      {activeCategory === "stress" && (
        <div className="space-y-6">
          <Card className="premium-card shadow-md border border-primary/20 overflow-hidden">
            <div className="absolute inset-0 bg-grid-pattern opacity-5 transition-opacity"></div>
            <CardHeader className="relative z-10 pb-2 border-b border-primary/10">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center">
                    <Zap className="h-5 w-5 text-amber-500" />
                  </div>
                  <div>
                    <CardTitle>Stress Tracking</CardTitle>
                    <CardDescription>Monitor your daily stress levels</CardDescription>
                  </div>
                </div>
                <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-800">
                  <Flame className="h-3 w-3 mr-1" />
                  {getStreakCount("stress")} day streak
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="relative z-10 pt-6">
              <div className="flex flex-col md:flex-row gap-8 items-center">
                <div className="flex-1 space-y-6">
                  <div>
                    <Label className="mb-2 block">Stress Level</Label>
                    <div className="space-y-6">
                      <div className="flex justify-between text-sm">
                        <span>Low</span>
                        <span>Medium</span>
                        <span>High</span>
                      </div>
                      <div className="h-2 bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 rounded-full"></div>
                      <div className="flex justify-between">
                        <Smile className="h-6 w-6 text-green-500" />
                        <Meh className="h-6 w-6 text-yellow-500" />
                        <Frown className="h-6 w-6 text-red-500" />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="mb-1 block">Select Your Stress Level</Label>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        className={`flex-1 py-6 flex flex-col gap-2 ${stressLevel <= 3 ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setStressLevel(2)}
                      >
                        <Smile className="h-6 w-6 text-green-500" />
                        <span>Low</span>
                      </Button>
                      <Button
                        variant="outline"
                        className={`flex-1 py-6 flex flex-col gap-2 ${stressLevel > 3 && stressLevel <= 7 ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setStressLevel(5)}
                      >
                        <Meh className="h-6 w-6 text-amber-500" />
                        <span>Medium</span>
                      </Button>
                      <Button
                        variant="outline"
                        className={`flex-1 py-6 flex flex-col gap-2 ${stressLevel > 7 ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setStressLevel(8)}
                      >
                        <Frown className="h-6 w-6 text-red-500" />
                        <span>High</span>
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="flex-1 space-y-4">
                  <div className="space-y-2">
                    <Label className="mb-1 block">Stress Factors (Optional)</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        className={`justify-start h-auto py-2 ${stressFactors?.includes("work") ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => {
                          const newFactors = stressFactors?.includes("work")
                            ? stressFactors.filter(f => f !== "work")
                            : [...(stressFactors || []), "work"];
                          setStressFactors(newFactors);
                        }}
                      >
                        <span className="mr-2">🏢</span>
                        Work
                      </Button>
                      <Button
                        variant="outline"
                        className={`justify-start h-auto py-2 ${stressFactors?.includes("family") ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => {
                          const newFactors = stressFactors?.includes("family")
                            ? stressFactors.filter(f => f !== "family")
                            : [...(stressFactors || []), "family"];
                          setStressFactors(newFactors);
                        }}
                      >
                        <span className="mr-2">👪</span>
                        Family
                      </Button>
                      <Button
                        variant="outline"
                        className={`justify-start h-auto py-2 ${stressFactors?.includes("financial") ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => {
                          const newFactors = stressFactors?.includes("financial")
                            ? stressFactors.filter(f => f !== "financial")
                            : [...(stressFactors || []), "financial"];
                          setStressFactors(newFactors);
                        }}
                      >
                        <span className="mr-2">💰</span>
                        Financial
                      </Button>
                      <Button
                        variant="outline"
                        className={`justify-start h-auto py-2 ${stressFactors?.includes("time") ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => {
                          const newFactors = stressFactors?.includes("time")
                            ? stressFactors.filter(f => f !== "time")
                            : [...(stressFactors || []), "time"];
                          setStressFactors(newFactors);
                        }}
                      >
                        <span className="mr-2">⏰</span>
                        Time Management
                      </Button>
                      <Button
                        variant="outline"
                        className={`justify-start h-auto py-2 ${stressFactors?.includes("training") ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => {
                          const newFactors = stressFactors?.includes("training")
                            ? stressFactors.filter(f => f !== "training")
                            : [...(stressFactors || []), "training"];
                          setStressFactors(newFactors);
                        }}
                      >
                        <span className="mr-2">🏋️</span>
                        Training
                      </Button>
                      <Button
                        variant="outline"
                        className={`justify-start h-auto py-2 ${stressFactors?.includes("other") ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => {
                          const newFactors = stressFactors?.includes("other")
                            ? stressFactors.filter(f => f !== "other")
                            : [...(stressFactors || []), "other"];
                          setStressFactors(newFactors);
                        }}
                      >
                        <span className="mr-2">➕</span>
                        Other
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="relative z-10 pt-4 border-t border-primary/10 flex justify-end gap-2">
              <Button variant="outline" onClick={() => openHistoryModal("stress")}>
                View History
              </Button>
              <Button
                onClick={() => logCategory("stress")}
                className="bg-amber-500 hover:bg-amber-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                {loggedCategories.stress ? "Update Stress" : "Log Stress"}
              </Button>
            </CardFooter>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="premium-card shadow-sm border border-primary/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Recent Stress Logs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {isLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : measurements && measurements.length > 0 ? measurements.filter(m => m.stressLevel !== null && m.stressLevel !== undefined).slice(0, 5).map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-2 rounded-md bg-background/60 border border-primary/10">
                      <div className="flex items-center gap-2">
                        <Zap className="h-4 w-4 text-amber-400" />
                        <span>{new Date(log.date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="font-medium flex items-center">
                          {log.stressLevel <= 3 ? (
                            <><Smile className="h-4 w-4 text-green-500 mr-1" /> Low</>
                          ) : log.stressLevel <= 7 ? (
                            <><Meh className="h-4 w-4 text-amber-500 mr-1" /> Medium</>
                          ) : (
                            <><Frown className="h-4 w-4 text-red-500 mr-1" /> High</>
                          )}
                        </span>
                        <div className="flex">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              setEditingLog(log)
                              setEditingCategory("stress")
                              setStressLevel(log.stressLevel)

                              // Set stress factors from notes or default to empty array
                              if (log.notes && log.notes.includes("Factors:")) {
                                const factorsMatch = log.notes.match(/Factors: (.+)$/)
                                if (factorsMatch && factorsMatch[1]) {
                                  const factors = factorsMatch[1].split(', ').filter(f => f.trim() !== '')
                                  setStressFactors(factors)
                                } else {
                                  setStressFactors([])
                                }
                              } else {
                                setStressFactors([])
                              }

                              setIsEditDialogOpen(true)
                            }}
                          >
                            <Pencil className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive"
                            onClick={() => deleteMeasurement(log.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )) : (
                    <div className="p-4 text-center text-muted-foreground">
                      <p>No stress logs found. Log your first stress level!</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="premium-card shadow-sm border border-primary/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Stress Management Tips</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 rounded-md bg-amber-50 border border-amber-100 dark:bg-amber-950/30 dark:border-amber-900/50">
                    <div className="font-medium text-amber-800 dark:text-amber-300 mb-1">Breathing Exercise</div>
                    <p className="text-sm text-amber-700 dark:text-amber-400">Try 4-7-8 breathing: Inhale for 4 seconds, hold for 7, exhale for 8.</p>
                  </div>

                  <div className="p-3 rounded-md bg-green-50 border border-green-100 dark:bg-green-950/30 dark:border-green-900/50">
                    <div className="font-medium text-green-800 dark:text-green-300 mb-1">Physical Activity</div>
                    <p className="text-sm text-green-700 dark:text-green-400">Even a short 10-minute walk can reduce stress hormones.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Nutrition Category Content */}
      {activeCategory === "nutrition" && (
        <div className="space-y-6">
          <Card className="premium-card shadow-md border border-primary/20 overflow-hidden">
            <div className="absolute inset-0 bg-grid-pattern opacity-5 transition-opacity"></div>
            <CardHeader className="relative z-10 pb-2 border-b border-primary/10">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                    <Utensils className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <CardTitle>Nutrition Tracking</CardTitle>
                    <CardDescription>Log and monitor your daily nutrition</CardDescription>
                  </div>
                </div>
                <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800">
                  <Flame className="h-3 w-3 mr-1" />
                  {getStreakCount("nutrition")} day streak
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="relative z-10 pt-6">
              <div className="flex flex-col md:flex-row gap-8">
                <div className="flex-1 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="meal-date" className="mb-1 block">Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal border-primary/20"
                        >
                          <Calendar className="mr-2 h-4 w-4" />
                          {format(selectedDate, "PPP")}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={selectedDate}
                          onSelect={(date) => date && setSelectedDate(date)}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="meal-type" className="mb-1 block">Meal Type</Label>
                    <div className="grid grid-cols-3 gap-2">
                      <Button
                        variant="outline"
                        className={`justify-start py-3 ${mealType === "breakfast" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setMealType("breakfast")}
                      >
                        <Coffee className="mr-2 h-4 w-4 text-amber-500" />
                        Breakfast
                      </Button>
                      <Button
                        variant="outline"
                        className={`justify-start py-3 ${mealType === "lunch" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setMealType("lunch")}
                      >
                        <Utensils className="mr-2 h-4 w-4 text-green-500" />
                        Lunch
                      </Button>
                      <Button
                        variant="outline"
                        className={`justify-start py-3 ${mealType === "dinner" ? "bg-primary/5 border-primary" : ""}`}
                        onClick={() => setMealType("dinner")}
                      >
                        <Utensils className="mr-2 h-4 w-4 text-indigo-500" />
                        Dinner
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="meal-name" className="mb-1 block">Meal Name</Label>
                    <Input
                      id="meal-name"
                      placeholder="Enter meal name"
                      value={mealName}
                      onChange={(e) => setMealName(e.target.value)}
                      className="border-primary/20"
                    />
                  </div>
                </div>

                <div className="flex-1 space-y-4">
                  <div className="space-y-2">
                    <Label className="mb-1 block">Quick Macros</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center gap-2">
                        <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 font-semibold">P</div>
                        <div className="flex-1">
                          <Label htmlFor="protein" className="text-xs text-muted-foreground">Protein (g)</Label>
                          <div className="flex items-center">
                            <Button
                              variant="outline"
                              size="icon"
                              className="rounded-r-none h-8 w-8"
                              onClick={() => setProtein(Math.max(0, protein - 5))}
                            >
                              -
                            </Button>
                            <Input
                              id="protein"
                              className="rounded-none text-center h-8 border-x-0"
                              value={protein}
                              onChange={(e) => setProtein(parseInt(e.target.value) || 0)}
                            />
                            <Button
                              variant="outline"
                              size="icon"
                              className="rounded-l-none h-8 w-8"
                              onClick={() => setProtein(protein + 5)}
                            >
                              +
                            </Button>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center text-amber-500 font-semibold">C</div>
                        <div className="flex-1">
                          <Label htmlFor="carbs" className="text-xs text-muted-foreground">Carbs (g)</Label>
                          <div className="flex items-center">
                            <Button
                              variant="outline"
                              size="icon"
                              className="rounded-r-none h-8 w-8"
                              onClick={() => setCarbs(Math.max(0, carbs - 5))}
                            >
                              -
                            </Button>
                            <Input
                              id="carbs"
                              className="rounded-none text-center h-8 border-x-0"
                              value={carbs}
                              onChange={(e) => setCarbs(parseInt(e.target.value) || 0)}
                            />
                            <Button
                              variant="outline"
                              size="icon"
                              className="rounded-l-none h-8 w-8"
                              onClick={() => setCarbs(carbs + 5)}
                            >
                              +
                            </Button>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center text-red-500 font-semibold">F</div>
                        <div className="flex-1">
                          <Label htmlFor="fat" className="text-xs text-muted-foreground">Fat (g)</Label>
                          <div className="flex items-center">
                            <Button
                              variant="outline"
                              size="icon"
                              className="rounded-r-none h-8 w-8"
                              onClick={() => setFat(Math.max(0, fat - 2))}
                            >
                              -
                            </Button>
                            <Input
                              id="fat"
                              className="rounded-none text-center h-8 border-x-0"
                              value={fat}
                              onChange={(e) => setFat(parseInt(e.target.value) || 0)}
                            />
                            <Button
                              variant="outline"
                              size="icon"
                              className="rounded-l-none h-8 w-8"
                              onClick={() => setFat(fat + 2)}
                            >
                              +
                            </Button>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-500 font-semibold">C</div>
                        <div className="flex-1">
                          <Label htmlFor="calories" className="text-xs text-muted-foreground">Calories</Label>
                          <Input
                            id="calories"
                            className="text-center h-8 bg-muted/30"
                            value={calories || (protein * 4 + carbs * 4 + fat * 9)}
                            onChange={(e) => setCalories(parseInt(e.target.value) || 0)}
                            readOnly
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="relative z-10 pt-4 border-t border-primary/10 flex justify-end gap-2">
              <Button variant="outline" onClick={() => openHistoryModal("nutrition")}>
                View History
              </Button>
              <Button
                onClick={() => logCategory("nutrition")}
                className="bg-green-500 hover:bg-green-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                {loggedCategories.nutrition ? "Update Meal" : "Log Meal"}
              </Button>
            </CardFooter>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="premium-card shadow-sm border border-primary/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Recent Nutrition Logs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {isLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : nutritionLogs && nutritionLogs.length > 0 ? nutritionLogs.slice(0, 5).map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-2 rounded-md bg-background/60 border border-primary/10">
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${
                          log.mealType === 'breakfast' ? 'bg-amber-500' :
                          log.mealType === 'lunch' ? 'bg-green-500' :
                          'bg-indigo-500'}`}></div>
                        <span>{new Date(log.date).toLocaleDateString()}</span>
                        <span className="text-sm text-muted-foreground">
                          {log.mealType.charAt(0).toUpperCase() + log.mealType.slice(1)}
                        </span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="font-medium text-sm">{log.name}</span>
                        <div className="flex">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              setEditingLog(log)
                              setEditingCategory("nutrition")
                              setMealType(log.mealType)
                              setMealName(log.name)
                              setProtein(log.protein || 0)
                              setCarbs(log.carbs || 0)
                              setFat(log.fat || 0)
                              setCalories(log.calories || 0)
                              setIsEditDialogOpen(true)
                            }}
                          >
                            <Pencil className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive"
                            onClick={() => deleteNutritionLog(log.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )) : (
                    <div className="p-4 text-center text-muted-foreground">
                      <p>No nutrition logs found. Log your first meal!</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="premium-card shadow-sm border border-primary/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Daily Nutrition Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-4 gap-2">
                    <div className="p-3 rounded-md bg-blue-50 border border-blue-100 dark:bg-blue-950/30 dark:border-blue-900/50 flex flex-col items-center">
                      <div className="text-xs text-blue-700 dark:text-blue-400">Protein</div>
                      <div className="font-bold text-blue-800 dark:text-blue-300">120g</div>
                      <div className="text-xs text-blue-700 dark:text-blue-400">of 150g</div>
                    </div>

                    <div className="p-3 rounded-md bg-amber-50 border border-amber-100 dark:bg-amber-950/30 dark:border-amber-900/50 flex flex-col items-center">
                      <div className="text-xs text-amber-700 dark:text-amber-400">Carbs</div>
                      <div className="font-bold text-amber-800 dark:text-amber-300">180g</div>
                      <div className="text-xs text-amber-700 dark:text-amber-400">of 200g</div>
                    </div>

                    <div className="p-3 rounded-md bg-red-50 border border-red-100 dark:bg-red-950/30 dark:border-red-900/50 flex flex-col items-center">
                      <div className="text-xs text-red-700 dark:text-red-400">Fat</div>
                      <div className="font-bold text-red-800 dark:text-red-300">65g</div>
                      <div className="text-xs text-red-700 dark:text-red-400">of 70g</div>
                    </div>

                    <div className="p-3 rounded-md bg-purple-50 border border-purple-100 dark:bg-purple-950/30 dark:border-purple-900/50 flex flex-col items-center">
                      <div className="text-xs text-purple-700 dark:text-purple-400">Calories</div>
                      <div className="font-bold text-purple-800 dark:text-purple-300">1850</div>
                      <div className="text-xs text-purple-700 dark:text-purple-400">of 2200</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span>Protein</span>
                      <span>80%</span>
                    </div>
                    <Progress value={80} className="h-2 bg-blue-100" indicatorClassName="bg-blue-500" />

                    <div className="flex justify-between text-xs">
                      <span>Carbs</span>
                      <span>90%</span>
                    </div>
                    <Progress value={90} className="h-2 bg-amber-100" indicatorClassName="bg-amber-500" />

                    <div className="flex justify-between text-xs">
                      <span>Fat</span>
                      <span>93%</span>
                    </div>
                    <Progress value={93} className="h-2 bg-red-100" indicatorClassName="bg-red-500" />

                    <div className="flex justify-between text-xs">
                      <span>Calories</span>
                      <span>84%</span>
                    </div>
                    <Progress value={84} className="h-2 bg-purple-100" indicatorClassName="bg-purple-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Progress Category Content */}
      {activeCategory === "progress" && (
        <div className="space-y-6">
          <Card className="premium-card shadow-md border border-primary/20 overflow-hidden">
            <div className="absolute inset-0 bg-grid-pattern opacity-5 transition-opacity"></div>
            <CardHeader className="relative z-10 pb-2 border-b border-primary/10">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                    <Scale className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <CardTitle>Progress Tracking</CardTitle>
                    <CardDescription>Track your body measurements and progress</CardDescription>
                  </div>
                </div>
                <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800">
                  <Flame className="h-3 w-3 mr-1" />
                  {getStreakCount("progress")} day streak
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="relative z-10 pt-6">
              <div className="flex flex-col md:flex-row gap-8">
                <div className="flex-1 space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="weight" className="mb-1 block">Weight</Label>
                      <div className="flex items-center">
                        <Button
                          variant="outline"
                          size="icon"
                          className="rounded-r-none h-10 w-10"
                          type="button"
                          onClick={() => setWeight(Math.max(0, weight - 0.5))}
                        >
                          -
                        </Button>
                        <Input
                          id="weight"
                          className="rounded-none text-center h-10 border-x-0"
                          value={weight}
                          onChange={(e) => setWeight(parseFloat(e.target.value) || 0)}
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          className="rounded-l-none h-10 w-10"
                          type="button"
                          onClick={() => setWeight(weight + 0.5)}
                        >
                          +
                        </Button>
                        <div className="ml-2 flex items-center gap-1">
                          <span>lbs</span>
                          <Button variant="ghost" size="sm" className="h-6 px-1 text-xs">
                            kg
                          </Button>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <span>Last: 176.2 lbs</span>
                        <span className="text-green-500 font-medium">(-0.7)</span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="body-fat" className="mb-1 block">Body Fat %</Label>
                      <div className="flex items-center">
                        <Button
                          variant="outline"
                          size="icon"
                          className="rounded-r-none h-10 w-10"
                          type="button"
                          onClick={() => setBodyFat(Math.max(0, bodyFat - 0.5))}
                        >
                          -
                        </Button>
                        <Input
                          id="body-fat"
                          className="rounded-none text-center h-10 border-x-0"
                          value={bodyFat}
                          onChange={(e) => setBodyFat(parseFloat(e.target.value) || 0)}
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          className="rounded-l-none h-10 w-10"
                          type="button"
                          onClick={() => setBodyFat(Math.min(100, bodyFat + 0.5))}
                        >
                          +
                        </Button>
                        <span className="ml-2">%</span>
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <span>Last: 19.1%</span>
                        <span className="text-green-500 font-medium">(-0.6)</span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="waist" className="mb-1 block">Waist</Label>
                      <div className="flex items-center">
                        <Button
                          variant="outline"
                          size="icon"
                          className="rounded-r-none h-10 w-10"
                          type="button"
                          onClick={() => setWaist(Math.max(0, waist - 0.25))}
                        >
                          -
                        </Button>
                        <Input
                          id="waist"
                          className="rounded-none text-center h-10 border-x-0"
                          value={waist}
                          onChange={(e) => setWaist(parseFloat(e.target.value) || 0)}
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          className="rounded-l-none h-10 w-10"
                          type="button"
                          onClick={() => setWaist(waist + 0.25)}
                        >
                          +
                        </Button>
                        <div className="ml-2 flex items-center gap-1">
                          <span>in</span>
                          <Button variant="ghost" size="sm" className="h-6 px-1 text-xs">
                            cm
                          </Button>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <span>Last: 35.0 in</span>
                        <span className="text-green-500 font-medium">(-0.5)</span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="chest" className="mb-1 block">Chest</Label>
                      <div className="flex items-center">
                        <Button
                          variant="outline"
                          size="icon"
                          className="rounded-r-none h-10 w-10"
                          type="button"
                          onClick={() => setChest(Math.max(0, chest - 0.25))}
                        >
                          -
                        </Button>
                        <Input
                          id="chest"
                          className="rounded-none text-center h-10 border-x-0"
                          value={chest}
                          onChange={(e) => setChest(parseFloat(e.target.value) || 0)}
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          className="rounded-l-none h-10 w-10"
                          type="button"
                          onClick={() => setChest(chest + 0.25)}
                        >
                          +
                        </Button>
                        <div className="ml-2 flex items-center gap-1">
                          <span>in</span>
                          <Button variant="ghost" size="sm" className="h-6 px-1 text-xs">
                            cm
                          </Button>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <span>Last: 41.5 in</span>
                        <span className="text-blue-500 font-medium">(+0.5)</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex-1 space-y-4">
                  <div className="space-y-2">
                    <Label className="mb-1 block">Progress Photo (Optional)</Label>
                    <div className="border-2 border-dashed border-primary/20 rounded-md p-6 flex flex-col items-center justify-center text-center">
                      <User className="h-10 w-10 text-muted-foreground mb-2" />
                      <p className="text-sm text-muted-foreground mb-4">Drag & drop a photo here or click to upload</p>
                      <Button variant="outline" size="sm">
                        Upload Photo
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="relative z-10 pt-4 border-t border-primary/10 flex justify-end gap-2">
              <Button variant="outline" onClick={() => openHistoryModal("progress")}>
                View History
              </Button>
              <Button
                onClick={() => logCategory("progress")}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                {loggedCategories.progress ? "Update Progress" : "Log Progress"}
              </Button>
            </CardFooter>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="premium-card shadow-sm border border-primary/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Recent Progress Logs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {isLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : measurements && measurements.length > 0 ? measurements.filter(m => m.weight !== null || m.bodyFat !== null).slice(0, 5).map((log, index) => {
                    // Find previous log to calculate difference
                    const prevLog = index < measurements.length - 1 ?
                      measurements.filter(m => m.weight !== null || m.bodyFat !== null)[index + 1] : null;
                    const weightDiff = prevLog && log.weight && prevLog.weight ?
                      (log.weight - prevLog.weight).toFixed(1) : null;

                    return (
                      <div key={log.id} className="flex items-center justify-between p-2 rounded-md bg-background/60 border border-primary/10">
                        <div className="flex items-center gap-2">
                          <Scale className="h-4 w-4 text-blue-400" />
                          <span>{new Date(log.date).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span className="font-medium flex items-center gap-2">
                            <span>{log.weight ? `${log.weight} lbs` : ""}{log.bodyFat ? ` / ${log.bodyFat}%` : ""}</span>
                            {weightDiff && (
                              <span className={`text-xs ${parseFloat(weightDiff) < 0 ? "text-green-500" : parseFloat(weightDiff) > 0 ? "text-red-500" : "text-gray-500"}`}>
                                ({weightDiff > 0 ? "+" : ""}{weightDiff})
                              </span>
                            )}
                          </span>
                          <div className="flex">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => {
                                setEditingLog(log)
                                setEditingCategory("progress")
                                setWeight(log.weight || 0)
                                setBodyFat(log.bodyFat || 0)
                                setWaist(log.waist || 0)
                                setChest(log.chest || 0)
                                setIsEditDialogOpen(true)
                              }}
                            >
                              <Pencil className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-destructive"
                              onClick={() => deleteMeasurement(log.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  }) : (
                    <div className="p-4 text-center text-muted-foreground">
                      <p>No progress logs found. Log your first measurements!</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="premium-card shadow-sm border border-primary/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Progress Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 rounded-md bg-blue-50 border border-blue-100 dark:bg-blue-950/30 dark:border-blue-900/50">
                    <div>
                      <div className="font-medium text-blue-800 dark:text-blue-300">Weight</div>
                      <p className="text-sm text-blue-700 dark:text-blue-400">Down 2.3 lbs this month</p>
                    </div>
                    <div className="p-2 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300">
                      <TrendingDown className="h-5 w-5" />
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 rounded-md bg-green-50 border border-green-100 dark:bg-green-950/30 dark:border-green-900/50">
                    <div>
                      <div className="font-medium text-green-800 dark:text-green-300">Body Fat</div>
                      <p className="text-sm text-green-700 dark:text-green-400">Down 1.2% this month</p>
                    </div>
                    <div className="p-2 rounded-full bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300">
                      <TrendingDown className="h-5 w-5" />
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 rounded-md bg-indigo-50 border border-indigo-100 dark:bg-indigo-950/30 dark:border-indigo-900/50">
                    <div>
                      <div className="font-medium text-indigo-800 dark:text-indigo-300">Chest</div>
                      <p className="text-sm text-indigo-700 dark:text-indigo-400">Up 0.5 inches this month</p>
                    </div>
                    <div className="p-2 rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-300">
                      <TrendingUp className="h-5 w-5" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Edit {editingCategory.charAt(0).toUpperCase() + editingCategory.slice(1)} Log
            </DialogTitle>
            <DialogDescription>
              Update your {editingCategory} log details below.
            </DialogDescription>
          </DialogHeader>

          {editingCategory === "sleep" && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-sleep-hours">Hours slept</Label>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-10 w-10"
                    onClick={() => setSleepHours(Math.max(0, sleepHours - 0.5))}
                    type="button"
                  >
                    -
                  </Button>
                  <Input
                    id="edit-sleep-hours"
                    type="number"
                    step="0.1"
                    value={sleepHours}
                    onChange={(e) => setSleepHours(parseFloat(e.target.value) || 0)}
                    className="text-center"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-10 w-10"
                    onClick={() => setSleepHours(sleepHours + 0.5)}
                    type="button"
                  >
                    +
                  </Button>
                  <span>hours</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Sleep Quality</Label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant={sleepQuality === "poor" ? "default" : "outline"}
                    onClick={() => setSleepQuality("poor")}
                    className="flex-1"
                  >
                    <Frown className="mr-2 h-4 w-4 text-red-500" />
                    Poor
                  </Button>
                  <Button
                    type="button"
                    variant={sleepQuality === "fair" ? "default" : "outline"}
                    onClick={() => setSleepQuality("fair")}
                    className="flex-1"
                  >
                    <Meh className="mr-2 h-4 w-4 text-amber-500" />
                    Fair
                  </Button>
                  <Button
                    type="button"
                    variant={sleepQuality === "good" ? "default" : "outline"}
                    onClick={() => setSleepQuality("good")}
                    className="flex-1"
                  >
                    <Smile className="mr-2 h-4 w-4 text-green-500" />
                    Good
                  </Button>
                </div>
              </div>
            </div>
          )}

          {editingCategory === "stress" && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-stress-level">Stress Level (1-10)</Label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant={stressLevel <= 3 ? "default" : "outline"}
                    onClick={() => setStressLevel(2)}
                    className="flex-1"
                  >
                    <Smile className="mr-2 h-4 w-4 text-green-500" />
                    Low
                  </Button>
                  <Button
                    type="button"
                    variant={stressLevel > 3 && stressLevel <= 7 ? "default" : "outline"}
                    onClick={() => setStressLevel(5)}
                    className="flex-1"
                  >
                    <Meh className="mr-2 h-4 w-4 text-amber-500" />
                    Medium
                  </Button>
                  <Button
                    type="button"
                    variant={stressLevel > 7 ? "default" : "outline"}
                    onClick={() => setStressLevel(8)}
                    className="flex-1"
                  >
                    <Frown className="mr-2 h-4 w-4 text-red-500" />
                    High
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Stress Factors</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    type="button"
                    variant={stressFactors?.includes("work") ? "default" : "outline"}
                    onClick={() => {
                      const newFactors = stressFactors?.includes("work")
                        ? stressFactors.filter(f => f !== "work")
                        : [...(stressFactors || []), "work"];
                      setStressFactors(newFactors);
                    }}
                    className="justify-start h-auto py-2"
                  >
                    <span className="mr-2">🏢</span>
                    Work
                  </Button>
                  <Button
                    type="button"
                    variant={stressFactors?.includes("family") ? "default" : "outline"}
                    onClick={() => {
                      const newFactors = stressFactors?.includes("family")
                        ? stressFactors.filter(f => f !== "family")
                        : [...(stressFactors || []), "family"];
                      setStressFactors(newFactors);
                    }}
                    className="justify-start h-auto py-2"
                  >
                    <span className="mr-2">👪</span>
                    Family
                  </Button>
                  <Button
                    type="button"
                    variant={stressFactors?.includes("financial") ? "default" : "outline"}
                    onClick={() => {
                      const newFactors = stressFactors?.includes("financial")
                        ? stressFactors.filter(f => f !== "financial")
                        : [...(stressFactors || []), "financial"];
                      setStressFactors(newFactors);
                    }}
                    className="justify-start h-auto py-2"
                  >
                    <span className="mr-2">💰</span>
                    Financial
                  </Button>
                  <Button
                    type="button"
                    variant={stressFactors?.includes("other") ? "default" : "outline"}
                    onClick={() => {
                      const newFactors = stressFactors?.includes("other")
                        ? stressFactors.filter(f => f !== "other")
                        : [...(stressFactors || []), "other"];
                      setStressFactors(newFactors);
                    }}
                    className="justify-start h-auto py-2"
                  >
                    <span className="mr-2">➕</span>
                    Other
                  </Button>
                </div>
              </div>
            </div>
          )}

          {editingCategory === "coffee" && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-coffee-cups">Cups of coffee</Label>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-10 w-10"
                    onClick={() => setCoffeeCount(Math.max(0, coffeeCount - 1))}
                    type="button"
                  >
                    -
                  </Button>
                  <Input
                    id="edit-coffee-cups"
                    type="number"
                    value={coffeeCount}
                    onChange={(e) => setCoffeeCount(parseInt(e.target.value) || 0)}
                    className="text-center"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-10 w-10"
                    onClick={() => setCoffeeCount(coffeeCount + 1)}
                    type="button"
                  >
                    +
                  </Button>
                  <span>cups</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Coffee Type</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    type="button"
                    variant={coffeeType === "espresso" ? "default" : "outline"}
                    onClick={() => setCoffeeType("espresso")}
                    className="justify-start py-2"
                  >
                    <span className="mr-2">☕</span>
                    Espresso
                  </Button>
                  <Button
                    type="button"
                    variant={coffeeType === "americano" ? "default" : "outline"}
                    onClick={() => setCoffeeType("americano")}
                    className="justify-start py-2"
                  >
                    <span className="mr-2">🍵</span>
                    Americano
                  </Button>
                  <Button
                    type="button"
                    variant={coffeeType === "latte" ? "default" : "outline"}
                    onClick={() => setCoffeeType("latte")}
                    className="justify-start py-2"
                  >
                    <span className="mr-2">🫖</span>
                    Latte
                  </Button>
                  <Button
                    type="button"
                    variant={coffeeType === "other" ? "default" : "outline"}
                    onClick={() => setCoffeeType("other")}
                    className="justify-start py-2"
                  >
                    <span className="mr-2">☕</span>
                    Other
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Time of Day</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    type="button"
                    variant={timeOfDay === "morning" ? "default" : "outline"}
                    onClick={() => setTimeOfDay("morning")}
                    className="justify-start py-2"
                  >
                    <span className="mr-2">🌅</span>
                    Morning
                  </Button>
                  <Button
                    type="button"
                    variant={timeOfDay === "afternoon" ? "default" : "outline"}
                    onClick={() => setTimeOfDay("afternoon")}
                    className="justify-start py-2"
                  >
                    <span className="mr-2">🌞</span>
                    Afternoon
                  </Button>
                </div>
              </div>
            </div>
          )}

          {editingCategory === "nutrition" && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-meal-date">Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {editingLog ? format(new Date(editingLog.date), "PPP") : format(selectedDate, "PPP")}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={editingLog ? new Date(editingLog.date) : selectedDate}
                      onSelect={(date) => {
                        if (date && editingLog) {
                          // Create a new object with the updated date
                          setEditingLog({
                            ...editingLog,
                            date: date.toISOString()
                          })
                        }
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-meal-type">Meal Type</Label>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    type="button"
                    variant={mealType === "breakfast" ? "default" : "outline"}
                    onClick={() => setMealType("breakfast")}
                  >
                    Breakfast
                  </Button>
                  <Button
                    type="button"
                    variant={mealType === "lunch" ? "default" : "outline"}
                    onClick={() => setMealType("lunch")}
                  >
                    Lunch
                  </Button>
                  <Button
                    type="button"
                    variant={mealType === "dinner" ? "default" : "outline"}
                    onClick={() => setMealType("dinner")}
                  >
                    Dinner
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-meal-name">Meal Name</Label>
                <Input
                  id="edit-meal-name"
                  value={mealName}
                  onChange={(e) => setMealName(e.target.value)}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-protein">Protein (g)</Label>
                  <Input
                    id="edit-protein"
                    type="number"
                    value={protein}
                    onChange={(e) => setProtein(parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-carbs">Carbs (g)</Label>
                  <Input
                    id="edit-carbs"
                    type="number"
                    value={carbs}
                    onChange={(e) => setCarbs(parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-fat">Fat (g)</Label>
                  <Input
                    id="edit-fat"
                    type="number"
                    value={fat}
                    onChange={(e) => setFat(parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-calories">Calories</Label>
                  <Input
                    id="edit-calories"
                    type="number"
                    value={calories || (protein * 4 + carbs * 4 + fat * 9)}
                    readOnly
                    disabled
                  />
                </div>
              </div>
            </div>
          )}

          {editingCategory === "progress" && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-weight">Weight (lbs)</Label>
                  <Input
                    id="edit-weight"
                    type="number"
                    step="0.1"
                    value={weight}
                    onChange={(e) => setWeight(parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-body-fat">Body Fat (%)</Label>
                  <Input
                    id="edit-body-fat"
                    type="number"
                    step="0.1"
                    value={bodyFat}
                    onChange={(e) => setBodyFat(parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-waist">Waist (in)</Label>
                  <Input
                    id="edit-waist"
                    type="number"
                    step="0.1"
                    value={waist}
                    onChange={(e) => setWaist(parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-chest">Chest (in)</Label>
                  <Input
                    id="edit-chest"
                    type="number"
                    step="0.1"
                    value={chest}
                    onChange={(e) => setChest(parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={async () => {
                try {
                  setIsLoading(true)

                  if (editingLog) {
                    if (editingCategory === "nutrition") {
                      await editNutritionLog(editingLog.id, {
                        date: editingLog.date, // Use the date from the editingLog object (which may have been updated)
                        mealType,
                        name: mealName || `${mealType.charAt(0).toUpperCase() + mealType.slice(1)} meal`,
                        calories: calories || (protein * 4 + carbs * 4 + fat * 9),
                        protein,
                        carbs,
                        fat
                      })
                    } else {
                      // For all measurement-based logs
                      const data: any = { weight }

                      if (editingCategory === "sleep") {
                        data.sleepHours = sleepHours
                        data.notes = `Sleep logged: ${sleepHours} hours, Quality: ${sleepQuality}`
                      } else if (editingCategory === "stress") {
                        data.stressLevel = stressLevel
                        data.notes = `Stress logged: Level ${stressLevel}, Factors: ${stressFactors.join(', ')}`
                        data.stressFactors = stressFactors.length > 0 ? JSON.stringify(stressFactors) : null
                      } else if (editingCategory === "coffee") {
                        data.coffeeCount = coffeeCount
                        data.notes = `Coffee logged: ${coffeeCount} cups, Type: ${coffeeType}, Time: ${timeOfDay}`
                        data.coffeeType = coffeeType
                        data.timeOfDay = timeOfDay
                      } else if (editingCategory === "progress") {
                        data.bodyFat = bodyFat
                        data.measurements = {
                          waist,
                          chest
                        }
                      }

                      await editMeasurement(editingLog.id, data)
                    }

                    setIsEditDialogOpen(false)
                  }
                } catch (error) {
                  console.error("Error updating log:", error)
                  toast({
                    title: "Error",
                    description: "Failed to update log. Please try again.",
                    variant: "destructive",
                  })
                } finally {
                  setIsLoading(false)
                }
              }}
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* History Modal */}
      <Dialog open={isHistoryModalOpen} onOpenChange={setIsHistoryModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {historyCategory.charAt(0).toUpperCase() + historyCategory.slice(1)} History
            </DialogTitle>
            <DialogDescription>
              View your complete {historyCategory} history below.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {historyCategory === "sleep" && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  {isLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : measurements && measurements.length > 0 ? measurements.filter(m => m.sleepHours !== null && m.sleepHours !== undefined).map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 rounded-md bg-background/60 border border-primary/10">
                      <div className="flex items-center gap-3">
                        <Moon className="h-5 w-5 text-indigo-400" />
                        <div>
                          <div className="font-medium">{new Date(log.date).toLocaleDateString()}</div>
                          <div className="text-sm text-muted-foreground">
                            {log.notes ? log.notes.replace(/Sleep logged: |, Quality: /g, '') : `${log.sleepHours} hours`}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="font-medium text-lg">{log.sleepHours} hrs</span>
                        <div className="flex">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              setEditingLog(log)
                              setEditingCategory("sleep")
                              setSleepHours(log.sleepHours)
                              // Set sleep quality from notes or default to "good"
                              if (log.notes && log.notes.includes("Quality:")) {
                                const qualityMatch = log.notes.match(/Quality: (\w+)/)
                                if (qualityMatch && qualityMatch[1]) {
                                  setSleepQuality(qualityMatch[1].toLowerCase())
                                } else {
                                  setSleepQuality("good")
                                }
                              } else {
                                setSleepQuality("good")
                              }
                              setIsHistoryModalOpen(false)
                              setIsEditDialogOpen(true)
                            }}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive"
                            onClick={() => {
                              deleteMeasurement(log.id)
                              // Keep the modal open after deletion
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )) : (
                    <div className="p-4 text-center text-muted-foreground">
                      <p>No sleep logs found.</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {historyCategory === "stress" && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  {isLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : measurements && measurements.length > 0 ? measurements.filter(m => m.stressLevel !== null && m.stressLevel !== undefined).map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 rounded-md bg-background/60 border border-primary/10">
                      <div className="flex items-center gap-3">
                        <Zap className="h-5 w-5 text-amber-500" />
                        <div>
                          <div className="font-medium">{new Date(log.date).toLocaleDateString()}</div>
                          <div className="text-sm text-muted-foreground">
                            {log.notes ? log.notes.replace(/Stress logged: Level |, Factors: /g, '') : `Level ${log.stressLevel}`}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="font-medium text-lg">
                          {log.stressLevel <= 3 ? (
                            <span className="flex items-center text-green-500"><Smile className="h-5 w-5 mr-1" /> Low</span>
                          ) : log.stressLevel <= 7 ? (
                            <span className="flex items-center text-amber-500"><Meh className="h-5 w-5 mr-1" /> Medium</span>
                          ) : (
                            <span className="flex items-center text-red-500"><Frown className="h-5 w-5 mr-1" /> High</span>
                          )}
                        </span>
                        <div className="flex">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              setEditingLog(log)
                              setEditingCategory("stress")
                              setStressLevel(log.stressLevel)

                              // Set stress factors from notes or default to empty array
                              if (log.stressFactors) {
                                try {
                                  setStressFactors(JSON.parse(log.stressFactors))
                                } catch {
                                  setStressFactors([])
                                }
                              } else {
                                setStressFactors([])
                              }

                              setIsHistoryModalOpen(false)
                              setIsEditDialogOpen(true)
                            }}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive"
                            onClick={() => {
                              deleteMeasurement(log.id)
                              // Keep the modal open after deletion
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )) : (
                    <div className="p-4 text-center text-muted-foreground">
                      <p>No stress logs found.</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {historyCategory === "coffee" && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  {isLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : measurements && measurements.length > 0 ? measurements.filter(m => m.coffeeCount !== null && m.coffeeCount !== undefined).map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 rounded-md bg-background/60 border border-primary/10">
                      <div className="flex items-center gap-3">
                        <Coffee className="h-5 w-5 text-amber-700" />
                        <div>
                          <div className="font-medium">{new Date(log.date).toLocaleDateString()}</div>
                          <div className="text-sm text-muted-foreground">
                            {log.notes ? log.notes.replace(/Coffee logged: |, Type: |, Time: /g, '') : `${log.coffeeCount} cups`}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="font-medium text-lg">{log.coffeeCount} {log.coffeeCount === 1 ? 'cup' : 'cups'}</span>
                        <div className="flex">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              setEditingLog(log)
                              setEditingCategory("coffee")
                              setCoffeeCount(log.coffeeCount)

                              // Set coffee type and time from notes or default values
                              if (log.notes) {
                                // Extract coffee type
                                if (log.notes.includes("Type:")) {
                                  const typeMatch = log.notes.match(/Type: (\w+)/)
                                  if (typeMatch && typeMatch[1]) {
                                    setCoffeeType(typeMatch[1].toLowerCase())
                                  } else {
                                    setCoffeeType("espresso")
                                  }
                                } else {
                                  setCoffeeType("espresso")
                                }

                                // Extract time of day
                                if (log.notes.includes("Time:")) {
                                  const timeMatch = log.notes.match(/Time: (\w+)/)
                                  if (timeMatch && timeMatch[1]) {
                                    setTimeOfDay(timeMatch[1].toLowerCase())
                                  } else {
                                    setTimeOfDay("morning")
                                  }
                                } else {
                                  setTimeOfDay("morning")
                                }
                              } else {
                                setCoffeeType("espresso")
                                setTimeOfDay("morning")
                              }

                              setIsHistoryModalOpen(false)
                              setIsEditDialogOpen(true)
                            }}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive"
                            onClick={() => {
                              deleteMeasurement(log.id)
                              // Keep the modal open after deletion
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )) : (
                    <div className="p-4 text-center text-muted-foreground">
                      <p>No coffee logs found.</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {historyCategory === "nutrition" && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  {isLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : nutritionLogs && nutritionLogs.length > 0 ? nutritionLogs.map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 rounded-md bg-background/60 border border-primary/10">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${log.mealType === 'breakfast' ? 'bg-amber-500' : log.mealType === 'lunch' ? 'bg-green-500' : 'bg-indigo-500'}`}></div>
                        <div>
                          <div className="font-medium">{new Date(log.date).toLocaleDateString()}</div>
                          <div className="text-sm text-muted-foreground">
                            {log.mealType.charAt(0).toUpperCase() + log.mealType.slice(1)}: {log.name}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-sm grid grid-cols-3 gap-2">
                          <span className="text-blue-600">P: {log.protein}g</span>
                          <span className="text-amber-600">C: {log.carbs}g</span>
                          <span className="text-red-600">F: {log.fat}g</span>
                        </div>
                        <span className="font-medium">{log.calories} cal</span>
                        <div className="flex">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              setEditingLog(log)
                              setEditingCategory("nutrition")
                              setMealType(log.mealType)
                              setMealName(log.name)
                              setProtein(log.protein || 0)
                              setCarbs(log.carbs || 0)
                              setFat(log.fat || 0)
                              setCalories(log.calories || 0)
                              setIsHistoryModalOpen(false)
                              setIsEditDialogOpen(true)
                            }}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive"
                            onClick={() => {
                              deleteNutritionLog(log.id)
                              // Keep the modal open after deletion
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )) : (
                    <div className="p-4 text-center text-muted-foreground">
                      <p>No nutrition logs found.</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {historyCategory === "progress" && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  {isLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : measurements && measurements.length > 0 ? measurements.filter(m => m.weight !== null || m.bodyFat !== null || m.waist !== null || m.chest !== null).map((log, index) => {
                    // Find previous log to calculate difference
                    const prevLog = index < measurements.length - 1 ?
                      measurements.filter(m => m.weight !== null || m.bodyFat !== null)[index + 1] : null;
                    const weightDiff = prevLog && log.weight && prevLog.weight ?
                      (log.weight - prevLog.weight).toFixed(1) : null;

                    return (
                      <div key={log.id} className="flex items-center justify-between p-3 rounded-md bg-background/60 border border-primary/10">
                        <div className="flex items-center gap-3">
                          <Scale className="h-5 w-5 text-blue-500" />
                          <div>
                            <div className="font-medium">{new Date(log.date).toLocaleDateString()}</div>
                            <div className="text-sm text-muted-foreground">
                              {log.weight ? `Weight: ${log.weight} lbs` : ""}
                              {log.bodyFat ? ` / Body Fat: ${log.bodyFat}%` : ""}
                              {log.waist ? ` / Waist: ${log.waist} in` : ""}
                              {log.chest ? ` / Chest: ${log.chest} in` : ""}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <span className="font-medium flex items-center gap-2">
                            {log.weight && (
                              <span>{log.weight} lbs</span>
                            )}
                            {weightDiff && (
                              <span className={`text-xs ${parseFloat(weightDiff) < 0 ? "text-green-500" : parseFloat(weightDiff) > 0 ? "text-red-500" : "text-gray-500"}`}>
                                ({weightDiff > 0 ? "+" : ""}{weightDiff})
                              </span>
                            )}
                          </span>
                          <div className="flex">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => {
                                setEditingLog(log)
                                setEditingCategory("progress")
                                setWeight(log.weight || 0)
                                setBodyFat(log.bodyFat || 0)
                                setWaist(log.waist || 0)
                                setChest(log.chest || 0)
                                setIsHistoryModalOpen(false)
                                setIsEditDialogOpen(true)
                              }}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-destructive"
                              onClick={() => {
                                deleteMeasurement(log.id)
                                // Keep the modal open after deletion
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  }) : (
                    <div className="p-4 text-center text-muted-foreground">
                      <p>No progress logs found.</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsHistoryModalOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
