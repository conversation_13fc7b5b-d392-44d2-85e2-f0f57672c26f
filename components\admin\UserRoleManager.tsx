"use client";

import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Loader2, Search } from "lucide-react";
import { debounce } from "lodash";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";

// Schema for creating a new user (admin or trainer)
const createUserSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Valid email is required"),
  role: z.enum(["admin", "trainer"]),
});

// Schema for updating an existing user's role
const updateRoleSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  role: z.enum(["admin", "trainer", "client"]),
});

type CreateUserFormValues = z.infer<typeof createUserSchema>;
type UpdateRoleFormValues = z.infer<typeof updateRoleSchema>;

type UserSearchResult = {
  id: string;
  name: string;
  email: string;
  role: string;
};

interface UserRoleManagerProps {
  onRoleUpdated?: () => void;
}

export function UserRoleManager({ onRoleUpdated }: UserRoleManagerProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserSearchResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Form for creating a new user
  const createForm = useForm<CreateUserFormValues>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      name: "",
      email: "",
      role: "trainer",
    },
  });

  // Form for updating an existing user's role
  const updateForm = useForm<UpdateRoleFormValues>({
    resolver: zodResolver(updateRoleSchema),
    defaultValues: {
      userId: "",
      role: "trainer",
    },
  });

  // Handle creating a new user
  const handleCreateUser = async (data: CreateUserFormValues) => {
    setIsCreating(true);
    setError(null);
    try {
      const response = await fetch("/api/users/role", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create user");
      }

      const result = await response.json();
      toast({
        title: "User Created",
        description: `${data.role === "admin" ? "Admin" : "Trainer"} created. They will need to set their password on first login.`,
      });
      createForm.reset();
      router.refresh();
      if (onRoleUpdated) {
        onRoleUpdated();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsCreating(false);
    }
  };

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      try {
        // Search for users, prioritizing clients
        const response = await fetch(`/api/users/search?q=${encodeURIComponent(query)}`);
        if (!response.ok) {
          throw new Error("Failed to search users");
        }

        const results = await response.json();
        setSearchResults(results);
      } catch (error) {
        toast({
          variant: "destructive",
          title: "Search Error",
          description: "Failed to search for users",
        });
      } finally {
        setIsSearching(false);
      }
    }, 300),
    [toast]
  );

  // Handle searching for users
  const handleSearch = useCallback(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    debouncedSearch(searchQuery);
  }, [searchQuery, debouncedSearch]);

  // Handle selecting a user from search results
  const handleSelectUser = (user: UserSearchResult) => {
    setSelectedUser(user);
    updateForm.setValue("userId", user.id);
    // Default to trainer role, but don't change if the form already has a selection
    if (!updateForm.getValues("role")) {
      updateForm.setValue("role", "trainer");
    }
  };

  // Handle updating a user's role
  const handleUpdateRole = async (data: UpdateRoleFormValues) => {
    setIsUpdating(true);
    setError(null);
    try {
      const response = await fetch("/api/users/role", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update user role");
      }

      const result = await response.json();
      toast({
        title: "Role Updated",
        description: `User has been updated to ${data.role}`,
      });
      setSelectedUser(null);
      setSearchResults([]);
      setSearchQuery("");
      updateForm.reset();
      router.refresh();
      if (onRoleUpdated) {
        onRoleUpdated();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      setError(errorMessage);
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>User Role Management</CardTitle>
        <CardDescription>
          Create new admin/trainer accounts or convert existing users to trainers.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <Tabs defaultValue="create">
          <TabsList className="mb-4 grid w-full grid-cols-2">
            <TabsTrigger value="create">Create New Account</TabsTrigger>
            <TabsTrigger value="convert">Convert Existing User</TabsTrigger>
          </TabsList>

          {/* Create New User Form */}
          <TabsContent value="create">
            <Form {...createForm}>
              <form onSubmit={createForm.handleSubmit(handleCreateUser)} className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Create a new Trainer or Administrator account. The user will be required to set their own password upon first login.
                </p>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={createForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input placeholder="John Doe" {...field} disabled={isCreating} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={createForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="<EMAIL>" {...field} disabled={isCreating} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={createForm.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Role</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={isCreating}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="trainer">Trainer</SelectItem>
                            <SelectItem value="admin">Administrator</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Button type="submit" disabled={isCreating} className="mt-2">
                  {isCreating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isCreating ? "Creating Account..." : "Create Account"}
                </Button>
              </form>
            </Form>
          </TabsContent>

          {/* Convert Existing User Form */}
          <TabsContent value="convert">
            <div className="space-y-4">
              {/* User Search Form - using a separate Form */}
              <Form {...updateForm}>
                <div className="space-y-2">
                  <Label htmlFor="user-search">Search User by Email or Name</Label>
                  <p className="text-xs text-muted-foreground mb-2">
                    This will search all users in the database. Results show actual users from the database.
                  </p>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        id="user-search"
                        type="text"
                        placeholder="Search..."
                        className="pl-8"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                        disabled={isSearching}
                      />
                    </div>
                    <Button type="button" onClick={handleSearch} disabled={isSearching || !searchQuery.trim()}>
                      {isSearching ? <Loader2 className="h-4 w-4 animate-spin" /> : "Search"}
                    </Button>
                  </div>
                </div>
              </Form>

              {/* Search Results */}
              {isSearching && <div className="text-center p-4"><Loader2 className="mx-auto h-6 w-6 animate-spin text-muted-foreground" /></div>}
              {!isSearching && searchResults.length > 0 && (
                <div className="mt-4 border rounded-md">
                  <div className="p-2 bg-muted text-sm font-medium">Search Results ({searchResults.length})</div>
                  <div className="divide-y max-h-60 overflow-y-auto">
                    {searchResults.map((user) => (
                      <div
                        key={user.id}
                        className={`flex items-center justify-between p-3 cursor-pointer hover:bg-muted/50 ${
                          selectedUser?.id === user.id ? "bg-muted" : ""
                        }`}
                        onClick={() => handleSelectUser(user)}
                      >
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-muted-foreground">{user.email}</div>
                        </div>
                        <Badge variant="secondary" className="capitalize text-xs font-normal">{user.role}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              {!isSearching && searchQuery && searchResults.length === 0 && (
                <p className="text-sm text-muted-foreground text-center mt-4">No users found matching "{searchQuery}".</p>
              )}

              {/* Update Role Form */}
              {selectedUser && (
                <Form {...updateForm}>
                  <form onSubmit={updateForm.handleSubmit(handleUpdateRole)} className="space-y-4 mt-6 border rounded-md p-4 bg-background">
                    <div className="space-y-1">
                      <h3 className="font-medium">Update Role for: <span className="text-primary">{selectedUser.name}</span></h3>
                      <p className="text-sm text-muted-foreground">Current role: <Badge variant="outline" className="capitalize text-xs font-normal">{selectedUser.role}</Badge></p>
                      {selectedUser.role === "client" && (
                        <Alert variant="default" className="mt-2 bg-amber-50 border-amber-200 text-amber-800 dark:bg-amber-900/30 dark:border-amber-800/50 dark:text-amber-300">
                          <AlertDescription className="text-xs">⚠️ Converting a client to a trainer will grant them access to trainer-specific features and dashboards.</AlertDescription>
                        </Alert>
                      )}
                      {selectedUser.role !== "client" && (
                        <Alert variant="default" className="mt-2">
                          <AlertDescription className="text-xs">You can change this user to a Trainer or Administrator.</AlertDescription>
                        </Alert>
                      )}
                    </div>

                    <FormField
                      control={updateForm.control}
                      name="role"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>New Role</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            disabled={isUpdating}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select new role" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="client">Client</SelectItem>
                              <SelectItem value="trainer">Trainer</SelectItem>
                              <SelectItem value="admin">Administrator</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex gap-2 pt-2">
                      <Button type="submit" disabled={isUpdating}>
                        {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        {isUpdating ? "Updating..." : "Update Role"}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setSelectedUser(null);
                          updateForm.reset();
                          setSearchQuery("");
                          setSearchResults([]);
                        }}
                        disabled={isUpdating}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </Form>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
} 