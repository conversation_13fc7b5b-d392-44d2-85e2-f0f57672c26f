"use client";

import { useEffect, useState, useRef } from "react";
import { useSession } from "next-auth/react";
import { formatDistanceToNow } from "date-fns";
import { 
  MessageSquare, 
  Search, 
  Phone, 
  MoreVertical, 
  Send, 
  User, 
  PlusCircle,
  UserPlus,
  Check,
  Image as ImageIcon
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { LoadingSpinner } from "@/components/loading-spinner";
import { getInitials } from "@/lib/utils";

// Import react-chat-elements
import 'react-chat-elements/dist/main.css';
import { 
  MessageList, 
  MessageBox, 
  ChatList, 
  ChatItem,
  Input as ChatInput,
  <PERSON><PERSON> as Chat<PERSON>utton
} from 'react-chat-elements';

type User = {
  id: string;
  name: string;
  email: string;
  avatarUrl: string;
  role: string;
};

type Message = {
  id: string;
  content: string;
  createdAt: string;
  senderId: string;
  conversationId: string;
  read: boolean;
};

type Conversation = {
  id: string;
  user: User;
  lastMessage: Message | null;
  unreadCount: number;
};

export default function ChatsPage() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true);
  const [loadingClients, setLoadingClients] = useState(true);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [clients, setClients] = useState<User[]>([]);
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [newMessage, setNewMessage] = useState("");
  const [activeTab, setActiveTab] = useState("general");
  const [searchQuery, setSearchQuery] = useState("");
  const [isNewChatDialogOpen, setIsNewChatDialogOpen] = useState(false);
  const [creatingConversation, setCreatingConversation] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter conversations based on search query
  const filteredConversations = conversations.filter((conv) =>
    conv.user.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Filter clients based on search query and exclude those who already have conversations
  const filteredClients = clients.filter(
    (client) =>
      client.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !conversations.some((conv) => conv.user.id === client.id)
  );

  // Fetch conversations
  useEffect(() => {
    const fetchConversations = async () => {
      try {
        setLoading(true);
        // Mock data for now
        const mockConversations: Conversation[] = [
          {
            id: "1",
            user: {
              id: "101",
              name: "John Smith",
              email: "<EMAIL>",
              avatarUrl: "https://ui-avatars.com/api/?name=John+Smith",
              role: "client",
            },
            lastMessage: {
              id: "m1",
              content: "Hey, how's your training going?",
              createdAt: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
              senderId: "101",
              conversationId: "1",
              read: false,
            },
            unreadCount: 2,
          },
          {
            id: "2",
            user: {
              id: "102",
              name: "Sarah Johnson",
              email: "<EMAIL>",
              avatarUrl: "https://ui-avatars.com/api/?name=Sarah+Johnson",
              role: "client",
            },
            lastMessage: {
              id: "m2",
              content: "I completed the workout you sent!",
              createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
              senderId: "102",
              conversationId: "2",
              read: true,
            },
            unreadCount: 0,
          },
        ];
        
        setConversations(mockConversations);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching conversations:", error);
        setLoading(false);
      }
    };

    fetchConversations();
  }, []);

  // Fetch clients
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setLoadingClients(true);
        // Mock data for now
        const mockClients: User[] = [
          {
            id: "101",
            name: "John Smith",
            email: "<EMAIL>",
            avatarUrl: "https://ui-avatars.com/api/?name=John+Smith",
            role: "client",
          },
          {
            id: "102",
            name: "Sarah Johnson",
            email: "<EMAIL>",
            avatarUrl: "https://ui-avatars.com/api/?name=Sarah+Johnson",
            role: "client",
          },
          {
            id: "103",
            name: "Michael Brown",
            email: "<EMAIL>",
            avatarUrl: "https://ui-avatars.com/api/?name=Michael+Brown",
            role: "client",
          },
          {
            id: "104",
            name: "Emily Davis",
            email: "<EMAIL>",
            avatarUrl: "https://ui-avatars.com/api/?name=Emily+Davis",
            role: "client",
          },
        ];
        
        setClients(mockClients);
        setLoadingClients(false);
      } catch (error) {
        console.error("Error fetching clients:", error);
        setLoadingClients(false);
      }
    };

    fetchClients();
  }, []);

  // Fetch messages for active conversation
  useEffect(() => {
    const fetchMessages = async () => {
      if (!activeConversation) return;

      try {
        setLoadingMessages(true);
        // Mock data for now
        const mockMessages: Message[] = [
          {
            id: "m1",
            content: "Hey, how's your training going?",
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
            senderId: activeConversation.user.id,
            conversationId: activeConversation.id,
            read: true,
          },
          {
            id: "m2",
            content: "It's going well! I've been following the plan you gave me.",
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 23).toISOString(),
            senderId: session?.user?.id || "",
            conversationId: activeConversation.id,
            read: true,
          },
          {
            id: "m3",
            content: "Great! Have you noticed any improvements?",
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 22).toISOString(),
            senderId: activeConversation.user.id,
            conversationId: activeConversation.id,
            read: true,
          },
          {
            id: "m4",
            content: "Yes, I feel much stronger and have more energy throughout the day.",
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 21).toISOString(),
            senderId: session?.user?.id || "",
            conversationId: activeConversation.id,
            read: true,
          },
          {
            id: "m5",
            content: "That's awesome! Keep up the good work.",
            createdAt: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
            senderId: activeConversation.user.id,
            conversationId: activeConversation.id,
            read: false,
          },
        ];
        
        setMessages(mockMessages);
        setLoadingMessages(false);
        
        // Mark conversation as read
        setConversations((prevConversations) =>
          prevConversations.map((conv) =>
            conv.id === activeConversation.id
              ? { ...conv, unreadCount: 0 }
              : conv
          )
        );
      } catch (error) {
        console.error("Error fetching messages:", error);
        setLoadingMessages(false);
      }
    };

    fetchMessages();
  }, [activeConversation, session?.user?.id]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  const selectConversation = (conversation: Conversation) => {
    setActiveConversation(conversation);
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() || !activeConversation) return;

    const newMsg: Message = {
      id: `new-${Date.now()}`,
      content: newMessage,
      createdAt: new Date().toISOString(),
      senderId: session?.user?.id || "",
      conversationId: activeConversation.id,
      read: false,
    };

    setMessages((prev) => [...prev, newMsg]);
    setNewMessage("");

    // Update last message in conversation
    setConversations((prevConversations) =>
      prevConversations.map((conv) =>
        conv.id === activeConversation.id
          ? { ...conv, lastMessage: newMsg }
          : conv
      )
    );
  };

  const createConversation = async (clientId: string) => {
    try {
      setCreatingConversation(true);
      
      // Find the client
      const client = clients.find((c) => c.id === clientId);
      if (!client) return;
      
      // Check if conversation already exists
      const existingConv = conversations.find((conv) => conv.user.id === clientId);
      if (existingConv) {
        setActiveConversation(existingConv);
        setIsNewChatDialogOpen(false);
        setCreatingConversation(false);
        return;
      }
      
      // Create new conversation (mock)
      const newConversation: Conversation = {
        id: `new-${Date.now()}`,
        user: client,
        lastMessage: null,
        unreadCount: 0,
      };
      
      setConversations((prev) => [...prev, newConversation]);
      setActiveConversation(newConversation);
      setIsNewChatDialogOpen(false);
      setCreatingConversation(false);
    } catch (error) {
      console.error("Error creating conversation:", error);
      setCreatingConversation(false);
    }
  };

  // Convert our messages to the format expected by react-chat-elements
  const chatMessages = messages.map((msg) => ({
    position: msg.senderId === session?.user?.id ? 'right' : 'left',
    type: 'text',
    title: msg.senderId === session?.user?.id ? session?.user?.name : activeConversation?.user.name,
    text: msg.content,
    date: new Date(msg.createdAt),
    avatar: msg.senderId === session?.user?.id 
      ? session?.user?.image || `https://ui-avatars.com/api/?name=${session?.user?.name}`
      : activeConversation?.user.avatarUrl
  }));

  // Convert our conversations to the format expected by react-chat-elements
  const chatItems = conversations.map((conv) => ({
    id: conv.id,
    avatar: conv.user.avatarUrl,
    alt: conv.user.name,
    title: conv.user.name,
    subtitle: conv.lastMessage?.content || 'Start a conversation',
    date: conv.lastMessage ? new Date(conv.lastMessage.createdAt) : new Date(),
    unread: conv.unreadCount,
    onClick: () => selectConversation(conv)
  }));

  return (
    <div className="flex h-[calc(100vh-120px)] bg-white rounded-lg overflow-hidden shadow-md border border-gray-200">
      {/* Left sidebar */}
      <div className="w-80 border-r border-gray-200 flex flex-col bg-gray-50">
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-semibold">Messages</h2>
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => setIsNewChatDialogOpen(true)}
            className="text-gray-500 hover:text-gray-700"
          >
            <PlusCircle className="h-5 w-5" />
          </Button>
        </div>
        
        <div className="flex border-b border-gray-200">
          <button 
            className={`flex-1 py-3 text-center font-medium ${activeTab === 'general' 
              ? 'text-pink-500 border-b-2 border-pink-500' 
              : 'text-gray-500'}`}
            onClick={() => setActiveTab('general')}
          >
            General {filteredConversations.length > 0 && (
              <Badge className="ml-1 bg-pink-500 text-white">{filteredConversations.length}</Badge>
            )}
          </button>
          <button 
            className={`flex-1 py-3 text-center font-medium ${activeTab === 'archive' 
              ? 'text-pink-500 border-b-2 border-pink-500' 
              : 'text-gray-500'}`}
            onClick={() => setActiveTab('archive')}
          >
            Archive {clients.length > 0 && (
              <Badge className="ml-1 bg-gray-200 text-gray-700">{clients.length}</Badge>
            )}
          </button>
        </div>
        
        <div className="p-3 relative">
          <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input 
            type="text" 
            placeholder="Search..." 
            className="w-full pl-9 pr-4 py-2 rounded-full border border-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        {activeTab === 'general' ? (
          <div className="flex-1 overflow-y-auto">
            {loading ? (
              <div className="flex flex-col justify-center items-center h-full gap-3">
                <LoadingSpinner className="h-8 w-8 text-pink-500/60" />
                <p className="text-sm text-gray-500">Loading conversations...</p>
              </div>
            ) : (
              <ChatList
                className="chat-list"
                dataSource={chatItems}
                onClick={(item: any) => {
                  const conversation = conversations.find(c => c.id === item.id);
                  if (conversation) selectConversation(conversation);
                }}
              />
            )}
          </div>
        ) : (
          <div className="flex-1 overflow-y-auto">
            {loadingClients ? (
              <div className="flex flex-col justify-center items-center h-full gap-3">
                <LoadingSpinner className="h-8 w-8 text-pink-500/60" />
                <p className="text-sm text-gray-500">Loading clients...</p>
              </div>
            ) : clients.length > 0 ? (
              <div className="divide-y divide-gray-100">
                <div className="px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50">
                  All Clients
                </div>
                {clients.map((client) => {
                  // Check if there's an existing conversation with this client
                  const existingConversation = conversations.find(
                    (conv) => conv.user.id === client.id
                  );
                  
                  return (
                    <div
                      key={client.id}
                      className="p-3 hover:bg-gray-100 cursor-pointer"
                      onClick={() => {
                        if (existingConversation) {
                          selectConversation(existingConversation);
                        } else {
                          createConversation(client.id);
                        }
                      }}
                    >
                      <div className="flex items-center">
                        <img 
                          src={client.avatarUrl} 
                          alt={client.name} 
                          className="w-12 h-12 rounded-full mr-3 border border-gray-200" 
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-center">
                            <p className="font-medium text-gray-900 truncate">{client.name}</p>
                            {existingConversation ? (
                              <Badge variant="outline" className="text-xs">Chat Active</Badge>
                            ) : (
                              <Badge variant="secondary" className="text-xs bg-gray-100">Start Chat</Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-500 truncate">{client.email}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full p-6">
                <div className="bg-gray-50 p-6 rounded-xl border border-gray-200 flex flex-col items-center">
                  <User className="h-12 w-12 text-gray-300 mb-4" />
                  <p className="text-gray-500 text-center">No clients found</p>
                  <p className="text-xs text-gray-400 text-center mt-1">
                    You don't have any clients yet
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Main chat area */}
      <div className="flex-1 flex flex-col bg-white">
        {activeConversation ? (
          <>
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <div className="flex items-center">
                <img 
                  src={activeConversation.user.avatarUrl} 
                  alt={activeConversation.user.name} 
                  className="w-10 h-10 rounded-full mr-3 border border-gray-200" 
                />
                <div>
                  <h3 className="font-medium">{activeConversation.user.name}</h3>
                  <p className="text-sm text-gray-500">{activeConversation.user.email}</p>
                </div>
              </div>
              <div className="flex gap-4">
                <button className="text-gray-400 hover:text-gray-600">
                  <Phone className="h-5 w-5" />
                </button>
                <button className="text-gray-400 hover:text-gray-600">
                  <MoreVertical className="h-5 w-5" />
                </button>
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto bg-gray-50 p-4">
              {loadingMessages ? (
                <div className="flex flex-col justify-center items-center h-full gap-3">
                  <LoadingSpinner className="h-8 w-8 text-pink-500/60" />
                  <p className="text-sm text-gray-500">Loading messages...</p>
                </div>
              ) : (
                <MessageList
                  className="message-list"
                  lockable={true}
                  toBottomHeight={'100%'}
                  dataSource={chatMessages}
                />
              )}
              <div ref={messagesEndRef} />
            </div>
            
            <div className="p-3 border-t border-gray-200 flex items-center">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <ImageIcon className="h-5 w-5" />
              </button>
              <input
                ref={inputRef}
                type="text"
                placeholder="Type a message..."
                className="flex-1 mx-2 py-2 px-4 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-pink-500"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSendMessage();
                  }
                }}
              />
              <button 
                className="p-2 rounded-full bg-pink-500 text-white"
                onClick={handleSendMessage}
                disabled={!newMessage.trim()}
              >
                <Send className="h-5 w-5" />
              </button>
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-full p-6 bg-gray-50">
            <div className="bg-white p-8 rounded-xl border border-gray-200 flex flex-col items-center max-w-md">
              <MessageSquare className="h-16 w-16 text-gray-300 mb-6" />
              <h3 className="text-xl font-medium mb-3">Select a conversation</h3>
              <p className="text-gray-500 text-center leading-relaxed mb-6">
                Choose a conversation from the sidebar or start a new one to begin chatting with your clients.
              </p>
              
              {session?.user?.role === "trainer" && (
                <Button 
                  onClick={() => setIsNewChatDialogOpen(true)}
                  className="mb-6 bg-pink-500 hover:bg-pink-600"
                >
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Start New Conversation
                </Button>
              )}
              
              <div className="w-full">
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-500 font-medium">?</span>
                  </div>
                  <div className="flex-1">
                    <div className="h-2 w-24 bg-gray-200 rounded-full mb-2"></div>
                    <div className="h-2 w-32 bg-gray-100 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* New Chat Dialog */}
      <Dialog open={isNewChatDialogOpen} onOpenChange={setIsNewChatDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>New Conversation</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input 
                type="text" 
                placeholder="Search clients..." 
                className="w-full pl-9 pr-4 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            {loadingClients ? (
              <div className="flex flex-col justify-center items-center py-8 gap-3">
                <LoadingSpinner className="h-8 w-8 text-pink-500/60" />
                <p className="text-sm text-gray-500">Loading clients...</p>
              </div>
            ) : filteredClients.length > 0 ? (
              <ScrollArea className="h-[300px]">
                <div className="space-y-2">
                  {filteredClients.map((client) => (
                    <Button
                      key={client.id}
                      variant="ghost"
                      className="w-full justify-start p-3 h-auto"
                      onClick={() => createConversation(client.id)}
                      disabled={creatingConversation}
                    >
                      <div className="flex items-center gap-3 w-full">
                        <img 
                          src={client.avatarUrl} 
                          alt={client.name} 
                          className="h-10 w-10 rounded-full border border-gray-200" 
                        />
                        <div className="flex-1 text-left">
                          <p className="font-medium">{client.name}</p>
                          <p className="text-sm text-gray-500">{client.email}</p>
                        </div>
                        {creatingConversation ? (
                          <LoadingSpinner className="h-4 w-4 text-gray-400" />
                        ) : (
                          <UserPlus className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className="py-8 text-center px-4">
                {filteredClients.length > 0 ? (
                  <>
                    <p className="text-gray-500">You already have conversations with all your clients</p>
                    <p className="text-xs text-gray-400 mt-1">
                      All your clients already have active conversations
                    </p>
                  </>
                ) : clients.length > 0 ? (
                  <p className="text-gray-500">No clients match your search</p>
                ) : (
                  <>
                    <p className="text-gray-500">No clients found</p>
                    <p className="text-xs text-gray-400 mt-1">
                      You don't have any clients yet
                    </p>
                  </>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
