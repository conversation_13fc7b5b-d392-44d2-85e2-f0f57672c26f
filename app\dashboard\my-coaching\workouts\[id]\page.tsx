"use client"

import {
  <PERSON><PERSON><PERSON><PERSON>,
  Calendar,
  Clock,
  <PERSON><PERSON>ircle,
  <PERSON><PERSON><PERSON>,
  Youtube,
  Info,
  Loader2
} from "lucide-react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"

interface ExerciseState {
  id: string;
  name: string;
  completed: boolean;
}

type WorkoutData = any;

export default function WorkoutDetailPage(props: any) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [workout, setWorkout] = useState<WorkoutData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [completionNote, setCompletionNote] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [exercises, setExercises] = useState<ExerciseState[]>([])

  const workoutId = props.params?.id;

  useEffect(() => {
    if (!workoutId) {
      console.error("Workout ID is missing from params");
      setIsLoading(false);
      return;
    }

    if (status === "authenticated") {
      const fetchWorkout = async () => {
        try {
          const mockWorkout: WorkoutData = {
            id: workoutId,
            title: "Push Workout A",
            description: "Focus on chest, shoulders, and triceps.",
            scheduled: "2023-11-08",
            duration: 60,
            completed: false,
            type: "strength",
            notes: "Warm up properly.",
            exercises: [
              { id: "ex1", name: "Bench Press", type: "compound", sets: 4, reps: "8-10", rest: 90, weight: "135 lbs", videoUrl: "https://www.youtube.com/watch?v=rT7DgCr-3pg" },
            ]
          };
          setWorkout(mockWorkout);
          setExercises(mockWorkout.exercises.map((ex: any) => ({ id: ex.id, name: ex.name, completed: false })));
        } catch (error) {
          console.error("Failed to fetch workout:", error);
        } finally {
          setIsLoading(false);
        }
      };
      fetchWorkout();
    }
  }, [workoutId, status]);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  if (status === "loading" || isLoading) {
    return (
      <div className="flex min-h-[600px] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!workout) {
    return (
      <div className="container py-10">
        <h1 className="text-2xl font-bold mb-6">Workout Not Found</h1>
        <p>The requested workout could not be found or loaded.</p>
        <Button onClick={() => router.push("/dashboard/my-coaching")} className="mt-4">
          Back to Coaching
        </Button>
      </div>
    );
  }

  const handleExerciseToggle = (id: string) => {
    setExercises((prev: ExerciseState[]) =>
      prev.map(ex => 
        ex.id === id ? { ...ex, completed: !ex.completed } : ex
      )
    )
  }

  const allExercisesCompleted = exercises.length > 0 && exercises.every(ex => ex.completed)

  const handleSubmitCompletion = async () => {
    if (!workout) return;
    setIsSubmitting(true)
    try {
      console.log("Marking workout as completed:", workout.id, "with note:", completionNote)
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setWorkout((prev: any) => prev ? { ...prev, completed: true } : null)
      
      setTimeout(() => {
        router.push("/dashboard/my-coaching?tab=workouts")
      }, 1500)
    } catch (error) {
      console.error("Error completing workout:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatYoutubeEmbedUrl = (url: string | undefined): string | undefined => {
    if (!url) return undefined;
    try {
      const videoIdMatch = url.match(/(?:v=|\/embed\/|\/)([^#&?]*).*/);
      const videoId = videoIdMatch ? videoIdMatch[1] : null;
      return videoId ? `https://www.youtube.com/embed/${videoId}` : url;
    } catch (e) {
      console.error("Error parsing YouTube URL:", url, e);
      return url;
    }
  }

  return (
    <div className="container py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={() => router.push("/dashboard/my-coaching")} className="mr-2">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">{workout.title}</h1>
        <Badge 
          variant={workout.completed ? "default" : "secondary"} 
          className="ml-4"
        >
          {workout.completed ? "Completed" : "Scheduled"}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Workout Details</CardTitle>
              <CardDescription>{workout.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>Scheduled for {new Date(workout.scheduled).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>Estimated duration: {workout.duration} minutes</span>
                  </div>
                  <div className="flex items-center">
                    <Dumbbell className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>Type: {workout.type.charAt(0).toUpperCase() + workout.type.slice(1)}</span>
                  </div>
                </div>
                <div>
                  {workout.notes && (
                    <div className="bg-muted/30 p-3 rounded-md text-sm">
                      <div className="flex items-center mb-1 font-medium">
                        <Info className="h-4 w-4 mr-1" />
                        Coach Notes:
                      </div>
                      <p>{workout.notes}</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Exercise List</CardTitle>
              <CardDescription>Complete all exercises as prescribed</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {workout.exercises.map((exercise: any, index: number) => (
                <div key={exercise.id} className="border rounded-lg overflow-hidden">
                  <div className="bg-muted p-4 flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <div className="bg-primary/10 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <h3 className="font-medium">{exercise.name}</h3>
                      <Badge variant="outline" className="ml-2">
                        {exercise.type}
                      </Badge>
                    </div>
                    {!workout.completed && (
                      <div className="flex items-center">
                        <Checkbox
                          id={`exercise-${exercise.id}`}
                          checked={exercises.find(ex => ex.id === exercise.id)?.completed}
                          onCheckedChange={() => handleExerciseToggle(exercise.id)}
                        />
                        <Label htmlFor={`exercise-${exercise.id}`} className="ml-2 cursor-pointer">
                          {exercises.find(ex => ex.id === exercise.id)?.completed ? "Completed" : "Mark as done"}
                        </Label>
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="grid grid-cols-2 gap-2">
                          <div className="bg-muted/30 p-2 rounded-md">
                            <div className="text-xs text-muted-foreground">Sets</div>
                            <div className="font-medium">{exercise.sets}</div>
                          </div>
                          <div className="bg-muted/30 p-2 rounded-md">
                            <div className="text-xs text-muted-foreground">Reps</div>
                            <div className="font-medium">{exercise.reps}</div>
                          </div>
                          <div className="bg-muted/30 p-2 rounded-md">
                            <div className="text-xs text-muted-foreground">Rest</div>
                            <div className="font-medium">{exercise.rest} sec</div>
                          </div>
                          <div className="bg-muted/30 p-2 rounded-md">
                            <div className="text-xs text-muted-foreground">Weight</div>
                            <div className="font-medium">{exercise.weight}</div>
                          </div>
                        </div>
                        {exercise.notes && (
                          <div className="text-sm">
                            <div className="font-medium">Notes:</div>
                            <p className="text-muted-foreground">{exercise.notes}</p>
                          </div>
                        )}
                      </div>
                      <div>
                        {exercise.videoUrl && (
                          <div className="flex flex-col">
                            <div className="relative pt-[56.25%] rounded-md overflow-hidden">
                              <iframe
                                src={formatYoutubeEmbedUrl(exercise.videoUrl)}
                                className="absolute top-0 left-0 w-full h-full"
                                title={`${exercise.name} demonstration`}
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                allowFullScreen
                              ></iframe>
                            </div>
                            <div className="flex items-center justify-center mt-2">
                              <a
                                href={exercise.videoUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center text-xs text-muted-foreground hover:text-foreground"
                              >
                                <Youtube className="h-3 w-3 mr-1" />
                                Watch on YouTube
                              </a>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        <div>
          {workout.completed ? (
            <Card>
              <CardHeader>
                <CardTitle>Workout Completed</CardTitle>
                <CardDescription>Great job on completing this workout!</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center py-6">
                  <div className="rounded-full bg-green-100 p-3 mb-4">
                    <CheckCircle className="h-12 w-12 text-green-600" />
                  </div>
                  <h3 className="text-xl font-medium mb-2">Workout Completed</h3>
                  <p className="text-center text-muted-foreground">
                    Your coach has been notified and will review your progress.
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => router.push("/dashboard/my-coaching")}
                >
                  Back to Dashboard
                </Button>
              </CardFooter>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Complete Workout</CardTitle>
                <CardDescription>
                  Mark this workout as complete when you've finished all exercises
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col items-center justify-center py-4">
                  <div className={`rounded-full p-3 mb-2 ${allExercisesCompleted ? 'bg-green-100' : 'bg-amber-100'}`}>
                    {allExercisesCompleted ? (
                      <CheckCircle className="h-8 w-8 text-green-600" />
                    ) : (
                      <Dumbbell className="h-8 w-8 text-amber-600" />
                    )}
                  </div>
                  <div className="text-center">
                    <h3 className="font-medium">
                      {allExercisesCompleted
                        ? "All exercises completed!"
                        : `${exercises.filter(ex => ex.completed).length} of ${exercises.length} exercises completed`}
                    </h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      {allExercisesCompleted
                        ? "You can now submit your workout as complete"
                        : "Check off exercises as you complete them"}
                    </p>
                  </div>
                </div>

                <Separator />

                <div>
                  <Label htmlFor="completionNote">Workout Notes (optional)</Label>
                  <Textarea
                    id="completionNote"
                    placeholder="How was the workout? Any challenges or achievements?"
                    value={completionNote}
                    onChange={(e) => setCompletionNote(e.target.value)}
                    className="mt-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Your coach will be able to see these notes.
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  onClick={handleSubmitCompletion}
                  disabled={!allExercisesCompleted || isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Mark Workout Complete"}
                </Button>
              </CardFooter>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
} 