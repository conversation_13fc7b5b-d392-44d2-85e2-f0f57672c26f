"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"

interface Trainer {
  id: string
  name: string
  email: string
  bio: string
  avatarUrl: string
  socialLinks: {
    instagram?: string
    twitter?: string
    website?: string
    youtube?: string
  }
  role: string
}

interface EditTrainerProfileDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  trainer: Trainer
  onSave: (updatedProfile: Partial<Trainer>) => void
}

export function EditTrainerProfileDialog({
  open,
  on<PERSON><PERSON><PERSON><PERSON><PERSON>,
  trainer,
  onSave
}: EditTrainerProfileDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: trainer.name,
    email: trainer.email,
    bio: trainer.bio,
    avatarUrl: trainer.avatarUrl,
    socialLinks: {
      instagram: trainer.socialLinks?.instagram || "",
      twitter: trainer.socialLinks?.twitter || "",
      website: trainer.socialLinks?.website || "",
      youtube: trainer.socialLinks?.youtube || ""
    }
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target

    if (name.includes('.')) {
      const [parent, child] = name.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // In a real implementation, this would be an API call
      // For now, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      onSave({
        name: formData.name,
        bio: formData.bio,
        avatarUrl: formData.avatarUrl,
        socialLinks: formData.socialLinks
      })

      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully."
      })

      onOpenChange(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Use FileReader to convert the image to a data URL
    const reader = new FileReader()

    reader.onload = (event) => {
      if (event.target?.result) {
        // Update the form data with the data URL
        setFormData(prev => ({
          ...prev,
          avatarUrl: event.target.result as string
        }))

        toast({
          title: 'Avatar selected',
          description: 'Your avatar has been selected. Click Save Changes to update your profile.',
        })
      }
    }

    reader.onerror = () => {
      toast({
        title: 'Error',
        description: 'Failed to read the image file. Please try again.',
        variant: 'destructive'
      })
    }

    // Read the file as a data URL
    reader.readAsDataURL(file)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Profile</DialogTitle>
          <DialogDescription>
            Update your trainer profile information
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="bio">Bio & Description</TabsTrigger>
              <TabsTrigger value="social">Social Links</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="flex flex-col items-center gap-4">
                <div className="flex flex-col items-center gap-2">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src={formData.avatarUrl} alt={formData.name} />
                    <AvatarFallback className="text-2xl">
                      {formData.name?.[0]?.toUpperCase() || "T"}
                    </AvatarFallback>
                  </Avatar>
                  {formData.avatarUrl && (
                    <p className="text-xs text-muted-foreground break-all max-w-[200px]">
                      {formData.avatarUrl}
                    </p>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Input
                    type="file"
                    id="avatar"
                    className="hidden"
                    accept="image/*"
                    onChange={handleAvatarChange}
                  />
                  <Label htmlFor="avatar" className="cursor-pointer">
                    <Button type="button" variant="outline" size="sm" onClick={() => document.getElementById('avatar')?.click()}>
                      Change Avatar
                    </Button>
                  </Label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  This email is used for account login and notifications
                </p>
              </div>
            </TabsContent>

            <TabsContent value="bio" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  name="bio"
                  rows={8}
                  value={formData.bio}
                  onChange={handleChange}
                  placeholder="Tell clients about your experience, certifications, and training philosophy..."
                />
                <p className="text-xs text-muted-foreground">
                  Your bio will be displayed on your public profile
                </p>
              </div>
            </TabsContent>

            <TabsContent value="social" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="socialLinks.instagram">Instagram</Label>
                <Input
                  id="socialLinks.instagram"
                  name="socialLinks.instagram"
                  value={formData.socialLinks.instagram}
                  onChange={handleChange}
                  placeholder="yourusername (without @)"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="socialLinks.twitter">Twitter</Label>
                <Input
                  id="socialLinks.twitter"
                  name="socialLinks.twitter"
                  value={formData.socialLinks.twitter}
                  onChange={handleChange}
                  placeholder="yourusername (without @)"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="socialLinks.youtube">YouTube</Label>
                <Input
                  id="socialLinks.youtube"
                  name="socialLinks.youtube"
                  value={formData.socialLinks.youtube}
                  onChange={handleChange}
                  placeholder="Your channel URL"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="socialLinks.website">Website</Label>
                <Input
                  id="socialLinks.website"
                  name="socialLinks.website"
                  value={formData.socialLinks.website}
                  onChange={handleChange}
                  placeholder="yourwebsite.com"
                />
              </div>
            </TabsContent>
          </Tabs>

          <Separator />

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
