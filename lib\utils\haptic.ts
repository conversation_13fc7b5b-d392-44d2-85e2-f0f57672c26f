/**
 * Utility functions for haptic feedback on mobile devices
 */

/**
 * Trigger vibration if supported by the device
 * @param pattern - Vibration pattern in milliseconds
 */
export function vibrate(pattern: number | number[]): void {
  if (typeof window !== 'undefined' && 'navigator' in window) {
    try {
      if ('vibrate' in navigator) {
        navigator.vibrate(pattern);
      }
    } catch (error) {
      console.error('Error using vibration API:', error);
    }
  }
}

/**
 * Predefined vibration patterns
 */
export const hapticPatterns = {
  success: [100, 50, 100], // Double short pulse
  error: [300], // Long pulse
  warning: [100, 100, 100], // Triple short pulse
  timerComplete: [50, 30, 50, 30, 50], // Multiple short pulses
  exerciseComplete: [100, 50, 200], // Short-long pattern
  navigationFeedback: [40], // Very short pulse
};

/**
 * Trigger success haptic feedback
 */
export function successHaptic(): void {
  vibrate(hapticPatterns.success);
}

/**
 * Trigger error haptic feedback
 */
export function errorHaptic(): void {
  vibrate(hapticPatterns.error);
}

/**
 * Trigger warning haptic feedback
 */
export function warningHaptic(): void {
  vibrate(hapticPatterns.warning);
}

/**
 * Trigger timer complete haptic feedback
 */
export function timerCompleteHaptic(): void {
  vibrate(hapticPatterns.timerComplete);
}

/**
 * Trigger exercise complete haptic feedback
 */
export function exerciseCompleteHaptic(): void {
  vibrate(hapticPatterns.exerciseComplete);
}

/**
 * Trigger navigation feedback haptic
 */
export function navigationHaptic(): void {
  vibrate(hapticPatterns.navigationFeedback);
}
