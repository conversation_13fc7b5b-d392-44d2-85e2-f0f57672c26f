import { NextResponse } from "next/server";
import { getServerAuthSession } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import logger from "@/lib/logger";
import logService from "@/lib/log-service";
import { getAllFeatureStatuses } from "@/lib/monitoring-store";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export const dynamic = 'force-dynamic'

// Get system status
export async function GET(request: Request) {
  try {
    const session = await getServerAuthSession();
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get developer request parameter
    const url = new URL(request.url);
    const isDeveloper = url.searchParams.get('dev') === 'true';

    // Check database connection
    let dbStatus = 'healthy';
    try {
      await prisma.$queryRaw`SELECT 1`;
    } catch (error) {
      dbStatus = 'error';
      logger.error({ error }, "Database connection error in system status check");
      logService.error("Database connection error in system status check", { error });
    }

    // Get system stats
    const memory = process.memoryUsage();
    const systemInfo = {
      database: dbStatus,
      api: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        rss: Math.round(memory.rss / 1024 / 1024) + 'MB',
        heapTotal: Math.round(memory.heapTotal / 1024 / 1024) + 'MB',
        heapUsed: Math.round(memory.heapUsed / 1024 / 1024) + 'MB'
      },
      environment: process.env.NODE_ENV || 'development'
    };

    // Get all feature statuses
    const features = getAllFeatureStatuses();

    // Format the response
    const response: any = {
      system: systemInfo,
      features: Object.entries(features).map(([name, data]) => ({
        name,
        status: data.status,
        incidents: data.incidents,
        lastCheck: data.lastCheck
      }))
    };

    // Include recent logs if developer mode
    if (isDeveloper || process.env.NODE_ENV === 'development') {
      response.logs = logService.getRecentLogs(50);
    }

    // Log this access
    logService.info("System status checked", { 
      user: session.user.id,
      isDeveloper
    });

    return NextResponse.json(response);
  } catch (error) {
    logger.error({ error }, "Error fetching system status");
    logService.error("Error fetching system status", { error });
    return new NextResponse("Internal error", { status: 500 });
  }
} 