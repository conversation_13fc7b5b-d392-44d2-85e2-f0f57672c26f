import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { PrismaClient } from "@prisma/client";

// Create a new instance for this request
const prisma = new PrismaClient();

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const userId = session.user.id;
  const { searchParams } = new URL(request.url);
  const conversationId = searchParams.get("conversationId");

  if (!conversationId) {
    return new NextResponse("Conversation ID is required", { status: 400 });
  }

  try {
    // Verify the user is part of this conversation
    const conversation = await prisma.conversation.findUnique({
      where: {
        id: conversationId,
      },
    });

    if (!conversation) {
      return new NextResponse("Conversation not found", { status: 404 });
    }

    if (conversation.user1Id !== userId && conversation.user2Id !== userId) {
      return new NextResponse("Unauthorized", { status: 403 });
    }

    // Get messages for this conversation
    const messages = await prisma.message.findMany({
      where: {
        conversationId,
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
          },
        },
        receiver: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    return NextResponse.json(messages);
  } catch (error) {
    console.error("Error fetching coaching messages:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  } finally {
    // Always disconnect the Prisma client to prevent connection issues
    await prisma.$disconnect();
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const userId = session.user.id;

  try {
    const body = await request.json();
    const { content, conversationId, attachments } = body;

    if (!content && (!attachments || attachments.length === 0)) {
      return new NextResponse("Message must have content or attachments", { status: 400 });
    }

    if (!conversationId) {
      return new NextResponse("Missing conversationId", { status: 400 });
    }

    // Prepare message content with attachments if any
    let messageContent = content || "";
    if (attachments && attachments.length > 0) {
      // Store attachments as JSON in the message content
      const messageData = {
        text: content || "",
        attachments: attachments
      };
      messageContent = JSON.stringify(messageData);
    }

    // Verify the user is part of this conversation
    const conversation = await prisma.conversation.findUnique({
      where: {
        id: conversationId,
      },
    });

    if (!conversation) {
      return new NextResponse("Conversation not found", { status: 404 });
    }

    if (conversation.user1Id !== userId && conversation.user2Id !== userId) {
      return new NextResponse("Unauthorized", { status: 403 });
    }

    // Determine the receiver
    const receiverId = conversation.user1Id === userId ? conversation.user2Id : conversation.user1Id;

    // Create the message
    const newMessage = await prisma.message.create({
      data: {
        content: messageContent,
        senderId: userId,
        receiverId,
        conversationId,
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
          },
        },
        receiver: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
          },
        },
      },
    });

    // Update the conversation's lastMessageAt
    await prisma.conversation.update({
      where: {
        id: conversationId,
      },
      data: {
        lastMessageAt: new Date(),
      },
    });

    // Create a notification for the receiver
    await prisma.notification.create({
      data: {
        userId: receiverId,
        title: "New Message",
        message: `${newMessage.sender.name || 'A user'}: ${
          attachments && attachments.length > 0
            ? attachments.length === 1
              ? `Sent an attachment: ${attachments[0].fileName}`
              : `Sent ${attachments.length} attachments`
            : newMessage.content.length > 50
              ? newMessage.content.substring(0, 50) + '...'
              : newMessage.content
        }`,
        type: "message",
        actionLink: `/dashboard/chats?conversationId=${conversationId}`,
        sourceId: newMessage.id,
        sourceType: "message",
        read: false,
      },
    });

    // Set headers to notify the client
    const headers = new Headers();
    headers.append('X-New-Message', 'true');
    headers.append('X-Message-Receiver', receiverId);
    headers.append('X-Message-Conversation', conversationId);

    // Send SSE event to the receiver
    try {
      console.log(`Attempting to send SSE event to user ${receiverId} for conversation ${conversationId}`);

      // Create the message payload
      const messagePayload = {
        type: 'new-message',
        conversationId: conversationId,
        message: newMessage
      };

      console.log('SSE message payload:', JSON.stringify(messagePayload));

      // Import the sendMessageToUser function
      const { sendMessageToUser } = await import('@/app/api/sse/route');

      // Send the message
      sendMessageToUser(receiverId, JSON.stringify(messagePayload));

      console.log(`Successfully sent SSE event to user ${receiverId} for conversation ${conversationId}`);
    } catch (error) {
      console.error('Error sending SSE message:', error);
      console.error('Error details:', error instanceof Error ? error.message : 'Unknown error');
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack available');
    }

    return NextResponse.json(newMessage);
  } catch (error) {
    console.error("Error creating coaching message:", error);

    // Return more detailed error information in development
    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      }, { status: 500 });
    }

    return new NextResponse("Internal Server Error", { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
