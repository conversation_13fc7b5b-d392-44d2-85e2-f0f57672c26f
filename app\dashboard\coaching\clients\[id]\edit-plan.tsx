"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
// Card components removed as they're not used
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Pencil } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { WeeklyPlanEditor } from "@/components/training-plan/weekly-plan-editor"

interface EditPlanProps {
  clientId: string
  onUpdate?: (updatedPlan: any) => void
  currentPlan: {
    id: string
    title: string
    description: string
    weeks?: Array<{
      id: string
      weekNumber: number
      dailyWorkouts: Array<{
        id: string
        day: string
        exercises: Array<{
          id: string
          name: string
          sets: number
          reps: number
          notes?: string
        }>
      }>
    }>
  }
  buttonProps?: React.ButtonHTMLAttributes<HTMLButtonElement>
}

export function EditPlan({ clientId, currentPlan, buttonProps, onUpdate }: EditPlanProps) {
  const router = useRouter()
  const [isOpen, setIsOpen] = useState(false)
  const [plan, setPlan] = useState(currentPlan || {
    id: '',
    title: '',
    description: '',
    type: 'personalized',
    weeks: []
  })
  const [availableExercises, setAvailableExercises] = useState<any[]>([])
  const [isLoadingExercises, setIsLoadingExercises] = useState(true)

  // Fetch available exercises when component mounts
  useEffect(() => {
    const fetchExercises = async () => {
      try {
        setIsLoadingExercises(true)
        const response = await fetch('/api/exercises?templateOnly=true', {
          credentials: 'include'
        })
        if (!response.ok) throw new Error('Failed to fetch exercises')
        const data = await response.json()
        setAvailableExercises(data)
      } catch (error) {
        console.error('Error fetching exercises:', error)
        toast({
          title: "Error",
          description: "Failed to load exercises. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoadingExercises(false)
      }
    }

    if (isOpen) {
      fetchExercises()
    }
  }, [isOpen])

  // Handle plan updates from the WeeklyPlanEditor
  const handlePlanUpdate = (updatedPlan: any) => {
    console.log('Plan update received:', updatedPlan);

    if (!updatedPlan) {
      console.error('Received undefined or null updatedPlan');
      return;
    }

    // Make sure we preserve the plan ID and other essential fields
    setPlan({
      ...updatedPlan,
      type: 'personalized', // Always use 'personalized' for client plans
      id: plan?.id || '',
      title: updatedPlan?.title || plan?.title || '',
      description: updatedPlan?.description || plan?.description || '',
      // Ensure weeks is properly formatted
      weeks: Array.isArray(updatedPlan?.weeks) ? updatedPlan.weeks : (Array.isArray(plan?.weeks) ? plan.weeks : [])
    });

    console.log('Plan updated successfully');
  }

  const handleSave = async () => {
    try {
      console.log('Saving plan:', plan);

      // Create a complete update payload
      const updatePayload = {
        title: plan?.title || '',
        description: plan?.description || '',
        type: 'personalized', // Always use 'personalized' for client plans
        weeks: Array.isArray(plan?.weeks) ? plan.weeks : [],
        // Include any other fields that need to be updated
      };

      console.log('Sending update payload:', updatePayload);

      const response = await fetch(`/api/clients/${clientId}/training-plan/update`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatePayload),
      })

      const responseData = await response.json().catch(() => null);
      console.log('Response status:', response.status, 'Response data:', responseData);

      if (!response.ok) {
        throw new Error(`Failed to update training plan: ${response.status} ${responseData?.error || ''}`)
      }

      toast({
        title: "Success",
        description: "Training plan updated successfully",
      })

      // Call the onUpdate callback if provided
      if (onUpdate && responseData) {
        onUpdate(responseData);
      }

      setIsOpen(false)
      router.refresh()
    } catch (error) {
      console.error("Error updating plan:", error)
      toast({
        title: "Error",
        description: "Failed to update training plan. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" {...buttonProps}>
          <Pencil className="mr-2 h-4 w-4" />
          Edit Plan
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[95vw] md:max-w-[90vw] lg:max-w-[85vw] xl:max-w-[80vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Training Plan</DialogTitle>
          <DialogDescription>
            Modify the client's training plan details
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4 px-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Plan Title</Label>
              <Input
                id="title"
                value={plan?.title || ''}
                onChange={(e) => setPlan({ ...plan, title: e.target.value })}
                placeholder="Enter plan title"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">Plan Type</Label>
              <Select
                value={plan?.type || 'personalized'}
                onValueChange={(value) => setPlan({ ...plan, type: value })}
              >
                <SelectTrigger id="type">
                  <SelectValue placeholder="Select plan type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="personalized">Personalized</SelectItem>
                  <SelectItem value="template">Template</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={plan?.description || ''}
              onChange={(e) => setPlan({ ...plan, description: e.target.value })}
              placeholder="Describe the purpose and goals of this training plan"
              className="min-h-[100px]"
            />
          </div>

          {/* Weekly Plan Editor */}
          {isLoadingExercises ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="bg-background rounded-lg border border-border p-4 shadow-sm">
              <WeeklyPlanEditor
              plan={{
                ...plan,
                // Ensure the plan has the required fields for WeeklyPlanEditor
                id: plan?.id || '',
                title: plan?.title || '',
                description: plan?.description || '',
                type: 'personalized', // Always use 'personalized' for client plans
                // Make sure weeks is properly formatted
                weeks: Array.isArray(plan?.weeks) ? plan.weeks.map(week => ({
                  ...week,
                  id: week?.id || `week-${week?.weekNumber || 1}`,
                  weekNumber: week?.weekNumber || 1,
                  // Ensure each week has the required fields
                  dailyWorkouts: Array.isArray(week?.dailyWorkouts) ? week.dailyWorkouts.map(day => ({
                    ...day,
                    id: day?.id || `day-${day?.dayNumber || 1}`,
                    dayNumber: day?.dayNumber || 1,
                    name: day?.name || 'Day',
                    exercises: Array.isArray(day?.exercises) ? day.exercises : []
                  })) : []
                })) : []
              }}
              onPlanUpdate={handlePlanUpdate}
              availableExercises={availableExercises}
              isEditMode={true}
            />
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
