/**
 * Sanitizes a string by removing potentially dangerous HTML and script content
 * @param input The string to sanitize
 * @returns The sanitized string
 */
export function sanitizeHtml(input: string): string {
  if (!input) return '';
  
  // Remove HTML tags
  const withoutTags = input.replace(/<[^>]*>/g, '');
  
  // Encode special characters
  return withoutTags
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * Sanitizes text for display in HTML (simple version)
 * @param input The string to sanitize
 * @returns The sanitized string
 */
export function sanitizeText(input: string): string {
  if (!input) return '';
  return String(input).trim();
}

/**
 * Sanitizes a user input name
 * @param name The name to sanitize
 * @returns The sanitized name
 */
export function sanitizeName(name: string): string {
  if (!name) return '';
  
  // Remove any non-alphabetic characters except spaces, hyphens, and apostrophes
  return name.replace(/[^a-zA-Z\s'-]/g, '').trim();
}

/**
 * Sanitizes an email address
 * @param email The email to sanitize
 * @returns The sanitized email
 */
export function sanitizeEmail(email: string): string {
  if (!email) return '';
  
  // Basic email sanitization - trim and lowercase
  return email.trim().toLowerCase();
}

/**
 * Sanitizes a credit card number by removing non-numeric characters
 * @param cardNumber The card number to sanitize
 * @returns The sanitized card number
 */
export function sanitizeCardNumber(cardNumber: string): string {
  if (!cardNumber) return '';
  
  // Remove all non-numeric characters
  return cardNumber.replace(/\D/g, '');
}

/**
 * Sanitizes form data based on field types
 * @param formData The form data to sanitize
 * @returns The sanitized form data
 */
export function sanitizeFormData<T extends Record<string, unknown>>(formData: T): T {
  const result = {} as T;
  
  // Create a new object with the sanitized values
  for (const [key, value] of Object.entries(formData)) {
    let sanitizedValue: unknown = value;
    
    if (typeof value === 'string') {
      if (key.toLowerCase().includes('name')) {
        sanitizedValue = sanitizeName(value);
      } 
      else if (key.toLowerCase().includes('email')) {
        sanitizedValue = sanitizeEmail(value);
      }
      else if (key.toLowerCase().includes('card') && key.toLowerCase().includes('number')) {
        sanitizedValue = sanitizeCardNumber(value);
      }
      else {
        sanitizedValue = sanitizeText(value);
      }
    }
    
    // Type assertion is needed here to handle property assignment
    (result as Record<string, unknown>)[key] = sanitizedValue;
  }
  
  return result;
}

/**
 * Removes potential SQL injection patterns
 * @param input The string to sanitize
 * @returns The sanitized string
 */
export function sanitizeSqlInput(input: string): string {
  if (!input) return '';
  
  // Replace potentially dangerous SQL patterns
  return input
    .replace(/'/g, "''")
    .replace(/;/g, '')
    .replace(/--/g, '')
    .replace(/\/\*/g, '')
    .replace(/\*\//g, '');
}

/**
 * Converts unsafe filenames to safe ones
 * @param filename The filename to sanitize
 * @returns The sanitized filename
 */
export function sanitizeFilename(filename: string): string {
  if (!filename) return '';
  
  // Remove path traversal characters and common unsafe characters
  return filename
    .replace(/[/\\?%*:|"<>]/g, '-')
    .replace(/\.\./g, '-')
    .trim();
} 