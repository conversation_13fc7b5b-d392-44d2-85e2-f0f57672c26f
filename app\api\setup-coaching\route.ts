import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    // In development mode, we don't require authentication for this endpoint
    // to make testing easier
    if (process.env.NODE_ENV !== "development") {
      const session = await getServerSession(authOptions);

      if (!session?.user?.id) {
        return new NextResponse("Unauthorized", { status: 401 });
      }
    }

    // Only allow this in development
    if (process.env.NODE_ENV !== "development") {
      return new NextResponse("Only available in development", { status: 403 });
    }

    // 1. Find a trainer and a client user
    const trainer = await prisma.user.findFirst({
      where: {
        role: "trainer",
      },
    });

    const client = await prisma.user.findFirst({
      where: {
        role: "client",
      },
    });

    if (!trainer || !client) {
      return new NextResponse("Could not find trainer and client users", { status: 404 });
    }

    // 2. Create or find a coaching relationship
    let coachingRelationship = await prisma.coachingRelationship.findFirst({
      where: {
        trainerId: trainer.id,
        clientId: client.id,
        status: "active",
      },
    });

    if (!coachingRelationship) {
      // First, check if there are any existing relationships with other statuses
      const existingRelationship = await prisma.coachingRelationship.findFirst({
        where: {
          trainerId: trainer.id,
          clientId: client.id,
        },
      });

      if (existingRelationship) {
        // Update the existing relationship to active
        coachingRelationship = await prisma.coachingRelationship.update({
          where: {
            id: existingRelationship.id,
          },
          data: {
            status: "active",
            plan: "Premium Coaching",
            notes: "Test coaching relationship for chat testing",
          },
        });
      } else {
        // Create a new relationship
        coachingRelationship = await prisma.coachingRelationship.create({
          data: {
            trainerId: trainer.id,
            clientId: client.id,
            status: "active",
            plan: "Premium Coaching",
            notes: "Test coaching relationship for chat testing",
          },
        });
      }
    }

    // 3. Create or find a conversation
    let conversation = await prisma.conversation.findFirst({
      where: {
        OR: [
          { user1Id: trainer.id, user2Id: client.id },
          { user1Id: client.id, user2Id: trainer.id },
        ],
      },
    });

    if (!conversation) {
      conversation = await prisma.conversation.create({
        data: {
          user1Id: trainer.id,
          user2Id: client.id,
        },
      });
    }

    // 4. Create a test message
    const message = await prisma.message.create({
      data: {
        content: "Hello! This is a test message to start the conversation.",
        senderId: trainer.id,
        receiverId: client.id,
        conversationId: conversation.id,
      },
    });

    // 5. Update the conversation's lastMessageAt
    await prisma.conversation.update({
      where: {
        id: conversation.id,
      },
      data: {
        lastMessageAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      trainer: {
        id: trainer.id,
        name: trainer.name,
      },
      client: {
        id: client.id,
        name: client.name,
      },
      coachingRelationship: {
        id: coachingRelationship.id,
      },
      conversation: {
        id: conversation.id,
      },
      message: {
        id: message.id,
      },
    });
  } catch (error) {
    console.error("Error setting up coaching relationship:", error);

    // Return more detailed error information in development
    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      }, { status: 500 });
    }

    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
