"use client"

import { useState } from "react"
import Image from "next/image"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { DigitalProduct, TrainerTheme } from "@/types/trainer"
import { Star, Award, TrendingUp, Clock, <PERSON><PERSON><PERSON>, Zap } from "lucide-react"

interface FeaturedContentProps {
  theme: TrainerTheme & {
    sectionBackground?: string
    sectionTextColor?: string
  }
  featuredProducts: DigitalProduct[]
  transformations?: Array<{
    id: string
    title: string
    description: string
    beforeImage: string
    afterImage: string
    duration: string
  }>
  achievements?: Array<{
    icon: string
    title: string
    description: string
  }>
  onSelectProduct: (product: DigitalProduct) => void
}

export function FeaturedContentSection({ 
  theme, 
  featuredProducts, 
  transformations = [],
  achievements = [],
  onSelectProduct 
}: FeaturedContentProps) {
  const [activeTab, setActiveTab] = useState("featured")
  const [activeTransformation, setActiveTransformation] = useState(transformations[0]?.id || "")
  
  // Animation variants for cards
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: "easeOut"
      }
    })
  }
  
  // Get achievement icon
  const getAchievementIcon = (iconName: string) => {
    switch(iconName) {
      case "award": return <Award className="h-8 w-8" />;
      case "trending": return <TrendingUp className="h-8 w-8" />;
      case "clock": return <Clock className="h-8 w-8" />;
      case "dumbbell": return <Dumbbell className="h-8 w-8" />;
      case "zap": return <Zap className="h-8 w-8" />;
      default: return <Award className="h-8 w-8" />;
    }
  }

  return (
    <section 
      className="py-16"
      style={{ 
        backgroundColor: theme.sectionBackground || 'transparent',
        color: theme.sectionTextColor || 'inherit'
      }}
    >
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Featured Content</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover my most popular programs and success stories
          </p>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full max-w-md mx-auto grid-cols-3 mb-10">
            <TabsTrigger value="featured">Featured Programs</TabsTrigger>
            <TabsTrigger value="transformations">Transformations</TabsTrigger>
            <TabsTrigger value="achievements">Achievements</TabsTrigger>
          </TabsList>
          
          {/* Featured Programs Tab */}
          <TabsContent value="featured" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredProducts.slice(0, 3).map((product, i) => (
                <motion.div
                  key={product.id}
                  custom={i}
                  initial="hidden"
                  animate="visible"
                  variants={cardVariants}
                >
                  <Card className="h-full overflow-hidden border-2 hover:border-primary/50 transition-all card-hover">
                    <div className="relative h-48 overflow-hidden">
                      <Image
                        src={product.thumbnailUrl || "/placeholder-image.jpg"}
                        alt={product.title}
                        fill
                        className="object-cover transition-transform hover:scale-105"
                      />
                      <div className="absolute top-2 right-2">
                        <Badge className="bg-primary text-white">Featured</Badge>
                      </div>
                    </div>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-xl">{product.title}</CardTitle>
                      <div className="flex items-center mt-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star key={star} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                        <span className="ml-2 text-sm text-muted-foreground">5.0 (24 reviews)</span>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="line-clamp-2 text-muted-foreground">
                        {product.description}
                      </p>
                    </CardContent>
                    <CardFooter className="flex justify-between items-center">
                      <span className="text-xl font-bold">${product.price}</span>
                      <Button 
                        onClick={() => onSelectProduct(product)}
                        className="custom-button"
                      >
                        Get Program
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </div>
            
            {featuredProducts.length > 3 && (
              <div className="text-center mt-8">
                <Button variant="outline" size="lg">
                  View All Programs
                </Button>
              </div>
            )}
          </TabsContent>
          
          {/* Transformations Tab */}
          <TabsContent value="transformations" className="space-y-8">
            {transformations.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-4">
                  {transformations.map((item) => (
                    <Card 
                      key={item.id}
                      className={`cursor-pointer transition-all ${
                        activeTransformation === item.id 
                          ? 'border-primary ring-2 ring-primary/20' 
                          : 'hover:border-primary/30'
                      }`}
                      onClick={() => setActiveTransformation(item.id)}
                    >
                      <CardHeader className="pb-2">
                        <CardTitle>{item.title}</CardTitle>
                        <CardDescription>{item.duration} transformation</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                
                <div className="bg-muted rounded-lg overflow-hidden">
                  {transformations.map((item) => (
                    item.id === activeTransformation && (
                      <div key={item.id} className="h-full">
                        <div className="flex flex-col md:flex-row h-full">
                          <div className="relative w-full md:w-1/2 h-64 md:h-auto">
                            <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">BEFORE</div>
                            <Image
                              src={item.beforeImage}
                              alt={`Before - ${item.title}`}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div className="relative w-full md:w-1/2 h-64 md:h-auto">
                            <div className="absolute top-2 left-2 bg-primary text-white text-xs px-2 py-1 rounded">AFTER</div>
                            <Image
                              src={item.afterImage}
                              alt={`After - ${item.title}`}
                              fill
                              className="object-cover"
                            />
                          </div>
                        </div>
                      </div>
                    )
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">No transformation stories available yet.</p>
              </div>
            )}
          </TabsContent>
          
          {/* Achievements Tab */}
          <TabsContent value="achievements" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {achievements.length > 0 ? (
                achievements.map((achievement, i) => (
                  <motion.div
                    key={i}
                    custom={i}
                    initial="hidden"
                    animate="visible"
                    variants={cardVariants}
                  >
                    <Card className="h-full border-2 hover:border-primary/30 transition-all">
                      <CardHeader>
                        <div className="flex items-center space-x-4">
                          <div className="p-3 rounded-full bg-primary/10 text-primary">
                            {getAchievementIcon(achievement.icon)}
                          </div>
                          <CardTitle>{achievement.title}</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground">{achievement.description}</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))
              ) : (
                <div className="col-span-3 text-center py-12">
                  <p className="text-muted-foreground">No achievements available yet.</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  )
}
