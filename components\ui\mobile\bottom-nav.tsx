"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Home, 
  Dumbbell, 
  User, 
  MessageSquare
} from 'lucide-react';

interface BottomNavProps {
  showWorkoutControls?: boolean;
  onPrevious?: () => void;
  onNext?: () => void;
  onComplete?: () => void;
  isLastExercise?: boolean;
}

export function BottomNav({ 
  showWorkoutControls = false,
  onPrevious,
  onNext,
  onComplete,
  isLastExercise = false
}: BottomNavProps) {
  const pathname = usePathname() || '';
  
  if (typeof window !== 'undefined' && window.innerWidth >= 768) {
    return null;
  }

  if (showWorkoutControls) {
    return (
      <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border z-50 px-2 py-2 flex justify-between items-center">
        <button
          onClick={onPrevious}
          className="flex flex-col items-center justify-center w-1/3 py-2 text-muted-foreground hover:text-primary"
          disabled={!onPrevious}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
          <span className="text-xs mt-1">Previous</span>
        </button>
        
        <button
          onClick={isLastExercise ? onComplete : onNext}
          className={`flex flex-col items-center justify-center w-1/3 py-2 ${isLastExercise ? 'text-green-500 hover:text-green-600' : 'text-primary hover:text-primary/90'}`}
          disabled={!onNext && !onComplete}
        >
          {isLastExercise ? (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
              <span className="text-xs mt-1">Complete</span>
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
              <span className="text-xs mt-1">Next</span>
            </>
          )}
        </button>
        
        <Link
          href="/dashboard"
          className="flex flex-col items-center justify-center w-1/3 py-2 text-muted-foreground hover:text-primary"
        >
          <Home className="h-6 w-6" />
          <span className="text-xs mt-1">Dashboard</span>
        </Link>
      </div>
    );
  }
  
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border z-50 px-2 py-2">
      <div className="flex justify-between items-center">
        <Link
          href="/dashboard"
          className={`flex flex-col items-center justify-center w-1/4 py-2 ${
            pathname === '/dashboard' ? 'text-primary' : 'text-muted-foreground hover:text-primary'
          }`}
        >
          <Home className="h-5 w-5" />
          <span className="text-xs mt-1">Home</span>
        </Link>
        
        <Link
          href="/dashboard/workouts/current"
          className={`flex flex-col items-center justify-center w-1/4 py-2 ${
            pathname.includes('/workouts') ? 'text-primary' : 'text-muted-foreground hover:text-primary'
          }`}
        >
          <Dumbbell className="h-5 w-5" />
          <span className="text-xs mt-1">Workouts</span>
        </Link>
        
        <Link
          href="/dashboard/messages"
          className={`flex flex-col items-center justify-center w-1/4 py-2 ${
            pathname.includes('/messages') ? 'text-primary' : 'text-muted-foreground hover:text-primary'
          }`}
        >
          <MessageSquare className="h-5 w-5" />
          <span className="text-xs mt-1">Messages</span>
        </Link>
        
        <Link
          href="/dashboard/profile"
          className={`flex flex-col items-center justify-center w-1/4 py-2 ${
            pathname.includes('/profile') ? 'text-primary' : 'text-muted-foreground hover:text-primary'
          }`}
        >
          <User className="h-5 w-5" />
          <span className="text-xs mt-1">Profile</span>
        </Link>
      </div>
    </div>
  );
}
