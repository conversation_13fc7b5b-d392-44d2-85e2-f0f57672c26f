'use client';

import { useState } from 'react';
import { CalendlyEmbed } from './calendly-embed';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Calendar, Clock, User } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface CalendlyModalProps {
  calendlyUrl: string;
  clientName: string;
  clientEmail: string;
  trainerName: string;
  trigger?: React.ReactNode;
}

export function CalendlyModal({
  calendlyUrl,
  clientName,
  clientEmail,
  trainerName,
  trigger,
}: CalendlyModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showQuickAccess, setShowQuickAccess] = useState(false);
  const { toast } = useToast();



  const handleEventScheduled = (event: any) => {
    console.log('Event scheduled:', event);
    toast({
      title: 'Session Scheduled!',
      description: `1:1 session with ${clientName} has been scheduled successfully.`,
    });
    setIsOpen(false);
  };

  const defaultTrigger = (
    <Button className="w-full">
      <Calendar className="mr-2 h-4 w-4" />
      Schedule Meeting
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5" />
                Schedule 1:1 Session with {clientName}
              </DialogTitle>
              <DialogDescription>
                Select a time that works for your 1:1 coaching session.
              </DialogDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const calendlyWindow = window.open(
                  `${calendlyUrl}?name=${encodeURIComponent(clientName)}&email=${encodeURIComponent(clientEmail)}`,
                  '_blank',
                  'width=800,height=700,scrollbars=yes,resizable=yes'
                );
                if (calendlyWindow) {
                  calendlyWindow.focus();
                }
                toast({
                  title: 'Calendly Opened',
                  description: 'Calendly has been opened in a new window for faster access.',
                });
              }}
              className="flex items-center gap-2"
            >
              <Calendar className="h-4 w-4" />
              Quick Access
            </Button>
          </div>
        </DialogHeader>
        <div className="flex-1 overflow-hidden">
          <CalendlyEmbed
            url={calendlyUrl}
            prefill={{
              email: clientEmail,
              name: clientName,
            }}
            onEventScheduled={handleEventScheduled}
            title=""
            description=""
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
