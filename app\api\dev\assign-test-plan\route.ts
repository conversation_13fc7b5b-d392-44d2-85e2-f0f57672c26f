import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(req: Request) {
  // Only allow in development mode
  if (process.env.NODE_ENV !== "development") {
    return new NextResponse("Not available in production", { status: 403 })
  }

  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }
  
  // Only trainers can assign plans
  if (session.user.role !== "trainer") {
    return NextResponse.json({ error: "Only trainers can assign plans" }, { status: 403 })
  }
  
  try {
    const { userId, trainingPlanId } = await req.json()
    
    if (!userId || !trainingPlanId) {
      return NextResponse.json({ error: "User ID and training plan ID are required" }, { status: 400 })
    }
    
    console.log(`Assigning training plan ${trainingPlanId} to user ${userId}`)
    
    // Get the client profile for this user
    const clientProfile = await prisma.clientProfile.findUnique({
      where: { userId },
      include: { user: true }
    })
    
    if (!clientProfile) {
      return NextResponse.json({ error: "Client profile not found" }, { status: 404 })
    }
    
    // Get the trainer profile
    const trainerProfile = await prisma.trainerProfile.findUnique({
      where: { userId: session.user.id }
    })
    
    if (!trainerProfile) {
      return NextResponse.json({ error: "Trainer profile not found" }, { status: 404 })
    }
    
    // Check if the client is assigned to this trainer
    if (clientProfile.assignedTrainerId !== trainerProfile.id) {
      // Update the client profile to assign it to this trainer
      await prisma.clientProfile.update({
        where: { id: clientProfile.id },
        data: { assignedTrainerId: trainerProfile.id }
      })
    }
    
    // Check if the training plan exists and belongs to the trainer
    const trainingPlan = await prisma.trainingPlanTemplate.findFirst({
      where: {
        id: trainingPlanId,
        trainerId: session.user.id
      }
    })
    
    if (!trainingPlan) {
      return NextResponse.json({ error: "Training plan not found or does not belong to this trainer" }, { status: 404 })
    }
    
    // Create a personalized training plan for the client
    const personalizedPlan = await prisma.trainingPlanTemplate.create({
      data: {
        title: `${trainingPlan.title} (Assigned)`,
        description: trainingPlan.description,
        difficulty: trainingPlan.difficulty,
        type: 'personalized',
        clientId: userId, // Use the user ID
        trainerId: session.user.id,
        weeks: trainingPlan.weeks
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        trainer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })
    
    return NextResponse.json({
      success: true,
      message: "Training plan assigned successfully",
      plan: personalizedPlan
    })
  } catch (error) {
    console.error("Error assigning training plan:", error)
    return NextResponse.json({ error: "Failed to assign training plan" }, { status: 500 })
  }
}
