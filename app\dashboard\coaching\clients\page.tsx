"use client"

import { format, formatDistanceToNow } from "date-fns"
import { 
  Search, 
  Plus, 
  MessageSquare, 
  <PERSON>mb<PERSON>, 
  Calendar,
  Clock,
  ArrowUpRight,
  MoreHorizontal,
  ChevronRight
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import React, { useState, useEffect } from "react"
import { toast } from "sonner"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface ClientData {
  id: string
  name: string
  email: string
  avatarUrl: string | null
  startDate: string
  status: "ACTIVE" | "PAUSED" | "TERMINATED"
  planName: string
  nextSession: {
    id: string
    title: string
    scheduledDate: string
  } | null
  lastWorkout: {
    id: string
    title: string
    completedDate: string
  } | null
  unreadMessages: number
}

export default function CoachingClientsPage() {
  const router = useRouter()
  const [clients, setClients] = useState<ClientData[]>([])
  const [filteredClients, setFilteredClients] = useState<ClientData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")

  useEffect(() => {
    fetchClients()
  }, [])

  useEffect(() => {
    filterClients()
  }, [searchQuery, activeTab, clients])

  const fetchClients = async () => {
    try {
      setLoading(true)
      
      // Use mock data instead of API call
      const mockClients: ClientData[] = [
        {
          id: "client1",
          name: "Jane Smith",
          email: "<EMAIL>",
          avatarUrl: null,
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          status: "ACTIVE",
          planName: "Premium 1:1 Coaching",
          nextSession: {
            id: "session1",
            title: "Weekly Check-in",
            scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
          },
          lastWorkout: {
            id: "workout1",
            title: "Full Body Strength",
            completedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          },
          unreadMessages: 2
        },
        {
          id: "client2",
          name: "Mike Johnson",
          email: "<EMAIL>",
          avatarUrl: null,
          startDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
          status: "ACTIVE",
          planName: "Premium 1:1 Coaching",
          nextSession: null,
          lastWorkout: {
            id: "workout2",
            title: "Upper Body Focus",
            completedDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          },
          unreadMessages: 0
        },
        {
          id: "client3",
          name: "Sarah Williams",
          email: "<EMAIL>",
          avatarUrl: null,
          startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          status: "PAUSED",
          planName: "Premium 1:1 Coaching",
          nextSession: null,
          lastWorkout: null,
          unreadMessages: 1
        }
      ]
      
      setClients(mockClients)
      setFilteredClients(mockClients)
    } catch (error) {
      console.error("Error setting up mock clients:", error)
      toast.error("Failed to load your coaching clients")
    } finally {
      setLoading(false)
    }
  }

  const filterClients = () => {
    let filtered = [...clients]
    
    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(client => 
        client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        client.email.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }
    
    // Filter by status
    if (activeTab !== "all") {
      filtered = filtered.filter(client => 
        activeTab === "active" ? client.status === "ACTIVE" : 
        activeTab === "paused" ? client.status === "PAUSED" :
        client.status === "TERMINATED"
      )
    }
    
    setFilteredClients(filtered)
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Coaching Clients</h1>
          <p className="text-muted-foreground">
            Manage your coaching clients
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-2">
          <Button asChild>
            <Link href="/dashboard/coaching/inquiries">
              <Plus className="w-4 h-4 mr-2" />
              Inquiries
            </Link>
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search clients..."
            className="pl-10"
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3 mb-6">
          <TabsTrigger value="all">All Clients</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="paused">Paused</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab}>
          {loading ? (
            <div className="flex justify-center items-center py-10">
              <div className="animate-spin h-8 w-8 border-t-2 border-primary rounded-full" />
              <span className="ml-2">Loading clients...</span>
            </div>
          ) : filteredClients.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10">
                <p className="text-muted-foreground">No clients found</p>
                {searchQuery ? (
                  <p className="text-sm text-muted-foreground mt-2">
                    Try adjusting your search
                  </p>
                ) : (
                  <p className="text-sm text-muted-foreground mt-2">
                    You don&apos;t have any {activeTab !== "all" ? activeTab : ""} coaching clients yet
                  </p>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredClients.map(client => (
                <Card key={client.id} className={`border-l-4 ${
                  client.status === "ACTIVE" ? "border-l-green-500" :
                  client.status === "PAUSED" ? "border-l-yellow-500" :
                  "border-l-red-500"
                }`}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row justify-between">
                      <div className="flex items-start gap-4">
                        <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                          {client.avatarUrl ? (
                            <img 
                              src={client.avatarUrl} 
                              alt={client.name} 
                              className="w-full h-full rounded-full object-cover" 
                            />
                          ) : (
                            <span className="text-lg font-bold">
                              {client.name.charAt(0)}
                            </span>
                          )}
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold text-lg">{client.name}</h3>
                            <Badge variant={
                              client.status === "ACTIVE" ? "default" :
                              client.status === "PAUSED" ? "secondary" :
                              "destructive"
                            }>
                              {client.status}
                            </Badge>
                          </div>
                          <p className="text-muted-foreground">{client.email}</p>
                          <p className="text-sm text-muted-foreground mt-1">
                            Client since {format(new Date(client.startDate), "MMMM d, yyyy")}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          asChild
                          className={client.unreadMessages > 0 ? "relative" : ""}
                        >
                          <Link href={`/dashboard/messages?clientId=${client.id}`}>
                            <MessageSquare className="w-4 h-4 mr-2" />
                            Message
                            {client.unreadMessages > 0 && (
                              <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                {client.unreadMessages}
                              </span>
                            )}
                          </Link>
                        </Button>
                        
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/dashboard/coaching/clients/${client.id}`}>
                            <span>View Profile</span>
                            <ChevronRight className="w-4 h-4 ml-1" />
                          </Link>
                        </Button>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => 
                              router.push(`/dashboard/coaching/clients/${client.id}/workouts/create`)
                            }>
                              <Dumbbell className="w-4 h-4 mr-2" />
                              Create Workout
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => 
                              router.push(`/dashboard/coaching/clients/${client.id}/sessions/create`)
                            }>
                              <Calendar className="w-4 h-4 mr-2" />
                              Schedule Session
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => {
                                // Would update client status in a real implementation
                                toast.success(`Client status updated`)
                              }}
                            >
                              {client.status === "ACTIVE" 
                                ? "Pause Coaching" 
                                : client.status === "PAUSED" 
                                  ? "Resume Coaching"
                                  : "Reactivate Coaching"
                              }
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                      <div className="border rounded-md p-3">
                        <h4 className="text-sm font-medium mb-2">Next Session</h4>
                        {client.nextSession ? (
                          <div>
                            <div className="font-medium">{client.nextSession.title}</div>
                            <div className="flex items-center text-sm text-muted-foreground mt-1">
                              <Calendar className="w-3 h-3 mr-1" /> 
                              {format(new Date(client.nextSession.scheduledDate), "EEE, MMM d")}
                              {" · "}
                              <Clock className="w-3 h-3 mx-1" />
                              {format(new Date(client.nextSession.scheduledDate), "h:mm a")}
                            </div>
                            <Button asChild variant="link" size="sm" className="mt-1 h-auto p-0">
                              <Link href={`/dashboard/coaching/sessions/${client.nextSession.id}`}>
                                View details
                                <ArrowUpRight className="w-3 h-3 ml-1" />
                              </Link>
                            </Button>
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground">No upcoming sessions</p>
                        )}
                      </div>
                      
                      <div className="border rounded-md p-3">
                        <h4 className="text-sm font-medium mb-2">Last Workout</h4>
                        {client.lastWorkout ? (
                          <div>
                            <div className="font-medium">{client.lastWorkout.title}</div>
                            <div className="text-sm text-muted-foreground mt-1">
                              Completed {formatDistanceToNow(new Date(client.lastWorkout.completedDate), { addSuffix: true })}
                            </div>
                            <Button asChild variant="link" size="sm" className="mt-1 h-auto p-0">
                              <Link href={`/dashboard/coaching/workouts/${client.lastWorkout.id}`}>
                                View workout
                                <ArrowUpRight className="w-3 h-3 ml-1" />
                              </Link>
                            </Button>
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground">No workouts completed</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
} 