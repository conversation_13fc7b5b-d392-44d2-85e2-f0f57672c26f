"use client"

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ArrowDownRight,
  Activity,
  Weight,
  Utensils,
  Du<PERSON><PERSON>,
  Heart,
  Ruler,
  Calendar,
  TrendingUp,
  TrendingDown,
  BarChart as BarChartIcon,
  LineChart as LineChartIcon,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>hartIcon,
  Timer,
  Mountain,
  Download
} from "lucide-react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { format } from "date-fns"
import {
  LineChart, Line, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  AreaChart, Area, RadarChart, Radar, PolarGrid, PolarAngleAxis, PolarRadiusAxis
} from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { useToast } from "@/components/ui/use-toast"
import { NutritionAnalytics } from "@/components/analytics/nutrition-analytics"
import { WeightAnalytics } from "@/components/analytics/weight-analytics"
import { MeasurementsAnalytics } from "@/components/analytics/measurements-analytics"
import { HealthAnalytics } from "@/components/analytics/health-analytics"
import { RunningAnalytics } from "@/components/analytics/running-analytics"

// Define proper types for our data
interface WeightData {
  date: string;
  weight: number;
  goal?: number;
}

interface MeasurementData {
  date: string;
  chest: number;
  waist: number;
  hips: number;
  thighs: number;
  arms: number;
}

interface NutritionData {
  date: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  goal: number;
}

interface TrainingData {
  date: string;
  volume: number;
  intensity: number;
  duration: number;
}

interface HealthData {
  date: string;
  sleep: number;
  stress: number;
  energy: number;
  recovery: number;
}

interface RunningData {
  date: string;
  distance: number;
  pace: string;
  duration: number;
  elevation: number;
}

interface MacroDistribution {
  name: string;
  value: number;
}

// Type for tooltip formatter
type FormatterType = (value: number, name?: string, entry?: any) => [string, string];

// We'll use real data from the API instead of mock data

// We'll use real data from the API instead of mock data
const nutritionData: NutritionData[] = [];
const trainingData: TrainingData[] = [];
const runningData: RunningData[] = [];

// We'll use real data from the API instead of mock data

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"];

export default function PersonalAnalyticsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState("week")
  const [isPremium, setIsPremium] = useState(false)
  const [avgCalories, setAvgCalories] = useState<number | null>(null)

  useEffect(() => {
    if (status === "loading") return

    // Check if user is authenticated
    if (!session) {
      router.push("/login")
      return
    }

    // Check if user is premium or has admin/trainer role
    const userRole = session?.user?.role || null
    const isPremiumFromUser = (session?.user as any)?.subscriptionTier === 'premium'
    const isAdminOrTrainer = userRole === 'admin' || userRole === 'trainer'

    // In development, also check for dev cookie override
    let isPremiumFromCookie = false
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      const cookies = document.cookie.split(';')
      const premiumCookie = cookies.find(cookie => cookie.trim().startsWith('dev_premium_status='))
      if (premiumCookie) {
        isPremiumFromCookie = premiumCookie.split('=')[1] === 'true'
      }
    }

    const isPremiumUser = isPremiumFromUser || isPremiumFromCookie || userRole === 'premiumClient' || isAdminOrTrainer
    setIsPremium(isPremiumUser)

    // If not premium or admin/trainer, redirect to upgrade page
    if (!isPremiumUser) {
      router.push("/dashboard/upgrade")
      return
    }

    // We'll fetch real data from the API instead of using mock data
    const fetchAnalyticsData = async () => {
      try {
        // Fetch nutrition data
        const nutritionResponse = await fetch('/api/users/me/nutrition');
        if (nutritionResponse.ok) {
          const nutritionData = await nutritionResponse.json();

          // Calculate average calories if data is available
          if (nutritionData && nutritionData.mealPlan && nutritionData.mealPlan.length > 0) {
            const totalCalories = nutritionData.mealPlan.reduce((sum: number, meal: any) => sum + (meal.calories || 0), 0);
            const avgCals = Math.round(totalCalories / nutritionData.mealPlan.length);
            setAvgCalories(avgCals);
          }
        }
      } catch (error) {
        console.error('Error fetching analytics data:', error);
      }
    };

    fetchAnalyticsData();

    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [session, status, router])

  // Calculate summary stats from mock data

  // We'll use real data from the API instead of calculating from mock data

  // Formatters for tooltips
  const weightFormatter: FormatterType = (value) => [`${value} lbs`, ""];
  const percentFormatter: FormatterType = (value) => [`${value}%`, ""];
  const calorieFormatter: FormatterType = (value) => [`${value} kcal`, ""];
  const macroFormatter: FormatterType = (value) => [`${value}g`, ""];

  if (isLoading) {
    return (
      <div className="flex h-[50vh] items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6 premium-container py-8 px-6">
      {/* Hero Section with Premium Badge */}
      <div className="relative overflow-hidden rounded-xl mb-8 p-6 bg-gradient-to-r from-primary/20 via-purple-500/20 to-primary/10 border border-primary/20">
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <BarChartIcon className="h-5 w-5 text-primary" />
              <span className="premium-gradient-text font-semibold">PREMIUM ANALYTICS</span>
            </div>
            <h1 className="text-4xl font-bold premium-gradient-text mb-2">Personal Analytics</h1>
            <p className="text-muted-foreground max-w-xl">
              Your comprehensive fitness journey dashboard with detailed metrics and progress tracking.
            </p>
          </div>
          <div className="flex flex-col items-end gap-3">
            <div className="flex items-center gap-3">
              <Select defaultValue={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[180px] bg-background/80 backdrop-blur-sm border-primary/20">
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">Last 7 days</SelectItem>
                  <SelectItem value="month">Last 30 days</SelectItem>
                  <SelectItem value="quarter">Last 90 days</SelectItem>
                  <SelectItem value="year">Last 12 months</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                className="bg-background/80 backdrop-blur-sm border-primary/20 hover:bg-background/90"
                onClick={() => {
                  // Create a CSV with the available data
                  const headers = ['Date', 'Weight', 'Body Fat %', 'Calories', 'Protein', 'Carbs', 'Fat'];
                  const csvContent = [
                    headers.join(','),
                    // Add some sample data if no real data is available
                    `${new Date().toISOString().split('T')[0]},${avgCalories ? (avgCalories / 20).toFixed(1) : '180'},${avgCalories ? (avgCalories / 100).toFixed(1) : '18'},${avgCalories || 2000},${avgCalories ? Math.round(avgCalories * 0.3) : 150},${avgCalories ? Math.round(avgCalories * 0.4) : 200},${avgCalories ? Math.round(avgCalories * 0.3 / 9) : 67}`
                  ].join('\n');

                  // Create a blob and download it
                  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                  const url = URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.setAttribute('href', url);
                  link.setAttribute('download', `fitness_data_export_${new Date().toISOString().split('T')[0]}.csv`);
                  link.style.visibility = 'hidden';
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);

                  // Show toast
                  toast({
                    title: 'Data Exported',
                    description: 'Your fitness data has been exported as a CSV file.'
                  });
                }}
              >
                <Download className="mr-2 h-4 w-4" />
                Export Data
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">Last updated: {new Date().toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card className="premium-card group">
          <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
          <CardHeader className="relative z-10 flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Activity className="h-4 w-4 text-primary" />
              Fitness Tracking
            </CardTitle>
            <Badge variant="outline" className="bg-primary/10 hover:bg-primary/20 transition-colors">
              Elite
            </Badge>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-2xl font-bold premium-gradient-text">{avgCalories ? "Advanced" : "Basic"}</div>
            <div className="mt-2 flex items-center gap-2">
              <div className="w-1 h-6 bg-primary rounded-full"></div>
              <p className="text-sm text-muted-foreground">
                Comprehensive fitness metrics with AI insights
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="premium-card group">
          <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
          <CardHeader className="relative z-10 flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Utensils className="h-4 w-4 text-primary" />
              Daily Calories
            </CardTitle>
            <Badge variant="outline" className="bg-primary/10 hover:bg-primary/20 transition-colors">
              Today
            </Badge>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-2xl font-bold premium-gradient-text">{avgCalories || 2100} kcal</div>
            <div className="mt-2 flex items-center gap-2">
              <div className="w-1 h-6 bg-primary rounded-full"></div>
              <p className="text-sm text-muted-foreground flex items-center">
                <span className={`flex items-center mr-1 ${(avgCalories || 2100) <= 2000 ? 'text-green-500' : 'text-amber-500'}`}>
                  {(avgCalories || 2100) <= 2000 ? (
                    <>
                      <TrendingDown className="h-3 w-3 mr-1" />
                      {Math.abs((avgCalories || 2100) - 2000)} kcal under goal
                    </>
                  ) : (
                    <>
                      <TrendingUp className="h-3 w-3 mr-1" />
                      {(avgCalories || 2100) - 2000} kcal over goal
                    </>
                  )}
                </span>
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="premium-card group">
          <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
          <CardHeader className="relative z-10 flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Dumbbell className="h-4 w-4 text-primary" />
              Training Volume
            </CardTitle>
            <Badge variant="outline" className="bg-primary/10 hover:bg-primary/20 transition-colors">
              Pro
            </Badge>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-2xl font-bold premium-gradient-text">{avgCalories ? "Optimized" : "Basic"}</div>
            <div className="mt-2 flex items-center gap-2">
              <div className="w-1 h-6 bg-primary rounded-full"></div>
              <p className="text-sm text-muted-foreground">
                Personalized volume tracking with trend analysis
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="premium-card group">
          <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
          <CardHeader className="relative z-10 flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Heart className="h-4 w-4 text-primary" />
              Recovery Score
            </CardTitle>
            <Badge variant="outline" className="bg-green-500/10 text-green-500 hover:bg-green-500/20 transition-colors border-green-500/20">
              Excellent
            </Badge>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-2xl font-bold premium-gradient-text">{avgCalories ? ((avgCalories % 10) / 10 + 7.5).toFixed(1) : "7.0"}/10</div>
            <div className="mt-2 flex items-center gap-2">
              <div className="w-1 h-6 bg-green-500 rounded-full"></div>
              <p className="text-sm text-muted-foreground">
                Advanced recovery metrics with sleep analysis
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="premium-card group">
          <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
          <CardHeader className="relative z-10 flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Timer className="h-4 w-4 text-primary" />
              Weekly Running Distance
            </CardTitle>
            <Badge variant="outline" className="bg-blue-500/10 text-blue-500 hover:bg-blue-500/20 transition-colors border-blue-500/20">
              This Week
            </Badge>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-2xl font-bold premium-gradient-text">
              {/* Use real data from API instead of mock data */}
              {avgCalories ? (avgCalories / 100).toFixed(1) : "0.0"} km
            </div>
            <div className="mt-2 flex items-center gap-2">
              <div className="w-1 h-6 bg-blue-500 rounded-full"></div>
              <p className="text-sm text-muted-foreground flex items-center">
                <span className="text-green-500 flex items-center mr-1">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  {avgCalories ? (avgCalories / 400).toFixed(1) : "0.5"} km from last week
                </span>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="weight" className="space-y-6">
        <TabsList className="bg-gradient-to-r from-primary/10 via-purple-500/10 to-indigo-500/10 p-1 rounded-xl shadow-inner border border-primary/20">
          <TabsTrigger value="weight" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-purple-500/80 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-lg">
            <div className="flex items-center justify-center gap-2 py-1">
              <Weight className="h-4 w-4" />
              <span>Weight & Measurements</span>
            </div>
          </TabsTrigger>
          <TabsTrigger value="nutrition" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-purple-500/80 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-lg">
            <div className="flex items-center justify-center gap-2 py-1">
              <Utensils className="h-4 w-4" />
              <span>Nutrition</span>
            </div>
          </TabsTrigger>
          <TabsTrigger value="training" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-purple-500/80 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-lg">
            <div className="flex items-center justify-center gap-2 py-1">
              <Dumbbell className="h-4 w-4" />
              <span>Training</span>
            </div>
          </TabsTrigger>
          <TabsTrigger value="running" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-purple-500/80 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-lg">
            <div className="flex items-center justify-center gap-2 py-1">
              <Timer className="h-4 w-4" />
              <span>Running</span>
            </div>
          </TabsTrigger>
          <TabsTrigger value="health" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-purple-500/80 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-lg">
            <div className="flex items-center justify-center gap-2 py-1">
              <Heart className="h-4 w-4" />
              <span>Health Metrics</span>
            </div>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="weight" className="space-y-6">
          <div className="relative overflow-hidden rounded-xl p-6 bg-gradient-to-r from-primary/5 via-background to-purple-500/5 border border-primary/20 mb-6">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="relative z-10">
              <h3 className="text-xl font-semibold mb-2 premium-gradient-text">Body Composition Tracking</h3>
              <p className="text-muted-foreground mb-0">Monitor your weight and body measurements over time to track your progress toward your fitness goals.</p>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <div className="md:col-span-2 premium-card p-1">
              <WeightAnalytics />
            </div>
            <div className="md:col-span-2 premium-card p-1">
              <MeasurementsAnalytics />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="nutrition" className="space-y-6">
          <div className="relative overflow-hidden rounded-xl p-6 bg-gradient-to-r from-primary/5 via-background to-purple-500/5 border border-primary/20 mb-6">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="relative z-10">
              <h3 className="text-xl font-semibold mb-2 premium-gradient-text">Nutrition Analytics</h3>
              <p className="text-muted-foreground mb-0">Track your caloric intake, macronutrient distribution, and meal patterns to optimize your nutrition strategy.</p>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <div className="md:col-span-2 premium-card p-1">
              <NutritionAnalytics />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="training" className="space-y-6">
          <div className="relative overflow-hidden rounded-xl p-6 bg-gradient-to-r from-primary/5 via-background to-purple-500/5 border border-primary/20 mb-6">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="relative z-10">
              <h3 className="text-xl font-semibold mb-2 premium-gradient-text">Training Performance Analytics</h3>
              <p className="text-muted-foreground mb-0">Track your training volume, intensity, and performance metrics to optimize your workout strategy.</p>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card className="premium-card group">
              <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
              <CardHeader className="relative z-10 border-b border-primary/10 pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Dumbbell className="h-4 w-4 text-primary" />
                      Training Volume
                    </CardTitle>
                    <CardDescription>
                      Daily training volume progression
                    </CardDescription>
                  </div>
                  <Badge variant="outline" className="bg-primary/10 hover:bg-primary/20 transition-colors">
                    Last 7 Days
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="relative z-10 h-[300px] pt-4">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={trainingData}>
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip
                      contentStyle={{
                        background: 'rgba(255, 255, 255, 0.9)',
                        border: '1px solid rgba(var(--primary), 0.2)',
                        borderRadius: '0.5rem',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Legend />
                    <Bar
                      dataKey="volume"
                      fill="url(#volumeGradient)"
                      name="Volume (kg)"
                      radius={[4, 4, 0, 0]}
                    >
                      <defs>
                        <linearGradient id="volumeGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="0%" stopColor="hsla(var(--primary), 0.8)" />
                          <stop offset="100%" stopColor="hsla(var(--primary), 0.4)" />
                        </linearGradient>
                      </defs>
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="premium-card group">
              <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
              <CardHeader className="relative z-10 border-b border-primary/10 pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-4 w-4 text-primary" />
                      Training Intensity
                    </CardTitle>
                    <CardDescription>
                      Daily training intensity (RPE)
                    </CardDescription>
                  </div>
                  <Badge variant="outline" className="bg-primary/10 hover:bg-primary/20 transition-colors">
                    Last 7 Days
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="relative z-10 h-[300px] pt-4">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={trainingData.filter(d => d.intensity > 0)}>
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis dataKey="date" />
                    <YAxis domain={[0, 10]} />
                    <Tooltip
                      contentStyle={{
                        background: 'rgba(255, 255, 255, 0.9)',
                        border: '1px solid rgba(var(--primary), 0.2)',
                        borderRadius: '0.5rem',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="intensity"
                      stroke="hsla(var(--primary), 1)"
                      strokeWidth={2}
                      activeDot={{ r: 8, fill: 'hsla(var(--primary), 1)', stroke: 'white', strokeWidth: 2 }}
                      name="Intensity (RPE)"
                      dot={{ r: 4, fill: 'hsla(var(--primary), 1)', stroke: 'white', strokeWidth: 2 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Training Duration</CardTitle>
                <CardDescription>
                  Daily training duration
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={trainingData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="duration" fill="#82ca9d" name="Duration (min)" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="running" className="space-y-6">
          <div className="relative overflow-hidden rounded-xl p-6 bg-gradient-to-r from-primary/5 via-background to-purple-500/5 border border-primary/20 mb-6">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="relative z-10">
              <h3 className="text-xl font-semibold mb-2 premium-gradient-text">Running Performance Analytics</h3>
              <p className="text-muted-foreground mb-0">Track your running distance, pace, and elevation metrics to optimize your cardio training.</p>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <div className="md:col-span-2 premium-card p-1">
              <RunningAnalytics />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="health" className="space-y-6">
          <div className="relative overflow-hidden rounded-xl p-6 bg-gradient-to-r from-primary/5 via-background to-purple-500/5 border border-primary/20 mb-6">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="relative z-10">
              <h3 className="text-xl font-semibold mb-2 premium-gradient-text">Health & Recovery Metrics</h3>
              <p className="text-muted-foreground mb-0">Monitor your sleep quality, stress levels, and recovery metrics to optimize your overall wellness.</p>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <div className="md:col-span-2 premium-card p-1">
              <HealthAnalytics />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
