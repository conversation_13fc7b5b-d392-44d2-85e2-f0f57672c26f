import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: Request) {
  const session = await getServerSession(authOptions)

  // Check authentication
  if (!session?.user) {
    return new NextResponse("Unauthorized", { status: 401 })
  }

  // Only trainers and admins can view coaching inquiries
  if (session.user.role !== "trainer" && session.user.role !== "admin") {
    return new NextResponse("Forbidden", { status: 403 })
  }

  try {
    // For trainers, show only inquiries directed to them
    // For admins, show all inquiries
    const inquiries = await prisma.coachingInquiry.findMany({
      where: session.user.role === "trainer" 
        ? { athleteId: session.user.id } 
        : undefined,
      orderBy: [
        { status: 'asc' }, // PENDING first, then APPROVED, then REJECTED
        { createdAt: 'desc' } // Newest first
      ]
    })

    return NextResponse.json(inquiries)
  } catch (error) {
    console.error("[COACHING_INQUIRIES_ERROR]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
} 