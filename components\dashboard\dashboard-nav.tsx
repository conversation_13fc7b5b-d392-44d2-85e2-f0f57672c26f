"use client"

import {
  LayoutDashboard,
  ShoppingCart,
  MessageCircle,
  Library,
  Settings,
  Activity,
  Dumbbell,
  Apple,
  Users,
  CircleDollarSign,
  Shield,
  BarChart,
  <PERSON><PERSON>,
  MessageSquare
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"

interface DashboardNavProps {
  effectiveRole: string;
}

// Navigation Item interface
interface NavItem {
  title: string
  href: string
  icon: JSX.Element
  roles: string[] // Which roles can see this item
}

export function DashboardNav({ effectiveRole }: DashboardNavProps) {
  const pathname = usePathname()

  // Define navigation items with role restrictions
  const navItems: NavItem[] = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: <LayoutDashboard className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer", "client"]
    },
    {
      title: "Profile",
      href: "/dashboard/profile",
      icon: <Activity className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer", "client"]
    },
    {
      title: "My Progress",
      href: "/dashboard/client-dashboard",
      icon: <BarChart className="mr-2 h-4 w-4" />,
      roles: ["client"]
    },
    {
      title: "Shop",
      href: "/dashboard/shop",
      icon: <ShoppingCart className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer", "client"]
    },
    {
      title: "My Library",
      href: "/dashboard/library",
      icon: <Library className="mr-2 h-4 w-4" />,
      roles: ["client"]
    },
    {
      title: "My Clients",
      href: "/dashboard/clients",
      icon: <Users className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer"]
    },
    {
      title: "Training Plans",
      href: "/dashboard/training-plans",
      icon: <Dumbbell className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer"]
    },
    {
      title: "Diet Plans",
      href: "/dashboard/diet-plans",
      icon: <Apple className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer"]
    },
    {
      title: "Earnings",
      href: "/dashboard/earnings",
      icon: <CircleDollarSign className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer"]
    },

    {
      title: "Account Settings",
      href: "/dashboard/settings",
      icon: <Settings className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer", "client"]
    },
    {
      title: "My Subscriptions",
      href: "/dashboard/my-subscriptions",
      icon: <CircleDollarSign className="mr-2 h-4 w-4" />,
      roles: ["client"]
    },
    {
      title: "Subscription Management",
      href: "/dashboard/subscription-management",
      icon: <Shield className="mr-2 h-4 w-4" />,
      roles: ["trainer", "admin"]
    },
    {
      title: "Brand & Landing Page",
      href: "/dashboard/profile/customization",
      icon: <Palette className="mr-2 h-4 w-4" />,
      roles: ["trainer"]
    },
    {
      title: "Monitoring",
      href: "/dashboard/monitoring",
      icon: <Shield className="mr-2 h-4 w-4" />,
      roles: ["admin"]
    },
    {
      title: "User Management",
      href: "/dashboard/user-management",
      icon: <Users className="mr-2 h-4 w-4" />,
      roles: ["admin"]
    },
    {
      title: "Test Chat Setup",
      href: "/test-chat-simple",
      icon: <MessageSquare className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer", "client"]
    },
    {
      title: "Mock Chat",
      href: "/mock-chat",
      icon: <MessageSquare className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer", "client"]
    },
    {
      title: "Direct Chat Setup",
      href: "/direct-test",
      icon: <MessageSquare className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer", "client"]
    },
    {
      title: "Real Chat Test",
      href: "/real-chat-test",
      icon: <MessageSquare className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer", "client"]
    },
    {
      title: "Standalone Chat",
      href: "/standalone-chat",
      icon: <MessageSquare className="mr-2 h-4 w-4" />,
      roles: ["admin", "trainer", "client"]
    }
  ]

  // Filter nav items based on user role
  const filteredNavItems = navItems.filter(item =>
    item.roles.includes(effectiveRole)
  )

  return (
    <ScrollArea className="my-4 h-[calc(100vh-8rem)] pb-10">
      <div className="flex flex-col space-y-1 p-2">
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-4 p-2 text-xs text-center bg-yellow-100 dark:bg-yellow-900 rounded-md">
            Role: <strong className="capitalize">{effectiveRole}</strong>
          </div>
        )}

        {filteredNavItems.map((item, index) => (
          <Button
            key={index}
            variant={pathname === item.href ? "secondary" : "ghost"}
            className="w-full justify-start"
            asChild
          >
            <Link href={item.href}>
              {item.icon}
              {item.title}
            </Link>
          </Button>
        ))}
      </div>
    </ScrollArea>
  )
}

