import { notFound } from "next/navigation"
import { getServerSession } from "next-auth"
import { TemplateExerciseForm } from "@/components/forms/template-exercise-form"
import { authOptions } from "@/lib/auth"

export default async function NewTemplateExercisePage() {
  const session = await getServerSession(authOptions)
  if (!session?.user || session.user.role !== "admin") {
    return notFound()
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Add New Template Exercise</h1>
      <TemplateExerciseForm />
    </div>
  )
} 