// API route handler for exercises
import { Exercise<PERSON>and<PERSON> } from "@/lib/api/exercise-handler";

const handler = new ExerciseHandler();

export async function GET(req: Request) {
  return handler.handleGet(req);
}

export async function POST(req: Request) {
  return handler.handlePost(req);
}

export async function PATCH(req: Request) {
  return handler.handlePut(req);
}

export async function DELETE(req: Request) {
  return handler.handleDelete(req);
}