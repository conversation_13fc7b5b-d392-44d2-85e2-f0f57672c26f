import { PrismaClient } from '@prisma/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('Setting up demo trainer...')

  // Create "Demo Trainer" account
  const demoTrainerTheme = {
    primaryColor: "#4A90E2",
    secondaryColor: "#50E3C2",
    logoUrl: "https://images.unsplash.com/photo-*************-640c2de311b2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    bannerUrl: "https://images.unsplash.com/photo-*************-696072aa579a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
    fontFamily: "'Poppins', sans-serif"
  }

  const demoSocialLinks = {
    instagram: "https://instagram.com/demotrainer",
    youtube: "https://youtube.com/demotrainer",
    twitter: "https://twitter.com/demotrainer",
    website: "https://demotrainer.com"
  }

  const demoPassword = await hash('password123', 10)

  // Create the demo trainer user
  const demoTrainer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      socialLinks: JSON.stringify(demoSocialLinks),
      themeSettings: JSON.stringify(demoTrainerTheme),
      slug: 'demo-trainer'
    },
    create: {
      email: '<EMAIL>',
      name: 'Demo Trainer',
      password: demoPassword,
      role: 'trainer',
      bio: 'I am a demo trainer account with a focus on strength training and nutrition. I help clients achieve their fitness goals through personalized training programs and nutrition guidance.',
      avatarUrl: 'https://images.unsplash.com/photo-*************-43a690fe2243?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      socialLinks: JSON.stringify(demoSocialLinks),
      themeSettings: JSON.stringify(demoTrainerTheme),
      slug: 'demo-trainer'
    }
  })

  console.log('Created Demo Trainer profile')

  // Create trainer settings
  await prisma.trainerSettings.upsert({
    where: { userId: demoTrainer.id },
    update: {
      serviceType: 'full-service',
      enableStore: true,
      enableSubscriptions: true,
      enablePremiumCoaching: true
    },
    create: {
      userId: demoTrainer.id,
      serviceType: 'full-service',
      enableStore: true,
      enableSubscriptions: true,
      enablePremiumCoaching: true
    }
  })

  console.log('Created trainer settings')

  // Create some products for the demo trainer
  await prisma.product.upsert({
    where: { id: 'demo-product-1' },
    update: {
      title: '6-Week Strength Program',
      description: 'A comprehensive 6-week strength training program designed to help you build muscle and increase strength.',
      price: 49.99,
      thumbnailUrl: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      athleteId: demoTrainer.id,
      type: 'digital'
    },
    create: {
      id: 'demo-product-1',
      title: '6-Week Strength Program',
      description: 'A comprehensive 6-week strength training program designed to help you build muscle and increase strength.',
      price: 49.99,
      thumbnailUrl: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      athleteId: demoTrainer.id,
      type: 'digital',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  })

  await prisma.product.upsert({
    where: { id: 'demo-product-2' },
    update: {
      title: 'Nutrition Guide',
      description: 'A complete nutrition guide with meal plans and recipes to help you reach your fitness goals.',
      price: 29.99,
      thumbnailUrl: 'https://images.unsplash.com/photo-1498837167922-ddd27525d352?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      athleteId: demoTrainer.id,
      type: 'digital'
    },
    create: {
      id: 'demo-product-2',
      title: 'Nutrition Guide',
      description: 'A complete nutrition guide with meal plans and recipes to help you reach your fitness goals.',
      price: 29.99,
      thumbnailUrl: 'https://images.unsplash.com/photo-1498837167922-ddd27525d352?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      athleteId: demoTrainer.id,
      type: 'digital',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  })

  console.log('Created demo products')

  console.log('Demo trainer setup complete!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
