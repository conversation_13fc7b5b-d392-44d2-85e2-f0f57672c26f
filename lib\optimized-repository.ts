/**
 * Base repository with optimized query patterns
 * This provides common patterns for efficient database access
 */

import { prisma } from './prisma';
import { queryCache } from './cache';
import { Prisma } from '@prisma/client';

// Type for pagination parameters
export type PaginationParams = {
  page?: number;
  pageSize?: number;
};

// Type for cache options
export type CacheOptions = {
  ttl?: number;
  useCache?: boolean;
};

// Default cache TTL values (in milliseconds)
const DEFAULT_CACHE_TTL = 60 * 1000; // 1 minute
const LONG_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Base repository class with optimized query patterns
 */
export class OptimizedRepository<T extends object> {
  constructor(
    protected model: any,
    protected defaultCacheTTL: number = DEFAULT_CACHE_TTL
  ) {}

  /**
   * Find a record by ID with optional caching
   */
  async findById(
    id: string,
    options: {
      select?: any;
      include?: any;
      cacheOptions?: CacheOptions;
    } = {}
  ): Promise<T | null> {
    const { select, include, cacheOptions = {} } = options;
    const { useCache = true, ttl = this.defaultCacheTTL } = cacheOptions;
    
    const cacheKey = `${this.model.name}:id:${id}:${JSON.stringify({ select, include })}`;
    
    if (useCache) {
      return queryCache.getOrSet(
        cacheKey,
        () => this.executeQuery({ id }, { select, include }),
        ttl
      );
    }
    
    return this.executeQuery({ id }, { select, include });
  }

  /**
   * Find many records with pagination and optional caching
   */
  async findMany(
    options: {
      where?: any;
      orderBy?: any;
      pagination?: PaginationParams;
      select?: any;
      include?: any;
      cacheOptions?: CacheOptions;
    } = {}
  ): Promise<T[]> {
    const {
      where = {},
      orderBy,
      pagination,
      select,
      include,
      cacheOptions = {},
    } = options;
    
    const { useCache = true, ttl = this.defaultCacheTTL } = cacheOptions;
    
    // Apply pagination if provided
    const skip = pagination?.page && pagination?.pageSize
      ? (pagination.page - 1) * pagination.pageSize
      : undefined;
    
    const take = pagination?.pageSize;
    
    const queryOptions = {
      where,
      orderBy,
      skip,
      take,
      select,
      include,
    };
    
    const cacheKey = `${this.model.name}:many:${JSON.stringify(queryOptions)}`;
    
    if (useCache) {
      return queryCache.getOrSet(
        cacheKey,
        () => this.executeFindManyQuery(queryOptions),
        ttl
      );
    }
    
    return this.executeFindManyQuery(queryOptions);
  }

  /**
   * Count records with optional caching
   */
  async count(
    where: any = {},
    cacheOptions: CacheOptions = {}
  ): Promise<number> {
    const { useCache = true, ttl = this.defaultCacheTTL } = cacheOptions;
    
    const cacheKey = `${this.model.name}:count:${JSON.stringify(where)}`;
    
    if (useCache) {
      return queryCache.getOrSet(
        cacheKey,
        () => prisma[this.model.name].count({ where }),
        ttl
      );
    }
    
    return prisma[this.model.name].count({ where });
  }

  /**
   * Execute a findUnique query
   */
  private async executeQuery(
    where: any,
    options: { select?: any; include?: any } = {}
  ): Promise<T | null> {
    const { select, include } = options;
    
    return prisma[this.model.name].findUnique({
      where,
      select,
      include,
    }) as Promise<T | null>;
  }

  /**
   * Execute a findMany query
   */
  private async executeFindManyQuery(options: any): Promise<T[]> {
    return prisma[this.model.name].findMany(options) as Promise<T[]>;
  }
}
