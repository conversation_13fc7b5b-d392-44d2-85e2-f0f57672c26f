import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rism<PERSON> } from "@prisma/client"
import { IClientRepository, ClientRepository } from "../repositories/client-repository"

export class ClientService {
  private static repository: IClientRepository = new ClientRepository()

  /**
   * Set a custom repository implementation (useful for testing)
   */
  static setRepository(repository: IClientRepository) {
    this.repository = repository
  }

  /**
   * Get client counts for a trainer
   */
  static async getClientCounts(trainerId: string): Promise<{ totalClients: number; activeClients: number }> {
    return this.repository.getClientCounts(trainerId)
  }

  /**
   * Find a client by ID
   */
  static async findById(id: string): Promise<ClientProfile | null> {
    return this.repository.findById(id)
  }

  /**
   * Find a client by ID with details
   */
  static async findByIdWithDetails(id: string): Promise<any | null> {
    return this.repository.findByIdWithDetails(id)
  }

  /**
   * Create a new client
   */
  static async create(data: Prisma.ClientProfileCreateInput): Promise<ClientProfile> {
    return this.repository.create(data)
  }

  /**
   * Update a client
   */
  static async update(id: string, data: Partial<ClientProfile>): Promise<ClientProfile> {
    return this.repository.update(id, data)
  }

  /**
   * Delete a client
   */
  static async delete(id: string): Promise<ClientProfile> {
    return this.repository.delete(id)
  }

  /**
   * Find multiple clients
   */
  static async findMany(params?: {
    skip?: number
    take?: number
    where?: Prisma.ClientProfileWhereInput
    orderBy?: Prisma.ClientProfileOrderByWithRelationInput
    include?: Prisma.ClientProfileInclude
  }): Promise<ClientProfile[]> {
    return this.repository.findMany(params)
  }

  /**
   * Find clients by trainer ID
   */
  static async findByTrainerId(trainerId: string): Promise<ClientProfile[]> {
    return this.repository.findByTrainerId(trainerId)
  }

  /**
   * Count clients
   */
  static async count(where?: Prisma.ClientProfileWhereInput): Promise<number> {
    return this.repository.count(where)
  }

  /**
   * Assign a training plan to a client
   */
  static async assignTrainingPlan(
    clientId: string,
    trainingPlanId: string,
    trainerId: string,
    customName?: string,
    planData?: any
  ): Promise<any> {
    return this.repository.assignTrainingPlan(clientId, trainingPlanId, trainerId, customName, planData)
  }
}
