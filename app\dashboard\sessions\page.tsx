'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { format, isPast, isToday } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Calendar, Clock, Video, ExternalLink, Calendar as CalendarIcon, MoreHorizontal } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { <PERSON>tar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';

interface CoachingSession {
  id: string;
  title: string;
  description: string | null;
  scheduledDate: string;
  duration: number;
  status: string;
  type: string;
  location: string | null;
  notes: string | null;
  videoConferenceUrl: string | null;
  coachingRelationship: {
    trainer: {
      id: string;
      name: string;
      email: string;
      avatarUrl: string | null;
    };
  };
}

export default function SessionsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [upcomingSessions, setUpcomingSessions] = useState<CoachingSession[]>([]);
  const [pastSessions, setPastSessions] = useState<CoachingSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [cancelReason, setCancelReason] = useState('');
  const [sessionToCancel, setSessionToCancel] = useState<string | null>(null);
  const [isCancelling, setIsCancelling] = useState(false);

  useEffect(() => {
    // For demo purposes, we'll use mock data if API fails
    const mockUpcomingSessions: CoachingSession[] = [
      {
        id: '1',
        title: 'Form Check & Progress Review',
        description: 'Review your form and progress on key exercises',
        scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
        duration: 45,
        status: 'scheduled',
        type: 'video',
        location: null,
        notes: null,
        videoConferenceUrl: 'https://meet.google.com/abc-defg-hij',
        coachingRelationship: {
          trainer: {
            id: '1',
            name: 'Coach John',
            email: '<EMAIL>',
            avatarUrl: null,
          },
        },
      },
      {
        id: '2',
        title: 'Nutrition Plan Adjustment',
        description: 'Adjust your nutrition plan based on recent progress',
        scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        duration: 30,
        status: 'scheduled',
        type: 'video',
        location: null,
        notes: null,
        videoConferenceUrl: null,
        coachingRelationship: {
          trainer: {
            id: '1',
            name: 'Coach John',
            email: '<EMAIL>',
            avatarUrl: null,
          },
        },
      },
    ];

    const mockPastSessions: CoachingSession[] = [
      {
        id: '3',
        title: 'Initial Assessment',
        description: 'Initial fitness assessment and goal setting',
        scheduledDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        duration: 60,
        status: 'completed',
        type: 'video',
        location: null,
        notes: 'Client showed good form on basic exercises. Need to work on squat depth and shoulder mobility.',
        videoConferenceUrl: null,
        coachingRelationship: {
          trainer: {
            id: '1',
            name: 'Coach John',
            email: '<EMAIL>',
            avatarUrl: null,
          },
        },
      },
    ];

    const fetchSessions = async () => {
      try {
        setIsLoading(true);

        // Fetch upcoming sessions
        const upcomingResponse = await fetch('/api/coaching/sessions?action=upcoming');
        if (!upcomingResponse.ok) {
          throw new Error('Failed to fetch upcoming sessions');
        }
        const upcomingData = await upcomingResponse.json();
        setUpcomingSessions(upcomingData.sessions?.length ? upcomingData.sessions : mockUpcomingSessions);

        // Fetch past sessions
        const pastResponse = await fetch('/api/coaching/sessions?action=past');
        if (!pastResponse.ok) {
          throw new Error('Failed to fetch past sessions');
        }
        const pastData = await pastResponse.json();
        setPastSessions(pastData.sessions?.length ? pastData.sessions : mockPastSessions);
      } catch (error) {
        console.error('Error fetching sessions:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load your sessions. Using demo data instead.',
        });

        // Use mock data if API fails
        setUpcomingSessions(mockUpcomingSessions);
        setPastSessions(mockPastSessions);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSessions();
  }, [toast]);

  const handleJoinSession = (session: CoachingSession) => {
    if (session.videoConferenceUrl) {
      window.open(session.videoConferenceUrl, '_blank');
    } else {
      toast({
        title: 'No video link available',
        description: 'The video conference link for this session is not available yet.',
      });
    }
  };

  const handleCancelSession = async () => {
    if (!sessionToCancel) return;

    try {
      setIsCancelling(true);

      const response = await fetch('/api/coaching/sessions?action=cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: sessionToCancel,
          reason: cancelReason,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to cancel session');
      }

      // Update the local state
      setUpcomingSessions(prevSessions =>
        prevSessions.map(session =>
          session.id === sessionToCancel
            ? { ...session, status: 'cancelled' }
            : session
        )
      );

      toast({
        title: 'Session cancelled',
        description: 'Your coaching session has been cancelled successfully.',
      });

      // Reset the form
      setSessionToCancel(null);
      setCancelReason('');
    } catch (error) {
      console.error('Error cancelling session:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to cancel the session. Please try again later.',
      });
    } finally {
      setIsCancelling(false);
    }
  };

  const renderSessionCard = (session: CoachingSession) => {
    const sessionDate = new Date(session.scheduledDate);
    const isPastSession = isPast(sessionDate) && !isToday(sessionDate);
    const isTodays = isToday(sessionDate);

    return (
      <Card key={session.id} className="overflow-hidden premium-card group">
        <div className="absolute inset-0 bg-grid-pattern opacity-5 group-hover:opacity-10 transition-opacity"></div>
        <CardHeader className="relative z-10 pb-3 border-b border-primary/10">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{session.title}</CardTitle>
              <CardDescription>
                {session.description || 'No description provided'}
              </CardDescription>
            </div>
            <Badge
              variant={
                session.status === 'cancelled'
                  ? 'destructive'
                  : isTodays
                  ? 'default'
                  : 'outline'
              }
              className={isTodays ? 'bg-primary/20 text-primary' : ''}
            >
              {session.status === 'cancelled'
                ? 'Cancelled'
                : isTodays
                ? 'Today'
                : format(sessionDate, 'MMM d')}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="relative z-10 pt-4 pb-3">
          <div className="flex flex-col space-y-3">
            <div className="flex items-center">
              <Avatar className="h-9 w-9 mr-3">
                <AvatarImage src={session.coachingRelationship.trainer.avatarUrl || ''} alt={session.coachingRelationship.trainer.name} />
                <AvatarFallback>{session.coachingRelationship.trainer.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">{session.coachingRelationship.trainer.name}</p>
                <p className="text-xs text-muted-foreground">Your coach</p>
              </div>
            </div>

            <div className="flex items-center text-sm">
              <CalendarIcon className="mr-2 h-4 w-4 text-muted-foreground" />
              <span>{format(sessionDate, 'EEEE, MMMM d, yyyy')}</span>
            </div>

            <div className="flex items-center text-sm">
              <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
              <span>{format(sessionDate, 'h:mm a')} ({session.duration} minutes)</span>
            </div>

            {session.type === 'video' && (
              <div className="flex items-center text-sm">
                <Video className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Video Conference</span>
              </div>
            )}

            {session.notes && (
              <div className="mt-2 p-3 bg-muted rounded-md text-sm">
                <p className="font-medium mb-1">Notes:</p>
                <p>{session.notes}</p>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between pt-0 relative z-10 border-t border-primary/10">
          {!isPastSession && session.status !== 'cancelled' && (
            <>
              {isTodays && (
                <Button onClick={() => handleJoinSession(session)}>
                  <Video className="mr-2 h-4 w-4" />
                  Join Session
                </Button>
              )}

              {!isTodays && (
                <Button variant="outline" onClick={() => router.push(`/dashboard/sessions/schedule/${session.coachingRelationship.trainer.id}`)}>
                  <Calendar className="mr-2 h-4 w-4" />
                  Reschedule
                </Button>
              )}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <Dialog>
                    <DialogTrigger asChild>
                      <DropdownMenuItem onSelect={(e) => {
                        e.preventDefault();
                        setSessionToCancel(session.id);
                      }}>
                        Cancel Session
                      </DropdownMenuItem>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Cancel Coaching Session</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to cancel this coaching session? This action cannot be undone.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="py-4">
                        <p className="text-sm font-medium mb-2">Reason for cancellation (optional):</p>
                        <Textarea
                          value={cancelReason}
                          onChange={(e) => setCancelReason(e.target.value)}
                          placeholder="Please provide a reason for cancellation..."
                        />
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setSessionToCancel(null)}>
                          Keep Session
                        </Button>
                        <Button variant="destructive" onClick={handleCancelSession} disabled={isCancelling}>
                          {isCancelling ? 'Cancelling...' : 'Cancel Session'}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  <DropdownMenuItem onSelect={(e) => {
                    e.preventDefault();
                    router.push(`/dashboard/sessions/${session.id}`);
                  }}>
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem onSelect={(e) => {
                    e.preventDefault();
                    router.push(`/dashboard/coaching-chat`);
                  }}>
                    Message Coach
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          )}

          {(isPastSession || session.status === 'cancelled') && (
            <>
              <Button variant="outline" onClick={() => router.push(`/dashboard/sessions/${session.id}`)}>
                <ExternalLink className="mr-2 h-4 w-4" />
                View Details
              </Button>

              {session.status === 'cancelled' && (
                <Badge variant="outline" className="ml-auto">Cancelled</Badge>
              )}

              {isPastSession && session.status !== 'cancelled' && (
                <Badge variant="outline" className="ml-auto">Completed</Badge>
              )}
            </>
          )}
        </CardFooter>
      </Card>
    );
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="relative overflow-hidden rounded-xl p-6 bg-gradient-to-r from-primary/5 via-background to-purple-500/5 border border-primary/20 mb-6">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="relative z-10">
          <h1 className="text-3xl font-bold tracking-tight premium-gradient-text">Coaching Sessions</h1>
          <p className="text-muted-foreground">
            Manage your 1:1 coaching sessions with your trainers
          </p>
        </div>
      </div>

      <div className="flex justify-end">
        <Button onClick={() => router.push('/dashboard/sessions/schedule/1')}>
          <Calendar className="mr-2 h-4 w-4" />
          Schedule New Session
        </Button>
      </div>

      <Tabs defaultValue="upcoming" className="space-y-6">
        <TabsList className="bg-gradient-to-r from-primary/10 via-purple-500/10 to-indigo-500/10 p-1 rounded-xl shadow-inner border border-primary/20">
          <TabsTrigger value="upcoming" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-purple-500/80 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-lg">
            Upcoming Sessions
          </TabsTrigger>
          <TabsTrigger value="past" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-purple-500/80 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300 rounded-lg">
            Past Sessions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming" className="space-y-6">
          {isLoading ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="premium-card">
                  <CardHeader>
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <Skeleton className="h-10 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Skeleton className="h-10 w-28" />
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : upcomingSessions.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {upcomingSessions.map(renderSessionCard)}
            </div>
          ) : (
            <Card className="premium-card">
              <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
              <CardHeader className="relative z-10">
                <CardTitle>No Upcoming Sessions</CardTitle>
                <CardDescription>
                  You don't have any upcoming coaching sessions scheduled.
                </CardDescription>
              </CardHeader>
              <CardContent className="relative z-10">
                <p className="text-sm text-muted-foreground">
                  Schedule a session with your coach to get personalized guidance and support.
                </p>
              </CardContent>
              <CardFooter className="relative z-10">
                <Button onClick={() => router.push('/dashboard/sessions/schedule/1')}>
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule a Session
                </Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="past" className="space-y-6">
          {isLoading ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="premium-card">
                  <CardHeader>
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <Skeleton className="h-10 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Skeleton className="h-10 w-28" />
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : pastSessions.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {pastSessions.map(renderSessionCard)}
            </div>
          ) : (
            <Card className="premium-card">
              <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
              <CardHeader className="relative z-10">
                <CardTitle>No Past Sessions</CardTitle>
                <CardDescription>
                  You don't have any past coaching sessions.
                </CardDescription>
              </CardHeader>
              <CardContent className="relative z-10">
                <p className="text-sm text-muted-foreground">
                  Once you complete coaching sessions, they will appear here for your reference.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
