import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    // Only allow this in development
    if (process.env.NODE_ENV !== "development") {
      return new NextResponse("Only available in development", { status: 403 });
    }
    
    // 1. Find a trainer and a client user
    const trainer = await prisma.user.findFirst({
      where: {
        role: "trainer",
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    const client = await prisma.user.findFirst({
      where: {
        role: "client",
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    if (!trainer || !client) {
      return NextResponse.json({
        success: false,
        error: "Could not find trainer and client users",
      }, { status: 404 });
    }

    // 2. Check for coaching relationships
    const coachingRelationships = await prisma.coachingRelationship.findMany({
      where: {
        OR: [
          { trainerId: trainer.id, clientId: client.id },
          { trainerId: client.id, clientId: trainer.id },
        ],
      },
    });

    // 3. Check for conversations
    const conversations = await prisma.conversation.findMany({
      where: {
        OR: [
          { user1Id: trainer.id, user2Id: client.id },
          { user1Id: client.id, user2Id: trainer.id },
        ],
      },
      include: {
        _count: {
          select: {
            messages: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      trainer,
      client,
      coachingRelationships,
      conversations,
    });
  } catch (error) {
    console.error("Error checking coaching relationships:", error);
    
    // Return more detailed error information in development
    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      }, { status: 500 });
    }
    
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
