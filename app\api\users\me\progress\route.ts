import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    // Handle regular JSON request
    const body = await request.json()
    const { weight, bodyFat, measurements: bodyMeasurements, date, notes, sleepHours, stressLevel, coffeeCount, sleepQuality, stressFactors, coffeeType } = body

    // Default weight to 0 if not provided (for health metrics only logs)
    const weightValue = weight || 0

    const progress = await prisma.measurement.create({
      data: {
        userId: session.user.id,
        date: date ? new Date(date) : new Date(),
        weight: parseFloat(weightValue.toString()),
        bodyFat: bodyFat ? parseFloat(bodyFat.toString()) : null,
        waist: bodyMeasurements?.waist ? parseFloat(bodyMeasurements.waist.toString()) : null,
        chest: bodyMeasurements?.chest ? parseFloat(bodyMeasurements.chest.toString()) : null,
        arms: bodyMeasurements?.arms ? parseFloat(bodyMeasurements.arms.toString()) : null,
        thighs: bodyMeasurements?.thighs ? parseFloat(bodyMeasurements.thighs.toString()) : null,
        // Health metrics
        sleepHours: sleepHours !== undefined ? parseFloat(sleepHours.toString()) : null,
        stressLevel: stressLevel !== undefined ? parseInt(stressLevel.toString()) : null,
        coffeeCount: coffeeCount !== undefined ? parseInt(coffeeCount.toString()) : null,
        notes: notes || null,
        // Additional fields stored in notes for now
        // In a real app, you would add these fields to the database schema
      },
    })

    // Update streaks
    try {
      // Temporarily disable streak updates until the API is implemented
      // We'll implement this in a future update
      /*
      // Update measurement streak
      await fetch('http://localhost:3001/api/users/me/streaks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'measurement' }),
      })

      // Update specific metric streaks if they were logged
      if (sleepHours !== undefined) {
        await fetch('http://localhost:3001/api/users/me/streaks', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'sleep' }),
        })
      }

      if (stressLevel !== undefined) {
        await fetch('http://localhost:3001/api/users/me/streaks', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'stress' }),
        })
      }

      if (coffeeCount !== undefined) {
        await fetch('http://localhost:3001/api/users/me/streaks', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ type: 'coffee' }),
        })
      }
      */
    } catch (streakError) {
      console.error("[STREAK_UPDATE_ERROR]", streakError)
      // Don't fail the main request if streak update fails
    }

    // Return all measurements after creating a new one
    const allMeasurements = await prisma.measurement.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 20,
    })

    return NextResponse.json(allMeasurements)
  } catch (error) {
    console.error("[PROGRESS_CREATE]", error)
    return new NextResponse("Internal error", { status: 500 })
  }
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const userId = session.user.id

    // Get user's progress metrics
    const measurements = await prisma.measurement.findMany({
      where: {
        userId: userId,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 20,
    })

    // Get user's targets from profile
    const userProfile = await prisma.clientProfile.findUnique({
      where: {
        userId: userId,
      },
      select: {
        weightTarget: true,
        bodyFatTarget: true,
      },
    })

    // Get user's exercise performance
    const exercisePerformance = await prisma.exerciseLog.findMany({
      where: {
        userId: userId,
      },
      orderBy: {
        createdAt: "desc",
      },
      distinct: ["exerciseName"],
      take: 10,
    })

    // Group measurements by week for check-ins
    const weeklyCheckIns = []
    const processedWeeks = new Set()

    measurements.forEach(measurement => {
      const weekNumber = getWeekNumber(measurement.createdAt)
      const weekKey = `${weekNumber.year}-${weekNumber.week}`

      if (!processedWeeks.has(weekKey)) {
        processedWeeks.add(weekKey)

        // Find previous measurement for comparison
        const previousMeasurements = measurements.filter(m => {
          const prevWeekNumber = getWeekNumber(m.createdAt)
          const prevWeekKey = `${prevWeekNumber.year}-${prevWeekNumber.week}`
          return prevWeekKey !== weekKey && m.createdAt < measurement.createdAt
        })

        const previousMeasurement = previousMeasurements[0]

        weeklyCheckIns.push({
          week: `Week ${weekNumber.week}`,
          date: measurement.createdAt,
          daysAgo: getDaysAgo(measurement.createdAt),
          weight: measurement.weight,
          weightChange: previousMeasurement ? (measurement.weight - previousMeasurement.weight).toFixed(1) : "0",
          bodyFat: measurement.bodyFat,
          bodyFatChange: previousMeasurement ? (measurement.bodyFat - previousMeasurement.bodyFat).toFixed(1) : "0",
          waist: measurement.waist,
          waistChange: previousMeasurement ? (measurement.waist - previousMeasurement.waist).toFixed(2) : "0",
        })
      }
    })

    // Format progress metrics
    const progressMetrics = []

    // Add weight metric if available
    const latestWeight = measurements.find(m => m.weight)
    if (latestWeight) {
      progressMetrics.push({
        name: "Weight",
        current: latestWeight.weight,
        target: userProfile?.weightTarget || latestWeight.weight - 10,
        unit: "lbs"
      })
    }

    // Add body fat metric if available
    const latestBodyFat = measurements.find(m => m.bodyFat)
    if (latestBodyFat) {
      progressMetrics.push({
        name: "Body Fat",
        current: latestBodyFat.bodyFat,
        target: userProfile?.bodyFatTarget || latestBodyFat.bodyFat - 5,
        unit: "%"
      })
    }

    // Add exercise performance metrics
    exercisePerformance.forEach(exercise => {
      if (exercise.weight && exercise.exerciseName) {
        // Only add common strength exercises
        const commonExercises = ["Bench Press", "Squat", "Deadlift"]
        if (commonExercises.some(e => exercise.exerciseName.includes(e))) {
          progressMetrics.push({
            name: exercise.exerciseName,
            current: exercise.weight,
            target: Math.round(exercise.weight * 1.2), // Target 20% improvement
            unit: "lbs"
          })
        }
      }
    })

    // If no metrics found, provide empty array
    if (progressMetrics.length === 0) {
      // Don't add mock data, just return empty array
    }

    return NextResponse.json({
      progressMetrics,
      weeklyCheckIns: weeklyCheckIns.slice(0, 5), // Return only the 5 most recent check-ins
      measurements, // Return the raw measurements
    })
  } catch (error) {
    console.error("[PROGRESS_GET]", error)
    return new NextResponse("Internal Error", { status: 500 })
  }
}

// Helper function to get week number from date
function getWeekNumber(date: Date) {
  const d = new Date(date)
  d.setHours(0, 0, 0, 0)
  d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7)
  const week1 = new Date(d.getFullYear(), 0, 4)
  const weekNumber = 1 + Math.round(((d.getTime() - week1.getTime()) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7)

  return {
    week: weekNumber,
    year: d.getFullYear()
  }
}

// Helper function to calculate days ago
function getDaysAgo(date: Date) {
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 0) return "Today"
  if (diffDays === 1) return "Yesterday"
  return `${diffDays} days ago`
}
