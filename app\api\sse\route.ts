import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Store active connections by user ID
const connections = new Map<string, Set<ReadableStreamController<Uint8Array>>>();

// Function to send a message to a specific user
export function sendMessageToUser(userId: string, message: string) {
  console.log(`sendMessageTo<PERSON><PERSON> called for user ${userId}`);

  const userConnections = connections.get(userId);
  console.log(`Found ${userConnections ? userConnections.size : 0} connections for user ${userId}`);

  if (userConnections && userConnections.size > 0) {
    console.log(`Sending message to ${userConnections.size} connections for user ${userId}`);

    userConnections.forEach(controller => {
      try {
        // Format the message as a proper SSE event
        const formattedMessage = `data: ${message}\n\n`;
        console.log(`Sending formatted message: ${formattedMessage}`);

        // Encode and send the message
        controller.enqueue(new TextEncoder().encode(formattedMessage));
        console.log(`Message sent successfully to a connection for user ${userId}`);
      } catch (error) {
        console.error(`Error sending message to client for user ${userId}:`, error);
      }
    });
  } else {
    console.log(`No active connections found for user ${userId}`);
  }
}

export async function GET(req: NextRequest) {
  console.log('SSE GET request received');

  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    console.log('SSE request unauthorized - no session user ID');
    return new NextResponse('Unauthorized', { status: 401 });
  }

  const userId = session.user.id;
  console.log(`SSE connection established for user ${userId}`);

  // Create a new stream
  const stream = new ReadableStream({
    start(controller) {
      console.log(`Initializing SSE stream for user ${userId}`);

      // Add this connection to the user's connections
      if (!connections.has(userId)) {
        console.log(`Creating new connection set for user ${userId}`);
        connections.set(userId, new Set());
      }

      connections.get(userId)?.add(controller);
      console.log(`Added controller to connections for user ${userId}`);
      console.log(`User ${userId} now has ${connections.get(userId)?.size || 0} active connections`);

      // Log all active connections
      console.log('All active connections:');
      connections.forEach((conns, uid) => {
        console.log(`- User ${uid}: ${conns.size} connections`);
      });

      // Send initial message
      console.log(`Sending initial 'connected' message to user ${userId}`);
      controller.enqueue(new TextEncoder().encode('data: connected\n\n'));
    },
    cancel() {
      console.log(`SSE connection cancelled for user ${userId}`);

      // Remove this connection when it's closed
      const userConnections = connections.get(userId);
      if (userConnections) {
        userConnections.delete(this as unknown as ReadableStreamController<Uint8Array>);
        console.log(`Removed controller from connections for user ${userId}`);
        console.log(`User ${userId} now has ${userConnections.size} active connections`);

        if (userConnections.size === 0) {
          connections.delete(userId);
          console.log(`Removed all connections for user ${userId}`);
        }
      }
    }
  });

  // Send a test message after a short delay to verify the connection
  setTimeout(() => {
    try {
      console.log(`Sending test message to user ${userId} to verify connection`);
      sendMessageToUser(userId, JSON.stringify({
        type: 'test',
        message: 'Connection test'
      }));
    } catch (error) {
      console.error(`Error sending test message to user ${userId}:`, error);
    }
  }, 1000);

  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      'Connection': 'keep-alive',
      'X-Accel-Buffering': 'no' // Disable buffering for Nginx
    }
  });
}
