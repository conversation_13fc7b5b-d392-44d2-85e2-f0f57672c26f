'use client';

import { useEffect, useState, useRef, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Send, Loader2 } from 'lucide-react';
import { format, isToday, isYesterday } from 'date-fns';
import { useSocket } from '@/components/socket-provider';

interface Message {
  id: string;
  content: string;
  senderId: string;
  receiverId: string;
  createdAt: string;
  sender: {
    id: string;
    name: string;
    avatarUrl: string | null;
  };
  receiver: {
    id: string;
    name: string;
    avatarUrl: string | null;
  };
}

interface CoachingChatProps {
  conversationId: string;
  otherUser: {
    id: string;
    name: string;
    avatarUrl: string | null;
  };
  relationshipId: string;
}

export function CoachingChat({ conversationId, otherUser, relationshipId }: CoachingChatProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const { isConnected, joinConversation, leaveConversation, sendMessage: socketSendMessage } = useSocket();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Fetch messages
  const fetchMessages = useCallback(async () => {
    if (!conversationId) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/coaching/messages?conversationId=${conversationId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch messages');
      }

      const data = await response.json();
      setMessages(data);
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load messages. Please try again.',
      });
    } finally {
      setIsLoading(false);
      scrollToBottom();
    }
  }, [conversationId, toast]);

  // Initial fetch of messages
  useEffect(() => {
    if (conversationId) {
      fetchMessages();
    }
  }, [conversationId, fetchMessages]);

  // Join the conversation room when the component mounts
  useEffect(() => {
    if (conversationId && isConnected) {
      console.log(`Joining conversation: ${conversationId}`);
      joinConversation(conversationId);

      // Leave the conversation when the component unmounts
      return () => {
        console.log(`Leaving conversation: ${conversationId}`);
        leaveConversation(conversationId);
      };
    }
  }, [conversationId, isConnected, joinConversation, leaveConversation]);

  // Listen for new messages
  useEffect(() => {
    const handleNewMessage = (event: CustomEvent) => {
      const { detail } = event;

      if (detail.type === 'new-message' && detail.message.conversationId === conversationId) {
        console.log('New message received:', detail.message);

        // Check if the message is already in the list
        const messageExists = messages.some(msg => msg.id === detail.message.id);

        if (!messageExists) {
          setMessages(prev => [...prev, detail.message]);
        }
      }
    };

    // Add event listener
    window.addEventListener('message-received', handleNewMessage as EventListener);

    // Clean up
    return () => {
      window.removeEventListener('message-received', handleNewMessage as EventListener);
    };
  }, [conversationId, messages]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim() || !session?.user?.id) return;

    try {
      setIsSending(true);

      // Create a temporary message with a temporary ID
      const tempId = `temp-${Date.now()}`;
      const tempMessage: Message = {
        id: tempId,
        content: newMessage,
        senderId: session.user.id,
        receiverId: otherUser.id,
        createdAt: new Date().toISOString(),
        sender: {
          id: session.user.id,
          name: session.user.name || 'You',
          avatarUrl: session.user.image || null,
        },
        receiver: {
          id: otherUser.id,
          name: otherUser.name,
          avatarUrl: otherUser.avatarUrl,
        },
      };

      // Optimistically add the message to the UI
      setMessages((prev) => [...prev, tempMessage]);

      // Clear the input field and focus it
      setNewMessage('');
      inputRef.current?.focus();

      // Send the message to the server
      const response = await fetch('/api/coaching/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: tempMessage.content,
          conversationId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      // Get the real message from the server
      const data = await response.json();

      // Replace the temporary message with the real one
      setMessages((prev) =>
        prev.map((msg) => (msg.id === tempId ? data : msg))
      );

      // Send the message via WebSocket for real-time updates
      if (isConnected) {
        socketSendMessage({
          ...data,
          receiverId: otherUser.id,
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to send message. Please try again.',
      });
    } finally {
      setIsSending(false);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  return (
    <Card className="flex flex-col h-[600px] border-primary/20 shadow-lg bg-gradient-to-b from-background to-background/80 overflow-hidden rounded-xl">
      <CardHeader className="border-b pb-3 bg-gradient-to-r from-primary/5 to-primary/10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <Avatar className="h-10 w-10 border-2 border-primary/20 shadow-sm">
                <AvatarImage src={otherUser.avatarUrl || undefined} alt={otherUser.name} />
                <AvatarFallback className="bg-primary text-white">{getInitials(otherUser.name)}</AvatarFallback>
              </Avatar>
              <span className="absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full bg-green-500 border-2 border-white"></span>
            </div>
            <div>
              <CardTitle className="text-base font-semibold">{otherUser.name}</CardTitle>
              <div className="flex items-center gap-2">
                <p className="text-xs text-muted-foreground">Premium Coach</p>
                <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-amber-500 to-amber-300 text-amber-900">
                  Online
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      <ScrollArea className="flex-1 p-4 bg-gradient-to-b from-background/50 to-background/30 backdrop-blur-sm">
        {isLoading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
                <div className="flex items-start gap-2 max-w-[80%]">
                  {i % 2 !== 0 && (
                    <Skeleton className="h-8 w-8 rounded-full" />
                  )}
                  <div>
                    <Skeleton className="h-16 w-64 rounded-lg shadow-sm" />
                    <Skeleton className="h-3 w-24 mt-1" />
                  </div>
                  {i % 2 === 0 && (
                    <Skeleton className="h-8 w-8 rounded-full" />
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <Send className="h-6 w-6 text-primary/50" />
                </div>
                <p className="text-lg font-medium mb-2">Start Your Premium Coaching Session</p>
                <p className="text-muted-foreground max-w-md">Send your first message to begin your personalized coaching experience.</p>
              </div>
            ) : (
              messages.map((message) => {
                const isCurrentUser = message.senderId === session?.user?.id;
                const messageUser = isCurrentUser ? message.sender : message.receiver;
                const messageDate = new Date(message.createdAt);
                let formattedTime;

                if (isToday(messageDate)) {
                  formattedTime = format(messageDate, 'h:mm a'); // Today at 2:30 PM
                } else if (isYesterday(messageDate)) {
                  formattedTime = 'Yesterday ' + format(messageDate, 'h:mm a'); // Yesterday at 2:30 PM
                } else {
                  formattedTime = format(messageDate, 'MMM d') + ' at ' + format(messageDate, 'h:mm a'); // Apr 14 at 2:30 PM
                }

                return (
                  <div key={message.id} className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
                    <div className="flex items-start gap-2 max-w-[80%]">
                      {!isCurrentUser && (
                        <div className="relative">
                          <Avatar className="h-8 w-8 border border-primary/20 shadow-sm">
                            <AvatarImage src={messageUser.avatarUrl || undefined} alt={messageUser.name} />
                            <AvatarFallback className="bg-primary text-white text-xs">{getInitials(messageUser.name)}</AvatarFallback>
                          </Avatar>
                        </div>
                      )}
                      <div>
                        <div className={`p-3 rounded-lg shadow-sm ${isCurrentUser
                          ? 'bg-primary text-white'
                          : 'bg-muted/80 border border-muted/30'}`}>
                          <p className="text-sm leading-relaxed">{message.content}</p>
                        </div>
                        <div className="flex items-center gap-1 mt-1">
                          <p className="text-xs font-medium text-muted-foreground">{formattedTime}</p>
                          {isCurrentUser && message.id && (
                            <div className="flex items-center">
                              <span className="mx-1 text-muted-foreground/50">•</span>
                              <svg className="h-3 w-3 text-primary/70" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M20 6L9 17l-5-5" />
                              </svg>
                            </div>
                          )}
                        </div>
                      </div>
                      {isCurrentUser && (
                        <div className="relative">
                          <Avatar className="h-8 w-8 border border-primary/20 shadow-sm">
                            <AvatarImage src={messageUser.avatarUrl || undefined} alt={messageUser.name} />
                            <AvatarFallback className="bg-primary text-white text-xs">{getInitials(messageUser.name)}</AvatarFallback>
                          </Avatar>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })
            )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </ScrollArea>
      <CardFooter className="border-t p-3 bg-gradient-to-r from-primary/5 to-primary/10">
        <form onSubmit={handleSendMessage} className="flex w-full gap-2">
          <Input
            ref={inputRef}
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type your message..."
            disabled={isSending}
            className="flex-1 bg-background/90 backdrop-blur-sm border-primary/20 focus-visible:ring-primary/30 shadow-sm rounded-full px-4"
          />
          <Button
            type="submit"
            disabled={isSending || !newMessage.trim()}
            className="bg-primary hover:bg-primary/90 shadow-md rounded-full px-3 aspect-square"
            size="icon"
          >
            {isSending ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
}
