# Calendly Scheduling Feature

## Overview
This feature allows trainers to schedule 1:1 sessions with their clients through Calendly, embedded directly in the app via a popup modal. The feature does not save any session data in the local database - it only opens Calendly and lets the trainer schedule sessions with their clients via their own Calendly account.

## Implementation Details

### Files Created/Modified

1. **`components/calendly/calendly-modal.tsx`** - New modal component that embeds Calendly widget
2. **`components/coaching/follow-up-tab.tsx`** - New Follow-up tab component for client detail page
3. **`app/dashboard/coaching/clients/[id]/page.tsx`** - Modified to include the new Follow-up tab
4. **`components/profile/connections-tab.tsx`** - New Connections tab for trainer profile
5. **`components/profile/trainer-profile-client.tsx`** - Modified to include the new Connections tab
6. **`app/api/trainer/upcoming-sessions/route.ts`** - New API endpoint to fetch upcoming sessions
7. **`components/dashboard/upcoming-sessions-widget.tsx`** - New dashboard widget for upcoming sessions
8. **`components/dashboard/trainer-dashboard.tsx`** - Modified to include upcoming sessions widget

### Key Features

- **Schedule Meeting Button**: Located in the Follow-up tab of each client's detail page
- **Calendly Modal**: Opens a popup modal with the Calendly inline widget
- **Client Data Pre-population**: Automatically fills client's name and email in Calendly form
- **Trainer Configuration**: Uses trainer's Calendly URL from their profile's Connections tab
- **Connections Tab**: New tab in trainer profile for managing integrations
- **Upcoming Sessions Widget**: Dashboard widget showing scheduled sessions from Calendly
- **Database Integration**: Sessions are automatically stored via Calendly webhooks
- **Real-time Updates**: Dashboard shows live upcoming sessions data

### How It Works

1. **Trainer Setup**: Trainer configures their Calendly URL in the "Connections" tab of their trainer profile
2. **Access Client**: Trainer navigates to a client's detail page and clicks on "Follow-up" tab
3. **Schedule Meeting**: Trainer clicks "Schedule Meeting" button to schedule a session with that client
4. **Calendly Modal**: Modal opens with embedded Calendly widget
5. **Pre-filled Data**: Client's name and email are automatically populated in the form
6. **Session Scheduling**: Trainer selects available time slot and completes booking through Calendly
7. **Confirmation**: Success message appears and modal closes

### Configuration Requirements

- Trainer must have a valid Calendly account
- Trainer must configure their Calendly URL in the trainer settings
- The `calendlyUserId` field in the User table stores the trainer's Calendly username

### User Experience

- Clean, intuitive interface integrated into existing coaching workflow
- Modal approach keeps users within the app context
- Automatic form pre-population reduces manual data entry
- Clear feedback when Calendly is not configured

### Technical Implementation

- Uses existing `CalendlyEmbed` component for consistency
- Leverages `react-calendly` library for Calendly integration
- Modal built with Radix UI Dialog component
- Responsive design works on desktop and mobile
- Error handling for missing Calendly configuration

## Usage Instructions

### For Trainers

1. **Setup Calendly Integration**:
   - Go to your trainer profile and click on the "Connections" tab
   - Enter your Calendly URL (e.g., `https://calendly.com/yourusername`)
   - Save settings

2. **Schedule Sessions with Clients**:
   - Navigate to a client's detail page
   - Click on "Follow-up" tab
   - Click "Schedule Meeting" button
   - Select available time slot in Calendly modal
   - Complete booking for that client

### For Development

The feature is self-contained and doesn't require database migrations. It uses the existing `calendlyUserId` field in the User table and integrates with the current Calendly infrastructure.

## Future Enhancements

While this implementation focuses on the core scheduling functionality without persistence, future enhancements could include:

- Session reminders and notifications
- Integration with calendar applications
- Session history tracking (if requested)
- Bulk scheduling capabilities
- Custom session types and durations
