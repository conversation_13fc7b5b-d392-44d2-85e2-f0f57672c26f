import { NextRequest } from "next/server"
import { Session } from "next-auth"
import { JWT } from "next-auth/jwt"
import NextAuth from "next-auth/next"
import { authOptions } from "@/lib/auth"

// Add support for dev role override in headers
async function handler(req: NextRequest, res: any) {
  // Log request for debugging
  console.log("[Auth] Request:", req.url);

  // Get headers for debugging
  const headers = Object.fromEntries(req.headers.entries());
  console.log("[Auth] Headers:", headers);

  // For development mode, if we have a role override in headers, modify the auth options
  if (process.env.NODE_ENV === "development") {
    const overrideRole = req.headers.get("x-dev-override-role");
    if (overrideRole && (overrideRole === "admin" || overrideRole === "trainer" || overrideRole === "client")) {
      console.log(`[Auth] Using role override: ${overrideRole}`);

      // Create a modified version of authOptions
      const modifiedAuthOptions = {
        ...authOptions,
        callbacks: {
          ...authOptions.callbacks,
          session: async ({ token, session }: { token: JWT; session: Session }) => {
            if (token) {
              session.user.id = token.id as string;
              // Override the role with our dev setting
              session.user.role = overrideRole as "admin" | "trainer" | "client";
            }
            return session;
          },
        },
        pages: {
          signIn: "/login",
          error: "/auth/error",
          signOut: "/",
          newUser: "/dashboard/dashboard",
        },
      };

      return await NextAuth(modifiedAuthOptions)(req, res);
    }
  }

  // Default handling with original authOptions
  return await NextAuth(authOptions)(req, res);
}

export { handler as GET, handler as POST }