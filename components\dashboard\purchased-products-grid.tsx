'use client';

import Link from "next/link";
import Image from "next/image";
import { FileText, Play, ArrowRight } from "lucide-react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

interface Product {
  id: string;
  title: string;
  thumbnailUrl?: string;
  progress?: number;
  type?: string;
}

interface PurchasedProductsGridProps {
  products: Product[];
  emptyMessage?: string;
}

export function PurchasedProductsGrid({
  products,
  emptyMessage = "You haven't purchased any digital products yet."
}: PurchasedProductsGridProps) {
  if (products.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center p-6">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-medium mb-2">No purchased products</h3>
          <p className="text-muted-foreground text-center mb-4">
            {emptyMessage}
          </p>
          <Button asChild>
            <Link href="/dashboard/shop">
              Browse Products
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
      {products.map((product) => (
        <Card key={product.id} className="overflow-hidden flex flex-col shadow-md hover:shadow-lg hover:-translate-y-1 transition-all border-border/40 group">
          <div className="relative h-44 bg-muted">
            {product.thumbnailUrl ? (
              <Image
                src={product.thumbnailUrl}
                alt={product.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-primary/5 to-primary/20">
                <FileText className="h-12 w-12 text-primary/50" />
              </div>
            )}

            <div className="absolute top-3 right-3">
              <Badge variant="outline" className="bg-background/80 border-primary/20 text-primary text-xs font-normal shadow-sm">
                {product.type === "guide" ? "Guide" : "Program"}
              </Badge>
            </div>
          </div>

          <CardContent className="p-5 flex-grow">
            <h3 className="font-medium text-lg line-clamp-1 mb-3">{product.title}</h3>

            {product.progress !== undefined && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Progress</span>
                  <span className="font-medium">{product.progress}%</span>
                </div>
                <Progress value={product.progress} className="h-2 bg-primary/10" />
              </div>
            )}
          </CardContent>

          <CardFooter className="p-5 pt-0">
            <Button asChild variant="outline" className="w-full hover:bg-primary/5 hover:text-primary hover:border-primary/20 transition-colors h-11">
              <Link href={`/dashboard/products/${product.id}`}>
                {product.progress !== undefined && product.progress > 0 ? (
                  <>
                    <Play className="mr-2 h-5 w-5" />
                    Continue
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-5 w-5" />
                    Start Workout
                  </>
                )}
              </Link>
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}
