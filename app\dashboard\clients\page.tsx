"use client"

import { <PERSON>rPlus, UserCheck, Mail, Calendar, AlertCircle, Info } from "lucide-react"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"

// Define types for clients
interface MockClient {
  id: string
  name: string
  email: string
  status: string
  joinDate: string
  avatar: string
  currentPlan: string
}

interface DbClient {
  id: string
  name: string
  email: string
  avatarUrl: string | null
  clientSubscriptions: {
    tier: {
      name: string
    }
  }[]
}

export default function ClientsPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [dbClients, setDbClients] = useState<DbClient[]>([])
  const [activeTab, setActiveTab] = useState("all")

  // Mock client data (would come from database in a real app)
  const mockClients: MockClient[] = [
    {
      id: "mock-1",
      name: "Jane Cooper",
      email: "<EMAIL>",
      status: "active",
      joinDate: "March 15, 2023",
      avatar: "",
      currentPlan: "Premium Coaching",
    },
    {
      id: "mock-2",
      name: "Alex Johnson",
      email: "<EMAIL>",
      status: "active",
      joinDate: "April 23, 2023",
      avatar: "",
      currentPlan: "Training Plan + Diet",
    },
    {
      id: "mock-3",
      name: "Morgan Smith",
      email: "<EMAIL>",
      status: "inactive",
      joinDate: "January 10, 2023",
      avatar: "",
      currentPlan: "Basic Coaching",
    }
  ]

  // Fetch clients from the database
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/trainer/clients', {
          credentials: 'include'
        })

        if (response.ok) {
          const data = await response.json()
          setDbClients(data)
        } else {
          console.error('Failed to fetch clients:', response.statusText)
        }
      } catch (error) {
        console.error('Error fetching clients:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchClients()
  }, [])

  // Filter clients based on active tab
  const filteredDbClients = activeTab === 'all'
    ? dbClients
    : activeTab === 'active'
      ? dbClients.filter(client =>
          client.clientSubscriptions.some(sub => sub.tier?.name))
      : []

  const filteredMockClients = activeTab === 'all'
    ? mockClients
    : activeTab === 'active'
      ? mockClients.filter(client => client.status === 'active')
      : mockClients.filter(client => client.status !== 'active')

  // Calculate total counts
  const totalClients = dbClients.length + mockClients.length
  const activeClients =
    dbClients.filter(client => client.clientSubscriptions.some(sub => sub.tier?.name)).length +
    mockClients.filter(client => client.status === 'active').length

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">My Clients</h2>
        <Button onClick={() => router.push('/dashboard/add-client')}>
          <UserPlus className="mr-2 h-4 w-4" />
          Add New Client
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Stats cards */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <>
                <div className="text-2xl font-bold">{totalClients}</div>
                <p className="text-xs text-muted-foreground">
                  {dbClients.length} real, {mockClients.length} mocked
                </p>
              </>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Clients</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <>
                <div className="text-2xl font-bold">{activeClients}</div>
                <p className="text-xs text-muted-foreground">
                  {dbClients.filter(client => client.clientSubscriptions.some(sub => sub.tier?.name)).length} real, {mockClients.filter(c => c.status === 'active').length} mocked
                </p>
              </>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Messages</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">7</div>
            <p className="text-xs text-muted-foreground">
              +2 since yesterday
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All Clients</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="inactive">Inactive</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {/* Mocked Data Alert */}
          <Alert className="bg-amber-50 border-amber-200">
            <AlertCircle className="h-4 w-4 text-amber-600" />
            <AlertTitle className="text-amber-800">Mocked Client Data</AlertTitle>
            <AlertDescription className="text-amber-700">
              The following clients are mocked for demonstration purposes. They do not represent real data from the database.
            </AlertDescription>
          </Alert>

          {/* Mocked Clients */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Mocked Clients</h3>

            <div className="rounded-md border">
              <div className="p-4">
                <div className="grid grid-cols-5 font-medium">
                  <div>Name</div>
                  <div>Status</div>
                  <div>Plan</div>
                  <div>Join Date</div>
                  <div>Actions</div>
                </div>
              </div>
              <div className="divide-y">
                {filteredMockClients.map((client) => (
                  <div key={client.id} className="grid grid-cols-5 items-center p-4">
                    <div className="font-medium">{client.name}</div>
                    <div>
                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                        client.status === "active"
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                      }`}>
                        {client.status === "active" ? "Active" : "Inactive"}
                      </span>
                    </div>
                    <div className="text-sm">{client.currentPlan}</div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="mr-2 h-4 w-4" />
                      {client.joinDate}
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">Message</Button>
                      <Button variant="outline" size="sm">View</Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Database Clients */}
          <div className="space-y-4 mt-8">
            <h3 className="text-xl font-semibold">Database Clients</h3>

            {isLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            ) : filteredDbClients.length > 0 ? (
              <div className="rounded-md border">
                <div className="p-4">
                  <div className="grid grid-cols-5 font-medium">
                    <div>Name</div>
                    <div>Email</div>
                    <div>Plan</div>
                    <div>Status</div>
                    <div>Actions</div>
                  </div>
                </div>
                <div className="divide-y">
                  {filteredDbClients.map((client) => (
                    <div key={client.id} className="grid grid-cols-5 items-center p-4">
                      <div className="font-medium">{client.name}</div>
                      <div className="text-sm">{client.email}</div>
                      <div className="text-sm">
                        {client.clientSubscriptions.length > 0
                          ? client.clientSubscriptions[0].tier?.name || "No Plan"
                          : "No Plan"}
                      </div>
                      <div>
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          client.clientSubscriptions.length > 0
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}>
                          {client.clientSubscriptions.length > 0 ? "Active" : "Inactive"}
                        </span>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" onClick={() => router.push(`/dashboard/clients/${client.id}/messages`)}>Message</Button>
                        <Button variant="outline" size="sm" onClick={() => router.push(`/dashboard/clients/${client.id}`)}>View</Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="rounded-md border p-8 text-center">
                <Info className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                <h3 className="text-lg font-medium">No clients found</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {activeTab === "all"
                    ? "You don't have any clients in the database yet."
                    : activeTab === "active"
                      ? "You don't have any active clients in the database yet."
                      : "You don't have any inactive clients in the database yet."}
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

