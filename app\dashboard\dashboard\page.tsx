import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import Image from "next/image"
import Link from "next/link"
import { Users, Star, Heart, ArrowRight, DollarSign, CircleDollarSign, CheckCircle2, Calendar, TrendingUp, UserPlus } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { format, startOfMonth } from "date-fns"
import { Product } from "@/lib/types"

// Import new components
import { PersonalizedWelcome } from "@/components/dashboard/personalized-welcome"
import { ProgramProgressCard } from "@/components/dashboard/program-progress-card"
import { SubscriptionSummaryCard } from "@/components/dashboard/subscription-summary-card"
import { CoachingSection } from "@/components/dashboard/coaching-section"
import { AnalyticsPreview } from "@/components/dashboard/analytics-preview"
import { PurchasedProductsGrid } from "@/components/dashboard/purchased-products-grid"
import { FeaturedProducts } from "@/components/dashboard/featured-products"
import { UpgradeMessage } from "@/components/dashboard/upgrade-message"
import { QuickAccessButton } from "@/components/training/quick-access-button"
import { UpcomingSessionsWidget } from "@/components/dashboard/upcoming-sessions-widget"

export default async function DashboardPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect("/login")
  }

  // Determine user role
  const userRole = session.user.role || "client"

  if (userRole === "trainer") {
    // Fetch real data for trainer dashboard
    const trainerId = session.user.id

    // Get active clients count
    const activeClients = await prisma.coachingRelationship.count({
      where: {
        trainerId: trainerId,
        status: 'active'
      }
    })

    // Calculate monthly revenue
    const startOfCurrentMonth = startOfMonth(new Date())
    const monthlyRevenue = await prisma.coachingRelationship.aggregate({
      where: {
        trainerId: trainerId,
        status: 'active',
        startDate: {
          lte: new Date()
        },
        OR: [
          { endDate: null },
          { endDate: { gt: new Date() } }
        ]
      },
      _sum: {
        monthlyFee: true
      }
    })

    // Get new clients this month
    const newClients = await prisma.coachingRelationship.count({
      where: {
        trainerId: trainerId,
        status: 'active',
        startDate: {
          gte: startOfCurrentMonth
        }
      }
    })

    // Get client progress data
    const clientProgress = await prisma.coachingRelationship.findMany({
      where: {
        trainerId: trainerId,
        status: 'active'
      },
      include: {
        client: {
          include: {
            clientProfile: true
          }
        }
      },
      take: 5
    })

    // Format client progress data
    const formattedClientProgress = clientProgress.map(relationship => ({
      id: relationship.id,
      name: relationship.client?.name || "Anonymous Client",
      plan: relationship.plan || "No Active Plan",
      progress: 0, // We'll need to implement this based on your progress tracking system
      status: "On Track", // This should be calculated based on your metrics
      lastActive: "Recently" // We'll need to implement this based on your activity tracking
    }))

    // Get upcoming calls (if you have a calendar/appointments system)
    // For now, we'll return an empty array since there's no appointments table yet
    const formattedUpcomingCalls: any[] = []

    const trainerStats = {
      totalActiveClients: activeClients,
      monthlyRevenue: Number(monthlyRevenue._sum?.monthlyFee || 0),
      newClientsThisMonth: newClients
    }

    return (
      <div className="container py-6 space-y-8 max-w-7xl mx-auto">
        {/* Welcome Section */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Welcome{session.user.name ? `, ${session.user.name}` : ''}</h1>
          <p className="text-muted-foreground mt-1">
            Here's an overview of your coaching business
          </p>
        </div>

        {/* Top Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="shadow-sm hover:shadow-lg transition-all">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-primary/10 rounded-xl">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Active Clients</p>
                  <h3 className="text-2xl font-bold">{trainerStats.totalActiveClients}</h3>
                  <p className="text-sm text-muted-foreground mt-1">Total active relationships</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-sm hover:shadow-lg transition-all">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-primary/10 rounded-xl">
                  <DollarSign className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Monthly Revenue</p>
                  <h3 className="text-2xl font-bold">${trainerStats.monthlyRevenue}</h3>
                  <p className="text-sm text-muted-foreground mt-1">Current month</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-sm hover:shadow-lg transition-all">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-primary/10 rounded-xl">
                  <UserPlus className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">New Clients</p>
                  <h3 className="text-2xl font-bold">{trainerStats.newClientsThisMonth}</h3>
                  <p className="text-sm text-muted-foreground mt-1">This month</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Sessions Widget */}
        <div className="mt-8">
          <UpcomingSessionsWidget />
        </div>

        {/* Client Progress Section */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Client Progress Tracker</CardTitle>
              <CardDescription>Track your clients' progress and engagement</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="divide-y">
                {formattedClientProgress.map((client) => (
                  <div key={client.id} className="py-4 flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium">{client.name}</h4>
                      <p className="text-sm text-muted-foreground">{client.plan}</p>
                    </div>
                    <div className="flex-1 px-4">
                      <div className="w-full bg-secondary h-2 rounded-full">
                        <div 
                          className={`h-2 rounded-full ${
                            client.status === "On Track" ? "bg-green-500" : 
                            client.status === "Behind" ? "bg-yellow-500" : "bg-blue-500"
                          }`}
                          style={{ width: `${client.progress}%` }}
                        />
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{client.progress}% Complete</p>
                    </div>
                    <div className="flex-1 text-right">
                      <Badge variant={
                        client.status === "On Track" ? "success" :
                        client.status === "Behind" ? "warning" : "default"
                      }>
                        {client.status}
                      </Badge>
                      <p className="text-sm text-muted-foreground mt-1">Last active: {client.lastActive}</p>
                    </div>
                  </div>
                ))}
                {formattedClientProgress.length === 0 && (
                  <div className="py-8 text-center text-muted-foreground">
                    No active clients found
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

      </div>
    )
  }

  // Determine user subscription tier
  const subscriptionTier = (session.user as any)?.subscriptionTier || "basic"
  const userName = session.user.name || "User"
  const lastLogin = (session.user as any)?.lastLoginAt || undefined

  // Determine if user has coaching
  const hasCoaching = subscriptionTier === "coaching"

  // Determine if user is premium
  const isPremium = subscriptionTier === "premium" || subscriptionTier === "coaching"

  // Mock data for current program
  const currentProgram = {
    name: "12-Week Strength Builder",
    trainerName: "John Smith",
    progress: 40,
    completedWorkouts: 2,
    totalWorkouts: 5,
    weekTitle: "Week of March 18, 2024",
  }

  // Mock data for analytics
  const analyticsData = {
    currentWeight: 175,
    weightChange: -3,
    trainingVolume: 15000,
    volumeChange: 12,
    workoutsCompleted: 4,
    workoutsChange: 1,
  }

  // Mock data for purchased products
  const purchasedProducts = [
    {
      id: "product-1",
      title: "12-Week Strength Training Guide",
      thumbnailUrl: "/placeholder.svg",
      progress: 35,
      type: "guide",
    },
    {
      id: "product-2",
      title: "Premium Nutrition Plan",
      thumbnailUrl: "/placeholder.svg",
      progress: 0,
      type: "nutrition",
    },
  ]

  // Mock data for featured products
  const storeProducts = [
    {
      id: "product-3",
      title: "Advanced Hypertrophy Program",
      description: "Build muscle mass with this science-based program",
      price: 29.99,
      thumbnailUrl: "/placeholder.svg",
      productType: "digital" as const,
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      athleteId: "1"
    },
    {
      id: "product-4",
      title: "Fat Loss Blueprint",
      description: "Complete guide to sustainable fat loss",
      price: 24.99,
      thumbnailUrl: "/placeholder.svg",
      productType: "digital" as const,
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      athleteId: "1"
    },
    {
      id: "product-5",
      title: "Mobility Mastery",
      description: "Improve flexibility and prevent injuries",
      price: 19.99,
      thumbnailUrl: "/placeholder.svg",
      productType: "digital" as const,
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      athleteId: "1"
    }
  ]

  // Mock data for coaching
  const coachingDetails = {
    coachName: "John Smith",
    coachAvatar: "/placeholder.svg",
    nextCheckInDate: new Date(2024, 3, 25, 15, 30),
  }

  // Client Dashboard
  return (
    <div className="container py-8 space-y-8 max-w-5xl mx-auto relative bg-background/50">
      {/* Quick Access Button for Gym Use */}
      {isPremium && (
        <QuickAccessButton workoutId="workout-1" />
      )}

      {/* Personalized Welcome Section */}
      <PersonalizedWelcome
        userName={userName}
        userTier={subscriptionTier}
        lastLogin={lastLogin}
      />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Left Column (2/3 width on desktop) */}
        <div className="md:col-span-2 space-y-6">
          {/* Program Progress Card */}
          <ProgramProgressCard
            programName={currentProgram.name}
            trainerName={currentProgram.trainerName}
            progress={currentProgram.progress}
            completedWorkouts={currentProgram.completedWorkouts}
            totalWorkouts={currentProgram.totalWorkouts}
            isPremium={isPremium}
            weekTitle={currentProgram.weekTitle}
          />

          {/* Analytics Preview */}
          <AnalyticsPreview
            isPremium={isPremium}
            currentWeight={analyticsData.currentWeight}
            weightChange={analyticsData.weightChange}
            trainingVolume={analyticsData.trainingVolume}
            volumeChange={analyticsData.volumeChange}
            workoutsCompleted={analyticsData.workoutsCompleted}
            workoutsChange={analyticsData.workoutsChange}
          />

          {/* Upgrade Message (only for non-coaching users) */}
          {subscriptionTier !== "coaching" && (
            <UpgradeMessage tier={subscriptionTier} />
          )}
        </div>

        {/* Right Column (1/3 width on desktop) */}
        <div className="space-y-6">
          {/* Subscription Summary Card */}
          {isPremium && (
            <SubscriptionSummaryCard
              trainerName="John Smith"
              trainerAvatar="/placeholder.svg"
              trainerRating={4.8}
              subscriptionType={subscriptionTier === "premium" ? "Premium Annual" : "Mid-tier Monthly"}
              nextRenewalDate={new Date(2024, 11, 15)}
              price="$120.00/year"
              paymentMethod="•••• 4242"
            />
          )}

          {/* Coaching Section */}
          <CoachingSection
            hasCoaching={hasCoaching}
            coachName={hasCoaching ? coachingDetails.coachName : undefined}
            coachAvatar={hasCoaching ? coachingDetails.coachAvatar : undefined}
            nextCheckInDate={hasCoaching ? coachingDetails.nextCheckInDate : undefined}
            coachingPrice="$299.99/month"
          />
        </div>
      </div>
      {!isPremium && (
        // No Active Subscription View - Featured Trainers Section
        <div className="space-y-8">
          <section>
            <h2 className="text-lg font-medium mb-4 flex items-center">
              <Users className="h-5 w-5 mr-2 text-primary" />
              Featured Trainers
            </h2>

            <div className="grid gap-6 md:grid-cols-3">
              {/* Placeholder for featured trainers */}
            </div>

            <Button className="mx-auto block mt-6" variant="premium" rounded="lg" asChild>
              <Link href="/dashboard/trainers" className="group">
                Browse All Trainers
                <ArrowRight className="ml-2 h-3.5 w-3.5 group-hover:translate-x-0.5 transition-transform" />
              </Link>
            </Button>
          </section>
        </div>
      )}

      {/* Purchased Products Section */}
      <div className="pt-4">
        <h2 className="text-lg font-medium mb-4">My Programs & Products</h2>
        <PurchasedProductsGrid products={purchasedProducts} />
      </div>

      {/* Featured Products Section */}
      <div className="pt-4">
        <FeaturedProducts products={storeProducts} isPremium={isPremium} />
      </div>
    </div>
  )
}