import { PrismaClient } from "@prisma/client"
import { hash } from "bcryptjs"

const prisma = new PrismaClient()

async function main() {
  try {
    // Create admin account
    const adminEmail = process.env.ADMIN_EMAIL
    const adminPassword = process.env.ADMIN_PASSWORD
    const adminName = process.env.ADMIN_NAME

    if (!adminEmail || !adminPassword || !adminName) {
      throw new Error("Admin credentials not found in environment variables")
    }

    const existingAdmin = await prisma.user.findUnique({
      where: { email: adminEmail }
    })

    if (!existingAdmin) {
      const hashedPassword = await hash(adminPassword, 10)
      const admin = await prisma.user.create({
        data: {
          email: adminEmail,
          password: hashedPassword,
          fullName: adminName,
          role: "admin",
          bio: "Platform Administrator"
        }
      })
      console.log("✅ Admin account created:", admin.email)
    } else {
      console.log("ℹ️ Admin account already exists")
    }

    // Create client account
    const clientEmail = process.env.TEST_USER_EMAIL
    const clientPassword = process.env.TEST_USER_PASSWORD
    const clientName = process.env.TEST_USER_NAME

    if (!clientEmail || !clientPassword || !clientName) {
      throw new Error("Client credentials not found in environment variables")
    }

    const existingClient = await prisma.user.findUnique({
      where: { email: clientEmail }
    })

    if (!existingClient) {
      const hashedPassword = await hash(clientPassword, 10)
      const client = await prisma.user.create({
        data: {
          email: clientEmail,
          password: hashedPassword,
          fullName: clientName,
          role: "client",
          bio: "Test Client Account"
        }
      })
      console.log("✅ Client account created:", client.email)
    } else {
      console.log("ℹ️ Client account already exists")
    }

    console.log("\n✨ Initial accounts setup completed!")
  } catch (error) {
    console.error("❌ Error creating accounts:", error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main() 