import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

// GET: Fetch all training plans for the trainer
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if the user is a trainer or admin
    const isAuthorized = session.user.role === "admin" || session.user.role === "trainer"
    if (!isAuthorized) {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      )
    }

    // Fetch training plans from the database
    const trainingPlans = await prisma.trainingPlanTemplate.findMany({
      where: {
        trainerId: session.user.id,
        type: "template" as const
      }
    })

    // Transform the data to match the expected format in the UI
    const formattedPlans = trainingPlans.map(plan => {
      // Parse the weeks JSON to ensure exercises are properly structured
      let weeks = [];
      try {
        // If weeks is already an array, use it directly
        if (Array.isArray(plan.weeks)) {
          weeks = plan.weeks;
        }
        // If weeks is a JSON object with week1, week2, etc. keys, convert to array
        else if (typeof plan.weeks === 'object' && plan.weeks !== null) {
          // Check if it has week1, week2 format
          if (plan.weeks.week1 || plan.weeks.week2) {
            weeks = Object.entries(plan.weeks)
              .filter(([key]) => key.startsWith('week'))
              .sort((a, b) => {
                const weekNumA = parseInt(a[0].replace('week', ''));
                const weekNumB = parseInt(b[0].replace('week', ''));
                return weekNumA - weekNumB;
              })
              .map(([key, value], index) => {
                const weekData = value as any;
                return {
                  id: `week-${index + 1}`,
                  weekNumber: index + 1,
                  title: weekData.title || `Week ${index + 1}`,
                  dailyWorkouts: Array.isArray(weekData.workouts)
                    ? weekData.workouts.map((workout: any, wIndex: number) => ({
                        id: workout.id || `workout-${wIndex}`,
                        day: workout.title || `Day ${wIndex + 1}`,
                        exercises: Array.isArray(workout.exercises)
                          ? workout.exercises.map((ex: any, eIndex: number) => ({
                              id: ex.id || `exercise-${eIndex}`,
                              name: ex.name,
                              sets: ex.sets || 0,
                              reps: typeof ex.reps === 'string' ? parseInt(ex.reps) || 0 : (ex.reps || 0),
                              weight: ex.weight || 0,
                              category: ex.category || '',
                              description: ex.description || '',
                              duration: ex.duration || '',
                              rest: ex.rest || 0
                            }))
                          : []
                      }))
                    : []
                };
              });
          } else {
            weeks = plan.weeks as any;
          }
        }
      } catch (error) {
        console.error('Error parsing weeks data:', error);
        weeks = [];
      }

      // Transform the database format to match the UI format
      return {
        id: plan.id,
        title: plan.title,
        description: plan.description || "",
        type: "template",
        targetLevel: plan.difficulty || "Beginner",
        weeks: weeks,
        createdAt: plan.createdAt.toISOString()
      }
    })

    return NextResponse.json(formattedPlans)
  } catch (error) {
    console.error("Error fetching trainer plans:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}