"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

// Define workout types
const workoutTypes = [
  "Strength",
  "Cardio",
  "HIIT",
  "Flexibility",
  "Recovery",
  "Custom",
]

// Define form schema
export const workoutFormSchema = z.object({
  title: z.string().min(1, "Workout title is required"),
  description: z.string().optional(),
  type: z.string().min(1, "Workout type is required"),
})

// Infer the type from the schema
export type WorkoutFormValues = z.infer<typeof workoutFormSchema>

// Define the component props
export interface WorkoutFormProps {
  defaultValues?: Partial<WorkoutFormValues>;
  onSubmit: (data: WorkoutFormValues) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export function WorkoutForm({
  defaultValues = {},
  onSubmit,
  onCancel,
  isSubmitting = false,
}: WorkoutFormProps) {
  // Form definition
  const form = useForm<WorkoutFormValues>({
    resolver: zodResolver(workoutFormSchema),
    defaultValues: {
      title: "",
      description: "",
      type: "Strength",
      ...defaultValues,
    },
  })

  // Form submission handler
  const handleSubmit = (data: WorkoutFormValues) => {
    onSubmit(data);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Workout Title <span className="text-red-500">*</span></FormLabel>
              <FormControl>
                <Input placeholder="Enter workout title" {...field} />
              </FormControl>
              <FormDescription>
                A short, descriptive title for your workout
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe the workout..."
                  className="resize-none min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Additional details about the workout (optional)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Workout Type <span className="text-red-500">*</span></FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select workout type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {workoutTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                Category of workout
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting 
              ? "Submitting..." 
              : defaultValues && Object.keys(defaultValues).length > 0 
                ? "Update Workout" 
                : "Create Workout"
            }
          </Button>
        </div>
      </form>
    </Form>
  )
} 