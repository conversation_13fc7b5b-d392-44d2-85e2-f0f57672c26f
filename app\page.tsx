import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { <PERSON>ada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"
import { Footer } from "@/components/landing/footer"
import { LandingHeader } from "@/components/landing/landing-header"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { authOptions } from "@/lib/auth"
import { TrainerContactForm } from "@/components/landing/trainer-contact-form"
import { ClientAuthRedirect } from "@/components/landing/client-auth-redirect"
import { CachedTrainersSection } from "@/components/landing/cached-trainers-section"

// Enable static generation and caching
export const revalidate = 3600 // Revalidate every hour

export const metadata: Metadata = {
  title: "Clear-Coach - Connect with top fitness coaches",
  description: "Find your perfect fitness coach and transform your health journey with personalized training programs and custom nutrition plans.",
}

export default function Home() {
  // Note: Removed session check to enable static generation
  // Authentication redirect is now handled client-side in the component
  
  // Mock featured trainers instead of using prisma directly
  // This avoids issues with prisma usage during build time
  const trainers = [
    {
      id: "trainer-sarah-johnson",
      name: "Sarah <PERSON>",
      avatarUrl: null,
      bio: "Certified personal trainer with 10+ years of experience specializing in strength training and weight loss. I help clients build sustainable fitness habits for long-term success.",
      themeSettings: JSON.stringify({
        primaryColor: '#3b82f6',
        secondaryColor: '#6366f1'
      })
    },
    {
      id: "trainer-michael-torres",
      name: "Michael Torres",
      avatarUrl: null,
      bio: "Professional bodybuilder and nutrition coach. My approach combines intense workouts with strategic nutrition to help you achieve your physique goals.",
      themeSettings: JSON.stringify({
        primaryColor: '#10b981',
        secondaryColor: '#0e9f6e'
      })
    },
    {
      id: "trainer-emma-wilson",
      name: "Emma Wilson",
      avatarUrl: null,
      bio: "Yoga instructor and fitness coach focusing on mobility, flexibility and functional fitness. I help clients build strong foundations and prevent injuries.",
      themeSettings: JSON.stringify({
        primaryColor: '#8b5cf6',
        secondaryColor: '#7c3aed'
      })
    }
  ];

  return (
    <main className="flex min-h-screen flex-col">
      <ClientAuthRedirect />
      <LandingHeader />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-slate-900 via-indigo-950 to-slate-800 text-white">
        <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px] bg-[radial-gradient(ellipse_at_center,rgba(var(--background-start-rgb),0)_0%,rgba(var(--background-end-rgb),.2)_100%)]"></div>
        <div className="container mx-auto px-4 py-24 relative z-10">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-5xl font-bold mb-6">Find Your Perfect Trainer</h1>
              <p className="text-xl mb-8">
                Connect with elite fitness professionals and transform your health journey 
                with personalized training programs.
              </p>
              <div className="flex flex-col gap-4 sm:flex-row">
                <Button asChild size="lg" className="font-semibold">
                  <Link href="/dashboard/trainers">
                    Browse All Trainers
                  </Link>
                </Button>
              </div>
            </div>
            <div className="rounded-lg overflow-hidden shadow-2xl border-4 border-white/20 hidden md:block">
              <img 
                src="/images/hero-trainer.jpg" 
                alt="Trainer coaching client" 
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-background" id="features">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">What Makes Clear-Coach Different?</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Our platform connects you directly with experienced fitness coaches who provide 
              personalized guidance and support.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="p-6 rounded-lg border bg-card text-card-foreground shadow">
              <div className="mb-4 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Personalized Training</h3>
              <p className="text-muted-foreground">
                Get custom workout plans tailored to your goals, fitness level, and preferences.
              </p>
            </div>
            
            {/* Feature 2 */}
            <div className="p-6 rounded-lg border bg-card text-card-foreground shadow">
              <div className="mb-4 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Direct Coach Communication</h3>
              <p className="text-muted-foreground">
                Chat directly with your trainer for feedback, motivation, and adjustments to your program.
              </p>
            </div>
            
            {/* Feature 3 */}
            <div className="p-6 rounded-lg border bg-card text-card-foreground shadow">
              <div className="mb-4 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Nutrition Guidance</h3>
              <p className="text-muted-foreground">
                Receive personalized nutrition plans and meal ideas to complement your training.
              </p>
            </div>
            
            {/* Feature 4 */}
            <div className="p-6 rounded-lg border bg-card text-card-foreground shadow">
              <div className="mb-4 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Progress Tracking</h3>
              <p className="text-muted-foreground">
                Track your workouts, measurements, and achievements with our easy-to-use tools.
              </p>
            </div>
            
            {/* Feature 5 */}
            <div className="p-6 rounded-lg border bg-card text-card-foreground shadow">
              <div className="mb-4 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Flexible Subscription Plans</h3>
              <p className="text-muted-foreground">
                Choose from various subscription tiers to fit your budget and fitness needs.
              </p>
            </div>
            
            {/* Feature 6 */}
            <div className="p-6 rounded-lg border bg-card text-card-foreground shadow">
              <div className="mb-4 h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Digital Products</h3>
              <p className="text-muted-foreground">
                Access training programs, nutritional guides, and other downloadable resources.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Trainers Section */}
      <CachedTrainersSection trainers={trainers} />

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">How Clear-Coach Works</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Getting started with personalized coaching is simple and straightforward.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="text-center">
              <div className="h-16 w-16 rounded-full bg-primary text-primary-foreground flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Create Your Account</h3>
              <p className="text-muted-foreground">
                Sign up and complete your profile with your fitness goals and preferences.
              </p>
            </div>
            
            {/* Step 2 */}
            <div className="text-center">
              <div className="h-16 w-16 rounded-full bg-primary text-primary-foreground flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Choose Your Trainer</h3>
              <p className="text-muted-foreground">
                Browse trainers, review their specialties, and select the one who matches your needs.
              </p>
            </div>
            
            {/* Step 3 */}
            <div className="text-center">
              <div className="h-16 w-16 rounded-full bg-primary text-primary-foreground flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Start Your Fitness Journey</h3>
              <p className="text-muted-foreground">
                Get personalized plans, follow workouts, and stay in touch with your trainer.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Trainer Contact Section - NEW */}
      <section className="py-20 bg-muted" id="trainer-contact">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4">Are You a Fitness Trainer?</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Join our platform to connect with clients, manage your coaching business, and grow your reach. 
              Fill out the form below to express your interest, and we'll get back to you!
            </p>
          </div>
          <div className="max-w-2xl mx-auto">
            <TrainerContactForm />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary to-primary-foreground/90 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/patterns/circuit-pattern.svg')] opacity-10"></div>
        
        {/* Abstract shapes */}
        <div className="absolute -top-24 -left-24 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-32 -right-32 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-4xl font-bold mb-6">Ready to Transform Your Fitness Journey?</h2>
            <p className="text-xl mb-8 text-white/80">
              Join thousands of clients who have achieved their fitness goals with our personalized 
              training programs and expert guidance.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Button asChild size="lg" className="font-semibold bg-white text-primary hover:bg-gray-100">
                <Link href="/register">
                  Get Started Today
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="font-semibold bg-transparent border-white/70 text-white hover:bg-white/10">
                <Link href="/dashboard/trainers">
                  Explore Trainers
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
      
      <Footer />
    </main>
  )
}

