"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  difficulty: z.enum(["beginner", "intermediate", "advanced"]),
  is_template: z.boolean().default(false),
  selectedExercises: z.array(z.string()).default([]),
})

type FormValues = z.infer<typeof formSchema>

interface TemplateExercise {
  id: string
  name: string
  description?: string
  sets?: number
  reps?: number
  duration?: number
  restTime?: number
  videoUrl?: string
  type: string
  difficulty: string
}

interface TrainingPlanFormProps {
  initialData?: {
    id: string
    title: string
    description: string
    difficulty: "beginner" | "intermediate" | "advanced"
    is_template: boolean
  }
}

export function TrainingPlanForm({ initialData }: TrainingPlanFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [templateExercises, setTemplateExercises] = useState<TemplateExercise[]>([])
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>("intermediate")

  useEffect(() => {
    const fetchTemplateExercises = async () => {
      try {
        // Fetch template exercises from the API
        const response = await fetch('/api/exercises/templates');

        if (!response.ok) {
          throw new Error('Failed to fetch template exercises');
        }

        const exercises = await response.json();

        // Filter exercises based on selected difficulty
        const filteredExercises = exercises.filter(
          (ex: TemplateExercise) => ex.difficulty?.toLowerCase() === selectedDifficulty ||
                (selectedDifficulty === "advanced" && ex.difficulty?.toLowerCase() === "intermediate")
        );

        setTemplateExercises(filteredExercises);
      } catch (error) {
        console.error("Error fetching template exercises:", error);
        toast.error("Failed to load exercise templates");
      }
    };

    fetchTemplateExercises();
  }, [selectedDifficulty]);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      title: "",
      description: "",
      difficulty: "intermediate",
      is_template: false,
      selectedExercises: [],
    },
  })

  async function onSubmit(values: FormValues) {
    try {
      setIsLoading(true);

      // Simulate API request with timeout
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (initialData) {
        // Update existing plan (mock)
        toast.success("Training plan updated successfully");
      } else {
        // Create new plan (mock)
        toast.success("Training plan created successfully");
      }

      router.push("/dashboard/training-plans");
    } catch (error) {
      toast.error("Something went wrong");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input placeholder="Enter plan title" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter plan description"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="difficulty"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Difficulty Level</FormLabel>
              <Select
                onValueChange={(value) => {
                  field.onChange(value)
                  setSelectedDifficulty(value)
                }}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select difficulty level" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="beginner">Beginner</SelectItem>
                  <SelectItem value="intermediate">Intermediate</SelectItem>
                  <SelectItem value="advanced">Advanced</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="is_template"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Save as Template</FormLabel>
              </div>
            </FormItem>
          )}
        />

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Template Exercises</h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {templateExercises.map((exercise) => (
              <FormField
                key={exercise.id}
                control={form.control}
                name="selectedExercises"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value?.includes(exercise.id)}
                        onCheckedChange={(checked) => {
                          const currentValue = field.value || []
                          if (checked) {
                            field.onChange([...currentValue, exercise.id])
                          } else {
                            field.onChange(currentValue.filter((id) => id !== exercise.id))
                          }
                        }}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>{exercise.name}</FormLabel>
                      {exercise.description && (
                        <p className="text-sm text-muted-foreground">
                          {exercise.description}
                        </p>
                      )}
                      <div className="text-sm">
                        {exercise.type === "strength" && (
                          <>
                            {exercise.sets && <span>Sets: {exercise.sets} </span>}
                            {exercise.reps && <span>Reps: {exercise.reps}</span>}
                          </>
                        )}
                        {(exercise.type === "cardio" || exercise.type === "flexibility") && (
                          exercise.duration && <span>Duration: {exercise.duration} min</span>
                        )}
                        {exercise.restTime && <span>Rest: {exercise.restTime}s</span>}
                      </div>
                    </div>
                  </FormItem>
                )}
              />
            ))}
          </div>
        </div>

        <div className="flex gap-4">
          <Button type="submit" disabled={isLoading}>
            {initialData ? "Update Plan" : "Create Plan"}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isLoading}
          >
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  )
}