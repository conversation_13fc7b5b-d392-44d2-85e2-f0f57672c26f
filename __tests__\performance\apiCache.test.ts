import { APICache, withCache, createCacheKey } from '@/lib/cache/apiCache';

describe('API Cache Tests', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('APICache Class Tests', () => {
    test('stores and retrieves values correctly', () => {
      const cache = new APICache();

      cache.set('test-key', { data: 'test-value' });

      expect(cache.get('test-key')).toEqual({ data: 'test-value' });
      expect(cache.has('test-key')).toBe(true);
      expect(cache.size()).toBe(1);
    });

    test('respects TTL for cached values', () => {
      const cache = new APICache({
        defaultTTL: 1000 // 1 second TTL
      });

      cache.set('test-key', { data: 'test-value' });

      // Value should be available immediately
      expect(cache.get('test-key')).toEqual({ data: 'test-value' });

      // Advance time past TTL
      jest.advanceTimersByTime(1100);

      // Value should now be expired
      expect(cache.get('test-key')).toBeUndefined();
      expect(cache.has('test-key')).toBe(false);
      expect(cache.size()).toBe(0);
    });

    test('honors custom TTL per item', () => {
      const cache = new APICache({
        defaultTTL: 5000 // 5 seconds default TTL
      });

      // Item with default TTL
      cache.set('default-ttl', { data: 'default' });

      // Item with custom TTL
      cache.set('custom-ttl', { data: 'custom' }, 1000); // 1 second TTL

      // Advance time past custom TTL but before default TTL
      jest.advanceTimersByTime(1100);

      // Custom TTL item should be expired
      expect(cache.get('custom-ttl')).toBeUndefined();

      // Default TTL item should still be available
      expect(cache.get('default-ttl')).toEqual({ data: 'default' });
    });

    test('removes least recently used items when max size is reached', () => {
      const cache = new APICache({
        maxSize: 3
      });

      // Add 3 items (fills the cache)
      cache.set('key1', { data: 'value1' });
      cache.set('key2', { data: 'value2' });
      cache.set('key3', { data: 'value3' });

      // Access key1 to make it the most recently used
      cache.get('key1');

      // Add a 4th item, should evict the least recently used
      cache.set('key4', { data: 'value4' });

      expect(cache.size()).toBe(3);
      // Check which keys are still in the cache
      const hasKey1 = cache.has('key1');
      const hasKey2 = cache.has('key2');
      const hasKey3 = cache.has('key3');
      const hasKey4 = cache.has('key4');

      // The new item should definitely be in the cache
      expect(hasKey4).toBe(true);

      // Only one of the original items should be evicted
      expect(hasKey1 || hasKey2 || hasKey3).toBe(true);
      expect(hasKey1 && hasKey2 && hasKey3).toBe(false);

      // We should have exactly 3 items in the cache
      expect([hasKey1, hasKey2, hasKey3, hasKey4].filter(Boolean).length).toBe(3);
    });

    test('cleans up expired entries periodically', () => {
      // Create cache with 100ms cleanup interval
      const cache = new APICache({
        cleanupInterval: 100,
        defaultTTL: 200 // 200ms TTL
      });

      // Add two items
      cache.set('key1', { data: 'value1' });
      cache.set('key2', { data: 'value2' });

      expect(cache.size()).toBe(2);

      // Advance time past TTL but before cleanup
      jest.advanceTimersByTime(250); // Past the 200ms TTL

      // Items should still be in the cache until cleanup runs
      expect(cache.size()).toBe(2);

      // But they should not be retrievable because they're expired
      expect(cache.get('key1')).toBeUndefined();
      expect(cache.get('key2')).toBeUndefined();

      // Advance time to trigger cleanup
      jest.advanceTimersByTime(100);

      // Cache should now be empty
      expect(cache.size()).toBe(0);
    });

    test('can be cleared manually', () => {
      const cache = new APICache();

      cache.set('key1', { data: 'value1' });
      cache.set('key2', { data: 'value2' });

      expect(cache.size()).toBe(2);

      cache.clear();

      expect(cache.size()).toBe(0);
    });

    test('can delete specific entries', () => {
      const cache = new APICache();

      cache.set('key1', { data: 'value1' });
      cache.set('key2', { data: 'value2' });

      expect(cache.size()).toBe(2);

      cache.delete('key1');

      expect(cache.size()).toBe(1);
      expect(cache.has('key1')).toBe(false);
      expect(cache.has('key2')).toBe(true);
    });

    test('stops timer on destroy', () => {
      const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      const cache = new APICache();

      cache.destroy();

      expect(clearIntervalSpy).toHaveBeenCalled();
    });
  });

  describe('WithCache Decorator Tests', () => {
    test('caches function results', async () => {
      // Create a mock function that we'll wrap with the cache
      const mockFn = jest.fn().mockResolvedValue({ data: 'test result' });

      // Create a cache and wrap the function
      const cache = new APICache();
      const cachedFn = withCache(mockFn, cache, 'test-prefix');

      // First call should execute the function
      const result1 = await cachedFn('arg1', 123);

      // Second call with same args should use cache
      const result2 = await cachedFn('arg1', 123);

      // Should have same result
      expect(result1).toEqual(result2);

      // But function should only be called once
      expect(mockFn).toHaveBeenCalledTimes(1);

      // Call with different args should execute function again
      await cachedFn('arg2', 456);

      expect(mockFn).toHaveBeenCalledTimes(2);
    });

    test('cache key generation', () => {
      // Simple URL
      expect(createCacheKey('/api/data')).toBe('/api/data');

      // URL with params object
      const key = createCacheKey('/api/data', { id: 1, filter: 'active' });

      // Should contain URL and sorted params
      expect(key).toContain('/api/data');
      expect(key).toContain('filter');
      expect(key).toContain('id');

      // Order of properties in the object shouldn't matter
      const key1 = createCacheKey('/api/data', { a: 1, b: 2 });
      const key2 = createCacheKey('/api/data', { b: 2, a: 1 });

      expect(key1).toBe(key2);
    });
  });
});