import { errorTracking } from '@/lib/errorTracking';

// Define the ErrorEvent type based on the actual implementation
interface ErrorEvent {
  message: string;
  source: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  timestamp: Date;
  context?: {
    user?: Record<string, string>;
    tags?: Record<string, string>;
    metadata?: Record<string, any>;
  };
  stack?: string;
}

// Mock the error tracking module implementation
jest.mock('@/lib/errorTracking', () => {
  // Create a state to track internal values
  const state = {
    initialized: false,
    breadcrumbs: [] as string[],
    user: null as Record<string, string> | null,
    maxBreadcrumbs: 10,
    apiEndpoint: null as string | null,
  };
  
  // Mock implementation
  return {
    errorTracking: {
      init: jest.fn(() => {
        if (!state.initialized) {
          console.log('Initializing error tracking service');
          state.initialized = true;
          
          // Add event listeners in browser environment
          if (typeof window !== 'undefined') {
            window.addEventListener('error', jest.fn());
            window.addEventListener('unhandledrejection', jest.fn());
          }
        } else {
          console.warn('Error tracking service already initialized');
        }
        return true;
      }),
      captureMessage: jest.fn((message: string) => {
        console.error(`Error: ${message}`);
        return true;
      }),
      captureException: jest.fn((error: Error | unknown) => {
        const errorMsg = error instanceof Error ? error.message : String(error);
        console.error(`Error: ${errorMsg}`);
        return true;
      }),
      setUser: jest.fn((user: Record<string, string>) => {
        state.user = user;
      }),
      addBreadcrumb: jest.fn((breadcrumb: string) => {
        state.breadcrumbs.push(breadcrumb);
        // Maintain the max breadcrumbs limit
        if (state.breadcrumbs.length > state.maxBreadcrumbs) {
          state.breadcrumbs.shift();
        }
      }),
      // Create a higher-order function for error tracking
      withErrorTracking: jest.fn(<T extends (...args: any[]) => any>(fn: T) => {
        return (...args: Parameters<T>) => {
          try {
            const result = fn(...args);
            // Check if the result is a promise
            if (result instanceof Promise) {
              return result.catch(error => {
                console.error(`Error in wrapped function: ${error}`);
                throw error;
              });
            }
            return result;
          } catch (error) {
            console.error(`Error in wrapped function: ${error}`);
            throw error;
          }
        };
      }),
      // Method for testing to access state
      __getBreadcrumbs: jest.fn(() => [...state.breadcrumbs]),
      __getUser: jest.fn(() => state.user),
      // Reset state for testing
      __resetState: jest.fn(() => {
        state.initialized = false;
        state.breadcrumbs = [];
        state.user = null;
      })
    },
  };
});

describe('Error Tracking Service Tests', () => {
  let consoleLogSpy: jest.SpyInstance;
  let consoleWarnSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;
  let addEventListenerSpy: jest.SpyInstance;
  
  beforeEach(() => {
    // Set up spies for console methods
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
    consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    
    // Mock window.addEventListener for browser tests
    addEventListenerSpy = jest.spyOn(window, 'addEventListener').mockImplementation(jest.fn());
    
    // Reset error tracking service state
    errorTracking.__resetState();
  });
  
  afterEach(() => {
    // Restore all mocks
    jest.restoreAllMocks();
  });
  
  describe('Error Tracking Initialization', () => {
    test('initializes only once', () => {
      // Initialize the service
      const firstInit = errorTracking.init();
      expect(firstInit).toBe(true);
      expect(consoleLogSpy).toHaveBeenCalledWith('Initializing error tracking service');
      
      // Try to initialize again
      const secondInit = errorTracking.init();
      expect(secondInit).toBe(true); // Still returns true
      expect(consoleWarnSpy).toHaveBeenCalledWith('Error tracking service already initialized');
      
      // Initialization logs should appear only once
      expect(consoleLogSpy).toHaveBeenCalledTimes(1);
    });
    
    test('sets up event listeners in browser environment', () => {
      // Initialize the service
      errorTracking.init();
      
      // Check if event listeners were added
      expect(addEventListenerSpy).toHaveBeenCalledWith('error', expect.any(Function));
      expect(addEventListenerSpy).toHaveBeenCalledWith('unhandledrejection', expect.any(Function));
    });
  });
  
  describe('Error Capture Methods', () => {
    test('captureMessage formats and processes messages correctly', () => {
      errorTracking.init();
      
      // Capture a message
      errorTracking.captureMessage('Test error message');
      
      // Check if the message was logged
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error: Test error message');
    });
    
    test('captureException handles Error objects', () => {
      errorTracking.init();
      
      // Create an error object
      const error = new Error('Test exception');
      
      // Capture the exception
      errorTracking.captureException(error);
      
      // Check if the error was logged
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error: Test exception');
    });
    
    test('captureException handles non-Error objects', () => {
      errorTracking.init();
      
      // Capture a non-Error object
      errorTracking.captureException('String exception');
      
      // Check if the error was logged
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error: String exception');
    });
  });
  
  describe('Context and Breadcrumbs', () => {
    test('addBreadcrumb adds breadcrumbs with timestamp', () => {
      errorTracking.init();
      
      // Add breadcrumbs
      errorTracking.addBreadcrumb('User clicked button');
      errorTracking.addBreadcrumb('User submitted form');
      
      // Check breadcrumbs are stored
      const breadcrumbs = errorTracking.__getBreadcrumbs();
      expect(breadcrumbs).toContain('User clicked button');
      expect(breadcrumbs).toContain('User submitted form');
    });
    
    test('setUser adds user context', () => {
      errorTracking.init();
      
      // Set user context
      const user = { id: '123', email: '<EMAIL>' };
      errorTracking.setUser(user);
      
      // Check user context is stored
      expect(errorTracking.__getUser()).toEqual(user);
    });
    
    test('respects maxBreadcrumbs limit', () => {
      errorTracking.init();
      
      // Add more breadcrumbs than the limit
      for (let i = 0; i < 15; i++) {
        errorTracking.addBreadcrumb(`Breadcrumb ${i}`);
      }
      
      // Check breadcrumbs are limited
      const breadcrumbs = errorTracking.__getBreadcrumbs();
      expect(breadcrumbs.length).toBe(10);
      expect(breadcrumbs[0]).toBe('Breadcrumb 5');
      expect(breadcrumbs[9]).toBe('Breadcrumb 14');
    });
  });
  
  describe('Higher-Order Function', () => {
    test('withErrorTracking wraps synchronous functions', () => {
      // Create a test function
      const testFn = jest.fn(() => 'result');
      
      // Wrap the function with error tracking
      const wrappedFn = errorTracking.withErrorTracking(testFn);
      
      // Call the wrapped function
      const result = wrappedFn();
      
      // Check if original function was called and result passed through
      expect(testFn).toHaveBeenCalled();
      expect(result).toBe('result');
    });
    
    test('withErrorTracking wraps asynchronous functions', async () => {
      // Create a test async function
      const testAsyncFn = jest.fn(async () => 'async result');
      
      // Wrap the function with error tracking
      const wrappedAsyncFn = errorTracking.withErrorTracking(testAsyncFn);
      
      // Call the wrapped function
      const result = await wrappedAsyncFn();
      
      // Check if original function was called and result passed through
      expect(testAsyncFn).toHaveBeenCalled();
      expect(result).toBe('async result');
    });
    
    test('withErrorTracking captures errors in synchronous functions', () => {
      // Create a test function that throws
      const testFn = jest.fn(() => {
        throw new Error('Sync error');
      });
      
      // Wrap the function with error tracking
      const wrappedFn = errorTracking.withErrorTracking(testFn);
      
      // Call the wrapped function and expect an error
      expect(() => wrappedFn()).toThrow('Sync error');
      
      // Check if error was captured in logs
      expect(consoleErrorSpy).toHaveBeenCalledWith(expect.stringContaining('Sync error'));
    });
    
    test('withErrorTracking captures errors in asynchronous functions', async () => {
      // Create a test async function that throws
      const testAsyncFn = jest.fn(async () => {
        throw new Error('Async error');
      });
      
      // Wrap the function with error tracking
      const wrappedAsyncFn = errorTracking.withErrorTracking(testAsyncFn);
      
      // Call the wrapped function and expect an error
      await expect(wrappedAsyncFn()).rejects.toThrow('Async error');
      
      // Check if error was captured in logs
      expect(consoleErrorSpy).toHaveBeenCalledWith(expect.stringContaining('Async error'));
    });
  });
}); 