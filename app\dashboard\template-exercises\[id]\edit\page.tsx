import { notFound } from "next/navigation"
import { getServerSession } from "next-auth"
import { TemplateExerciseForm } from "@/components/forms/template-exercise-form"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { Exercise } from "@prisma/client"

// Define allowed types for the form
type AllowedExerciseType = "strength" | "cardio" | "flexibility" | "recovery";
type AllowedDifficulty = "beginner" | "intermediate" | "advanced";

// Type guard to check if a string is an allowed exercise type
function isAllowedExerciseType(type: string | null | undefined): type is AllowedExerciseType {
    return !!type && ["strength", "cardio", "flexibility", "recovery"].includes(type);
}

// Type guard for difficulty
function isAllowedDifficulty(difficulty: string | null | undefined): difficulty is AllowedDifficulty {
    return !!difficulty && ["beginner", "intermediate", "advanced"].includes(difficulty);
}

export default async function EditTemplateExercisePage(props: any) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id || session.user.role !== "admin") {
    notFound()
  }

  const templateId = props.params?.id;
  if (!templateId) {
      console.error("Template Exercise ID missing from params");
      notFound();
  }

  const exercise: Exercise | null = await prisma.exercise.findUnique({
    where: {
      id: templateId,
      isTemplate: true,
    },
  })

  if (!exercise) {
    notFound()
  }

  // Map Prisma Exercise type to TemplateExerciseForm initialData type
  const initialDataForForm = {
      id: exercise.id,
      name: exercise.name,
      description: exercise.description ?? undefined,
      sets: exercise.sets ?? undefined,
      reps: exercise.reps ?? undefined,
      duration: exercise.duration ?? undefined,
      restTime: exercise.restTime ?? undefined,
      videoUrl: exercise.videoUrl ?? undefined,
      // Validate and default type
      type: isAllowedExerciseType(exercise.type) ? exercise.type : 'strength', 
      // Validate and default difficulty
      difficulty: isAllowedDifficulty(exercise.difficulty) ? exercise.difficulty : 'intermediate',
      muscleGroup: exercise.muscleGroup ?? undefined,
      equipment: exercise.equipment ?? undefined,
      thumbnailUrl: exercise.thumbnailUrl ?? undefined
  };

  return (
    <div className="container mx-auto py-6 max-w-3xl">
      <h1 className="text-2xl font-bold mb-6">Edit Template Exercise</h1>
      <TemplateExerciseForm initialData={initialDataForForm} />
    </div>
  )
} 