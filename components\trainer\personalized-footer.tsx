"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Instagram, Twitter, Youtube, Facebook, Linkedin, Globe, Mail, Phone, MapPin, Send } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useToast } from "@/components/ui/use-toast"
import { TrainerTheme, Trainer } from "@/types/trainer"

interface PersonalizedFooterProps {
  trainer: Trainer
  theme: TrainerTheme & {
    footerStyle?: 'minimal' | 'standard' | 'expanded'
    footerBackground?: string
    footerTextColor?: string
    showNewsletter?: boolean
    showMap?: boolean
    mapLocation?: {
      address: string
      embedUrl: string
    }
    contactInfo?: {
      email?: string
      phone?: string
      address?: string
    }
    footerLinks?: Array<{
      title: string
      url: string
    }>
    copyrightText?: string
  }
}

// Social media icon mapping
const socialIcons: Record<string, JSX.Element> = {
  instagram: <Instagram className="h-5 w-5" />,
  twitter: <Twitter className="h-5 w-5" />,
  youtube: <Youtube className="h-5 w-5" />,
  facebook: <Facebook className="h-5 w-5" />,
  linkedin: <Linkedin className="h-5 w-5" />,
  website: <Globe className="h-5 w-5" />,
}

export function PersonalizedFooter({ trainer, theme }: PersonalizedFooterProps) {
  const { toast } = useToast()
  const [email, setEmail] = useState("")
  
  // Parse social links safely
  let socialLinksParsed: Record<string, string | null> = {}
  try {
    socialLinksParsed = typeof trainer.socialLinks === 'string' 
      ? JSON.parse(trainer.socialLinks) 
      : trainer.socialLinks || {}
  } catch (e) {
    console.error("Error parsing social links:", e)
    socialLinksParsed = {} // Ensure it's an object even on error
  }
  
  // Handle newsletter subscription
  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email.trim()) {
      toast({
        title: "Error",
        description: "Please enter your email address",
        variant: "destructive"
      })
      return
    }
    
    // Simulate API call
    toast({
      title: "Success!",
      description: "You've been subscribed to the newsletter",
    })
    
    setEmail("")
  }
  
  // Determine footer style
  const footerStyle = theme.footerStyle || 'standard'
  
  // Minimal footer
  if (footerStyle === 'minimal') {
    return (
      <footer 
        className="py-6"
        style={{ 
          backgroundColor: theme.footerBackground || '#f9fafb',
          color: theme.footerTextColor || 'inherit'
        }}
      >
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <p className="text-sm text-muted-foreground">
                {theme.copyrightText || `© ${new Date().getFullYear()} ${trainer.name}. All rights reserved.`}
              </p>
            </div>
            
            <div className="flex space-x-4">
              {Object.entries(socialLinksParsed).map(([platform, url]) => 
                url && socialIcons[platform] ? (
                  <a 
                    key={platform}
                    href={url as string}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors"
                    aria-label={`Link to ${platform}`}
                  >
                    {socialIcons[platform]}
                  </a>
                ) : null
              )}
            </div>
          </div>
        </div>
      </footer>
    )
  }
  
  // Expanded footer
  if (footerStyle === 'expanded') {
    return (
      <footer 
        className="pt-12 pb-6"
        style={{ 
          backgroundColor: theme.footerBackground || '#f9fafb',
          color: theme.footerTextColor || 'inherit'
        }}
      >
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            {/* About */}
            <div>
              <h3 className="font-bold text-lg mb-4">About</h3>
              <div className="mb-4">
                {theme.logoUrl ? (
                  <div className="h-12 w-12 relative mb-4">
                    <Image 
                      src={theme.logoUrl} 
                      alt={`${trainer.name || 'Trainer'}'s logo`}
                      width={48}
                      height={48}
                      className="w-full h-full object-contain"
                    />
                  </div>
                ) : (
                  <span className="text-xl font-bold">{trainer.name}</span>
                )}
              </div>
              <p className="text-sm text-muted-foreground mb-4">
                {trainer.bio ? trainer.bio.substring(0, 120) + '...' : 'Professional fitness trainer providing personalized coaching and programs.'}
              </p>
              <div className="flex space-x-3">
                {Object.entries(socialLinksParsed).map(([platform, url]) => 
                  url && socialIcons[platform] ? (
                    <a 
                      key={platform}
                      href={url as string}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors"
                      aria-label={`Link to ${platform}`}
                    >
                      {socialIcons[platform]}
                    </a>
                  ) : null
                )}
              </div>
            </div>
            
            {/* Quick Links */}
            <div>
              <h3 className="font-bold text-lg mb-4">Quick Links</h3>
              <ul className="space-y-2">
                {theme.footerLinks && theme.footerLinks.length > 0 ? (
                  theme.footerLinks.map((link, i) => (
                    <li key={i}>
                      <Link 
                        href={link.url}
                        className="text-muted-foreground hover:text-primary transition-colors"
                      >
                        {link.title}
                      </Link>
                    </li>
                  ))
                ) : (
                  <>
                    <li>
                      <Link 
                        href="#"
                        className="text-muted-foreground hover:text-primary transition-colors"
                      >
                        Programs
                      </Link>
                    </li>
                    <li>
                      <Link 
                        href="#"
                        className="text-muted-foreground hover:text-primary transition-colors"
                      >
                        Memberships
                      </Link>
                    </li>
                    <li>
                      <Link 
                        href="#"
                        className="text-muted-foreground hover:text-primary transition-colors"
                      >
                        About Me
                      </Link>
                    </li>
                    <li>
                      <Link 
                        href="#"
                        className="text-muted-foreground hover:text-primary transition-colors"
                      >
                        Contact
                      </Link>
                    </li>
                  </>
                )}
              </ul>
            </div>
            
            {/* Contact Info */}
            <div>
              <h3 className="font-bold text-lg mb-4">Contact</h3>
              <ul className="space-y-3">
                {theme.contactInfo?.email && (
                  <li className="flex items-start">
                    <Mail className="h-5 w-5 mr-2 text-primary mt-0.5" />
                    <a 
                      href={`mailto:${theme.contactInfo.email}`}
                      className="text-muted-foreground hover:text-primary transition-colors"
                    >
                      {theme.contactInfo.email}
                    </a>
                  </li>
                )}
                
                {theme.contactInfo?.phone && (
                  <li className="flex items-start">
                    <Phone className="h-5 w-5 mr-2 text-primary mt-0.5" />
                    <a 
                      href={`tel:${theme.contactInfo.phone}`}
                      className="text-muted-foreground hover:text-primary transition-colors"
                    >
                      {theme.contactInfo.phone}
                    </a>
                  </li>
                )}
                
                {theme.contactInfo?.address && (
                  <li className="flex items-start">
                    <MapPin className="h-5 w-5 mr-2 text-primary mt-0.5" />
                    <span className="text-muted-foreground">
                      {theme.contactInfo.address}
                    </span>
                  </li>
                )}
              </ul>
            </div>
            
            {/* Newsletter */}
            {theme.showNewsletter && (
              <div>
                <h3 className="font-bold text-lg mb-4">Newsletter</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Subscribe to get updates on new programs and fitness tips.
                </p>
                <form onSubmit={handleSubscribe} className="flex">
                  <Input
                    type="email"
                    placeholder="Your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="rounded-r-none"
                  />
                  <Button type="submit" className="rounded-l-none">
                    <Send className="h-4 w-4" />
                  </Button>
                </form>
              </div>
            )}
          </div>
          
          {/* Map */}
          {theme.showMap && theme.mapLocation?.embedUrl && (
            <div className="mb-12">
              <div className="rounded-lg overflow-hidden h-64">
                <iframe
                  src={theme.mapLocation.embedUrl}
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Location map"
                />
              </div>
            </div>
          )}
          
          {/* Copyright */}
          <div className="border-t pt-6 text-center">
            <p className="text-sm text-muted-foreground">
              {theme.copyrightText || `© ${new Date().getFullYear()} ${trainer.name}. All rights reserved.`}
            </p>
          </div>
        </div>
      </footer>
    )
  }
  
  // Standard footer (default)
  return (
    <footer 
      className="pt-10 pb-6"
      style={{ 
        backgroundColor: theme.footerBackground || '#f9fafb',
        color: theme.footerTextColor || 'inherit'
      }}
    >
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between mb-8">
          <div className="mb-6 md:mb-0">
            <div className="mb-4">
              {theme.logoUrl ? (
                <div className="h-12 w-12 relative">
                  <Image 
                    src={theme.logoUrl} 
                    alt={`${trainer.name || 'Trainer'}'s logo`}
                    width={48}
                    height={48}
                    className="w-full h-full object-contain"
                  />
                </div>
              ) : (
                <span className="text-xl font-bold">{trainer.name}</span>
              )}
            </div>
            <p className="text-sm text-muted-foreground max-w-md mb-4">
              {trainer.bio ? trainer.bio.substring(0, 120) + '...' : 'Professional fitness trainer providing personalized coaching and programs.'}
            </p>
            <div className="flex space-x-3">
              {Object.entries(socialLinksParsed).map(([platform, url]) => 
                url && socialIcons[platform] ? (
                  <a 
                    key={platform}
                    href={url as string}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors"
                    aria-label={`Link to ${platform}`}
                  >
                    {socialIcons[platform]}
                  </a>
                ) : null
              )}
            </div>
          </div>
          
          {theme.showNewsletter && (
            <div className="md:w-1/3">
              <h3 className="font-bold text-lg mb-4">Stay Updated</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Subscribe to my newsletter for fitness tips and program updates.
              </p>
              <form onSubmit={handleSubscribe} className="flex">
                <Input
                  type="email"
                  placeholder="Your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="rounded-r-none"
                />
                <Button type="submit" className="rounded-l-none">
                  Subscribe
                </Button>
              </form>
            </div>
          )}
        </div>
        
        <div className="border-t pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground mb-4 md:mb-0">
            {theme.copyrightText || `© ${new Date().getFullYear()} ${trainer.name}. All rights reserved.`}
          </p>
          
          <div className="flex space-x-4">
            {theme.footerLinks && theme.footerLinks.length > 0 ? (
              theme.footerLinks.map((link, i) => (
                <Link 
                  key={i}
                  href={link.url}
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  {link.title}
                </Link>
              ))
            ) : (
              <>
                <Link 
                  href="#"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  Privacy Policy
                </Link>
                <Link 
                  href="#"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  Terms of Service
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </footer>
  )
}
