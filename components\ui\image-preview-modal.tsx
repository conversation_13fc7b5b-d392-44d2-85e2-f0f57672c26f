'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
} from '@/components/ui/dialog';
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { X } from 'lucide-react';
import Image from 'next/image';

interface ImagePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  fileName?: string;
}

export function ImagePreviewModal({
  isOpen,
  onClose,
  imageUrl,
  fileName,
}: ImagePreviewModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl p-0 overflow-hidden bg-black/95 backdrop-blur-sm border-none">
        <div className="relative w-full h-[85vh] flex flex-col">
          {/* Top bar with file info and close button */}
          <div className="flex items-center justify-between p-3 bg-background/10 backdrop-blur-sm z-10">
            {fileName && (
              <div className="flex items-center">
                <div className="text-sm font-medium text-white/90 truncate max-w-md">
                  {fileName}
                </div>
              </div>
            )}
            <div className="flex items-center gap-2">
              <button
                onClick={() => window.open(imageUrl, '_blank')}
                className="rounded-full p-2 bg-background/20 hover:bg-background/30 text-white/90 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                <span className="sr-only">Download</span>
              </button>
              <DialogPrimitive.Close className="rounded-full p-2 bg-background/20 hover:bg-background/30 text-white/90 transition-colors">
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </DialogPrimitive.Close>
            </div>
          </div>

          {/* Image container */}
          <div className="flex-1 flex items-center justify-center overflow-hidden">
            <div className="relative w-full h-full max-h-[calc(85vh-48px)]">
              <Image
                src={imageUrl}
                alt={fileName || 'Image preview'}
                fill
                style={{ objectFit: 'contain' }}
                className="select-none"
                quality={100}
                priority
              />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
