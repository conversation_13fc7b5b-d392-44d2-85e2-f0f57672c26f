import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// Get user's achievements
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const userId = session.user.id

    // Get user's achievements
    const achievements = await prisma.achievement.findMany({
      where: {
        userId: userId,
      },
      orderBy: {
        earnedAt: "desc",
      },
    })

    return NextResponse.json(achievements)
  } catch (error) {
    console.error("[ACHIEVEMENTS_GET]", error)
    return new NextResponse("Internal Error", { status: 500 })
  }
}

// Create a new achievement (typically called from other endpoints)
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const userId = session.user.id
    const body = await request.json()
    const { type, name, description, iconName, level } = body

    if (!type || !name || !description) {
      return new NextResponse("Type, name, and description are required", { status: 400 })
    }

    // Create achievement
    const achievement = await prisma.achievement.create({
      data: {
        userId,
        type,
        name,
        description,
        iconName: iconName || null,
        level: level || 1,
      },
    })

    return NextResponse.json(achievement)
  } catch (error) {
    console.error("[ACHIEVEMENT_CREATE]", error)
    return new NextResponse("Internal Error", { status: 500 })
  }
}
