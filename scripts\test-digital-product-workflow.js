// Manual test script for digital product workflow
const fetch = require('node-fetch');

async function testDigitalProductWorkflow() {
  console.log('Starting digital product workflow test...');
  
  try {
    // Step 1: Login as dev user
    console.log('\n1. Logging in as dev user...');
    console.log('Visit: http://localhost:3000/api/auth/dev-login?role=client');
    console.log('You should be redirected to the dashboard');
    
    // Step 2: Browse products
    console.log('\n2. Browse products in the shop');
    console.log('Visit: http://localhost:3000/dashboard/shop');
    console.log('You should see a list of products');
    
    // Step 3: Add a product to cart
    console.log('\n3. Add a product to cart');
    console.log('Click the "Add to Cart" button on any product');
    console.log('You should see a toast notification confirming the product was added');
    
    // Step 4: View cart
    console.log('\n4. View your cart');
    console.log('Visit: http://localhost:3000/dashboard/cart');
    console.log('You should see the product you added in your cart');
    
    // Step 5: Checkout
    console.log('\n5. Checkout');
    console.log('Click the "Checkout" button');
    console.log('You should see a success message and be redirected to the library page');
    
    // Step 6: View library
    console.log('\n6. View your library');
    console.log('Visit: http://localhost:3000/dashboard/library');
    console.log('You should see the product you purchased in your library');
    
    console.log('\nTest completed! If all steps worked as expected, the digital product workflow is functioning correctly.');
  } catch (error) {
    console.error('Error during test:', error);
  }
}

// Run the test
testDigitalProductWorkflow();
