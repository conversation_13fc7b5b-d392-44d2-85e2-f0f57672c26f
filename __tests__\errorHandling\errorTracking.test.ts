import {
  errorTracking,
  captureException,
  addBreadcrumb,
  withErrorTracking,
  initErrorTracking as init,
  Breadcrumb
} from '@/lib/errorHandling/errorTracking';

// Import or create the ErrorEvent interface for type safety
interface ErrorEvent {
  message: string;
  source: string;
  severity: string;
  timestamp: Date;
  context?: {
    user?: { id?: string; email?: string; role?: string };
    tags?: Record<string, string>;
    metadata?: Record<string, any>;
  };
  stack?: string;
}

describe('Error Tracking Service Tests', () => {
  let consoleErrorSpy: jest.SpyInstance;
  let consoleLogSpy: jest.SpyInstance;
  let consoleWarnSpy: jest.SpyInstance;
  let fetchSpy: jest.SpyInstance;

  beforeEach(() => {
    // Mock console methods
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
    consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

    // Mock fetch for API endpoint tests
    fetchSpy = jest.spyOn(global, 'fetch').mockImplementation(() =>
      Promise.resolve({} as Response)
    );

    // Reset the error tracking service for each test
    // @ts-ignore - Accessing private property for testing
    errorTracking.initialized = false;
    // @ts-ignore - Accessing private property for testing
    errorTracking.breadcrumbs = [];
    // @ts-ignore - Accessing private property for testing
    errorTracking.config = {
      environment: 'test',
      maxBreadcrumbs: 100,
      sampleRate: 1.0
    };
  });

  afterEach(() => {
    // Restore mocks
    consoleErrorSpy.mockRestore();
    consoleLogSpy.mockRestore();
    consoleWarnSpy.mockRestore();
    fetchSpy.mockRestore();
  });

  describe('Error Tracking Initialization', () => {
    test('initializes only once', () => {
      // Initialize twice
      init();
      init();

      // Should only log initialization once
      const initLogs = consoleLogSpy.mock.calls.filter(
        call => call[0].includes('initialized')
      );

      expect(initLogs.length).toBe(1);
    });

    test('sets up event listeners in browser environment', () => {
      // Mock window.addEventListener
      const addEventListenerSpy = jest.spyOn(window, 'addEventListener');

      // Initialize
      init();

      // Should set up error and unhandledrejection listeners
      expect(addEventListenerSpy).toHaveBeenCalledWith('error', expect.any(Function));
      expect(addEventListenerSpy).toHaveBeenCalledWith('unhandledrejection', expect.any(Function));

      addEventListenerSpy.mockRestore();
    });
  });

  describe('Error Capture Methods', () => {
    beforeEach(() => {
      init();
    });

    test('captureException formats and processes messages correctly', () => {
      // Spy on console.error which is called by processEvent
      captureException(new Error('Test message'));

      // Should call console.error with the captured message
      expect(consoleErrorSpy).toHaveBeenCalled();
      const capturedEvent = consoleErrorSpy.mock.calls[0][1];

      expect(capturedEvent.message).toBe('Test message');
      expect(capturedEvent.stack).toBeDefined();
    });

    test('captureException handles Error objects', () => {
      const testError = new Error('Test error');

      captureException(testError, 'error', 'test-source');

      // Should call console.error with the captured error
      expect(consoleErrorSpy).toHaveBeenCalled();
      const capturedEvent = consoleErrorSpy.mock.calls[0][1];

      expect(capturedEvent.message).toBe('Test error');
      expect(capturedEvent.stack).toBeDefined();
      expect(capturedEvent.severity).toBe('error');
      expect(capturedEvent.source).toBe('test-source');
    });

    test('captureException handles non-Error objects', () => {
      // String error
      captureException('String error', 'warning');

      // Should call console.error with the captured error
      expect(consoleErrorSpy).toHaveBeenCalled();
      const capturedEvent = consoleErrorSpy.mock.calls[0][1];

      expect(capturedEvent.message).toBe('String error');
      expect(capturedEvent.severity).toBe('warning');

      // Object error
      consoleErrorSpy.mockClear();
      captureException({ custom: 'error' });

      const capturedObjectEvent = consoleErrorSpy.mock.calls[0][1];
      expect(capturedObjectEvent.message).toBe('[object Object]');
    });
  });

  describe('Context and Breadcrumbs', () => {
    beforeEach(() => {
      init();
    });

    test('addBreadcrumb adds breadcrumbs with timestamp', () => {
      // Add a breadcrumb
      addBreadcrumb({
        type: 'navigation',
        category: 'routing',
        message: 'Navigated to /dashboard'
      });

      // Capture an error to include breadcrumbs
      captureException(new Error('Error with breadcrumbs'));

      // Verify breadcrumbs are included in context
      expect(consoleErrorSpy).toHaveBeenCalled();
      const capturedEvent = consoleErrorSpy.mock.calls[0][1];

      expect(capturedEvent.context.metadata.breadcrumbs).toHaveLength(1);
      expect(capturedEvent.context.metadata.breadcrumbs[0].type).toBe('navigation');
      expect(capturedEvent.context.metadata.breadcrumbs[0].category).toBe('routing');
      expect(capturedEvent.context.metadata.breadcrumbs[0].message).toBe('Navigated to /dashboard');
      expect(capturedEvent.context.metadata.breadcrumbs[0].timestamp).toBeDefined();
    });

    test('user context can be added to error tracking', () => {
      // Set user context directly on the errorTracking instance
      // @ts-ignore - Accessing private property for testing
      errorTracking.setUser({ id: '123', email: '<EMAIL>', role: 'admin' });

      // Capture an error to include user context
      captureException(new Error('Error with user context'));

      // Verify user context is included
      expect(consoleErrorSpy).toHaveBeenCalled();
      const capturedEvent = consoleErrorSpy.mock.calls[0][1];

      expect(capturedEvent.context.user).toEqual({
        id: '123',
        email: '<EMAIL>',
        role: 'admin'
      });
    });

    test('respects maxBreadcrumbs limit', () => {
      // @ts-ignore - Access private config for testing
      const maxBreadcrumbs = errorTracking.config.maxBreadcrumbs;

      // Add more breadcrumbs than the limit
      for (let i = 0; i < maxBreadcrumbs + 10; i++) {
        addBreadcrumb({
          type: 'info',
          category: 'test',
          message: `Breadcrumb ${i}`
        });
      }

      // Capture an error to include breadcrumbs
      captureException(new Error('Error with many breadcrumbs'));

      // Verify only maxBreadcrumbs are included
      expect(consoleErrorSpy).toHaveBeenCalled();
      const capturedEvent = consoleErrorSpy.mock.calls[0][1];

      expect(capturedEvent.context.metadata.breadcrumbs).toHaveLength(maxBreadcrumbs);
      // The oldest breadcrumbs should be dropped
      expect(capturedEvent.context.metadata.breadcrumbs[0].message).toBe(`Breadcrumb ${10}`);
    });
  });

  describe('Error Processing', () => {
    beforeEach(() => {
      init();
    });

    test('sends errors to API endpoint in production', () => {
      // Set up as if in production
      // @ts-ignore - Modifying private properties for testing
      errorTracking.config.environment = 'production';
      // @ts-ignore
      errorTracking.config.apiEndpoint = 'https://api.example.com/errors';

      captureException(new Error('Production error'));

      // Should attempt to send to API
      expect(fetchSpy).toHaveBeenCalledWith(
        'https://api.example.com/errors',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.any(String)
        })
      );

      // Restore environment
      // @ts-ignore
      errorTracking.config.environment = 'test';
    });
  });

  describe('Higher-Order Function', () => {
    beforeEach(() => {
      init();
    });
    test('withErrorTracking wraps synchronous functions', () => {
      const mockFn = jest.fn().mockImplementation(() => 'result');
      const wrappedFn = withErrorTracking(mockFn);

      const result = wrappedFn('arg1', 'arg2');

      // Should return the correct result
      expect(result).toBe('result');
      // Should pass arguments correctly
      expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
    });

    test('withErrorTracking wraps asynchronous functions', async () => {
      const mockFn = jest.fn().mockResolvedValue('async result');
      const wrappedFn = withErrorTracking(mockFn);

      const result = await wrappedFn('arg1', 'arg2');

      // Should return the correct result
      expect(result).toBe('async result');
      // Should pass arguments correctly
      expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
    });

    test('withErrorTracking captures errors in synchronous functions', () => {
      const error = new Error('Sync error');
      const mockFn = jest.fn().mockImplementation(() => {
        throw error;
      });

      const wrappedFn = withErrorTracking(mockFn);

      // Should rethrow the error
      expect(() => wrappedFn()).toThrow(error);

      // Should capture the exception
      expect(consoleErrorSpy).toHaveBeenCalled();
    });

    test('withErrorTracking captures errors in asynchronous functions', async () => {
      const error = new Error('Async error');
      const mockFn = jest.fn().mockRejectedValue(error);
      const wrappedFn = withErrorTracking(mockFn);

      // Should rethrow the error
      await expect(wrappedFn()).rejects.toThrow(error);

      // Should capture the exception
      expect(consoleErrorSpy).toHaveBeenCalled();
    });
  });
});