import { NextRequest, NextResponse } from 'next/server'

/**
 * CSP Violation Report Endpoint
 * 
 * This endpoint receives Content Security Policy violation reports from browsers.
 * It helps identify and fix security issues by logging when scripts, styles, or
 * other resources violate the site's security policy.
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the CSP violation report
    const report = await request.json()
    
    // Log the violation details (would connect to monitoring system in production)
    if (process.env.NODE_ENV === 'production') {
      // In production, you would send this to your logging/monitoring service
      // Example: await sendToSecurityMonitoring(report)
      
      // For now, log to console with sensitive details redacted
      const sanitizedReport = sanitizeReport(report)
      console.error('CSP Violation:', JSON.stringify(sanitizedReport, null, 2))
    } else {
      // In development, log complete details for debugging
      console.warn('CSP Violation:', JSON.stringify(report, null, 2))
    }
    
    // Return success to acknowledge receipt
    return NextResponse.json({ received: true }, { status: 204 })
  } catch (error) {
    console.error('Error processing CSP report:', error)
    return NextResponse.json(
      { error: 'Failed to process CSP report' },
      { status: 400 }
    )
  }
}

/**
 * Sanitize the CSP report to remove potentially sensitive information
 * before logging or storing it.
 */
function sanitizeReport(report: any) {
  // Make a copy to avoid mutating the original
  const sanitized = { ...report }
  
  // If there's a 'csp-report' object as per the CSP spec
  if (sanitized['csp-report']) {
    const cspReport = { ...sanitized['csp-report'] }
    
    // Redact potentially sensitive parts of URLs
    if (cspReport['document-uri']) {
      cspReport['document-uri'] = redactUrl(cspReport['document-uri'])
    }
    
    if (cspReport['blocked-uri']) {
      cspReport['blocked-uri'] = redactUrl(cspReport['blocked-uri'])
    }
    
    if (cspReport['source-file']) {
      cspReport['source-file'] = redactUrl(cspReport['source-file'])
    }
    
    // Replace the original report with sanitized version
    sanitized['csp-report'] = cspReport
  }
  
  return sanitized
}

/**
 * Redact sensitive parts of URLs such as query parameters that might
 * contain tokens, session IDs, or other sensitive data.
 */
function redactUrl(url: string): string {
  try {
    // Parse the URL
    const parsedUrl = new URL(url)
    
    // Remove query parameters and hash
    parsedUrl.search = parsedUrl.search ? '?[REDACTED]' : ''
    parsedUrl.hash = parsedUrl.hash ? '#[REDACTED]' : ''
    
    // If the path contains user-specific information, consider redacting parts of it
    // For example: /users/123/profile -> /users/[REDACTED]/profile
    const pathSegments = parsedUrl.pathname.split('/')
    const redactedPath = pathSegments
      .map((segment, index) => {
        // Skip empty segments and standard path elements
        if (!segment || index === 0) return segment
        
        // Redact numeric IDs and UUIDs that might identify users
        if (/^[0-9]+$/.test(segment) || /^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$/i.test(segment)) {
          return '[REDACTED]'
        }
        
        return segment
      })
      .join('/')
    
    parsedUrl.pathname = redactedPath
    
    return parsedUrl.toString()
  } catch (error) {
    // If URL parsing fails, return a basic redacted string
    return url.includes('?') 
      ? `${url.split('?')[0]}?[REDACTED]` 
      : url
  }
} 