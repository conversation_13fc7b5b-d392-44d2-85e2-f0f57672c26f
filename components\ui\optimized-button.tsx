'use client'

import { Loader2 } from 'lucide-react'
import React, { useState, useCallback, useRef, useEffect } from 'react'
import { Button, ButtonProps } from '@/components/ui/button'
import { cn } from '@/lib/utils'

export interface OptimizedButtonProps extends ButtonProps {
  loading?: boolean
  debounce?: number
  loadingText?: string
  clickAnalytics?: string
  successText?: string
  errorText?: string
  successDuration?: number
  loaderPosition?: 'left' | 'right'
  loaderClassName?: string
  asyncOnClick?: (e: React.MouseEvent<HTMLButtonElement>) => Promise<any>
}

/**
 * OptimizedButton enhances the standard Button with:
 * - Loading states
 * - Click debouncing (prevent double-clicks)
 * - Async click handler support
 * - Success/error states with auto-reset
 * - Analytics tracking
 */
export function OptimizedButton({
  loading = false,
  debounce = 500,
  loadingText,
  successText,
  errorText,
  successDuration = 2000,
  className,
  disabled,
  children,
  loaderPosition = 'left',
  loaderClassName,
  asyncOnClick,
  onClick,
  clickAnalytics,
  ...props
}: OptimizedButtonProps) {
  const [isLoading, setIsLoading] = useState(loading)
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const lastClickTime = useRef<number>(0)
  const timeoutRef = useRef<NodeJS.Timeout>()

  // Reset status to idle when external loading prop changes to false
  useEffect(() => {
    if (loading !== isLoading) {
      setIsLoading(loading)
      if (!loading) {
        setStatus('idle')
      } else {
        setStatus('loading')
      }
    }
  }, [loading, isLoading])

  // Clear any pending timeouts when component unmounts
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const handleClick = useCallback(
    async (event: React.MouseEvent<HTMLButtonElement>) => {
      // Handle debounce
      const now = Date.now()
      if (now - lastClickTime.current < debounce) {
        event.preventDefault()
        return
      }
      lastClickTime.current = now

      // Track analytics if provided
      if (clickAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
        try {
          const gtag = (window as any).gtag
          gtag('event', clickAnalytics, {
            event_category: 'Button',
            event_label: clickAnalytics,
          })
        } catch (err) {
          console.error('Analytics error:', err)
        }
      }

      // Handle regular onClick
      if (onClick) {
        onClick(event)
      }

      // Handle async onClick
      if (asyncOnClick) {
        try {
          setStatus('loading')
          setIsLoading(true)
          await asyncOnClick(event)
          setStatus('success')
          
          if (successText || errorText) {
            timeoutRef.current = setTimeout(() => {
              setStatus('idle')
              setIsLoading(false)
            }, successDuration)
          } else {
            setIsLoading(false)
          }
        } catch (error) {
          setStatus('error')
          console.error('Button action error:', error)
          
          if (successText || errorText) {
            timeoutRef.current = setTimeout(() => {
              setStatus('idle')
              setIsLoading(false)
            }, successDuration)
          } else {
            setIsLoading(false)
          }
        }
      }
    },
    [onClick, asyncOnClick, debounce, clickAnalytics, successText, errorText, successDuration]
  )

  // Get text based on current status
  const buttonText = 
    status === 'loading' && loadingText ? loadingText :
    status === 'success' && successText ? successText :
    status === 'error' && errorText ? errorText :
    children

  const isDisabled = disabled || isLoading

  return (
    <Button
      className={cn(
        // Apply status-specific styles
        status === 'success' && 'bg-green-600 hover:bg-green-700 text-white border-green-700',
        status === 'error' && 'bg-red-600 hover:bg-red-700 text-white border-red-700',
        className
      )}
      onClick={handleClick}
      disabled={isDisabled}
      {...props}
    >
      {isLoading && loaderPosition === 'left' && (
        <Loader2 className={cn('h-4 w-4 animate-spin mr-2', loaderClassName)} />
      )}
      {buttonText}
      {isLoading && loaderPosition === 'right' && (
        <Loader2 className={cn('h-4 w-4 animate-spin ml-2', loaderClassName)} />
      )}
    </Button>
  )
} 