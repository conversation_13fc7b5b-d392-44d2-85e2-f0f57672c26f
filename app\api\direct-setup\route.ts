import { NextResponse } from "next/server";
import { hash } from "bcryptjs";
import { PrismaClient } from "@prisma/client";

// Create a new instance for this request
const prisma = new PrismaClient();

export async function GET() {
  try {
    // Only allow this in development
    if (process.env.NODE_ENV !== "development") {
      return new NextResponse("Only available in development", { status: 403 });
    }

    console.log("Starting direct setup...");

    // Create a hashed password for test users
    const hashedPassword = await hash("password123", 10);

    // 1. Create or find a trainer user
    console.log("Setting up trainer user...");
    let trainer = await prisma.user.findFirst({
      where: {
        id: "dev-trainer-id", // Use a fixed ID for the trainer
      },
    });

    if (!trainer) {
      console.log("Creating new trainer user...");
      trainer = await prisma.user.create({
        data: {
          id: "dev-trainer-id", // Use a fixed ID for the trainer
          name: "Test Trainer",
          email: "<EMAIL>",
          password: hashedPassword,
          role: "trainer",
          emailVerified: new Date(),
        },
      });
      console.log("Created trainer user:", trainer.id);
    } else {
      console.log("Found existing trainer user:", trainer.id);

      // Update the role to ensure it's a trainer
      if (trainer.role !== "trainer") {
        trainer = await prisma.user.update({
          where: { id: trainer.id },
          data: { role: "trainer" },
        });
        console.log("Updated user to trainer role");
      }
    }

    // 2. Create or find a client user
    console.log("Setting up client user...");
    let client = await prisma.user.findFirst({
      where: {
        id: "dev-user-id", // Use the dev-user-id for the client
      },
    });

    if (!client) {
      console.log("Creating new client user...");
      client = await prisma.user.create({
        data: {
          id: "dev-user-id", // Use the dev-user-id for the client
          name: "Test Client",
          email: "<EMAIL>",
          password: hashedPassword,
          role: "client",
          emailVerified: new Date(),
        },
      });
      console.log("Created client user:", client.id);
    } else {
      console.log("Found existing client user:", client.id);

      // Update the role to ensure it's a client
      if (client.role !== "client") {
        client = await prisma.user.update({
          where: { id: client.id },
          data: { role: "client" },
        });
        console.log("Updated user to client role");
      }
    }

    // 3. Create trainer profile if it doesn't exist
    console.log("Setting up trainer profile...");
    let trainerProfile = await prisma.trainerProfile.findUnique({
      where: {
        userId: trainer.id,
      },
    });

    if (!trainerProfile) {
      console.log("Creating new trainer profile...");
      trainerProfile = await prisma.trainerProfile.create({
        data: {
          userId: trainer.id,
        },
      });
      console.log("Created trainer profile:", trainerProfile.id);
    } else {
      console.log("Found existing trainer profile:", trainerProfile.id);
    }

    // 4. Create client profile if it doesn't exist
    console.log("Setting up client profile...");
    let clientProfile = await prisma.clientProfile.findUnique({
      where: {
        userId: client.id,
      },
    });

    if (!clientProfile) {
      console.log("Creating new client profile...");
      clientProfile = await prisma.clientProfile.create({
        data: {
          userId: client.id,
          assignedTrainerId: trainerProfile.id,
        },
      });
      console.log("Created client profile:", clientProfile.id);
    } else {
      console.log("Found existing client profile:", clientProfile.id);
    }

    // 5. Create a coaching relationship if it doesn't exist
    console.log("Setting up coaching relationship...");
    let coachingRelationship = await prisma.coachingRelationship.findFirst({
      where: {
        trainerId: trainer.id,
        clientId: client.id,
        status: "active",
      },
    });

    console.log("Coaching relationship search result:", coachingRelationship);

    if (!coachingRelationship) {
      console.log("Creating new coaching relationship...");
      try {
        coachingRelationship = await prisma.coachingRelationship.create({
          data: {
            trainerId: trainer.id,
            clientId: client.id,
            status: "active",
            plan: "Premium Coaching",
            notes: "Test coaching relationship for chat testing",
          },
        });
        console.log("Created coaching relationship:", coachingRelationship.id);
      } catch (error) {
        console.error("Error creating coaching relationship:", error);
        // If there's an error, try to find any existing relationship and update it
        const existingRelationship = await prisma.coachingRelationship.findFirst({
          where: {
            trainerId: trainer.id,
            clientId: client.id,
          },
        });

        if (existingRelationship) {
          coachingRelationship = await prisma.coachingRelationship.update({
            where: { id: existingRelationship.id },
            data: { status: "active" },
          });
          console.log("Updated existing relationship to active:", coachingRelationship.id);
        } else {
          // Create a new relationship if we couldn't find an existing one
          console.log("No existing relationship found, creating a new one...");
          coachingRelationship = await prisma.coachingRelationship.create({
            data: {
              trainerId: trainer.id,
              clientId: client.id,
              status: "active",
              plan: "Premium Coaching",
              notes: "Test coaching relationship for chat testing",
            },
          });
          console.log("Created coaching relationship after error:", coachingRelationship.id);
        }
      }
    } else {
      console.log("Found existing coaching relationship:", coachingRelationship.id);
    }

    // 5.5. Create a subscription between trainer and client if it doesn't exist
    console.log("Setting up subscription relationship...");
    let subscription = await prisma.subscription.findFirst({
      where: {
        athleteId: trainer.id,
        clientId: client.id,
        status: "active",
      },
    });

    if (!subscription) {
      console.log("Creating new subscription relationship...");
      try {
        // First, create a subscription tier if it doesn't exist
        let tier = await prisma.subscriptionTier.findFirst({
          where: {
            name: "Premium Coaching",
            trainerId: trainer.id,
          },
        });

        if (!tier) {
          tier = await prisma.subscriptionTier.create({
            data: {
              name: "Premium Coaching",
              description: "Premium coaching tier for test chat",
              price: 99.99,
              trainerId: trainer.id,
              features: ["1:1 Coaching", "Chat Support", "Custom Training Plans"],
            },
          });
          console.log("Created subscription tier:", tier.id);
        } else {
          console.log("Found existing subscription tier:", tier.id);
        }

        // Now create the subscription
        subscription = await prisma.subscription.create({
          data: {
            athleteId: trainer.id,
            clientId: client.id,
            tierId: tier.id,
            status: "active",
            startDate: new Date(),
          },
        });
        console.log("Created subscription:", subscription.id);
      } catch (error) {
        console.error("Error creating subscription:", error);
      }
    } else {
      console.log("Found existing subscription:", subscription.id);
    }

    // 6. Create a conversation if it doesn't exist
    console.log("Setting up conversation...");
    let conversation = await prisma.conversation.findFirst({
      where: {
        OR: [
          { user1Id: trainer.id, user2Id: client.id },
          { user1Id: client.id, user2Id: trainer.id },
        ],
      },
    });

    if (!conversation) {
      console.log("Creating new conversation...");
      conversation = await prisma.conversation.create({
        data: {
          user1Id: trainer.id,
          user2Id: client.id,
        },
      });
      console.log("Created conversation:", conversation.id);
    } else {
      console.log("Found existing conversation:", conversation.id);
    }

    // 7. Create a test message if there are no messages
    console.log("Checking for existing messages...");
    const messageCount = await prisma.message.count({
      where: {
        conversationId: conversation.id,
      },
    });

    let message;
    if (messageCount === 0) {
      console.log("Creating test message...");
      message = await prisma.message.create({
        data: {
          content: "Hello! This is a test message to start the conversation.",
          senderId: trainer.id,
          receiverId: client.id,
          conversationId: conversation.id,
        },
      });
      console.log("Created test message:", message.id);

      // Update the conversation's lastMessageAt
      console.log("Updating conversation lastMessageAt...");
      await prisma.conversation.update({
        where: {
          id: conversation.id,
        },
        data: {
          lastMessageAt: new Date(),
        },
      });
    } else {
      console.log(`Found ${messageCount} existing messages in the conversation`);
      message = await prisma.message.findFirst({
        where: {
          conversationId: conversation.id,
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    }

    console.log("Direct setup complete!");

    return NextResponse.json({
      success: true,
      message: "Direct setup completed successfully",
      trainer: {
        id: trainer.id,
        name: trainer.name,
        email: trainer.email,
        role: trainer.role,
      },
      client: {
        id: client.id,
        name: client.name,
        email: client.email,
        role: client.role,
      },
      trainerProfile: {
        id: trainerProfile.id,
      },
      clientProfile: {
        id: clientProfile.id,
      },
      coachingRelationship: coachingRelationship ? {
        id: coachingRelationship.id,
        status: coachingRelationship.status,
      } : null,
      subscription: subscription ? {
        id: subscription.id,
        status: subscription.status,
      } : null,
      conversation: {
        id: conversation.id,
      },
      testMessage: message ? {
        id: message.id,
        content: message.content,
      } : null,
      loginLinks: {
        trainer: "http://localhost:3000/api/auth/dev-login?role=trainer",
        client: "http://localhost:3000/api/auth/dev-login?role=client",
      },
      chatLink: "http://localhost:3000/dashboard/coaching-chat",
    });
  } catch (error) {
    console.error("Error in direct setup:", error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
    }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
