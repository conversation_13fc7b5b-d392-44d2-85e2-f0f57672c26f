# Security Hooks Library

This directory contains a collection of React hooks designed to enhance security in our application. These hooks handle common security concerns such as XSS prevention, CSRF protection, secure form handling, and safe data fetching.

## Available Hooks

### `useSafeQuery` & `useSafeMutation`

These hooks provide secure data fetching capabilities with built-in CSRF protection, XSS sanitization, and robust error handling.

```tsx
// Fetch example
const { data, error, isLoading, refetch } = useSafeQuery({
  url: '/api/items',
  cacheKey: 'items-list', // Optional caching
  sanitize: true, // Default is true - sanitizes all responses
  retry: true // Retry failed requests
});

// Mutation example
const { mutate, isLoading, error } = useSafeMutation({
  url: '/api/items',
  method: 'POST',
  onSuccess: (data) => {
    // Handle success
  },
  onError: (error) => {
    // Handle error
  }
});

// Usage in component
const handleSubmit = async () => {
  await mutate({ name: "<PERSON> Item" });
};
```

### `useSafeForm`

A comprehensive form handling hook with built-in validation via Zod schemas and automatic sanitization of inputs to prevent XSS attacks.

```tsx
// Define validation schema using Zod
const userFormSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
  age: z.number().min(18)
});

// Use the hook in your component
const {
  values,
  errors,
  isSubmitting,
  handleChange,
  handleSelectChange,
  handleSubmit,
  getFieldError
} = useSafeForm({
  initialValues: {
    name: "",
    email: "",
    age: 0
  },
  validationSchema: userFormSchema,
  onSubmit: async (values) => {
    // values are already sanitized and validated
    await submitToApi(values);
  },
  sanitize: true // Enable sanitization (default is true)
});

// Use in your JSX
<form onSubmit={handleSubmit}>
  <input 
    name="email" 
    value={values.email} 
    onChange={handleChange} 
    className={getFieldError('email') ? 'error' : ''} 
  />
  {getFieldError('email') && <p className="error">{getFieldError('email')}</p>}
  
  <select
    value={values.country}
    onChange={(e) => handleSelectChange('country', e.target.value)}
  >
    <option value="US">United States</option>
    <option value="CA">Canada</option>
  </select>
  
  <button type="submit" disabled={isSubmitting}>Submit</button>
</form>
```

### `useCSRF`

Hook for Cross-Site Request Forgery (CSRF) protection. This is automatically used by `useSafeQuery` and `useSafeMutation`.

```tsx
const { csrfToken, fetchWithCSRF, tokenHeaderName } = useCSRF();

// Safe fetch with CSRF protection
const handleSubmit = async () => {
  const response = await fetchWithCSRF('/api/protected-route', {
    method: 'POST',
    body: JSON.stringify({ data: 'value' })
  });
  
  if (response.ok) {
    // Handle successful response
  }
};

// Or use the token directly
<form>
  <input type="hidden" name="csrfToken" value={csrfToken} />
  {/* Other form fields */}
</form>
```

## Security Features

1. **XSS Protection**: All data fetched or submitted through these hooks is automatically sanitized using our XSS protection utilities.

2. **CSRF Protection**: All API calls made with these hooks include CSRF tokens for protection against cross-site request forgery attacks.

3. **Input Validation**: The `useSafeForm` hook provides robust validation using Zod schemas, with clear error handling.

4. **Error Recovery**: The query hooks include retry logic with exponential backoff for failed requests.

5. **Caching**: Optional built-in caching for repeated queries to improve performance.

6. **Toast Notifications**: Integrated with our UI toast system for error reporting to users.

## Best Practices

- Always use these security hooks instead of raw `fetch` or manual form state when handling user input or API calls.
- Provide validation schemas for all forms to ensure data integrity.
- Use the error handling capabilities rather than try/catch blocks for more consistent error reporting.
- Consider setting appropriate cache keys for frequently accessed data that doesn't change often. 