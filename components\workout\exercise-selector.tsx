"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface Exercise {
  id: string
  name: string
  type: string
  muscleGroup: string
  equipment: string[]
  difficulty: string
}

interface ExerciseSelectorProps {
  workoutId: string
  workoutType: string
  onExerciseAdded: () => void
}

export function ExerciseSelector({ workoutId, workoutType, onExerciseAdded }: ExerciseSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedMuscleGroup, setSelectedMuscleGroup] = useState<string>("all")
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>("all")
  const [exercises, setExercises] = useState<Exercise[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const searchExercises = async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        query: searchQuery,
        type: workoutType
      });
      
      if (selectedMuscleGroup !== "all") params.append("muscleGroup", selectedMuscleGroup);
      if (selectedDifficulty !== "all") params.append("difficulty", selectedDifficulty);
      
      const response = await fetch(`/api/exercises/search?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to search exercises")
      const data = await response.json()
      setExercises(data)
    } catch (error) {
      console.error("Error searching exercises:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const addExercise = async (exerciseId: string) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/training-plans/workouts/${workoutId}/exercises`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ exerciseId }),
      })

      if (!response.ok) throw new Error("Failed to add exercise")
      onExerciseAdded()
    } catch (error) {
      console.error("Error adding exercise:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex gap-4">
        <Input
          placeholder="Search exercises..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && searchExercises()}
        />
        <Select value={selectedMuscleGroup} onValueChange={setSelectedMuscleGroup}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Muscle Group" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Groups</SelectItem>
            <SelectItem value="chest">Chest</SelectItem>
            <SelectItem value="back">Back</SelectItem>
            <SelectItem value="legs">Legs</SelectItem>
            <SelectItem value="shoulders">Shoulders</SelectItem>
            <SelectItem value="arms">Arms</SelectItem>
            <SelectItem value="core">Core</SelectItem>
          </SelectContent>
        </Select>
        <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Difficulty" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Levels</SelectItem>
            <SelectItem value="beginner">Beginner</SelectItem>
            <SelectItem value="intermediate">Intermediate</SelectItem>
            <SelectItem value="advanced">Advanced</SelectItem>
          </SelectContent>
        </Select>
        <Button onClick={searchExercises} disabled={isLoading}>
          Search
        </Button>
      </div>

      <div className="grid gap-4">
        {exercises.map((exercise) => (
          <Card key={exercise.id} className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">{exercise.name}</h4>
                <p className="text-sm text-gray-500">
                  {exercise.muscleGroup} • {exercise.difficulty}
                </p>
              </div>
              <Button
                onClick={() => addExercise(exercise.id)}
                disabled={isLoading}
              >
                Add
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
} 