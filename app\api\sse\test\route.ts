import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { sendMessageToUser } from '../route';

export const dynamic = 'force-dynamic'

// This endpoint is for testing SSE
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the target user ID from the query parameters
    const targetUserId = req.nextUrl.searchParams.get('userId');
    if (!targetUserId) {
      return new NextResponse('Target user ID is required', { status: 400 });
    }

    // Create a test message
    const testMessage = {
      type: 'new-message',
      conversationId: 'test-conversation',
      message: {
        id: 'test-message',
        content: 'This is a test message',
        createdAt: new Date().toISOString()
      }
    };

    console.log(`Test endpoint: Sending test message to user ${targetUserId}`);
    
    // Send the test message
    sendMessageToUser(targetUserId, JSON.stringify(testMessage));

    return NextResponse.json({ success: true, message: `Test message sent to user ${targetUserId}` });
  } catch (error) {
    console.error('Error in SSE test endpoint:', error);
    return NextResponse.json({ error: 'Failed to send test message' }, { status: 500 });
  }
}
