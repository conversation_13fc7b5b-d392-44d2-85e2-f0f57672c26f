import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('Creating sample training plan...')
  
  // Get the admin user
  const admin = await prisma.user.findFirst({
    where: { role: 'admin' }
  })

  if (!admin) {
    throw new Error('Admin user not found')
  }

  // Create a training plan
  const plan = await prisma.trainingPlan.create({
    data: {
      title: "Beginner Strength Program",
      description: "A comprehensive strength training program for beginners",
      difficulty: "beginner",
      is_template: true,
      athleteId: admin.id,
    },
  })

  console.log(`Created training plan: ${plan.title}`)

  // Create a week
  const week = await prisma.week.create({
    data: {
      weekNumber: 1,
      trainingPlanId: plan.id,
    },
  })

  console.log(`Created week ${week.weekNumber}`)

  // Create a workout
  const workout = await prisma.workout.create({
    data: {
      title: "Full Body Workout",
      description: "A complete full body workout for beginners",
      type: "strength",
      weekId: week.id,
      trainingPlanId: plan.id,
    },
  })

  console.log(`Created workout: ${workout.title}`)

  // Get some template exercises
  const exercises = await prisma.templateExercise.findMany({
    take: 5,
  })

  // Create exercises from templates
  for (const exercise of exercises) {
    const created = await prisma.exercise.create({
      data: {
        name: exercise.name,
        description: exercise.description,
        sets: exercise.sets,
        reps: exercise.reps,
        duration: exercise.duration,
        restTime: exercise.restTime,
        videoUrl: exercise.videoUrl,
        workoutId: workout.id,
        isTemplate: true,
      },
    })
    console.log(`Created exercise: ${created.name}`)
  }

  console.log('Finished creating sample training plan')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 