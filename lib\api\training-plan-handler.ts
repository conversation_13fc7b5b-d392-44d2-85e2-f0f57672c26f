import { NextResponse } from "next/server"
import { BaseApiHandler } from "./base-api-handler"
import { TrainingPlanService } from "../services/training-plan-service"

export class TrainingPlanTemplateHandler extends BaseApiHandler {
  /**
   * Get all training plan templates
   */
  protected async get(req: Request, userId: string): Promise<NextResponse> {
    const templates = await TrainingPlanService.getTemplatesForTrainer(userId)
    return NextResponse.json(templates)
  }

  /**
   * Create a new training plan template
   */
  protected async post(req: Request, userId: string): Promise<NextResponse> {
    const data = await req.json()
    
    if (!data.title) {
      return NextResponse.json({ error: "Title is required" }, { status: 400 })
    }
    
    const template = await TrainingPlanService.createTemplate(userId, data)
    
    return NextResponse.json(template)
  }

  /**
   * Update a training plan template (not implemented in main route)
   */
  protected async put(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete a training plan template (not implemented in main route)
   */
  protected async delete(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}

export class TrainingPlanTemplateByIdHandler extends BaseApiHandler {
  /**
   * Get a training plan template by ID
   */
  protected async get(req: Request, userId: string, params: { templateId: string }): Promise<NextResponse> {
    const template = await TrainingPlanService.getTemplateById(params.templateId, userId)
    
    if (!template) {
      return NextResponse.json({ error: "Template not found" }, { status: 404 })
    }
    
    return NextResponse.json(template)
  }

  /**
   * Create a training plan template (not implemented in [id] route)
   */
  protected async post(req: Request, userId: string, params: { templateId: string }): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Update a training plan template
   */
  protected async put(req: Request, userId: string, params: { templateId: string }): Promise<NextResponse> {
    const data = await req.json()
    
    const template = await TrainingPlanService.updateTemplate(params.templateId, userId, data)
    
    return NextResponse.json(template)
  }

  /**
   * Delete a training plan template
   */
  protected async delete(req: Request, userId: string, params: { templateId: string }): Promise<NextResponse> {
    await TrainingPlanService.deleteTemplate(params.templateId, userId)
    
    return NextResponse.json({ success: true })
  }
}

export class TrainingPlanAssignHandler extends BaseApiHandler {
  /**
   * Get assigned training plans (not implemented)
   */
  protected async get(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Assign a training plan to a client
   */
  protected async post(req: Request, userId: string): Promise<NextResponse> {
    const data = await req.json()
    
    if (!data.clientId) {
      return NextResponse.json({ error: "Client ID is required" }, { status: 400 })
    }
    
    if (!data.planData || !data.planData.title) {
      return NextResponse.json({ error: "Plan data with title is required" }, { status: 400 })
    }
    
    const plan = await TrainingPlanService.assignPlanToClient(userId, data.clientId, data.planData)
    
    return NextResponse.json(plan)
  }

  /**
   * Update an assigned training plan (not implemented)
   */
  protected async put(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }

  /**
   * Delete an assigned training plan (not implemented)
   */
  protected async delete(req: Request, userId: string): Promise<NextResponse> {
    return NextResponse.json({ error: "Method not implemented" }, { status: 501 })
  }
}
