"use client"

import {
  Message<PERSON><PERSON>re,
  ClipboardList,
  BarChart3,
  Calendar,
  Plus,
  Pencil,
  Dumbbell,
  Scale,
  Target,
  Trash2,
  Edit,
  TrendingUp,
  TrendingDown,
  Utensils,
  Heart,
  Activity,
  Clock,
  Ruler,
  Camera,
  Award,
  CalendarCheck,
  ArrowUpRight,
  ArrowDownRight,
  ChevronDown,
  Filter,
  Download
} from "lucide-react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { useSession } from "next-auth/react"
import { useEffect, useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { EditGoalsDialog } from "./edit-goals-dialog"
import { EditPlan } from "./edit-plan"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell, Legend, AreaChart, Area } from "recharts"
import { AssignPlan } from "./assign-plan"
import { EditExerciseDialog } from "./edit-exercise-dialog"
import { AddExerciseDialog } from "./add-exercise-dialog"
import { toast } from "@/components/ui/use-toast"
import { PremiumExerciseGrid } from "@/components/premium/premium-exercise-grid"
import { PremiumTrainingPlan } from "@/components/premium/premium-training-plan"
import { CreateNutritionPlan } from "@/components/nutrition/create-nutrition-plan"
import { NutritionPlanDisplay } from "@/components/nutrition/nutrition-plan-display"
import { EditNutritionPlan } from "@/components/nutrition/edit-nutrition-plan"
import { PremiumClientChat } from "@/components/premium/premium-client-chat"
import { FollowUpTab } from "@/components/coaching/follow-up-tab"
import { CalendlyModal } from "@/components/calendly/calendly-modal"

interface Exercise {
  id: string;
  name: string;
  sets: number;
  reps: number;
  notes?: string;
  weight?: number;
  category?: string;
  difficulty?: string;
  targetMuscles?: string[];
}

interface DailyWorkout {
  id: string;
  day: string;
  exercises: Exercise[];
}

interface Week {
  id: string;
  weekNumber: number;
  dailyWorkouts: DailyWorkout[];
}

interface ClientPlan {
  id: string;
  title: string;
  description: string;
  exercises: Exercise[];
  weeks?: Week[];
}

export default function ClientPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { data: session, status } = useSession()
  const [client, setClient] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedWeek, setSelectedWeek] = useState(1)
  const [selectedDay, setSelectedDay] = useState('Monday')
  const [nutritionPlan, setNutritionPlan] = useState<any>(null)
  const [loadingNutritionPlan, setLoadingNutritionPlan] = useState(true)
  const [progressTimeRange, setProgressTimeRange] = useState("month")
  const [workoutLogs, setWorkoutLogs] = useState<any[]>([])
  const [nutritionLogs, setNutritionLogs] = useState<any[]>([])
  const [loadingWorkoutLogs, setLoadingWorkoutLogs] = useState(true)
  const [loadingNutritionLogs, setLoadingNutritionLogs] = useState(true)
  const [progressMetrics, setProgressMetrics] = useState<any>({})
  const [calendlySettings, setCalendlySettings] = useState<{calendlyUserId: string | null, calendlyUrl: string}>({
    calendlyUserId: null,
    calendlyUrl: ''
  })
  const clientId = params.id

  useEffect(() => {
    const fetchClient = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/clients/${clientId}`)

        if (!response.ok) {
          throw new Error(`Failed to fetch client: ${response.status}`)
        }

        const data = await response.json()
        console.log('Fetched client data:', data)

        // Extract goals from preferences if available
        let clientGoals: string[] = []
        if (data.preferences) {
          try {
            const preferences = typeof data.preferences === 'string'
              ? JSON.parse(data.preferences)
              : data.preferences

            if (preferences.goals && Array.isArray(preferences.goals)) {
              clientGoals = preferences.goals
            }
          } catch (e) {
            console.error('Error parsing preferences:', e)
          }
        }

        // Check if we have data from localStorage (for development)
        const localData = localStorage.getItem(`client_${clientId}`)
        if (localData) {
          const parsedLocalData = JSON.parse(localData)
          console.log('Found local data:', parsedLocalData)

          // Merge the API data with localStorage data
          setClient({
            ...data,
            ...parsedLocalData,
            goals: parsedLocalData.goals || clientGoals
          })
        } else {
          setClient({
            ...data,
            goals: clientGoals
          })
        }
      } catch (error) {
        console.error('Error fetching client:', error)
        toast({
          title: "Error",
          description: `Failed to load client data: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    const fetchNutritionPlan = async () => {
      try {
        setLoadingNutritionPlan(true)
        // Fetch the client's assigned diet plans
        const response = await fetch(`/api/clients/${clientId}/diet-plans`)

        if (!response.ok) {
          if (response.status === 404) {
            // No nutrition plan found, this is expected for new clients
            setNutritionPlan(null)
            return
          }
          throw new Error(`Failed to fetch nutrition plan: ${response.status}`)
        }

        const data = await response.json()
        console.log('Fetched nutrition plan data:', data)

        if (data && data.length > 0) {
          // Use the most recently assigned diet plan
          setNutritionPlan(data[0])
        } else {
          setNutritionPlan(null)
        }
      } catch (error) {
        console.error('Error fetching nutrition plan:', error)
        // Don't show toast for nutrition plan errors to avoid overwhelming the user
      } finally {
        setLoadingNutritionPlan(false)
      }
    }

    // Function to fetch client's workout logs
    const fetchWorkoutLogs = async () => {
      try {
        setLoadingWorkoutLogs(true)
        const response = await fetch(`/api/clients/${clientId}/workout-logs`)

        if (!response.ok) {
          if (response.status !== 404) { // 404 is expected if no logs exist
            throw new Error(`Failed to fetch workout logs: ${response.status}`)
          }
          setWorkoutLogs([])
          return
        }

        const data = await response.json()
        console.log('Fetched workout logs:', data)
        setWorkoutLogs(data)
      } catch (error) {
        console.error('Error fetching workout logs:', error)
        // Use mock data for development
        const mockWorkoutLogs = [
          {
            id: 'wl1',
            date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            duration: 45,
            completed: true,
            name: 'Full Body Strength'
          },
          {
            id: 'wl2',
            date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            duration: 30,
            completed: true,
            name: 'Upper Body Focus'
          },
          {
            id: 'wl3',
            date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            duration: 60,
            completed: true,
            name: 'Lower Body & Core'
          }
        ]
        setWorkoutLogs(mockWorkoutLogs)
      } finally {
        setLoadingWorkoutLogs(false)
      }
    }

    // Function to fetch client's nutrition logs
    const fetchNutritionLogs = async () => {
      try {
        setLoadingNutritionLogs(true)
        const response = await fetch(`/api/clients/${clientId}/nutrition-logs`)

        if (!response.ok) {
          if (response.status !== 404) { // 404 is expected if no logs exist
            throw new Error(`Failed to fetch nutrition logs: ${response.status}`)
          }
          setNutritionLogs([])
          return
        }

        const data = await response.json()
        console.log('Fetched nutrition logs:', data)
        setNutritionLogs(data)
      } catch (error) {
        console.error('Error fetching nutrition logs:', error)
        // Use mock data for development
        const mockNutritionLogs = [
          {
            id: 'nl1',
            date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            mealType: 'breakfast',
            calories: 450,
            protein: 30,
            carbs: 45,
            fat: 15
          },
          {
            id: 'nl2',
            date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            mealType: 'lunch',
            calories: 650,
            protein: 40,
            carbs: 60,
            fat: 20
          },
          {
            id: 'nl3',
            date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            mealType: 'dinner',
            calories: 550,
            protein: 35,
            carbs: 50,
            fat: 18
          }
        ]
        setNutritionLogs(mockNutritionLogs)
      } finally {
        setLoadingNutritionLogs(false)
      }
    }

    const fetchCalendlySettings = async () => {
      try {
        const response = await fetch('/api/calendly?action=my-settings')

        if (!response.ok) {
          throw new Error('Failed to fetch Calendly settings')
        }

        const data = await response.json()

        if (data.user?.calendlyUserId) {
          setCalendlySettings({
            calendlyUserId: data.user.calendlyUserId,
            calendlyUrl: `https://calendly.com/${data.user.calendlyUserId}`,
          })
        } else {
          setCalendlySettings({
            calendlyUserId: null,
            calendlyUrl: '',
          })
        }
      } catch (error) {
        console.error('Error fetching Calendly settings:', error)
        setCalendlySettings({
          calendlyUserId: null,
          calendlyUrl: '',
        })
      }
    }

    if (status === "authenticated") {
      fetchClient()
      fetchNutritionPlan()
      fetchWorkoutLogs()
      fetchNutritionLogs()
      fetchCalendlySettings()
    }
  }, [clientId, status])

  // Calculate progress metrics
  useEffect(() => {
    if (!client?.progress || client.progress.length === 0) {
      return
    }

    // Sort progress by date
    const sortedProgress = [...client.progress].sort((a, b) =>
      new Date(a.date).getTime() - new Date(b.date).getTime()
    )

    // Get first and last measurements
    const firstMeasurement = sortedProgress[0]
    const lastMeasurement = sortedProgress[sortedProgress.length - 1]

    // Calculate weight change
    const weightChange = lastMeasurement.weight - firstMeasurement.weight
    const weightChangePercent = (weightChange / firstMeasurement.weight) * 100

    // Calculate body fat change if available
    let bodyFatChange = 0
    let bodyFatChangePercent = 0
    if (firstMeasurement.bodyFat && lastMeasurement.bodyFat) {
      bodyFatChange = lastMeasurement.bodyFat - firstMeasurement.bodyFat
      bodyFatChangePercent = (bodyFatChange / firstMeasurement.bodyFat) * 100
    }

    // Calculate workout completion rate
    const workoutCompletionRate = workoutLogs.length > 0
      ? (workoutLogs.filter(log => log.completed).length / workoutLogs.length) * 100
      : 0

    // Calculate nutrition adherence
    const nutritionAdherence = nutritionLogs.length > 0 ? 85 : 0 // Mock value

    setProgressMetrics({
      weightChange,
      weightChangePercent,
      bodyFatChange,
      bodyFatChangePercent,
      workoutCompletionRate,
      nutritionAdherence,
      measurementCount: sortedProgress.length,
      firstMeasurementDate: firstMeasurement.date,
      lastMeasurementDate: lastMeasurement.date
    })
  }, [client?.progress, workoutLogs, nutritionLogs])

  const handlePlanAssign = (plan: ClientPlan) => {
    console.log('Assigning plan:', plan)

    setClient(prev => {
      if (!prev) return prev

      const updatedClient = {
        ...prev,
        plan
      }

      // Update localStorage
      localStorage.setItem(`client_${clientId}`, JSON.stringify(updatedClient))
      return updatedClient
    })

    toast({
      title: "Plan Assigned",
      description: `The training plan "${plan.title}" has been assigned to the client.`,
    })
  }

  const handlePlanUpdate = (updatedPlan: any) => {
    console.log('Plan updated:', updatedPlan)

    setClient(prev => {
      if (!prev) return prev

      const updatedClient = {
        ...prev,
        plan: updatedPlan
      }

      // Update localStorage
      localStorage.setItem(`client_${clientId}`, JSON.stringify(updatedClient))
      return updatedClient
    })
  }

  const handleNutritionPlanCreated = () => {
    // Refresh the nutrition plan data
    setLoadingNutritionPlan(true)
    fetch(`/api/clients/${clientId}/diet-plans`)
      .then(response => response.ok ? response.json() : null)
      .then(data => {
        if (data && data.length > 0) {
          setNutritionPlan(data[0])
        }
        setLoadingNutritionPlan(false)
      })
      .catch(error => {
        console.error('Error refreshing nutrition plan:', error)
        setLoadingNutritionPlan(false)
      })
  }

  const handleExerciseEdit = (weekIndex: number, dayId: string, exerciseId: string, updatedExercise: Exercise) => {
    console.log('Editing exercise:', exerciseId, 'with data:', updatedExercise)

    setClient(prev => {
      if (!prev) return prev

      // If there's no plan, just return the previous state
      if (!prev.plan) return prev

      const updatedPlan = {
        ...prev.plan,
        weeks: prev.plan.weeks?.map((week, index) => {
          // Try to match by week ID first
          if (week.id && week.id === `week-${weekIndex + 1}`) {
            console.log('Found week by ID:', week.id)
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: day.exercises.map(ex =>
                    ex.id === exerciseId ? updatedExercise : ex
                  )
                }
              })
            }
          }

          // If no match by ID, try by index
          if (index === weekIndex) {
            console.log('Found week by index:', index)
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: day.exercises.map(ex =>
                    ex.id === exerciseId ? updatedExercise : ex
                  )
                }
              })
            }
          }

          // If no match, return the week unchanged
          return week
        })
      }

      const updatedClient = { ...prev, plan: updatedPlan }

      // Update localStorage
      localStorage.setItem(`client_${clientId}`, JSON.stringify(updatedClient))

      // Update the plan in the database using PUT
      try {
        console.log('Sending plan update to server:', updatedPlan)
        fetch(`/api/clients/${clientId}/training-plan/update`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedPlan),
        })
        .then(response => {
          if (!response.ok) {
            return response.json().then(data => {
              console.error('Error updating plan:', data)
              throw new Error(data.error || 'Failed to update plan')
            })
          }
          return response.json()
        })
        .then(data => {
          console.log('Plan updated successfully:', data)

          // No need to reload, we've already updated the client state
          toast({
            title: "Success",
            description: "Exercise updated and saved to database",
          })
        })
        .catch(error => {
          console.error('Error updating plan:', error)
          toast({
            title: "Error",
            description: `Failed to save exercise to database: ${error.message}`,
            variant: "destructive",
          })
        })
      } catch (error) {
        console.error('Error updating plan:', error)
        toast({
          title: "Error",
          description: `Failed to save exercise to database: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      }

      return updatedClient
    })
  }

  const handleExerciseDelete = (weekIndex: number, dayId: string, exerciseId: string) => {
    console.log('Deleting exercise:', exerciseId, 'from week:', weekIndex, 'day:', dayId)

    setClient(prev => {
      if (!prev) return prev

      // If there's no plan, just return the previous state
      if (!prev.plan) return prev

      const updatedPlan = {
        ...prev.plan,
        weeks: prev.plan.weeks?.map((week, index) => {
          // Try to match by week ID first
          if (week.id && week.id === `week-${weekIndex + 1}`) {
            console.log('Found week by ID:', week.id)
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: day.exercises.filter(ex => ex.id !== exerciseId)
                }
              })
            }
          }

          // If no match by ID, try by index
          if (index === weekIndex) {
            console.log('Found week by index:', index)
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: day.exercises.filter(ex => ex.id !== exerciseId)
                }
              })
            }
          }

          // If no match, return the week unchanged
          return week
        })
      }

      const updatedClient = { ...prev, plan: updatedPlan }

      // Update localStorage
      localStorage.setItem(`client_${clientId}`, JSON.stringify(updatedClient))

      // Update the plan in the database using PUT
      try {
        console.log('Sending plan update to server:', updatedPlan)
        fetch(`/api/clients/${clientId}/training-plan`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedPlan),
        })
        .then(response => {
          if (!response.ok) {
            return response.json().then(data => {
              console.error('Error updating plan:', data)
              throw new Error(data.error || 'Failed to update plan')
            })
          }
          return response.json()
        })
        .then(data => {
          console.log('Plan updated successfully:', data)

          // No need to reload, we've already updated the client state
          toast({
            title: "Success",
            description: "Exercise deleted and saved to database",
          })
        })
        .catch(error => {
          console.error('Error updating plan:', error)
          toast({
            title: "Error",
            description: `Failed to save exercise to database: ${error.message}`,
            variant: "destructive",
          })
        })
      } catch (error) {
        console.error('Error updating plan:', error)
        toast({
          title: "Error",
          description: `Failed to save exercise to database: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      }

      return updatedClient
    })
  }

  const handleExerciseAdd = async (weekIndex: number, dayId: string, newExercise: Exercise) => {
    console.log('Adding exercise to week:', weekIndex, 'day:', dayId, 'exercise:', newExercise)

    setClient(prev => {
      if (!prev) return prev

      // If there's no plan, just return the previous state
      if (!prev.plan) return prev

      const updatedPlan = {
        ...prev.plan,
        weeks: prev.plan.weeks?.map((week, index) => {
          // Try to match by week ID first
          if (week.id && week.id === `week-${weekIndex + 1}`) {
            console.log('Found week by ID:', week.id)
            const isTargetWeek = true
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: [...day.exercises, newExercise]
                }
              })
            }
          }

          // If no match by ID, try by index
          if (index === weekIndex) {
            console.log('Found week by index:', index)
            return {
              ...week,
              dailyWorkouts: week.dailyWorkouts.map(day => {
                if (day.id !== dayId) return day
                return {
                  ...day,
                  exercises: [...day.exercises, newExercise]
                }
              })
            }
          }

          // If no match, return the week unchanged
          return week
        })
      }

      const updatedClient = { ...prev, plan: updatedPlan }

      // Update localStorage
      localStorage.setItem(`client_${clientId}`, JSON.stringify(updatedClient))

      // Update the plan in the database using PUT
      try {
        console.log('Sending plan update to server:', updatedPlan)
        fetch(`/api/clients/${clientId}/training-plan`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedPlan),
        })
        .then(response => {
          if (!response.ok) {
            return response.json().then(data => {
              console.error('Error updating plan:', data)
              throw new Error(data.error || 'Failed to update plan')
            })
          }
          return response.json()
        })
        .then(data => {
          console.log('Plan updated successfully:', data)

          // No need to reload, we've already updated the client state
          toast({
            title: "Success",
            description: "Exercise added and saved to database",
          })
        })
        .catch(error => {
          console.error('Error updating plan:', error)
          toast({
            title: "Error",
            description: `Failed to save exercise to database: ${error.message}`,
            variant: "destructive",
          })
        })
      } catch (error) {
        console.error('Error updating plan:', error)
        toast({
          title: "Error",
          description: `Failed to save exercise to database: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        })
      }

      return updatedClient
    })
  }

  const handlePlanDisassociate = async () => {
    try {
      // Get the plan ID or title for the API call
      const planId = client?.plan?.id
      const planTitle = client?.plan?.title

      if (!planId && !planTitle) {
        throw new Error('No plan ID or title found')
      }

      // Build the URL with query parameters
      let url = `/api/clients/${clientId}/training-plan`
      if (planId) {
        url += `?planId=${encodeURIComponent(planId)}`
      } else if (planTitle) {
        url += `?title=${encodeURIComponent(planTitle)}`
      }

      console.log('Attempting to delete plan with URL:', url)

      // Remove the plan from the database
      const response = await fetch(url, {
        method: 'DELETE',
      })

      console.log('Delete response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Error response data:', errorData)
        throw new Error(`Failed to remove plan: ${response.status} ${response.statusText}`)
      }

      // Update the client state
      setClient(prev => {
        if (!prev) return null

        // Preserve the mock data
        const updatedClient = {
          ...prev,
          plan: null,
          workouts: []
        }

        // Update localStorage
        localStorage.setItem(`client_${clientId}`, JSON.stringify(updatedClient))
        return updatedClient
      })

      // Show success toast
      toast({
        title: "Plan Disassociated",
        description: "The training plan has been successfully removed from the client.",
      })
    } catch (error) {
      console.error('Error removing plan:', error)
      // Show error toast
      toast({
        title: "Error",
        description: `Failed to remove the plan: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      })
    }
  }

  // Add console log to see what's happening when selecting week/day
  useEffect(() => {
    if (client?.plan) {
      const selectedDayWorkouts = client.plan.weeks?.[selectedWeek - 1]?.dailyWorkouts
        ?.find((d: DailyWorkout) => d.day === selectedDay)?.exercises
      console.log('Selected week:', selectedWeek)
      console.log('Selected day:', selectedDay)
      console.log('Found exercises:', selectedDayWorkouts)
    }
  }, [selectedWeek, selectedDay, client?.plan])

  // Add debug logging for week selection
  useEffect(() => {
    if (client?.plan) {
      console.log('Current plan weeks:', client.plan.weeks)
      console.log('Selected week:', selectedWeek)
      const weekData = client.plan.weeks?.find(w => w.weekNumber === selectedWeek)
      console.log('Found week data:', weekData)
    }
  }, [client?.plan, selectedWeek])

  if (status === "loading" || isLoading || !client) {
    return <div>Loading...</div>
  }

  const userRole = session?.user?.role

  if (userRole !== "trainer" && userRole !== "admin") {
    router.push("/dashboard/dashboard")
    return null
  }

  return (
    <div className="container py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={client?.avatarUrl || `https://avatar.vercel.sh/${client?.name}`} />
            <AvatarFallback>{client?.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{client?.name}</h1>
            <p className="text-muted-foreground">{client?.email}</p>
            {client?.clientSubscriptions && client.clientSubscriptions.length > 0 ? (
              <p className="text-xs text-muted-foreground">
                {client.clientSubscriptions[0].tier?.name || 'Standard'} Plan
              </p>
            ) : client?.assignedTrainer && (
              <p className="text-xs text-muted-foreground">
                Assigned to {client.assignedTrainer.name}
              </p>
            )}
          </div>
        </div>
        <div className="flex gap-2 mt-4 md:mt-0">
          <Button variant="outline" asChild>
            <Link href={`/dashboard/messaging?client=${client.id}`}>
              <MessageSquare className="mr-2 h-4 w-4" />
              Message
            </Link>
          </Button>
          {calendlySettings.calendlyUrl ? (
            <CalendlyModal
              calendlyUrl={calendlySettings.calendlyUrl}
              clientName={client?.name || 'Client'}
              clientEmail={client?.email || ''}
              trainerName={session?.user?.name || 'Trainer'}
              trigger={
                <Button>
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule Session
                </Button>
              }
            />
          ) : (
            <Button disabled>
              <Calendar className="mr-2 h-4 w-4" />
              Schedule Session
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="progress" className="space-y-6">
        <TabsList>
          <TabsTrigger value="progress">
            <BarChart3 className="mr-2 h-4 w-4" />
            Progress Overview
          </TabsTrigger>
          <TabsTrigger value="workouts">
            <ClipboardList className="mr-2 h-4 w-4" />
            Workouts & Plans
          </TabsTrigger>
          <TabsTrigger value="nutrition">
            <Utensils className="mr-2 h-4 w-4" />
            Nutrition
          </TabsTrigger>
          <TabsTrigger value="chat">
            <MessageSquare className="mr-2 h-4 w-4" />
            Chat
          </TabsTrigger>
          <TabsTrigger value="followup">
            <Calendar className="mr-2 h-4 w-4" />
            Follow-up
          </TabsTrigger>
        </TabsList>



        <TabsContent value="workouts" className="space-y-6">
          {client?.plan ? (
            <PremiumTrainingPlan
              plan={client.plan}
              onDisassociate={handlePlanDisassociate}
              onEdit={() => {
                // Use the existing EditPlan component
                const editButton = document.querySelector('[data-edit-plan-button]');
                if (editButton) {
                  (editButton as HTMLButtonElement).click();
                }
              }}
              onExerciseAdd={handleExerciseAdd}
              onExerciseEdit={handleExerciseEdit}
              onExerciseDelete={handleExerciseDelete}
            />
          ) : (
            <Card>
              <CardContent className="pt-6 pb-6">
                <div className="text-center py-12 bg-gradient-to-b from-muted/5 to-muted/10 rounded-lg border border-dashed">
                  <div className="max-w-md mx-auto space-y-4">
                    <div className="w-16 h-16 mx-auto rounded-full bg-primary/10 flex items-center justify-center">
                      <ClipboardList className="h-8 w-8 text-primary/70" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">No Training Plan</h3>
                      <p className="text-muted-foreground mt-1 mb-4">
                        This client doesn't have a training plan assigned yet.
                      </p>
                      <AssignPlan clientId={client.id} onAssign={handlePlanAssign} />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Hidden button for EditPlan to be triggered programmatically */}
          <div className="hidden">
            <EditPlan clientId={client.id} currentPlan={client?.plan} buttonProps={{ 'data-edit-plan-button': true }} onUpdate={handlePlanUpdate} />
          </div>
        </TabsContent>

        <TabsContent value="nutrition" className="space-y-6">
          {loadingNutritionPlan ? (
            <Card>
              <CardHeader>
                <CardTitle>Nutrition Plan</CardTitle>
                <CardDescription>Loading client's nutrition plan...</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              </CardContent>
            </Card>
          ) : nutritionPlan ? (
            <>
              <NutritionPlanDisplay
                plan={nutritionPlan.dietPlan}
                onEdit={() => {
                  // Use the hidden edit button
                  const editButton = document.querySelector('[data-edit-nutrition-plan-button]');
                  if (editButton) {
                    (editButton as HTMLButtonElement).click();
                  }
                }}
              />
              {/* Hidden button for EditNutritionPlan to be triggered programmatically */}
              <div className="hidden">
                <EditNutritionPlan
                  plan={nutritionPlan.dietPlan}
                  buttonProps={{ 'data-edit-nutrition-plan-button': true }}
                  onSuccess={handleNutritionPlanCreated}
                />
              </div>
            </>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Nutrition Plan</CardTitle>
                <CardDescription>Client's current nutrition plan</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">
                    No nutrition plan has been created for this client yet.
                  </p>
                  <CreateNutritionPlan
                    clientId={client?.id}
                    onSuccess={handleNutritionPlanCreated}
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="chat" className="space-y-6">
          <PremiumClientChat
            clientId={clientId}
            clientName={client?.name || 'Client'}
            clientAvatar={client?.avatarUrl || `https://avatar.vercel.sh/${client?.name}`}
          />
        </TabsContent>

        <TabsContent value="progress" className="space-y-6">
          {/* Client Goals Section */}
          <Card>
            <CardHeader>
              <CardTitle>Client Goals</CardTitle>
              <CardDescription>Current fitness and health objectives</CardDescription>
            </CardHeader>
            <CardContent>
              {client?.goals && client.goals.length > 0 ? (
                <ul className="space-y-2">
                  {client.goals.map((goal: string, index: number) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="rounded-full h-5 w-5 bg-primary/10 text-primary flex items-center justify-center mt-0.5">
                        {index + 1}
                      </div>
                      <span>{goal}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-muted-foreground">No goals set yet. Add some goals to help track progress.</p>
              )}
              <div className="mt-4">
                <EditGoalsDialog
                  clientId={client?.id || ''}
                  initialGoals={client?.goals || []}
                  onGoalsUpdated={(updatedGoals) => {
                    // Update client state with new goals
                    setClient(prev => {
                      if (!prev) return prev
                      return {
                        ...prev,
                        goals: updatedGoals
                      }
                    })
                  }}
                />
              </div>
            </CardContent>
          </Card>

          {/* Progress Overview Header with Time Range Selector */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-2">
            <div>
              <h2 className="text-2xl font-bold tracking-tight">Progress Overview</h2>
              <p className="text-muted-foreground">Comprehensive view of client's progress and metrics</p>
            </div>
            <div className="flex items-center gap-2">
              <Select
                value={progressTimeRange}
                onValueChange={setProgressTimeRange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">Last Week</SelectItem>
                  <SelectItem value="month">Last Month</SelectItem>
                  <SelectItem value="quarter">Last 3 Months</SelectItem>
                  <SelectItem value="year">Last Year</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Progress Summary Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="bg-gradient-to-br from-background to-muted/30">
              <CardContent className="pt-6">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Weight Change</p>
                    <h3 className="text-2xl font-bold mt-1 flex items-center">
                      {progressMetrics.weightChange > 0 ? (
                        <ArrowUpRight className="mr-1 h-5 w-5 text-red-500" />
                      ) : (
                        <ArrowDownRight className="mr-1 h-5 w-5 text-green-500" />
                      )}
                      {progressMetrics.weightChange ? Math.abs(progressMetrics.weightChange).toFixed(1) : '0'} lbs
                    </h3>
                    <p className="text-xs text-muted-foreground mt-1">
                      {progressMetrics.weightChangePercent ? Math.abs(progressMetrics.weightChangePercent).toFixed(1) : '0'}% {progressMetrics.weightChange > 0 ? 'increase' : 'decrease'}
                    </p>
                  </div>
                  <div className="p-2 rounded-full bg-primary/10">
                    <Scale className="h-5 w-5 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-background to-muted/30">
              <CardContent className="pt-6">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Body Fat Change</p>
                    <h3 className="text-2xl font-bold mt-1 flex items-center">
                      {progressMetrics.bodyFatChange > 0 ? (
                        <ArrowUpRight className="mr-1 h-5 w-5 text-red-500" />
                      ) : (
                        <ArrowDownRight className="mr-1 h-5 w-5 text-green-500" />
                      )}
                      {progressMetrics.bodyFatChange ? Math.abs(progressMetrics.bodyFatChange).toFixed(1) : '0'}%
                    </h3>
                    <p className="text-xs text-muted-foreground mt-1">
                      {progressMetrics.bodyFatChangePercent ? Math.abs(progressMetrics.bodyFatChangePercent).toFixed(1) : '0'}% {progressMetrics.bodyFatChange > 0 ? 'increase' : 'decrease'}
                    </p>
                  </div>
                  <div className="p-2 rounded-full bg-primary/10">
                    <Target className="h-5 w-5 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-background to-muted/30">
              <CardContent className="pt-6">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Workout Completion</p>
                    <h3 className="text-2xl font-bold mt-1 flex items-center">
                      {progressMetrics.workoutCompletionRate ? progressMetrics.workoutCompletionRate.toFixed(0) : '0'}%
                    </h3>
                    <p className="text-xs text-muted-foreground mt-1">
                      {workoutLogs.filter(log => log.completed).length} of {workoutLogs.length} workouts completed
                    </p>
                  </div>
                  <div className="p-2 rounded-full bg-primary/10">
                    <Dumbbell className="h-5 w-5 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-background to-muted/30">
              <CardContent className="pt-6">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Nutrition Adherence</p>
                    <h3 className="text-2xl font-bold mt-1 flex items-center">
                      {progressMetrics.nutritionAdherence ? progressMetrics.nutritionAdherence.toFixed(0) : '0'}%
                    </h3>
                    <p className="text-xs text-muted-foreground mt-1">
                      Based on {nutritionLogs.length} nutrition logs
                    </p>
                  </div>
                  <div className="p-2 rounded-full bg-primary/10">
                    <Utensils className="h-5 w-5 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Progress Charts */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Weight Progress Chart */}
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Weight Progress</CardTitle>
                    <CardDescription>Client's weight over time</CardDescription>
                  </div>
                  <Badge variant="outline" className="ml-2">
                    Current: {client?.progress && client.progress.length > 0 ? client.progress[client.progress.length - 1].weight : 'N/A'} lbs
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={client?.progress ? [...client.progress].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()) : []}
                      margin={{ top: 5, right: 10, left: 10, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(date) => new Date(date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                      />
                      <YAxis domain={['dataMin - 5', 'dataMax + 5']} />
                      <Tooltip
                        labelFormatter={(date) => new Date(date).toLocaleDateString()}
                        formatter={(value) => [`${value} lbs`, 'Weight']}
                      />
                      <Line
                        type="monotone"
                        dataKey="weight"
                        stroke="#8884d8"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
                <div className="flex justify-end mt-4">
                  <Button variant="outline" size="sm">
                    <Scale className="mr-2 h-4 w-4" />
                    Add Measurement
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Body Fat Progress Chart */}
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Body Fat Progress</CardTitle>
                    <CardDescription>Client's body fat percentage</CardDescription>
                  </div>
                  <Badge variant="outline" className="ml-2">
                    Current: {client?.progress && client.progress.length > 0 ? client.progress[client.progress.length - 1].bodyFat || 'N/A' : 'N/A'}%
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={client?.progress ? [...client.progress].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()) : []}
                      margin={{ top: 5, right: 10, left: 10, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(date) => new Date(date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                      />
                      <YAxis domain={['dataMin - 2', 'dataMax + 2']} />
                      <Tooltip
                        labelFormatter={(date) => new Date(date).toLocaleDateString()}
                        formatter={(value) => [`${value}%`, 'Body Fat']}
                      />
                      <Line
                        type="monotone"
                        dataKey="bodyFat"
                        stroke="#82ca9d"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
                <div className="flex justify-end mt-4">
                  <Button variant="outline" size="sm">
                    <Target className="mr-2 h-4 w-4" />
                    Set Goal
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Workout and Nutrition Tracking */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Workout Completion */}
            <Card>
              <CardHeader>
                <CardTitle>Workout Completion</CardTitle>
                <CardDescription>Client's workout adherence</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[...workoutLogs].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())}
                      margin={{ top: 5, right: 10, left: 10, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(date) => new Date(date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                      />
                      <YAxis />
                      <Tooltip
                        labelFormatter={(date) => new Date(date).toLocaleDateString()}
                        formatter={(value, name) => [value, name === 'duration' ? 'Duration (min)' : name]}
                      />
                      <Bar dataKey="duration" fill="#8884d8" name="Duration (min)" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Recent Workouts:</span>
                    <span className="font-medium">{workoutLogs.length} completed</span>
                  </div>
                  <div className="space-y-1 max-h-[150px] overflow-y-auto pr-2">
                    {[...workoutLogs]
                      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                      .slice(0, 5)
                      .map((log, index) => (
                      <div key={log.id} className="flex justify-between items-center p-2 rounded-md bg-muted/50 text-sm">
                        <div className="flex items-center gap-2">
                          <CalendarCheck className="h-4 w-4 text-primary/70" />
                          <span>{log.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span>{new Date(log.date).toLocaleDateString()}</span>
                          <Badge variant="outline">{log.duration} min</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Nutrition Tracking */}
            <Card>
              <CardHeader>
                <CardTitle>Nutrition Tracking</CardTitle>
                <CardDescription>Client's nutrition adherence</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[...nutritionLogs].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())}
                      margin={{ top: 5, right: 10, left: 10, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(date) => new Date(date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                      />
                      <YAxis />
                      <Tooltip
                        labelFormatter={(date) => new Date(date).toLocaleDateString()}
                        formatter={(value, name) => [value, name === 'calories' ? 'Calories' : name]}
                      />
                      <Bar dataKey="calories" fill="#82ca9d" name="Calories" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Recent Nutrition Logs:</span>
                    <span className="font-medium">{nutritionLogs.length} entries</span>
                  </div>
                  <div className="space-y-1 max-h-[150px] overflow-y-auto pr-2">
                    {[...nutritionLogs]
                      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                      .slice(0, 5)
                      .map((log, index) => (
                      <div key={log.id} className="flex justify-between items-center p-2 rounded-md bg-muted/50 text-sm">
                        <div className="flex items-center gap-2">
                          <Utensils className="h-4 w-4 text-primary/70" />
                          <span>{log.mealType.charAt(0).toUpperCase() + log.mealType.slice(1)}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span>{log.calories} cal</span>
                          <Badge variant="outline">{log.protein}g protein</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Body Measurements and Progress Photos */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Body Measurements */}
            <Card>
              <CardHeader>
                <CardTitle>Body Measurements</CardTitle>
                <CardDescription>Client's detailed body measurements</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {client?.progress && client.progress.length > 0 && client.progress[0].measurements ? (
                    <div className="grid grid-cols-2 gap-4">
                      {Object.entries(client.progress[client.progress.length - 1].measurements || {}).map(([key, value]) => (
                        <div key={key} className="flex flex-col space-y-1">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium capitalize">{key}</span>
                            <span className="text-sm">{value} in</span>
                          </div>
                          <Progress value={75} className="h-1" />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col space-y-4">
                      <div className="flex flex-col space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Chest</span>
                          <span className="text-sm">N/A</span>
                        </div>
                        <Progress value={0} className="h-1" />
                      </div>
                      <div className="flex flex-col space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Waist</span>
                          <span className="text-sm">N/A</span>
                        </div>
                        <Progress value={0} className="h-1" />
                      </div>
                      <div className="flex flex-col space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Arms</span>
                          <span className="text-sm">N/A</span>
                        </div>
                        <Progress value={0} className="h-1" />
                      </div>
                      <div className="flex flex-col space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Thighs</span>
                          <span className="text-sm">N/A</span>
                        </div>
                        <Progress value={0} className="h-1" />
                      </div>
                    </div>
                  )}
                  <div className="flex justify-end mt-2">
                    <Button variant="outline" size="sm">
                      <Ruler className="mr-2 h-4 w-4" />
                      Add Measurements
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Progress Photos */}
            <Card>
              <CardHeader>
                <CardTitle>Progress Photos</CardTitle>
                <CardDescription>Visual progress tracking</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-2">
                  {client?.progress && client.progress.some(p => p.frontPhotoUrl || p.sidePhotoUrl || p.backPhotoUrl) ? (
                    // Show actual photos if available
                    client.progress
                      .filter(p => p.frontPhotoUrl || p.sidePhotoUrl || p.backPhotoUrl)
                      .slice(-3)
                      .map((entry, index) => (
                        <div key={index} className="relative aspect-square rounded-md overflow-hidden border">
                          <img
                            src={entry.frontPhotoUrl || entry.sidePhotoUrl || entry.backPhotoUrl}
                            alt={`Progress photo ${index + 1}`}
                            className="object-cover w-full h-full"
                          />
                          <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-1 text-center">
                            {new Date(entry.date).toLocaleDateString()}
                          </div>
                        </div>
                      ))
                  ) : (
                    // Placeholder boxes if no photos
                    Array(3).fill(0).map((_, index) => (
                      <div key={index} className="aspect-square rounded-md border border-dashed flex items-center justify-center bg-muted/50">
                        <Camera className="h-8 w-8 text-muted-foreground/50" />
                      </div>
                    ))
                  )}
                </div>
                <div className="flex justify-end mt-4">
                  <Button variant="outline" size="sm">
                    <Camera className="mr-2 h-4 w-4" />
                    Upload Photos
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Progress History Table */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Progress History</CardTitle>
                  <CardDescription>Client's recorded measurements</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Export Data
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="grid grid-cols-5 p-4 font-medium border-b bg-muted/50">
                  <div>Date</div>
                  <div>Weight (lbs)</div>
                  <div>Body Fat (%)</div>
                  <div>Measurements</div>
                  <div>Notes</div>
                </div>

                <div className="max-h-[300px] overflow-y-auto">
                  {client?.progress && client.progress.length > 0 ? [...client.progress]
                    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()) // Sort in reverse chronological order
                    .map((entry: any, index: number) => (
                    <div
                      key={index}
                      className="grid grid-cols-5 p-4 border-b last:border-0 hover:bg-muted/50"
                    >
                      <div>{new Date(entry.date).toLocaleDateString()}</div>
                      <div>{entry.weight}</div>
                      <div>{entry.bodyFat || 'N/A'}</div>
                      <div>
                        {entry.measurements ? (
                          <Button variant="ghost" size="sm" className="h-6 px-2">
                            View Details
                          </Button>
                        ) : 'None'}
                      </div>
                      <div className="truncate">{entry.notes || 'No notes'}</div>
                    </div>
                  )) : (
                    <div className="p-4 text-center text-muted-foreground">
                      No progress data available
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="followup" className="space-y-6">
          <FollowUpTab
            clientId={clientId}
            clientName={client?.name || 'Client'}
            clientEmail={client?.email || ''}
            calendlySettings={calendlySettings}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
