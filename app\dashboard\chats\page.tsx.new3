  // Create a new conversation with a client
  const createConversation = async (clientId: string) => {
    if (!session?.user?.id || creatingConversation) return;
    
    try {
      setCreatingConversation(true);
      
      const response = await fetch("/api/coaching/conversations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId,
        }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to create conversation");
      }
      
      const data = await response.json();
      
      // Close the dialog
      setIsNewChatDialogOpen(false);
      
      // Manually fetch conversations to get the new one
      await fetchConversations();
      
      // Set the new conversation as active
      setActiveConversation(data);
      
      // Fetch messages for the new conversation
      fetchMessages(data.id);
      
      toast({
        title: "Success",
        description: "Conversation created successfully",
      });
    } catch (error) {
      console.error("Error creating conversation:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create conversation. Please try again.",
      });
    } finally {
      setCreatingConversation(false);
    }
  };

  // Handle manual refresh of conversations
  const handleRefreshConversations = () => {
    fetchConversations();
    toast({
      title: "Refreshed",
      description: "Conversations refreshed",
    });
  };

  // Handle manual refresh of messages
  const handleRefreshMessages = () => {
    if (activeConversation?.id) {
      fetchMessages(activeConversation.id);
      toast({
        title: "Refreshed",
        description: "Messages refreshed",
      });
    }
  };

  return (
    <div className="flex h-[calc(100vh-4rem)] flex-col md:flex-row">
      {/* Left sidebar with conversations */}
      <div className={`w-full md:w-80 lg:w-96 border-r flex flex-col ${activeConversation ? 'hidden md:flex' : 'flex'}`}>
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold">Messages</h2>
            <div className="flex gap-2">
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={handleRefreshConversations}
                title="Refresh conversations"
              >
                <RefreshCw className="h-5 w-5" />
              </Button>
              <Dialog open={isNewChatDialogOpen} onOpenChange={setIsNewChatDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="icon" title="New conversation">
                    <PlusCircle className="h-5 w-5" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>New Conversation</DialogTitle>
                    <DialogDescription>
                      Select a client to start a new conversation.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4">
                    <div className="relative mb-4">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search clients..."
                        className="pl-8"
                        value={clientSearchQuery}
                        onChange={(e) => setClientSearchQuery(e.target.value)}
                      />
                    </div>
                    <div className="max-h-[300px] overflow-y-auto">
                      {loadingClients ? (
                        <div className="flex justify-center py-4">
                          <LoadingSpinner />
                        </div>
                      ) : filteredClients.length === 0 ? (
                        <p className="text-center py-4 text-muted-foreground">No clients found</p>
                      ) : (
                        filteredClients.map((client) => (
                          <div
                            key={client.id}
                            className="flex items-center gap-3 p-2 rounded-md hover:bg-accent cursor-pointer"
                            onClick={() => createConversation(client.id)}
                          >
                            <Avatar>
                              <AvatarImage src={client.avatarUrl} />
                              <AvatarFallback>{client.name?.charAt(0) || "U"}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{client.name}</p>
                              <p className="text-sm text-muted-foreground">{client.email}</p>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search conversations..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <Tabs defaultValue="all" className="flex-1 flex flex-col">
          <div className="px-4 pt-2">
            <TabsList className="w-full">
              <TabsTrigger value="all" className="flex-1">All</TabsTrigger>
              <TabsTrigger value="clients" className="flex-1">Clients</TabsTrigger>
            </TabsList>
          </div>
          
          <TabsContent value="all" className="flex-1 overflow-y-auto p-0">
            {loading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : filteredConversations.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full p-4">
                <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-center text-muted-foreground">No conversations found</p>
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={() => setIsNewChatDialogOpen(true)}
                >
                  Start a new conversation
                </Button>
              </div>
            ) : (
              <div className="divide-y">
                {/* Unread conversations */}
                {unreadConversations.length > 0 && (
                  <>
                    <div className="px-4 py-2 text-sm font-medium text-muted-foreground bg-muted/50">
                      Unread
                    </div>
                    {unreadConversations.map((conversation) => (
                      <div
                        key={conversation.id}
                        className={`p-3 cursor-pointer hover:bg-accent ${
                          activeConversation?.id === conversation.id ? "bg-accent" : ""
                        }`}
                        onClick={() => setActiveConversation(conversation)}
                      >
                        <div className="flex items-start gap-3">
                          <Avatar>
                            <AvatarImage src={conversation.user.avatarUrl} />
                            <AvatarFallback>{conversation.user.name?.charAt(0) || "U"}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="font-medium truncate">{conversation.user.name}</p>
                              {conversation.lastMessage?.createdAt && (
                                <p className="text-xs text-muted-foreground">
                                  {formatDistanceToNow(new Date(conversation.lastMessage.createdAt), { addSuffix: true })}
                                </p>
                              )}
                            </div>
                            <div className="flex items-center justify-between mt-1">
                              <p className="text-sm truncate text-muted-foreground">
                                {conversation.lastMessage?.content || "No messages yet"}
                              </p>
                              {conversation.unreadCount > 0 && (
                                <Badge variant="default" className="ml-2 px-1.5 py-0.5 rounded-full">
                                  {conversation.unreadCount}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </>
                )}

                {/* Read conversations */}
                {readConversations.length > 0 && (
                  <>
                    <div className="px-4 py-2 text-sm font-medium text-muted-foreground bg-muted/50">
                      All conversations
                    </div>
                    {readConversations.map((conversation) => (
                      <div
                        key={conversation.id}
                        className={`p-3 cursor-pointer hover:bg-accent ${
                          activeConversation?.id === conversation.id ? "bg-accent" : ""
                        }`}
                        onClick={() => setActiveConversation(conversation)}
                      >
                        <div className="flex items-start gap-3">
                          <Avatar>
                            <AvatarImage src={conversation.user.avatarUrl} />
                            <AvatarFallback>{conversation.user.name?.charAt(0) || "U"}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="font-medium truncate">{conversation.user.name}</p>
                              {conversation.lastMessage?.createdAt && (
                                <p className="text-xs text-muted-foreground">
                                  {formatDistanceToNow(new Date(conversation.lastMessage.createdAt), { addSuffix: true })}
                                </p>
                              )}
                            </div>
                            <p className="text-sm truncate text-muted-foreground mt-1">
                              {conversation.lastMessage?.content || "No messages yet"}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </>
                )}
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="clients" className="flex-1 overflow-y-auto p-0">
            {loadingClients ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : filteredClients.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full p-4">
                <User className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-center text-muted-foreground">No clients found</p>
              </div>
            ) : (
              <div className="divide-y">
                {filteredClients.map((client) => (
                  <div
                    key={client.id}
                    className="p-3 cursor-pointer hover:bg-accent"
                    onClick={() => createConversation(client.id)}
                  >
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={client.avatarUrl} />
                        <AvatarFallback>{client.name?.charAt(0) || "U"}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{client.name}</p>
                        <p className="text-sm text-muted-foreground">{client.email}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Main chat area */}
      <div className={`flex-1 flex flex-col ${!activeConversation ? 'hidden md:flex' : 'flex'}`}>
        {activeConversation ? (
          <>
            {/* Chat header */}
            <div className="p-4 border-b flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="icon"
                  className="md:hidden"
                  onClick={() => setActiveConversation(null)}
                >
                  <ArrowLeft className="h-5 w-5" />
                </Button>
                <Avatar>
                  <AvatarImage src={activeConversation.user.avatarUrl} />
                  <AvatarFallback>{activeConversation.user.name?.charAt(0) || "U"}</AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium">{activeConversation.user.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {activeConversation.user.id}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button 
                  variant="ghost" 
                  size="icon"
                  onClick={handleRefreshMessages}
                  title="Refresh messages"
                >
                  <RefreshCw className="h-5 w-5" />
                </Button>
                <Button variant="ghost" size="icon">
                  <MoreVertical className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {updatingMessages ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : messages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-center text-muted-foreground">No messages yet</p>
                  <p className="text-center text-muted-foreground text-sm mt-2">
                    Send a message to start the conversation
                  </p>
                </div>
              ) : (
                messages.map((message) => {
                  const isCurrentUser = message.senderId === session?.user?.id;
                  return (
                    <div
                      key={message.id}
                      className={`flex ${isCurrentUser ? "justify-end" : "justify-start"}`}
                    >
                      <div className="flex items-start gap-2 max-w-[80%]">
                        {!isCurrentUser && (
                          <Avatar className="mt-1">
                            <AvatarImage src={message.sender?.avatarUrl} />
                            <AvatarFallback>
                              {message.sender?.name?.charAt(0) || "U"}
                            </AvatarFallback>
                          </Avatar>
                        )}
                        <div>
                          <div
                            className={`rounded-lg p-3 ${
                              isCurrentUser
                                ? "bg-primary text-primary-foreground"
                                : "bg-muted"
                            }`}
                          >
                            <p className="whitespace-pre-wrap break-words">{message.content}</p>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message input */}
            <div className="p-4 border-t">
              <form onSubmit={sendNewMessage} className="flex gap-2">
                <Input
                  ref={inputRef}
                  placeholder="Type a message..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  disabled={sendingMessage}
                  className="flex-1"
                />
                <Button type="submit" disabled={!newMessage.trim() || sendingMessage}>
                  <Send className="h-5 w-5" />
                </Button>
              </form>
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-full p-4">
            <MessageSquare className="h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-xl font-medium mb-2">Your Messages</h3>
            <p className="text-center text-muted-foreground mb-6">
              Select a conversation or start a new one
            </p>
            <Button onClick={() => setIsNewChatDialogOpen(true)}>
              <UserPlus className="h-5 w-5 mr-2" />
              New Conversation
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
