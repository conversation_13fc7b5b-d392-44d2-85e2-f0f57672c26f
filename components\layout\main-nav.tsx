"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"
import { cn } from "@/lib/utils"

export function MainNav() {
  const pathname = usePathname()
  const [role, setRole] = useState("client")
  const [isPremium, setIsPremium] = useState(false)

  // Load role and premium status from localStorage
  useEffect(() => {
    const savedRole = localStorage.getItem("userRole")
    if (savedRole) {
      setRole(savedRole)
    }

    // Check for premium status in development mode
    if (process.env.NODE_ENV === 'development') {
      const cookies = document.cookie.split(';')

      // Check for premium status cookie
      const premiumCookie = cookies.find(cookie => cookie.trim().startsWith('dev_premium_status='))
      if (premiumCookie) {
        setIsPremium(premiumCookie.split('=')[1] === 'true')
      }

      // Check for explicit premiumClient role
      const roleCookie = cookies.find(cookie => cookie.trim().startsWith('dev_override_role='))
      if (roleCookie) {
        const roleValue = roleCookie.split('=')[1]
        if (roleValue === 'premiumClient') {
          setIsPremium(true)
        }
      }
    }
  }, [])

  // Define navigation items based on role - simplified
  const clientNavItems = [
    {
      title: "Dashboard",
      href: "/dashboard",
    },
    {
      title: "My Workouts",
      href: "/dashboard/workouts/current",
    },
    {
      title: "Trainers",
      href: "/dashboard/trainers",
    },
  ]

  // Premium client navigation items
  const premiumClientNavItems = [
    ...clientNavItems
  ]

  const trainerNavItems = [
    {
      title: "Dashboard",
      href: "/dashboard",
    },
    {
      title: "My Profile",
      href: "/dashboard/trainer-profile",
    },
    {
      title: "My Clients",
      href: "/dashboard/my-clients",
    },
    {
      title: "Training Plans",
      href: "/dashboard/training-plans",
    },
    {
      title: "Analytics",
      href: "/dashboard/analytics",
    },
  ]

  // Determine which navigation items to use based on role and premium status
  let navItems = clientNavItems

  if (role === "trainer") {
    navItems = trainerNavItems
  } else if (isPremium || role === "premiumClient") {
    navItems = premiumClientNavItems
  }

  return (
    <nav className="flex items-center space-x-4 lg:space-x-6">
      {navItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className={cn(
            "text-sm font-medium transition-colors hover:text-primary",
            pathname === item.href
              ? "text-primary"
              : "text-muted-foreground"
          )}
        >
          {item.title}
        </Link>
      ))}
    </nav>
  )
}