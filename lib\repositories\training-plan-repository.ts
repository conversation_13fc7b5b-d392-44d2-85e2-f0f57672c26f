import { prisma } from "@/lib/prisma"
import { TrainingPlanTemplate } from "@prisma/client"
import { TrainingPlanData } from "../services/training-plan-service"

export interface ITrainingPlanRepository {
  createTemplate(trainerId: string, planData: TrainingPlanData): Promise<TrainingPlanTemplate>
  getTemplatesForTrainer(trainerId: string): Promise<any[]>
  getTemplateById(id: string, trainerId: string): Promise<TrainingPlanTemplate | null>
  updateTemplate(id: string, trainerId: string, data: Partial<TrainingPlanData>): Promise<TrainingPlanTemplate>
  deleteTemplate(id: string, trainerId: string): Promise<TrainingPlanTemplate>
  assignPlanToClient(trainerId: string, clientId: string, planData: TrainingPlanData): Promise<TrainingPlanTemplate>
}

export class TrainingPlanRepository implements ITrainingPlanRepository {
  async createTemplate(trainerId: string, planData: TrainingPlanData): Promise<TrainingPlanTemplate> {
    try {
      console.log('Creating template with data:', {
        title: planData.title,
        description: planData.description,
        difficulty: planData.difficulty,
        trainerId: trainerId,
        weeksType: typeof planData.weeks,
        type: planData.type
      })

      // Ensure weeks is properly handled as a JSON object
      let weeksData;

      if (typeof planData.weeks === 'string') {
        try {
          // If it's a string, try to parse it to make sure it's valid JSON
          const parsed = JSON.parse(planData.weeks);
          weeksData = parsed;
          console.log('Successfully parsed weeks from string to object');
        } catch (e) {
          console.error('Error parsing weeks string:', e);
          // If parsing fails, use an empty object
          weeksData = {};
        }
      } else if (planData.weeks && typeof planData.weeks === 'object') {
        // If it's already an object, use it directly
        weeksData = planData.weeks;
        console.log('Using weeks as object directly');
      } else {
        // Default to empty object
        weeksData = {};
        console.log('Using empty object for weeks');
      }

      return prisma.trainingPlanTemplate.create({
        data: {
          title: planData.title,
          description: planData.description || "",
          difficulty: planData.difficulty || "beginner",
          trainerId: trainerId,
          weeks: weeksData,
          type: planData.type || "template"
        }
      })
    } catch (error) {
      console.error('Error in createTemplate:', error)
      throw error
    }
  }

  async getTemplatesForTrainer(trainerId: string): Promise<any[]> {
    try {
      console.log(`Fetching templates for trainer: ${trainerId}`)

      const templates = await prisma.trainingPlanTemplate.findMany({
        where: {
          trainerId: trainerId,
        },
        orderBy: {
          createdAt: "desc"
        }
      })

      console.log(`Found ${templates.length} templates for trainer ${trainerId}`)
      return templates
    } catch (error) {
      console.error('Error in getTemplatesForTrainer:', error)
      throw error
    }
  }

  async getTemplateById(id: string, userId: string): Promise<TrainingPlanTemplate | null> {
    try {
      const template = await prisma.trainingPlanTemplate.findFirst({
        where: {
          id: id,
          OR: [
            { trainerId: userId },  // User is the trainer
            { clientId: userId }    // User is the client
          ]
        }
      })

      if (template && template.weeks && typeof template.weeks === 'string') {
        try {
          const parsedWeeks = JSON.parse(template.weeks as string)
          return {
            ...template,
            weeks: parsedWeeks
          }
        } catch (e) {
          console.error('Error parsing weeks JSON:', e)
        }
      }

      return template
    } catch (error) {
      console.error('Error in getTemplateById:', error)
      throw error
    }
  }

  async updateTemplate(
    id: string,
    trainerId: string,
    data: Partial<TrainingPlanData>
  ): Promise<TrainingPlanTemplate> {
    try {
      console.log('Updating template with data:', {
        id,
        trainerId,
        title: data.title,
        description: data.description,
        difficulty: data.difficulty,
        weeksType: data.weeks ? typeof data.weeks : 'undefined'
      })

      // Prepare update data
      const updateData: any = {}

      if (data.title) updateData.title = data.title
      if (data.description !== undefined) updateData.description = data.description
      if (data.difficulty) updateData.difficulty = data.difficulty

      // Handle weeks data specially
      if (data.weeks) {
        let weeksData;

        if (typeof data.weeks === 'string') {
          try {
            // If it's a string, try to parse it to make sure it's valid JSON
            const parsed = JSON.parse(data.weeks);
            weeksData = parsed;
            console.log('Successfully parsed weeks from string to object for update');
          } catch (e) {
            console.error('Error parsing weeks string for update:', e);
            // If parsing fails, use an empty object
            weeksData = {};
          }
        } else if (typeof data.weeks === 'object') {
          // If it's already an object, use it directly
          weeksData = data.weeks;
          console.log('Using weeks as object directly for update');
        } else {
          // Default to empty object
          weeksData = {};
          console.log('Using empty object for weeks in update');
        }

        updateData.weeks = weeksData;
      }

      console.log('Update data prepared:', updateData)

      return prisma.trainingPlanTemplate.update({
        where: {
          id: id,
          trainerId: trainerId // Ensure the trainer owns this template
        },
        data: updateData
      })
    } catch (error) {
      console.error('Error in updateTemplate:', error)
      throw error
    }
  }

  async deleteTemplate(id: string, trainerId: string): Promise<TrainingPlanTemplate> {
    return prisma.trainingPlanTemplate.delete({
      where: {
        id: id,
        trainerId: trainerId
      }
    })
  }

  async assignPlanToClient(
    trainerId: string,
    clientId: string,
    planData: TrainingPlanData
  ): Promise<TrainingPlanTemplate> {
    try {
      console.log('Assigning plan to client with data:', {
        title: planData.title,
        description: planData.description,
        difficulty: planData.difficulty,
        clientId: clientId,
        trainerId: trainerId,
        weeksType: typeof planData.weeks
      });

      // Ensure weeks is properly handled as a JSON object
      let weeksData;

      if (typeof planData.weeks === 'string') {
        try {
          // If it's a string, try to parse it to make sure it's valid JSON
          const parsed = JSON.parse(planData.weeks);
          weeksData = parsed;
          console.log('Successfully parsed weeks from string to object for client plan');
        } catch (e) {
          console.error('Error parsing weeks string for client plan:', e);
          // If parsing fails, use an empty object
          weeksData = {};
        }
      } else if (planData.weeks && typeof planData.weeks === 'object') {
        // If it's already an object, use it directly
        weeksData = planData.weeks;
        console.log('Using weeks as object directly for client plan');
      } else {
        // Default to empty object
        weeksData = {};
        console.log('Using empty object for weeks in client plan');
      }

      return prisma.trainingPlanTemplate.create({
        data: {
          title: planData.title,
          description: planData.description || "",
          difficulty: planData.difficulty || "beginner",
          type: "personalized",
          clientId: clientId,
          trainerId: trainerId,
          weeks: weeksData
        }
      });
    } catch (error) {
      console.error('Error in assignPlanToClient:', error);
      throw error;
    }
  }
}
