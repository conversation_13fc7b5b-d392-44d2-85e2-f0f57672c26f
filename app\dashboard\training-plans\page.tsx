"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dumbbell, Calendar, Plus, Clock, Users, FolderPlus, ChevronRight, MoreHorizontal } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface TrainingPlanTemplate {
  id: string
  title: string
  description: string
  difficulty: string
  trainerId: string
  weeks: any[]
  createdAt: string
  updatedAt: string
  featured: boolean
  activeClients: number
  tierTrainingPlans?: any[]
  productTrainingPlans?: any[]
  type: string
  clientId?: string
}

function StatsCard({
  title,
  value,
  change,
  icon: Icon,
}: {
  title: string
  value: string | number
  change: string
  icon: any
}) {
  return (
    <Card className="shadow-sm hover:shadow-lg hover:-translate-y-1 transition-all">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="p-2 rounded-full bg-primary/10">
            <Icon className="h-5 w-5 text-primary" />
          </div>
          <div className="p-2 rounded-full hover:bg-accent/10 transition-colors cursor-pointer">
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          </div>
        </div>
        <div className="space-y-1 mt-4">
          <p className="text-sm text-muted-foreground">{title}</p>
          <p className="text-2xl font-semibold">{value}</p>
          <p className="text-sm text-muted-foreground">{change}</p>
        </div>
      </CardContent>
    </Card>
  )
}

function PlanCard({ plan, onDelete }: { plan: TrainingPlanTemplate, onDelete: (id: string) => void }) {
  const router = useRouter()
  const getDifficultyBadge = (level?: string) => {
    const baseClasses = "text-xs px-2 py-0.5 rounded-full"
    switch (level?.toLowerCase()) {
      case "beginner":
        return `${baseClasses} bg-blue-500/10 text-blue-500`
      case "intermediate":
        return `${baseClasses} bg-yellow-500/10 text-yellow-500`
      case "advanced":
        return `${baseClasses} bg-red-500/10 text-red-500`
      default:
        return `${baseClasses} bg-gray-500/10 text-gray-500`
    }
  }

  return (
    <Card
      className={`shadow-sm hover:shadow-lg hover:-translate-y-1 transition-all cursor-pointer ${
        plan.featured ? "col-span-2" : ""
      }`}
      onClick={() => {
        console.log('PlanCard clicked, redirecting to:', `/dashboard/training-plans/preview/${plan.id}`);
        router.push(`/dashboard/training-plans/preview/${plan.id}`);
      }}
    >
      {plan.featured && (
        <div className="bg-primary text-primary-foreground text-xs px-3 py-1 rounded-full absolute -top-2 left-4">
          Featured Plan
        </div>
      )}
      <CardHeader className="p-6">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-xl">{plan.title}</CardTitle>
            <p className="text-sm text-muted-foreground">{plan.description}</p>
          </div>
          <span className={getDifficultyBadge(plan.difficulty)}>
            {plan.difficulty}
          </span>
        </div>
      </CardHeader>
      <CardContent className="p-6 pt-0">
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Created:</p>
            <p className="font-medium">
              {new Date(plan.createdAt).toLocaleDateString()}
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Type:</p>
            <div className="flex flex-wrap gap-1">
              <Badge variant="outline" className={plan.type === "template" ? "bg-green-50 text-green-700 border-green-200" : "bg-orange-50 text-orange-700 border-orange-200"}>
                {plan.type === "template" ? "Template" : "Personalized"}
              </Badge>
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Usage:</p>
            <div className="flex flex-wrap gap-1">
              {plan.tierTrainingPlans && plan.tierTrainingPlans.length > 0 && (
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  {plan.tierTrainingPlans.length} Subscription{plan.tierTrainingPlans.length !== 1 ? 's' : ''}
                </Badge>
              )}
              {plan.clientId && (
                <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                  1:1 Coaching
                </Badge>
              )}
              {/* Product assignments will be added in a future update */}
              {(!plan.tierTrainingPlans || plan.tierTrainingPlans.length === 0) &&
               !plan.clientId && (
                <span className="text-sm text-muted-foreground">Not assigned</span>
              )}
            </div>
          </div>
        </div>
        <div className="flex gap-2 mt-6">
          <Button
            variant="outline"
            className="flex-1"
            onClick={(e) => {
              e.stopPropagation()
              router.push(`/dashboard/training-plans/${plan.id}/edit`)
            }}
          >
            Edit
          </Button>
          <Button
            variant="destructive"
            className="flex-1"
            onClick={(e) => {
              e.stopPropagation();
              if (confirm("Are you sure you want to delete this plan?")) {
                onDelete(plan.id);
              }
            }}
          >
            Delete
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default function TrainingPlans() {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")

  // Redirect clients to the workouts page
  useEffect(() => {
    // Check if user is a client (in a real app, this would check the user's role)
    const isClient = document.cookie.includes('dev_override_role=client')

    if (isClient) {
      router.replace('/dashboard/workouts/current')
    }
  }, [router])

  const [plans, setPlans] = useState<TrainingPlanTemplate[]>([])
  const [activeClients, setActiveClients] = useState(0)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        console.log('Fetching training plan templates...')

        // Fetch plans
        const plansResponse = await fetch("/api/training-plan-templates")
        const responseText = await plansResponse.text()

        if (!plansResponse.ok) {
          console.error('Failed to fetch plans:', responseText)
          throw new Error(`Failed to fetch plans: ${plansResponse.status} ${plansResponse.statusText}`)
        }

        // Try to parse the JSON response
        let plansData
        try {
          plansData = JSON.parse(responseText)
          console.log('Parsed plans data:', plansData)
        } catch (parseError) {
          console.error('Error parsing plans data:', parseError, 'Raw response:', responseText)
          throw new Error('Invalid response format')
        }

        setPlans(Array.isArray(plansData) ? plansData : [])
        console.log('Plans set:', Array.isArray(plansData) ? plansData.length : 0, 'plans')

        // Fetch active clients count
        try {
          const clientsResponse = await fetch("/api/clients/count")
          if (!clientsResponse.ok) {
            console.warn("Failed to fetch client count, using default value")
            setActiveClients(0)
          } else {
            const clientsData = await clientsResponse.json()
            setActiveClients(clientsData.activeClients || 0)
          }
        } catch (clientError) {
          console.warn("Error fetching client count:", clientError)
          setActiveClients(0)
        }
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: typeof error === 'object' && error !== null ? error.message : "Failed to load data",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [toast])

  const deletePlan = async (id: string) => {
    try {
      const response = await fetch(`/api/training-plan-templates/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete plan')
      }

      setPlans(plans.filter(plan => plan.id !== id))
      toast({
        title: "Success",
        description: "The training plan has been successfully deleted.",
      })
    } catch (error) {
      console.error('Error deleting plan:', error)
      toast({
        title: "Error",
        description: "Failed to delete the training plan. Please try again.",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Training Plans</h1>
            <p className="text-muted-foreground mt-1">
              Create and manage your training programs
            </p>
          </div>
        </div>

        <div className="flex items-center justify-center h-[400px]">
          <div className="flex flex-col items-center gap-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="text-muted-foreground">Loading training plans...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Training Plans</h1>
          <p className="text-muted-foreground mt-1">
            Create and manage your training programs
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-2">
          <Button onClick={() => router.push("/dashboard/training-plans/create")}>
            <Plus className="mr-2 h-4 w-4" />
            Create New Plan
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push("/dashboard/training-plans/import")}
          >
            Import from AI Template
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <StatsCard
          title="Total Plans"
          value={plans.length}
          change={`Total training plans`}
          icon={FolderPlus}
        />
        <StatsCard
          title="Templates"
          value={plans.filter(plan => plan.type === "template").length}
          change={`Reusable plan templates`}
          icon={Dumbbell}
        />
        <StatsCard
          title="Personalized"
          value={plans.filter(plan => plan.type === "personalized").length}
          change={`Client-specific plans`}
          icon={Users}
        />
        <StatsCard
          title="Avg. Duration"
          value={`${Math.round(plans.reduce((sum, plan) => sum + (plan.weeks?.length || 1), 0) / Math.max(plans.length, 1))} weeks`}
          change={`Average across all plans`}
          icon={Clock}
        />
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Your Training Plans</h2>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="all">All Plans</TabsTrigger>
            <TabsTrigger value="template">Templates</TabsTrigger>
            <TabsTrigger value="personalized">Personalized</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {plans.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="max-w-sm mx-auto">
            <h3 className="text-lg font-medium mb-2">You haven't created any training plans yet.</h3>
            <p className="text-muted-foreground mb-4">
              Start by creating your first training plan to share with your clients.
            </p>
            <Button onClick={() => router.push("/dashboard/training-plans/create")}>
              Create Your First Plan
            </Button>
          </div>
        </Card>
      ) : (
        <>
          {/* Filter plans based on selected tab */}
          {plans.filter(plan => {
            if (activeTab === "all") return true;
            return plan.type === activeTab;
          }).length === 0 ? (
            <Card className="p-8 text-center">
              <div className="max-w-sm mx-auto">
                <h3 className="text-lg font-medium mb-2">No {activeTab === "template" ? "template" : "personalized"} plans found.</h3>
                <p className="text-muted-foreground mb-4">
                  {activeTab === "template"
                    ? "Create a template plan to reuse with multiple clients."
                    : "Create personalized plans for specific clients."}
                </p>
                <Button onClick={() => router.push("/dashboard/training-plans/create")}>
                  Create New Plan
                </Button>
              </div>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {plans
                .filter(plan => {
                  if (activeTab === "all") return true;
                  return plan.type === activeTab;
                })
                .map((plan) => (
                  <PlanCard key={plan.id} plan={plan} onDelete={deletePlan} />
                ))}
            </div>
          )}
        </>
      )}
    </div>
  )
}

