/**
 * Optimized API utilities for Next.js API routes
 * Provides performance optimizations for database queries in API routes
 */

import { NextRequest, NextResponse } from 'next/server';
import { queryCache } from '../cache';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth';

// Cache TTL values (in milliseconds)
const DEFAULT_CACHE_TTL = 60 * 1000; // 1 minute
const STATIC_CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const DYNAMIC_CACHE_TTL = 30 * 1000; // 30 seconds

// Types for API handler options
type ApiHandlerOptions = {
  requireAuth?: boolean;
  cacheResponse?: boolean;
  cacheTTL?: number;
};

/**
 * Wrap an API handler with optimizations
 * @param handler The API handler function
 * @param options Options for the handler
 * @returns An optimized API handler
 */
export function optimizedApiHandler(
  handler: (req: NextRequest, context: any, session?: any) => Promise<NextResponse>,
  options: ApiHandlerOptions = {}
) {
  const { requireAuth = true, cacheResponse = false, cacheTTL = DEFAULT_CACHE_TTL } = options;
  
  return async function(req: NextRequest, context: any): Promise<NextResponse> {
    try {
      // Check authentication if required
      if (requireAuth) {
        const session = await getServerSession(authOptions);
        
        if (!session?.user) {
          return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }
        
        // Pass session to handler
        return await handleRequest(req, context, handler, cacheResponse, cacheTTL, session);
      }
      
      // No auth required, just handle the request
      return await handleRequest(req, context, handler, cacheResponse, cacheTTL);
    } catch (error) {
      console.error('[API_ERROR]', error);
      return NextResponse.json(
        { error: 'An error occurred processing your request' },
        { status: 500 }
      );
    }
  };
}

/**
 * Handle an API request with caching if enabled
 */
async function handleRequest(
  req: NextRequest,
  context: any,
  handler: (req: NextRequest, context: any, session?: any) => Promise<NextResponse>,
  cacheResponse: boolean,
  cacheTTL: number,
  session?: any
): Promise<NextResponse> {
  // Only cache GET requests
  if (cacheResponse && req.method === 'GET') {
    const cacheKey = generateCacheKey(req, session);
    
    // Try to get from cache
    const cachedResponse = queryCache.get<NextResponse>(cacheKey);
    if (cachedResponse) {
      // Add cache header for debugging
      const headers = new Headers(cachedResponse.headers);
      headers.set('X-Cache', 'HIT');
      
      // Clone the response with the new headers
      return new NextResponse(cachedResponse.body, {
        status: cachedResponse.status,
        statusText: cachedResponse.statusText,
        headers
      });
    }
    
    // Not in cache, execute the handler
    const response = await handler(req, context, session);
    
    // Only cache successful responses
    if (response.ok) {
      // Clone the response before caching
      const responseToCache = response.clone();
      
      // Store in cache
      queryCache.set(cacheKey, responseToCache, cacheTTL);
      
      // Add cache header for debugging
      response.headers.set('X-Cache', 'MISS');
    }
    
    return response;
  }
  
  // No caching, just execute the handler
  return handler(req, context, session);
}

/**
 * Generate a cache key from the request and session
 */
function generateCacheKey(req: NextRequest, session?: any): string {
  const url = new URL(req.url);
  const pathname = url.pathname;
  const search = url.search;
  
  // Include user ID in cache key to prevent sharing data between users
  const userId = session?.user?.id || 'anonymous';
  
  return `${pathname}${search}:${userId}`;
}

/**
 * Example usage:
 * 
 * // In your API route file:
 * import { optimizedApiHandler } from '@/lib/api/optimized-api';
 * 
 * // For a route that requires auth and should be cached:
 * export const GET = optimizedApiHandler(
 *   async (req, { params }, session) => {
 *     // Your handler logic here
 *     return NextResponse.json({ data: 'example' });
 *   },
 *   { requireAuth: true, cacheResponse: true, cacheTTL: 60000 }
 * );
 * 
 * // For a public route that should be cached longer:
 * export const GET = optimizedApiHandler(
 *   async (req, { params }) => {
 *     // Your handler logic here
 *     return NextResponse.json({ data: 'example' });
 *   },
 *   { requireAuth: false, cacheResponse: true, cacheTTL: 300000 }
 * );
 */
