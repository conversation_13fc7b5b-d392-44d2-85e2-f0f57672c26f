// Simple console-based logger to avoid worker thread issues
interface LoggerParams {
  [key: string]: any;
}

// Create a very simple console-based logger that won't use worker threads
const logger = {
  info: (obj: LoggerParams, msg: string) => {
    try {
      console.log(`[INFO] ${msg}`, JSON.stringify(obj));
    } catch (error) {
      console.log(`[INFO] ${msg} (Error serializing object: ${error})`);
    }
  },
  
  warn: (obj: LoggerParams, msg: string) => {
    try {
      console.warn(`[WARN] ${msg}`, JSON.stringify(obj));
    } catch (error) {
      console.warn(`[WARN] ${msg} (Error serializing object: ${error})`);
    }
  },
  
  error: (obj: LoggerParams, msg: string) => {
    try {
      console.error(`[ERROR] ${msg}`, JSON.stringify(obj));
    } catch (error) {
      console.error(`[ERROR] ${msg} (Error serializing object: ${error})`);
    }
  },
  
  debug: (obj: LoggerParams, msg: string) => {
    if (process.env.NODE_ENV === 'development') {
      try {
        console.debug(`[DEBUG] ${msg}`, JSON.stringify(obj));
      } catch (error) {
        console.debug(`[DEBUG] ${msg} (Error serializing object: ${error})`);
      }
    }
  }
};

export default logger; 