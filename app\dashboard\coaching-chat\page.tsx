'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { CoachingChat } from '@/components/coaching/coaching-chat';
import CoachingChatFallback from '@/components/coaching/coaching-chat-fallback';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, MessageSquare, User } from 'lucide-react';
import { format, isToday, isYesterday } from 'date-fns';

interface Conversation {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
    avatarUrl: string | null;
  };
  lastMessage: {
    id: string;
    content: string;
    createdAt: string;
  } | null;
  relationship: {
    id: string;
    status: string;
    startDate: string;
  };
}

export default function CoachingChatPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const { toast } = useToast();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const conversationId = searchParams.get('id');

  useEffect(() => {
    const fetchConversations = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/coaching/conversations');

        if (!response.ok) {
          throw new Error('Failed to fetch conversations');
        }

        const data = await response.json();
        setConversations(data);

        // If conversationId is provided, select that conversation
        if (conversationId) {
          const conversation = data.find((c: Conversation) => c.id === conversationId);
          if (conversation) {
            setSelectedConversation(conversation);
          }
        } else if (data.length > 0) {
          // Otherwise, select the first conversation
          setSelectedConversation(data[0]);
        }
      } catch (error) {
        console.error('Error fetching conversations:', error);
        setHasError(true);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load conversations. Please try again.',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (session?.user) {
      fetchConversations();
    }
  }, [session, conversationId, toast]);

  const handleSelectConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    // Update URL with conversation ID
    router.push(`/dashboard/coaching-chat?id=${conversation.id}`);
  };

  if (!session?.user) {
    return (
      <div className="container py-6">
        <Card>
          <CardHeader>
            <CardTitle>Not Authenticated</CardTitle>
            <CardDescription>Please sign in to access coaching chat.</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // Show fallback component if there's an error or no conversations after loading
  if (hasError || (!isLoading && conversations.length === 0)) {
    return <CoachingChatFallback />;
  }

  return (
    <div className="container py-6 space-y-6 max-w-7xl">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          <Link href="/dashboard/premium">
            <Button variant="outline" className="hover:bg-primary/5 border-primary/20 shadow-sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </Link>
          <div className="flex items-center gap-2">
            <div className="relative">
              <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center shadow-md">
                <MessageSquare className="h-4 w-4 text-white" />
              </div>
              <span className="absolute -bottom-0.5 -right-0.5 h-2.5 w-2.5 rounded-full bg-green-500 border-2 border-white"></span>
            </div>
            <div>
              <h2 className="text-lg font-bold text-primary">Premium Coaching</h2>
            </div>
          </div>
        </div>

        {conversations.length > 0 && (
          <div className="flex items-center gap-2">
            <select
              className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              value={selectedConversation?.id || ''}
              onChange={(e) => {
                const conversation = conversations.find(c => c.id === e.target.value);
                if (conversation) handleSelectConversation(conversation);
              }}
            >
              <option value="" disabled>Select a conversation</option>
              {conversations.map((conversation) => (
                <option key={conversation.id} value={conversation.id}>
                  {conversation.user.name}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="w-full">
        {isLoading ? (
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div>
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-24 mt-1" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="h-[600px] flex items-center justify-center">
              <Skeleton className="h-64 w-full rounded-lg" />
            </CardContent>
          </Card>
        ) : conversations.length === 0 ? (
          <Card className="flex flex-col items-center justify-center h-[600px] shadow-lg bg-gradient-to-b from-background to-background/80 overflow-hidden border-primary/20 rounded-xl">
            <div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center mx-auto mb-6 border border-primary/20">
              <MessageSquare className="h-8 w-8 text-primary/60" />
            </div>
            <CardTitle className="text-2xl mb-3 text-primary font-bold">No Active Conversations</CardTitle>
            <CardDescription className="text-center max-w-md mb-6">
              Connect with a premium coach to start your 1:1 coaching experience
            </CardDescription>
            <Button
              className="mt-2 bg-primary hover:bg-primary/90 text-white shadow-md"
              onClick={() => router.push('/dashboard/premium')}
            >
              <User className="mr-2 h-4 w-4" />
              Find a Coach
            </Button>
          </Card>
        ) : selectedConversation ? (
          <CoachingChat
            conversationId={selectedConversation.id}
            otherUser={selectedConversation.user}
            relationshipId={selectedConversation.relationship.id}
          />
        ) : (
          <Card className="flex flex-col items-center justify-center h-[600px] shadow-lg bg-gradient-to-b from-background to-background/80 overflow-hidden border-primary/20 rounded-xl">
            <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mb-6">
              <MessageSquare className="h-8 w-8 text-primary/70" />
            </div>
            <CardTitle className="text-2xl mb-3 text-primary font-bold">Premium Coaching</CardTitle>
            <CardDescription className="text-center max-w-md mb-6">
              Select a conversation from the dropdown above to start your premium coaching session
            </CardDescription>
            <div className="flex items-center justify-center mt-2">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-amber-500 to-amber-300 text-amber-900">
                Premium Feature
              </span>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}
