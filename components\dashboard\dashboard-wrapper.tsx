"use client"

import { Menu } from "lucide-react"
import { MainNav } from "@/components/dashboard/main-nav"
import { RoleSwitcher } from "@/components/role-switcher"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

interface DashboardWrapperProps {
  children: React.ReactNode
  userRole: string | null
}

export function DashboardWrapper({ children, userRole }: DashboardWrapperProps) {
  return (
    <div className="flex min-h-[calc(100vh-4rem)]">
      {/* Mobile Menu Button */}
      <Sheet>
        <SheetTrigger asChild>
          <Button 
            variant="outline" 
            size="icon" 
            className="md:hidden fixed left-3 top-16 z-40 h-8 w-8 shadow-sm"
            aria-label="Open menu"
          >
            <Menu className="h-4 w-4" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 p-0">
          <div className="flex h-full flex-col">
            <div className="flex-1 p-3">
              <MainNav userRole={userRole} />
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Desktop Sidebar */}
      <div className="hidden md:flex w-56 shrink-0 flex-col border-r bg-background">
        <div className="flex-1 p-3 pt-4">
          <MainNav userRole={userRole} />
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-auto">
        <div className="py-4">
          {children}
        </div>
      </div>

      {/* Role Switcher - always visible */}
      <RoleSwitcher />
    </div>
  )
} 