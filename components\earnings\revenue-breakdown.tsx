"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface RevenueBreakdownProps {
  subscriptionRevenue: number
  productRevenue: number
}

export function RevenueBreakdown({ subscriptionRevenue, productRevenue }: RevenueBreakdownProps) {
  const [period, setPeriod] = useState("month")

  // In a real app, this would be a chart using a library like Recharts or Chart.js
  const totalRevenue = subscriptionRevenue + productRevenue
  const subscriptionPercentage = totalRevenue > 0 ? Math.round((subscriptionRevenue / totalRevenue) * 100) : 0
  const productPercentage = totalRevenue > 0 ? Math.round((productRevenue / totalRevenue) * 100) : 0

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Revenue Breakdown</CardTitle>
            <CardDescription>Your revenue sources</CardDescription>
          </div>
          <Tabs defaultValue="month" onValueChange={setPeriod}>
            <TabsList className="grid w-[200px] grid-cols-3">
              <TabsTrigger value="week">Week</TabsTrigger>
              <TabsTrigger value="month">Month</TabsTrigger>
              <TabsTrigger value="year">Year</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[200px] w-full bg-muted/20 rounded-md flex items-center justify-center text-muted-foreground">
          {totalRevenue > 0 ? (
            <div className="text-center">
              <div className="mb-4">Revenue chart for {period}</div>
              <div className="flex justify-center gap-4">
                <div className="text-center">
                  <div className="font-medium">Subscriptions</div>
                  <div className="text-2xl font-bold">{subscriptionPercentage}%</div>
                </div>
                <div className="text-center">
                  <div className="font-medium">Products</div>
                  <div className="text-2xl font-bold">{productPercentage}%</div>
                </div>
              </div>
            </div>
          ) : (
            "No revenue data available"
          )}
        </div>
      </CardContent>
    </Card>
  )
}

