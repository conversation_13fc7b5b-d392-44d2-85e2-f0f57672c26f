'use client';

import { useState, useEffect } from 'react';
import { Edit, Trash2, Play, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface Exercise {
  id: string;
  name: string;
  sets: number;
  reps: number;
  notes?: string;
  weight?: number | string | null; // Handle different weight formats
  category?: string;
  difficulty?: string;
  targetMuscles?: string[];
  video?: string;
  videoUrl?: string; // Added for compatibility with database
  description?: string;
  isAlternative?: boolean;
  parentExerciseId?: string | null;
}

interface PremiumExerciseCardProps {
  exercise: Exercise;
  onEdit: (exercise: Exercise) => void;
  onDelete: () => void;
  index: number;
  expanded: boolean;
  onToggleExpand: () => void;
}

export function PremiumExerciseCard({ exercise, onEdit, onDelete, index, expanded, onToggleExpand }: PremiumExerciseCardProps) {
  const [videoLoaded, setVideoLoaded] = useState(false);

  // Reset videoLoaded state when expanded changes
  useEffect(() => {
    if (!expanded) {
      setVideoLoaded(false);
    }
  }, [expanded]);

  // Function to get YouTube video ID from URL
  const getYouTubeVideoId = (url?: string): string | null => {
    if (!url) return null;
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[2].length === 11 ? match[2] : null;
  };

  // Helper function to normalize weight value
  const normalizeWeight = (weight: number | string | null | undefined): number | null => {
    if (weight === null || weight === undefined) return null;
    if (typeof weight === 'string') {
      const parsed = parseFloat(weight);
      return isNaN(parsed) ? null : parsed;
    }
    return weight;
  };

  // Get normalized weight value
  const weightValue = normalizeWeight(exercise.weight);

  // Log the exercise data for debugging
  const videoUrl = exercise.video || exercise.videoUrl;
  console.log('Exercise data:', {
    id: exercise.id,
    name: exercise.name,
    rawWeight: exercise.weight,
    weightType: typeof exercise.weight,
    normalizedWeight: weightValue,
    video: videoUrl
  });

  const videoId = getYouTubeVideoId(videoUrl);
  console.log('Extracted video ID:', videoId);

  const embedUrl = videoId ? `https://www.youtube-nocookie.com/embed/${videoId}` : null;
  console.log('Embed URL:', embedUrl);

  // Determine difficulty color
  const getDifficultyColor = (difficulty?: string) => {
    if (!difficulty) return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';

    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400';
      case 'intermediate':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'advanced':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';
      case 'expert':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  return (
    <div className={cn(
      "relative overflow-hidden rounded-lg border transition-all duration-300",
      "bg-gradient-to-br from-background to-muted/20",
      "hover:shadow-md hover:border-primary/20",
      expanded ? "shadow-md border-primary/20" : "shadow-sm"
    )}>
      {/* Exercise number indicator */}
      <div className="absolute top-0 left-0 w-8 h-8 flex items-center justify-center">
        <div className="absolute inset-0 bg-primary opacity-5 rounded-tl-lg rounded-br-xl"></div>
        <span className="relative text-sm font-medium text-primary">{index + 1}</span>
      </div>

      {/* Exercise header */}
      <div className="p-4 pl-10 flex items-center justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h3 className="font-medium text-base truncate">{exercise.name}</h3>
            {exercise.isAlternative && (
              <Badge variant="outline" className="text-xs py-0 px-1.5 h-4 bg-primary/5 text-primary border-primary/20">
                Alternative
              </Badge>
            )}
          </div>
          <div className="flex flex-wrap items-center gap-x-2 gap-y-1 mt-1.5">
            <span className="inline-flex items-center text-xs text-muted-foreground bg-muted/30 px-2 py-0.5 rounded-full border border-muted/10">
              <span className="font-medium text-foreground/70 mr-1">{exercise.sets}</span> sets ×
              <span className="font-medium text-foreground/70 mx-1">{exercise.reps}</span> reps
            </span>
            {(weightValue !== null) && (
              <span className="inline-flex items-center text-xs text-muted-foreground bg-muted/30 px-2 py-0.5 rounded-full border border-muted/10">
                <span className="font-medium text-foreground/70 mr-1">
                  {weightValue}
                </span>kg
              </span>
            )}
            {exercise.difficulty && (
              <Badge variant="outline" className={cn("text-xs py-0.5 h-5 font-medium", getDifficultyColor(exercise.difficulty))}>
                {exercise.difficulty}
              </Badge>
            )}
            {exercise.category && (
              <span className="inline-flex items-center text-xs text-muted-foreground bg-muted/30 px-2 py-0.5 rounded-full border border-muted/10">
                {exercise.category}
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-8 rounded-full transition-all",
              expanded ? "bg-primary/10 text-primary hover:bg-primary/20" : "hover:bg-muted/50"
            )}
            onClick={onToggleExpand}
          >
            {expanded ? "Hide details" : "Show details"}
            {expanded ? <ChevronUp className="h-3.5 w-3.5 ml-1" /> : <ChevronDown className="h-3.5 w-3.5 ml-1" />}
          </Button>
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => onEdit(exercise)}>
            <Edit className="h-3.5 w-3.5" />
          </Button>
          <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10" onClick={onDelete}>
            <Trash2 className="h-3.5 w-3.5" />
          </Button>
        </div>
      </div>

      {/* Expanded content */}
      {expanded && (
        <div className="px-6 pb-6 pt-1 space-y-4">
          <div className="h-px w-full bg-border/50"></div>

          {/* Description and Notes */}
          {(exercise.description || exercise.notes) && (
            <div className="space-y-3">
              {exercise.description && (
                <div className="space-y-1">
                  <h4 className="text-xs font-medium uppercase text-muted-foreground tracking-wider">Description</h4>
                  <p className="text-sm">{exercise.description}</p>
                </div>
              )}
              {exercise.notes && (
                <div className="space-y-1">
                  <h4 className="text-xs font-medium uppercase text-muted-foreground tracking-wider">Notes</h4>
                  <p className="text-sm text-muted-foreground">{exercise.notes}</p>
                </div>
              )}
            </div>
          )}

          {/* Target muscles */}
          {exercise.targetMuscles && exercise.targetMuscles.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-xs font-medium uppercase text-muted-foreground tracking-wider">Target Muscles</h4>
              <div className="flex flex-wrap gap-1.5">
                {exercise.targetMuscles.map((muscle, i) => (
                  <Badge key={i} variant="secondary" className="text-xs bg-secondary/50 hover:bg-secondary/70">
                    {muscle}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Video preview */}
          {embedUrl && (
            <div className="space-y-2">
              <h4 className="text-xs font-medium uppercase text-muted-foreground tracking-wider">Video Demonstration</h4>
              <div className="relative aspect-video rounded-md overflow-hidden bg-black/5 border shadow-sm">
                {!videoLoaded && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="animate-pulse rounded-md bg-muted/50 w-full h-full"></div>
                    <div className="absolute flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 text-primary">
                      <Play className="h-8 w-8 text-primary" />
                    </div>
                  </div>
                )}
                <iframe
                  src={embedUrl}
                  className={cn("w-full h-full", !videoLoaded && "opacity-0")}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  onLoad={() => setVideoLoaded(true)}
                ></iframe>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
