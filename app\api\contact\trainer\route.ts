import { NextResponse } from "next/server"
import { Resend } from "resend"
import { prisma } from "@/lib/prisma"
import logger from "@/lib/logger"

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY)

const FROM_EMAIL = process.env.EMAIL_FROM || "<EMAIL>" // Replace with your verified Resend domain/email

export async function POST(request: Request) {
  try {
    const { name, email, message } = await request.json()

    if (!name || !email || !message) {
      return NextResponse.json(
        { message: "Name, email, and message are required." },
        { status: 400 },
      )
    }

    // Basic email validation (can be enhanced)
    if (!/\S+@\S+\.\S+/.test(email)) {
        logger.warn({ receivedEmail: email }, "Invalid email format in trainer inquiry.")
        return NextResponse.json(
          { message: "Invalid email format." },
          { status: 400 },
        )
    }

    const inquiry = await prisma.trainerInquiry.create({
      data: {
        name,
        email,
        message,
        status: "new", // Default status
      },
    })
    logger.info({ inquiryId: inquiry.id, email }, "New trainer inquiry created.")

    // 2. Send email notification
    try {
      await resend.emails.send({
        from: FROM_EMAIL,
        to: FROM_EMAIL, // Send to the FROM_EMAIL for external forwarding
        subject: `New Trainer Inquiry: ${name}`,
        html: `
          <h1>New Trainer Inquiry</h1>
          <p>A potential trainer has submitted an inquiry through the landing page.</p>
          <ul>
            <li><strong>Name:</strong> ${name}</li>
            <li><strong>Email:</strong> ${email}</li>
          </ul>
          <h2>Message:</h2>
          <p>${message.replace(/\n/g, "<br>")}</p>
          <hr>
          <p>Inquiry ID: ${inquiry.id}</p>
          <p>You can view this inquiry in the admin dashboard (link TBD).</p>
        `,
      });
      logger.info({ inquiryId: inquiry.id }, `Admin notification email sent for trainer inquiry.`)
    } catch (emailError) {
      logger.error({ inquiryId: inquiry.id, error: emailError }, "Failed to send admin notification email for trainer inquiry")
      // Don't fail the whole request if email fails, but log it.
      // Consider adding more robust error handling/retry logic here.
    }

    return NextResponse.json(
      { 
        message: "Inquiry submitted successfully.", 
        inquiryId: inquiry.id 
      }, 
      { status: 201 }
    )

  } catch (error) {
    logger.error({ error }, "Trainer Inquiry submission failed")
    return NextResponse.json(
      { message: "An internal server error occurred." },
      { status: 500 },
    )
  }
}