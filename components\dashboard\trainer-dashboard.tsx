import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Utensils, Dumbbell } from "lucide-react"
import { <PERSON>ge } from "@/components/ui/badge"
import { UpcomingSessionsWidget } from "@/components/dashboard/upcoming-sessions-widget"

interface TrainerDashboardProps {
  user: any;
  stats: any;
  inquiries: any[];
}

export function TrainerDashboard({ user, stats, inquiries }: TrainerDashboardProps) {
  return (
    <div className="space-y-6">
      {/* Upcoming Sessions Widget - Full Width */}
      <div className="grid gap-4 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <UpcomingSessionsWidget />
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks and shortcuts</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button asChild className="w-full" variant="outline">
              <Link href="/dashboard/coaching">Premium Coaching</Link>
            </Button>
            <Button asChild className="w-full" variant="outline">
              <Link href="/dashboard/training-plans/create">Create Training Plan</Link>
            </Button>
            <Button asChild className="w-full" variant="outline">
              <Link href="/dashboard/profile">Profile Settings</Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card>
        <CardHeader>
          <CardTitle>Clients</CardTitle>
          <CardDescription>Manage your client relationships</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[100px] w-full bg-muted/20 rounded-md flex items-center justify-center text-muted-foreground">
            Client list preview
          </div>
        </CardContent>
        <CardFooter>
          <Button asChild className="w-full">
            <Link href="/dashboard/clients">Manage Clients</Link>
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Messages</CardTitle>
          <CardDescription>Chat with your clients</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[100px] w-full bg-muted/20 rounded-md flex items-center justify-center text-muted-foreground">
            Messages preview
          </div>
        </CardContent>
        <CardFooter>
          <Button asChild className="w-full">
            <Link href="/dashboard/coaching-chat">Open Coaching Chat</Link>
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Nutrition Plans</CardTitle>
          <CardDescription>Create and manage nutrition plans</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[100px] w-full bg-muted/20 rounded-md flex items-center justify-center text-muted-foreground">
            <Utensils className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
        <CardFooter>
          <Button asChild className="w-full">
            <Link href="/dashboard/nutrition-plans">Manage Plans</Link>
          </Button>
        </CardFooter>
      </Card>

      {/* Training Plans Section */}
      <Card className="col-span-1 lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Training Plans</span>
            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
              <Link href="/dashboard/training-plans">
                Manage Plans
              </Link>
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Replace with actual plan data */}
          {stats.trainingPlans > 0 ? (
            <div className="text-sm">You have {stats.trainingPlans} active plans.</div>
          ) : (
            <div className="text-center py-6">
              <Dumbbell className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">You haven&apos;t created any training plans yet.</p>
              <Button variant="secondary" size="sm" className="mt-3" asChild>
                <Link href="/dashboard/training-plans/create">
                  Create Your First Plan
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Latest Coaching Inquiries */}
      <Card className="col-span-1 lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Latest Coaching Inquiries</span>
            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
              <Link href="/dashboard/coaching/inquiries">
                View All
              </Link>
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-3">
            {/* Map through inquiries if they exist */}
            {inquiries && inquiries.map((inquiry: any) => (
               <li key={inquiry.id} className="flex items-center justify-between">
                 <div>
                   <p className="text-sm font-medium">{inquiry.name}</p>
                   <p className="text-xs text-muted-foreground">{inquiry.interest}</p>
                 </div>
                 <Badge variant="secondary">{inquiry.status}</Badge>
               </li>
            ))}
            {/* Show placeholder if no inquiries */}
            {(!inquiries || inquiries.length === 0) && (
              <li className="text-center py-6">
                <p className="text-sm text-muted-foreground">No coaching inquiries yet.</p>
              </li>
            )}
          </ul>
        </CardContent>
      </Card>

      {/* Digital Products */}
      <Card className="col-span-1 lg:col-span-1">
        <CardHeader>
          <CardTitle>Digital Products</CardTitle>
          <CardDescription>Manage your digital products</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center">
            <p className="text-sm mb-2">
              {stats.products > 0 
                ? `You have ${stats.products} products in your store.` 
                : "You haven't created any products yet."}
            </p>
            <Button variant="secondary" size="sm" asChild>
              <Link href="/dashboard/products">
                Manage Products
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}
