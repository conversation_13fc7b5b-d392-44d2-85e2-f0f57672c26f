"use client"

import { TrainerLandingPage } from "@/components/trainer/trainer-landing-page"

export default function DemoTrainerPage() {
  // Mock trainer data
  const trainer = {
    id: "trainer-sarah-demo",
    name: "<PERSON>",
    email: "<EMAIL>",
    bio: "Certified personal trainer with 10+ years of experience specializing in strength training and weight loss. I help clients build sustainable fitness habits for long-term success.",
    avatarUrl: null,
    socialLinks: {
      instagram: "https://instagram.com/sarah<PERSON>hnson",
      twitter: "https://twitter.com/sarahjohnson",
      youtube: "https://youtube.com/@sarah<PERSON><PERSON>son"
    },
    themeSettings: {
      primaryColor: '#3b82f6',
      secondaryColor: '#6366f1'
    }
  }
  
  // Theme settings
  const theme = {
    primaryColor: "#3b82f6",
    secondaryColor: "#6366f1",
    logoUrl: null,
    bannerUrl: "https://images.unsplash.com/photo-1571902943202-507ec2618539?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
    fontFamily: "Inter, sans-serif"
  }
  
  // Subscription tiers
  const subscriptionTiers = [
    {
      id: `tier-basic-demo`,
      name: "Basic Plan",
      description: "Essential training and support",
      price: 29.99,
      features: [
        "Weekly workout plans",
        "Email support",
        "Training videos"
      ]
    },
    {
      id: `tier-pro-demo`,
      name: "Pro Plan",
      description: "Advanced training with personalized support",
      price: 59.99,
      features: [
        "Personalized workout plans",
        "Priority email support",
        "Weekly check-ins",
        "Nutrition guidance"
      ]
    },
    {
      id: `tier-elite-demo`,
      name: "Elite Plan",
      description: "Premium coaching with personal attention",
      price: 99.99,
      features: [
        "Fully customized training program",
        "1-on-1 video consultations",
        "24/7 message support",
        "Detailed nutrition planning",
        "Progress tracking",
        "Monthly performance reviews"
      ]
    }
  ]
  
  // Digital products
  const digitalProducts = [
    {
      id: `product-1-demo`,
      title: "30-Day Strength Builder",
      description: "A comprehensive 30-day program to build overall strength",
      price: 49.99,
      thumbnailUrl: "https://images.unsplash.com/photo-1517836357463-d25dfeac3438?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    },
    {
      id: `product-2-demo`,
      title: "Nutrition Guide",
      description: "Complete nutrition guide with meal plans and recipes",
      price: 29.99,
      thumbnailUrl: "https://images.unsplash.com/photo-1498837167922-ddd27525d352?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    },
    {
      id: `product-3-demo`,
      title: "Home Workout Bundle",
      description: "Complete home workout program with minimal equipment",
      price: 39.99,
      thumbnailUrl: "https://images.unsplash.com/photo-1599058917765-a780eda07a3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    }
  ]
  
  // Coaching services
  const coachingServices = [
    {
      id: `coaching-1-demo`,
      name: "Initial Consultation",
      description: "One-time consultation to discuss goals and create a plan",
      price: 89.99,
      duration: "session",
      features: [
        "60-minute video call",
        "Goal assessment",
        "Custom plan outline",
        "Nutrition recommendations"
      ],
      availableSlots: [
        {
          id: "slot-1",
          date: "Mon, Aug 28, 2023",
          startTime: "10:00 AM",
          endTime: "11:00 AM",
          isBooked: false
        },
        {
          id: "slot-2",
          date: "Wed, Aug 30, 2023",
          startTime: "2:00 PM",
          endTime: "3:00 PM",
          isBooked: false
        },
        {
          id: "slot-3",
          date: "Fri, Sep 1, 2023",
          startTime: "11:30 AM",
          endTime: "12:30 PM",
          isBooked: true
        }
      ]
    },
    {
      id: `coaching-2-demo`,
      name: "Monthly Coaching",
      description: "Ongoing personal coaching and accountability",
      price: 199.99,
      duration: "month",
      features: [
        "Weekly video check-ins",
        "Personalized workout plans",
        "Diet adjustments",
        "24/7 messaging support",
        "Progress tracking"
      ],
      // No available slots for this service to demonstrate the UI for requesting availability
      availableSlots: []
    }
  ]
  
  return (
    <TrainerLandingPage
      trainer={trainer}
      theme={theme}
      subscriptionTiers={subscriptionTiers}
      digitalProducts={digitalProducts}
      coachingServices={coachingServices}
    />
  )
} 