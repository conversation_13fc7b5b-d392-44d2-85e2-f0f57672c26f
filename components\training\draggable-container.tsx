"use client"

import { 
  Dnd<PERSON>ontext, 
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from "@dnd-kit/core"
import { restrictToVerticalAxis, restrictToWindowEdges } from "@dnd-kit/modifiers"
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { useState } from "react"
import { ExerciseCard } from "@/components/training/exercise-card"

interface Exercise {
  id: string
  name: string
  sets?: number | null
  reps?: number | null
  description?: string | null
  duration?: number | null
  restTime?: number | null
  videoUrl?: string | null
  muscleGroup?: string | null
  order: number
  type?: string | null
  thumbnailUrl?: string | null
  isTemplate?: boolean
  difficulty?: string | null
  equipment?: string | null
  calories?: number | null
}

interface DraggableContainerProps {
  exercises: Exercise[]
  onReorder: (exercises: Exercise[]) => void
  onEdit?: (exercise: Exercise) => void
  onDelete?: (id: string) => void
  onDuplicate?: (exercise: Exercise) => void
}

export function DraggableContainer({
  exercises,
  onReorder,
  onEdit,
  onDelete,
  onDuplicate,
}: DraggableContainerProps) {
  const [activeId, setActiveId] = useState<string | null>(null)

  // Find the active exercise
  const activeExercise = activeId 
    ? exercises.find(exercise => exercise.id === activeId) 
    : null

  // Set up sensors for keyboard and pointer interaction
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string)
  }

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (over && active.id !== over.id) {
      const oldIndex = exercises.findIndex(item => item.id === active.id)
      const newIndex = exercises.findIndex(item => item.id === over.id)
      
      const newExercises = arrayMove(exercises, oldIndex, newIndex).map((exercise, index) => ({
        ...exercise,
        order: index
      }))
      
      onReorder(newExercises)
    }

    setActiveId(null)
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToVerticalAxis, restrictToWindowEdges]}
    >
      <SortableContext
        items={exercises.map(exercise => exercise.id)}
        strategy={verticalListSortingStrategy}
      >
        <div className="space-y-1">
          {exercises.map(exercise => (
            <ExerciseCard
              key={exercise.id}
              exercise={exercise}
              onEdit={onEdit}
              onDelete={onDelete}
              onDuplicate={onDuplicate}
            />
          ))}
        </div>
      </SortableContext>

      <DragOverlay>
        {activeExercise && (
          <ExerciseCard
            exercise={activeExercise}
            onEdit={onEdit}
            onDelete={onDelete}
            onDuplicate={onDuplicate}
          />
        )}
      </DragOverlay>
    </DndContext>
  )
} 