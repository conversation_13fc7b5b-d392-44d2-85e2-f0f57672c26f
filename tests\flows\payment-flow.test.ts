import { test, expect, vi } from 'vitest';
import { appRouter } from '@/server/api/trpc';
import { createInnerTRPCContext } from '@/server/api/trpc';
import { PrismaClient } from '@prisma/client';
import <PERSON>e from 'stripe';

vi.mock('stripe', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      checkout: {
        sessions: {
          create: vi.fn().mockResolvedValue({
            id: 'test_session_id',
            url: 'https://test-checkout-url.com',
          }),
        },
      },
      paymentIntents: {
        create: vi.fn().mockResolvedValue({
          id: 'test_payment_intent_id',
          client_secret: 'test_client_secret',
        }),
      },
    })),
  };
});

const prisma = new PrismaClient();

const testUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Payment Test User',
  role: 'client',
};

const testProduct = {
  id: 'test-product-id',
  title: 'Test Product',
  price: 29.99,
  type: 'digital',
};

test('Payment processing flow - add to cart, checkout', async () => {
  const caller = appRouter.createCaller(
    createInnerTRPCContext({ 
      session: { 
        user: testUser, 
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() 
      } 
    })
  );
  
  const cartItem = await caller.cart.addItem({
    productId: testProduct.id,
    quantity: 1,
  });
  
  expect(cartItem).toBeDefined();
  expect(cartItem.productId).toBe(testProduct.id);
  
  const cart = await caller.cart.getCart();
  
  expect(cart).toBeDefined();
  expect(cart.items.length).toBe(1);
  expect(cart.items[0].productId).toBe(testProduct.id);
  
  const checkoutSession = await caller.checkout.createSession({
    items: cart.items,
    returnUrl: 'http://localhost:3000/checkout/success',
  });
  
  expect(checkoutSession).toBeDefined();
  expect(checkoutSession.url).toBe('https://test-checkout-url.com');
  
  const order = await prisma.order.create({
    data: {
      userId: testUser.id,
      status: 'completed',
      total: testProduct.price,
      stripePaymentIntentId: 'test_payment_intent_id',
      orderItems: {
        create: {
          productId: testProduct.id,
          quantity: 1,
          price: testProduct.price,
          title: testProduct.title,
        },
      },
    },
  });
  
  expect(order).toBeDefined();
  expect(order.status).toBe('completed');
  
  const userOrders = await caller.orders.getUserOrders();
  
  expect(userOrders).toBeDefined();
  expect(userOrders.length).toBeGreaterThan(0);
  
  await prisma.orderItem.deleteMany({
    where: { orderId: order.id },
  });
  await prisma.order.delete({
    where: { id: order.id },
  });
  await prisma.cartItem.deleteMany({
    where: { userId: testUser.id },
  });
});
