"use client"

import {
  <PERSON><PERSON>pR<PERSON>,
  ArrowDownRight,
  TrendingUp,
  Activity,
  Timer
} from "lucide-react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"

import {
  LineChart, Line, BarChart, Bar,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { PremiumLockOverlay } from "@/components/analytics/premium-lock-overlay"

// Default sample data for charts (will be replaced with real data from API)
const defaultWeightData = [
  { date: "Jan 1", weight: 180 },
  { date: "Jan 15", weight: 178 },
  { date: "Feb 1", weight: 176 },
  { date: "Feb 15", weight: 175 },
  { date: "Mar 1", weight: 173 },
  { date: "Mar 15", weight: 172 },
];

const defaultTrainingData = [
  { date: "Jan 1", volume: 12000 },
  { date: "Jan 8", volume: 14000 },
  { date: "Jan 15", volume: 13000 },
  { date: "Jan 22", volume: 15000 },
  { date: "Jan 29", volume: 16000 },
  { date: "Feb 5", volume: 14000 },
  { date: "Feb 12", volume: 17000 },
];

const defaultRunningData = [
  { date: "Jan 1", distance: 5.2, pace: "5:30", duration: 28.6, elevation: 120 },
  { date: "Jan 5", distance: 3.1, pace: "5:45", duration: 17.8, elevation: 45 },
  { date: "Jan 10", distance: 6.5, pace: "5:25", duration: 35.2, elevation: 210 },
  { date: "Jan 15", distance: 4.0, pace: "5:20", duration: 21.3, elevation: 85 },
  { date: "Jan 20", distance: 8.0, pace: "5:35", duration: 44.7, elevation: 320 },
  { date: "Jan 25", distance: 5.5, pace: "5:15", duration: 28.9, elevation: 150 },
  { date: "Feb 1", distance: 10.0, pace: "5:40", duration: 56.7, elevation: 410 },
  { date: "Feb 5", distance: 4.5, pace: "5:10", duration: 23.3, elevation: 95 },
];

// Type definitions for chart data
// Using any[] for simplicity in this implementation

export default function AnalyticsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState("month")
  // Business analytics data (for trainers/admins)
  // Using underscore prefix to indicate it's used but not directly referenced
  const [_businessAnalytics, setBusinessAnalytics] = useState({
    activeClients: 0,
    revenue: 0,
    productSales: 0,
    coachingServices: 0,
    trainingPlans: 0
  })

  // State for user analytics data
  const [weightData, setWeightData] = useState(defaultWeightData)
  const [trainingData, setTrainingData] = useState(defaultTrainingData)
  const [runningData] = useState(defaultRunningData) // Using state but not setting it directly
  const [userMetrics, setUserMetrics] = useState({
    totalWorkouts: 0,
    totalVolume: 0,
    avgDuration: 0
  })

  // State for user subscription tier
  const [subscriptionTier, setSubscriptionTier] = useState<string>("basic")
  // State for tracking data access limits
  const [dataAccessLimit, setDataAccessLimit] = useState<number>(30) // Default to 30 days for mid-tier
  // State for showing upgrade message
  const [showUpgradeMessage, setShowUpgradeMessage] = useState<boolean>(false)

  useEffect(() => {
    if (status === "loading") return

    // Check if user is authenticated
    if (!session) {
      router.push("/login")
      return
    }

    // Determine user's subscription tier
    const userRole = session?.user?.role || null
    const userTier = (session?.user as any)?.subscriptionTier || "basic"
    const isAdminOrTrainer = userRole === 'admin' || userRole === 'trainer'

    // Set subscription tier based on user role and subscription
    if (isAdminOrTrainer) {
      setSubscriptionTier("premium")
      setDataAccessLimit(-1) // No limit for admins/trainers
      setShowUpgradeMessage(false)
    } else if (userTier === "premium" || userTier === "coaching" || (userRole as string) === "premiumClient") {
      setSubscriptionTier("premium")
      setDataAccessLimit(-1) // No limit for premium users
      setShowUpgradeMessage(false)
    } else if (userTier === "mid") {
      setSubscriptionTier("mid")
      setDataAccessLimit(30) // 30 days for mid-tier
      setShowUpgradeMessage(true)
    } else {
      setSubscriptionTier("basic")
      setDataAccessLimit(7) // 7 days for basic tier
      setShowUpgradeMessage(true)
    }

    // Fetch analytics data
    const fetchAnalytics = async () => {
      try {
        setIsLoading(true)

        // For trainers/admins, fetch business analytics
        if (isAdminOrTrainer) {
          const response = await fetch(`/api/analytics?timeRange=${timeRange}`)
          if (response.ok) {
            const data = await response.json()
            setBusinessAnalytics(data)
          }
        } else {
          // For regular users, fetch training metrics
          const response = await fetch(`/api/user-analytics?timeRange=${timeRange}`)
          if (response.ok) {
            const data = await response.json()

            // Update state with real data if available
            if (data.weightData && data.weightData.length > 0) {
              setWeightData(data.weightData)
            }

            if (data.trainingData && data.trainingData.length > 0) {
              setTrainingData(data.trainingData)
            }

            if (data.metrics) {
              setUserMetrics(data.metrics)
            }
          }
        }
      } catch (error) {
        console.error('Error fetching analytics:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchAnalytics()
  }, [status, session, router, timeRange])

  // Calculate some metrics
  const currentWeight = weightData.length > 0 ? weightData[weightData.length - 1].weight : 0
  const startWeight = weightData.length > 0 ? weightData[0].weight : 0

  // Filter data based on user's subscription tier and access limit
  const filterDataByAccessLimit = (data: any[], dateKey: string = 'date') => {
    if (dataAccessLimit < 0) return data; // No limit for premium users

    // For basic/mid-tier users, filter data to only show the allowed time range
    const today = new Date();
    const limitDate = new Date();
    limitDate.setDate(today.getDate() - dataAccessLimit);

    return data.filter(item => {
      const itemDate = new Date(item[dateKey]);
      return itemDate >= limitDate;
    });
  }

  // Apply filters to our datasets
  const filteredWeightData = filterDataByAccessLimit(weightData);
  const filteredTrainingData = filterDataByAccessLimit(trainingData);
  const filteredRunningData = filterDataByAccessLimit(runningData);
  const weightChange = currentWeight - startWeight

  // Formatter for tooltips - will be used in charts if needed
  // const percentFormatter: FormatterType = (value) => [`${value}%`, ""];

  if (isLoading) {
    return (
      <div className="flex h-[50vh] items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Upgrade message for mid-tier and basic users */}
      {showUpgradeMessage && (
        <div className="relative overflow-hidden rounded-lg border bg-gradient-to-r from-primary/5 via-background to-primary/5 p-6 mb-6">
          <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
          <div className="relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h3 className="text-xl font-semibold mb-2">Unlock Full Analytics History</h3>
              <p className="text-muted-foreground max-w-xl">
                {subscriptionTier === "mid" ?
                  "You currently have access to the last 30 days of data. Upgrade to 1:1 Coaching to unlock your full training history and personalized performance insights." :
                  "You currently have access to the last 7 days of data. Upgrade to unlock more historical data and advanced analytics features."}
              </p>
            </div>
            <Button className="bg-primary hover:bg-primary/90" onClick={() => router.push('/dashboard/upgrade')}>
              Upgrade Now
            </Button>
          </div>
        </div>
      )}

      <div className="flex flex-col gap-1 pb-6">
        <div className="flex items-center justify-between">
          <div className="grid gap-1">
            <h1 className="text-3xl font-bold tracking-tight">Training Analytics</h1>
            <p className="text-muted-foreground">Track your training progress and performance metrics</p>
          </div>
          <div className="flex items-center gap-2">
            <Select defaultValue={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">Last 7 days</SelectItem>
                <SelectItem value="month">Last 30 days</SelectItem>
                <SelectItem value="quarter">Last 90 days</SelectItem>
                <SelectItem value="year">Last 12 months</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">Export Data</Button>
          </div>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Current Weight
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentWeight || 0} lbs</div>
            <p className="text-xs text-muted-foreground flex items-center">
              {weightChange < 0 ? (
                <span className="text-green-500 flex items-center mr-1">
                  <ArrowDownRight className="h-3 w-3 mr-1" />
                  {Math.abs(weightChange)} lbs
                </span>
              ) : (
                <span className="text-red-500 flex items-center mr-1">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  {weightChange} lbs
                </span>
              )}
              since start
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Weekly Training Volume
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userMetrics.totalVolume.toLocaleString()} kg</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <span className="text-green-500 flex items-center mr-1">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                12%
              </span>
              from last week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Workouts Completed
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userMetrics.totalWorkouts}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <span className="text-green-500 flex items-center mr-1">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                1
              </span>
              from last week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Workout Duration
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userMetrics.avgDuration} min</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <span className="text-green-500 flex items-center mr-1">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                5 min
              </span>
              from last week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Weekly Running Distance
            </CardTitle>
            <Timer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">42.8 km</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <span className="text-green-500 flex items-center mr-1">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                3.5 km
              </span>
              from last week
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="weight" className="space-y-4">
        <TabsList>
          <TabsTrigger value="weight">Weight Tracking</TabsTrigger>
          <TabsTrigger value="training">Training Volume</TabsTrigger>
          <TabsTrigger value="running">Running</TabsTrigger>
        </TabsList>
        <TabsContent value="weight" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Weight Progress</CardTitle>
              <CardDescription>
                Track your weight changes over time
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] relative">
              {subscriptionTier !== "premium" && dataAccessLimit > 0 && (
                <PremiumLockOverlay
                  message={`You can view the last ${dataAccessLimit} days of data. Upgrade to see your full history.`}
                />
              )}
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={filteredWeightData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis domain={['dataMin - 5', 'dataMax + 5']} />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="weight"
                    stroke="#0088FE"
                    name="Weight (lbs)"
                    activeDot={{ r: 8 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="training" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Training Volume</CardTitle>
              <CardDescription>
                Track your weekly training volume
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] relative">
              {subscriptionTier !== "premium" && dataAccessLimit > 0 && (
                <PremiumLockOverlay
                  message={`You can view the last ${dataAccessLimit} days of data. Upgrade to see your full history.`}
                />
              )}
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={filteredTrainingData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="volume" fill="#00C49F" name="Volume (kg)" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="running" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Running Distance</CardTitle>
              <CardDescription>
                Track your running distance over time
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] relative">
              {subscriptionTier !== "premium" && dataAccessLimit > 0 && (
                <PremiumLockOverlay
                  message={`You can view the last ${dataAccessLimit} days of data. Upgrade to see your full history.`}
                />
              )}
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={filteredRunningData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="distance"
                    stroke="#00C49F"
                    name="Distance (km)"
                    activeDot={{ r: 8 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Elevation Gain</CardTitle>
                <CardDescription>
                  Track your elevation gain during runs
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[300px] relative">
                {subscriptionTier !== "premium" && dataAccessLimit > 0 && (
                  <PremiumLockOverlay
                    message={`You can view the last ${dataAccessLimit} days of data. Upgrade to see your full history.`}
                    showButton={false}
                  />
                )}
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={filteredRunningData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="elevation" fill="#8884d8" name="Elevation (m)" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Running Pace</CardTitle>
                <CardDescription>
                  Track your average pace per run
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[300px] relative">
                {subscriptionTier !== "premium" && dataAccessLimit > 0 && (
                  <PremiumLockOverlay
                    message={`You can view the last ${dataAccessLimit} days of data. Upgrade to see your full history.`}
                    showButton={false}
                  />
                )}
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={filteredRunningData.map(run => ({
                      ...run,
                      paceMinutes: parseFloat(run.pace.split(':')[0]) + parseFloat(run.pace.split(':')[1])/60
                    }))}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={['dataMin - 0.5', 'dataMax + 0.5']} />
                    <Tooltip formatter={(value: any) => [`${Math.floor(value)}:${Math.round((value % 1) * 60).toString().padStart(2, '0')}`, 'Pace (min/km)']} />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="paceMinutes"
                      stroke="#FF8042"
                      name="Pace (min/km)"
                      activeDot={{ r: 8 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
