# Clear Coach App - Premium Coaching Platform

A modern platform connecting fitness trainers with their clients through personalized 1:1 premium coaching programs.

## Overview

The Clear Coach App is a focused platform that enables fitness trainers to deliver premium 1:1 coaching services to their clients. This application specializes in personalized coaching relationships with direct trainer-client interaction.

## Core Features

### 1:1 Premium Coaching Programs
- Personalized training sessions with direct trainer interaction
- Scheduled video/audio calls via Calendly integration
- Real-time progress tracking and measurements
- Custom workout and nutrition plans
- Direct messaging and chat with trainers
- Session scheduling and management
- Client progress monitoring and analytics
- Exercise logging and workout tracking
- Nutrition logging and meal planning
- Body measurement tracking with photo uploads
- Achievement and streak tracking

## Technical Stack

### Frontend
- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS with custom components
- **UI Components**: Shadcn/ui
- **State Management**: React Hooks
- **Authentication**: NextAuth.js

### Backend
- **API**: Next.js API Routes
- **Database**: PostgreSQL with Prisma ORM
- **File Storage**: Cloudinary
- **Real-time Chat**: WebSocket
- **Payments**: Stripe Integration (for coaching payments)

### Key Technologies
- TypeScript for type safety
- Prisma for database management
- Stripe for payment processing
- WebSocket for real-time features
- Cloudinary for media storage

## Project Structure

```
Clear-Coach-app/
├── app/
│   ├── api/           # API routes
│   ├── dashboard/     # Dashboard pages
│   ├── auth/         # Authentication pages
│   └── globals.css    # Global styles
├── components/
│   ├── ui/           # Reusable UI components
│   ├── coaching/     # Coaching-specific components
│   ├── training-plan/ # Training plan components
│   └── chat/         # Chat components
├── lib/
│   ├── prisma.ts     # Prisma client
│   ├── auth.ts       # Auth configuration
│   └── stripe.ts     # Stripe integration
└── prisma/
    └── schema.prisma # Database schema
```

## User Roles

### 1. Admin
- Platform management
- User management
- Analytics and reporting

### 2. Trainer
- Client management
- Training plan creation
- Progress monitoring
- Client communication
- Coaching session management

### 3. Client
- Access to personalized training plans
- Progress tracking
- Trainer communication
- Workout and nutrition logging
- Measurement tracking

## Key Features Implementation

### Authentication
- Role-based access control
- Secure session management
- OAuth integration
- Email verification

### Dashboard
- Role-specific views
- Progress tracking
- Training plan management
- Communication tools

### Training Programs
- Personalized plan creation
- Progress tracking
- Exercise library
- Client assignment

### Coaching Features
- Real-time chat
- Progress monitoring
- Measurement tracking
- Achievement system

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL 14+
- npm or yarn
- Git

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/clear-coach-MVP.git
cd clear-coach-MVP
```

2. Install dependencies
```bash
npm install --legacy-peer-deps
# or
yarn install --legacy-peer-deps
```

## Testing

The application uses a comprehensive testing strategy to ensure code quality and prevent regressions.

### Unit Tests

Run unit tests with:

```bash
npm test
```

### Workflow Tests

Workflow tests verify that complete user flows work correctly. These tests cover key functionality like user management, client management, and training plans.

Run workflow tests with:

```bash
npm run test:workflows
```

### Pre-Merge Testing

Before merging code to the main branch, run the complete test suite that matches what will run in the GitHub pipeline:

```bash
npm run test:pre-merge
```

This command runs:
1. Linting to check code style and potential errors
2. Jest unit and integration tests
3. Vitest workflow tests

This ensures your code meets all quality standards and doesn't break any existing functionality.

If you want to run a more comprehensive test suite including end-to-end tests, use:

```bash
npm run test:pre-merge-e2e
```

### Individual E2E Tests

You can also run individual end-to-end tests:

```bash
# Run client journey test
npm run test:e2e:client

# Run premium client journey test
npm run test:e2e:premium

# Run trainer journey test
npm run test:e2e:trainer

# Run coaching journey test
npm run test:e2e:coaching
```

### GitHub Actions

A GitHub Action automatically runs the same tests on pull requests to the main branch. The workflow is defined in `.github/workflows/e2e-tests.yml`. Make sure all GitHub Action checks pass before merging.

### Environment Setup

3. Set up environment variables
```bash
cp .env.example .env.local
```

Edit the `.env.local` file with your own values:
```
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/clear-coach

# Authentication
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000

# Stripe (for coaching payments)
STRIPE_SECRET_KEY=sk_test_your_stripe_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key

# Cloudinary (for file uploads)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

4. Set up the database
```bash
npx prisma generate
npx prisma db push
```

5. Run the development server
```bash
npm run dev
# or
yarn dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

## Development

### Running Tests

#### Unit and Integration Tests
```bash
npm run test
# or
yarn test
```

#### End-to-End Tests
```bash
# Run all E2E tests
npm run test:e2e

# Run pre-merge tests (same as in GitHub pipeline)
npm run test:pre-merge-e2e
```

### Linting
```bash
npm run lint
# or
yarn lint
```

### Building for Production
```bash
npm run build
# or
yarn build
```

## Deployment

The application can be deployed to various platforms:

### Vercel (Recommended)
```bash
npm install -g vercel
vercel
```

### Docker
```bash
docker build -t clear-coach .
docker run -p 3000:3000 clear-coach
```

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, email <EMAIL> or join our Slack channel.