import { DietPlanList } from "@/components/nutrition/diet-plan-list"
import { NutritionTracker } from "@/components/nutrition/nutrition-tracker"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { redirect } from 'next/navigation'
import { prisma } from "@/lib/prisma"

export default async function NutritionPage() {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    redirect('/login');
  }

  const assignedPlans = await prisma.dietPlanAssignment.findMany({
    where: {
      clientId: session.user.id,
    },
    include: {
      dietPlan: {
        include: {
          trainer: true, // Who created the plan
        },
      },
      assignedBy: true, // Who assigned it to this client
    },
    orderBy: {
      startDate: 'desc',
    },
  });

  return (
    <div className="flex flex-col gap-8">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Nutrition Plans</h2>
        <p className="text-muted-foreground">View your personalized nutrition plans and track your diet.</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <DietPlanList plans={assignedPlans || []} />
        <NutritionTracker />
      </div>
    </div>
  )
}

