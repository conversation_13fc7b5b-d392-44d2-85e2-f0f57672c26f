// API route handler for client training plan
import { ClientTrainingPlanHandler } from "@/lib/api/client-handler";

const handler = new ClientTrainingPlanHandler();

export async function GET(req: Request, { params }: { params: { id: string } }) {
  return handler.handleGet(req, params);
}

export async function POST(req: Request, { params }: { params: { id: string } }) {
  return handler.handlePost(req, params);
}

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  return handler.handlePut(req, params);
}

export async function DELETE(req: Request, { params }: { params: { id: string } }) {
  return handler.handleDelete(req, params);
}