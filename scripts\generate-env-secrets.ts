#!/usr/bin/env ts-node

/**
 * Generate Secure Environment Secrets
 * 
 * This script generates cryptographically secure random strings
 * for sensitive environment variables like authentication secrets.
 * 
 * Usage:
 *   npm run generate:secrets
 * 
 * Or directly:
 *   ts-node scripts/generate-env-secrets.ts
 */

import fs from 'fs';
import path from 'path';
import { generateSecureString } from '../lib/security/audit';
import dotenv from 'dotenv';

// List of secrets to generate
interface SecretConfig {
  name: string;
  length: number;
  description: string;
}

const secretsToGenerate: SecretConfig[] = [
  { 
    name: 'NEXTAUTH_SECRET', 
    length: 64, 
    description: 'Secret used by NextAuth.js for signing cookies and tokens' 
  },
  { 
    name: 'CSRF_SECRET', 
    length: 64, 
    description: 'Secret used for CSRF protection' 
  },
  { 
    name: 'ENCRYPTION_KEY', 
    length: 32, 
    description: 'Used for symmetric encryption of sensitive data' 
  },
  { 
    name: 'JWT_SECRET', 
    length: 64, 
    description: 'Secret for signing JWTs' 
  }
];

// Main function
async function main() {
  console.log('Generating secure environment secrets...\n');
  
  // Load existing .env file if it exists
  const envPath = path.join(process.cwd(), '.env');
  const envExamplePath = path.join(process.cwd(), '.env.example');
  
  let existingEnv: Record<string, string> = {};
  
  try {
    if (fs.existsSync(envPath)) {
      // Load existing .env contents
      existingEnv = dotenv.parse(fs.readFileSync(envPath, 'utf8'));
      console.log('Loaded existing .env file');
    }
  } catch (error) {
    console.warn('Warning: Could not read existing .env file. Creating new one.');
  }
  
  // Generate secrets for missing variables
  const generatedSecrets: Record<string, string> = {};
  let envFileChanged = false;
  
  for (const secret of secretsToGenerate) {
    // Only generate if not already set or if explicitly requested to overwrite
    if (!existingEnv[secret.name] || process.argv.includes('--force')) {
      const value = generateSecureString(secret.length);
      generatedSecrets[secret.name] = value;
      existingEnv[secret.name] = value;
      envFileChanged = true;
      
      console.log(`Generated ${secret.name} (${secret.description})`);
    } else {
      console.log(`Skipped ${secret.name} (already exists)`);
    }
  }
  
  // Save updated .env file if anything changed
  if (envFileChanged) {
    // Format environment variables
    const envContent = Object.entries(existingEnv)
      .map(([key, value]) => `${key}=${value}`)
      .join('\n');
    
    // Write to .env file
    fs.writeFileSync(envPath, envContent);
    console.log('\nUpdated .env file with generated secrets');
  } else {
    console.log('\nNo changes made to .env file');
  }
  
  // Check if .env.example exists and contains required variables
  let exampleEnvContent = '';
  let exampleEnvChanged = false;
  
  try {
    if (fs.existsSync(envExamplePath)) {
      // Read existing .env.example
      exampleEnvContent = fs.readFileSync(envExamplePath, 'utf8');
      
      // Check for missing variables
      for (const secret of secretsToGenerate) {
        if (!exampleEnvContent.includes(secret.name)) {
          exampleEnvContent += `\n# ${secret.description}\n${secret.name}=generate-a-secure-string-here\n`;
          exampleEnvChanged = true;
        }
      }
    } else {
      console.log('.env.example not found. Consider creating one.');
    }
    
    // Save updated .env.example file if anything changed
    if (exampleEnvChanged) {
      fs.writeFileSync(envExamplePath, exampleEnvContent);
      console.log('Updated .env.example with missing variables');
    }
  } catch (error) {
    console.warn('Warning: Could not update .env.example');
  }
  
  // Print summary
  console.log('\nSecurity key generation complete!');
  
  if (Object.keys(generatedSecrets).length > 0) {
    console.log('The following secure strings were generated:');
    for (const [name, value] of Object.entries(generatedSecrets)) {
      console.log(`  ${name}: ${value.substring(0, 5)}...${value.substring(value.length - 5)} (length: ${value.length})`);
    }
    console.log('\nThese have been saved to your .env file');
  }
  
  console.log('\nIMPORTANT: Keep your .env file secure and never commit it to version control.');
}

// Run the main function
main().catch(error => {
  console.error('Error generating secrets:', error);
  process.exit(1);
}); 