import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Get the authenticated user
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = session.user.id
    const clientId = params.id

    // Get the goals data from the request body
    const { goals } = await req.json()

    if (!Array.isArray(goals)) {
      return NextResponse.json(
        { error: "Goals must be an array" },
        { status: 400 }
      )
    }

    // Get the user to check their role
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, role: true }
    })

    if (!currentUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Get the client user
    const clientUser = await prisma.user.findUnique({
      where: { id: clientId },
      include: {
        clientProfile: {
          include: {
            assignedTrainer: true
          }
        }
      }
    })

    if (!clientUser) {
      return NextResponse.json(
        { error: "Client user not found" },
        { status: 404 }
      )
    }

    // Check authorization:
    // 1. User is updating their own goals
    // 2. User is a trainer updating their client's goals
    // 3. User is an admin
    const isOwnGoals = userId === clientId
    const isTrainer = currentUser.role === "trainer"
    const isAdmin = currentUser.role === "admin"
    
    // Check if the user is the assigned trainer for this client
    const isAssignedTrainer = clientUser.clientProfile?.assignedTrainer?.userId === userId

    if (!isOwnGoals && !isAdmin && !(isTrainer && isAssignedTrainer)) {
      return NextResponse.json(
        { error: "You are not authorized to update this client's goals" },
        { status: 403 }
      )
    }

    // Get current preferences
    let preferences = {}
    if (clientUser.preferences) {
      try {
        preferences = typeof clientUser.preferences === 'string'
          ? JSON.parse(clientUser.preferences)
          : clientUser.preferences
      } catch (e) {
        console.error('Error parsing preferences:', e)
      }
    }

    // Update preferences with goals
    preferences = {
      ...preferences,
      goals
    }

    // Update the user with the new goals in preferences
    const updatedUser = await prisma.user.update({
      where: { id: clientId },
      data: {
        preferences
      },
      select: {
        id: true,
        name: true,
        email: true,
        preferences: true
      }
    })

    return NextResponse.json({
      success: true,
      message: "Goals updated successfully",
      user: {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        goals
      }
    })
  } catch (error) {
    console.error("Error updating goals:", error)
    return NextResponse.json(
      { error: "Failed to update goals" },
      { status: 500 }
    )
  }
}
