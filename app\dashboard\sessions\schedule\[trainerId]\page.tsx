'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { CalendlyEmbed } from '@/components/calendly/calendly-embed';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, Calendar, Clock, User } from 'lucide-react';

interface TrainerData {
  id: string;
  name: string;
  calendlyUserId: string | null;
}

export default function ScheduleSessionPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const { toast } = useToast();
  const [trainer, setTrainer] = useState<TrainerData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const trainerId = params.trainerId as string;

  useEffect(() => {
    const fetchTrainerData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/calendly?action=trainer-settings&trainerId=${trainerId}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch trainer data');
        }
        
        const data = await response.json();
        setTrainer(data.trainer);
      } catch (error) {
        console.error('Error fetching trainer data:', error);
        setError('Failed to load trainer data. Please try again later.');
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load trainer data. Please try again later.',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (trainerId) {
      fetchTrainerData();
    }
  }, [trainerId, toast]);

  const handleEventScheduled = (event: any) => {
    toast({
      title: 'Session scheduled',
      description: 'Your 1:1 coaching session has been scheduled successfully.',
    });
    
    // Redirect to sessions page after a short delay
    setTimeout(() => {
      router.push('/dashboard/sessions');
    }, 2000);
  };

  if (isLoading) {
    return (
      <div className="container py-6 space-y-6">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-full max-w-md" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[600px] w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !trainer) {
    return (
      <div className="container py-6 space-y-6">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>
              {error || 'Failed to load trainer data. Please try again later.'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => router.back()}>Go Back</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!trainer.calendlyUserId) {
    return (
      <div className="container py-6 space-y-6">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <Card>
          <CardHeader>
            <CardTitle>Scheduling Not Available</CardTitle>
            <CardDescription>
              This trainer has not set up their scheduling system yet. Please contact them directly to schedule a session.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => router.back()}>Go Back</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const calendlyUrl = `https://calendly.com/${trainer.calendlyUserId}`;
  
  return (
    <div className="container py-6 space-y-6">
      <Button variant="outline" onClick={() => router.back()}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back
      </Button>
      
      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <CalendlyEmbed
            url={calendlyUrl}
            prefill={{
              email: session?.user?.email || '',
              name: session?.user?.name || '',
            }}
            onEventScheduled={handleEventScheduled}
            title={`Schedule a Session with ${trainer.name}`}
            description="Select a time that works for you for your 1:1 coaching session."
          />
        </div>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>About Your Coach</CardTitle>
              <CardDescription>
                Information about your 1:1 coaching sessions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-primary" />
                <div>
                  <p className="font-medium">{trainer.name}</p>
                  <p className="text-sm text-muted-foreground">Your personal coach</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-primary" />
                <div>
                  <p className="font-medium">Session Duration</p>
                  <p className="text-sm text-muted-foreground">Typically 30-60 minutes</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-primary" />
                <div>
                  <p className="font-medium">Scheduling</p>
                  <p className="text-sm text-muted-foreground">Choose from available time slots</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>What to Expect</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-sm">
                During your 1:1 coaching session, you'll have the opportunity to:
              </p>
              <ul className="text-sm list-disc pl-5 space-y-1">
                <li>Discuss your fitness goals and progress</li>
                <li>Get personalized advice and feedback</li>
                <li>Ask questions about your training plan</li>
                <li>Address any challenges you're facing</li>
                <li>Adjust your program as needed</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
