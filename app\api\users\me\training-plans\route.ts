import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export const dynamic = 'force-dynamic'

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    
    const userId = session.user.id
    
    // Fetch training plans assigned to this user
    const trainingPlans = await prisma.trainingPlanTemplate.findMany({
      where: {
        clientId: userId,
        type: "personalized"
      },
      orderBy: {
        createdAt: "desc"
      }
    })
    
    return NextResponse.json(trainingPlans)
  } catch (error) {
    console.error("Error fetching user training plans:", error)
    return NextResponse.json(
      { error: "An error occurred while fetching training plans" },
      { status: 500 }
    )
  }
}
