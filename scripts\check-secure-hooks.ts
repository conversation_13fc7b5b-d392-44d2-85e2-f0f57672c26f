#!/usr/bin/env ts-node

/**
 * Check Secure Hooks Usage
 * 
 * This script scans client components for proper use of secure API and form hooks
 * and reports any potentially unsafe usage patterns.
 * 
 * Usage:
 *   npm run security:check-hooks
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';
import chalk from 'chalk';

interface IssueResult {
  file: string;
  line: number;
  issue: string;
  severity: 'high' | 'medium' | 'low';
  suggestion: string;
}

// Define patterns to search for
const securePatterns = [
  {
    pattern: /useSafeQuery/g,
    type: 'secure',
    description: 'Safe query hook usage'
  },
  {
    pattern: /useSafeMutation/g,
    type: 'secure',
    description: 'Safe mutation hook usage'
  },
  {
    pattern: /useSafeForm/g,
    type: 'secure',
    description: 'Safe form hook usage'
  },
  {
    pattern: /fetchWithCSRF/g,
    type: 'secure',
    description: 'CSRF-protected fetch usage'
  }
];

const unsafePatterns = [
  {
    pattern: /fetch\s*\(/g,
    type: 'unsafe',
    description: 'Raw fetch API usage',
    severity: 'high' as const,
    suggestion: 'Replace with useSafeQuery or useSafeMutation hook'
  },
  {
    pattern: /useState.*Form/g,
    type: 'unsafe',
    description: 'Manual form state management',
    severity: 'medium' as const,
    suggestion: 'Consider using useSafeForm hook for built-in validation and sanitization'
  },
  {
    pattern: /new\s+FormData/g,
    type: 'unsafe',
    description: 'Raw FormData usage',
    severity: 'medium' as const,
    suggestion: 'Use sanitizeFormData utility from lib/validation/sanitization'
  },
  {
    pattern: /\$\.ajax|\$\.get|\$\.post/g,
    type: 'unsafe',
    description: 'jQuery AJAX usage',
    severity: 'high' as const,
    suggestion: 'Replace with useSafeQuery or useSafeMutation hook'
  },
  {
    pattern: /axios\./g,
    type: 'unsafe',
    description: 'Axios usage',
    severity: 'medium' as const,
    suggestion: 'Replace with useSafeQuery or useSafeMutation hook'
  }
];

// Function to scan a file for issues
async function scanFile(filePath: string): Promise<IssueResult[]> {
  const issues: IssueResult[] = [];
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  // First, check for secure imports
  const usesSecureHooks = /import.*from ['"]@\/lib\/hooks\/useSafe/.test(content);
  const usesCSRF = /import.*from ['"]@\/lib\/hooks\/useCSRF/.test(content);
  
  // If we see secure imports, we might be more lenient
  
  // Check for patterns line by line for better reporting
  lines.forEach((line, i) => {
    // Check for unsafe patterns
    for (const pattern of unsafePatterns) {
      if (pattern.pattern.test(line)) {
        // Skip if this file is using secure hooks and this is just in a comment
        if ((usesSecureHooks || usesCSRF) && line.trim().startsWith('//')) {
          continue;
        }
        
        // Skip if this is inside a comment
        if (line.trim().startsWith('//') || line.trim().startsWith('/*')) {
          continue;
        }
        
        issues.push({
          file: filePath,
          line: i + 1,
          issue: pattern.description,
          severity: pattern.severity,
          suggestion: pattern.suggestion
        });
      }
    }
  });
  
  return issues;
}

// Main function
async function main() {
  console.log(chalk.blue('Checking for secure hooks usage...'));
  
  const clientComponentGlobs = [
    'app/**/*.tsx',
    'components/**/*.tsx',
    'app/**/*.jsx',
    'components/**/*.jsx'
  ];
  
  let allIssues: IssueResult[] = [];
  
  // Find all client component files
  for (const pattern of clientComponentGlobs) {
    const files = await glob(pattern, { cwd: process.cwd() });
    
    for (const file of files) {
      const filePath = path.join(process.cwd(), file);
      const fileIssues = await scanFile(filePath);
      allIssues = [...allIssues, ...fileIssues];
    }
  }
  
  // Report results
  console.log(chalk.blue('\nScan completed!'));
  
  if (allIssues.length === 0) {
    console.log(chalk.green('✓ No security issues found in client components.'));
  } else {
    console.log(chalk.yellow(`⚠ Found ${allIssues.length} potential security issues:`));
    
    // Group by severity
    const highSeverity = allIssues.filter(issue => issue.severity === 'high');
    const mediumSeverity = allIssues.filter(issue => issue.severity === 'medium');
    const lowSeverity = allIssues.filter(issue => issue.severity === 'low');
    
    if (highSeverity.length > 0) {
      console.log(chalk.red(`\n${highSeverity.length} High Severity Issues:`));
      highSeverity.forEach((issue, i) => {
        console.log(chalk.red(`  ${i + 1}. ${issue.file}:${issue.line} - ${issue.issue}`));
        console.log(chalk.white(`     Suggestion: ${issue.suggestion}`));
      });
    }
    
    if (mediumSeverity.length > 0) {
      console.log(chalk.yellow(`\n${mediumSeverity.length} Medium Severity Issues:`));
      mediumSeverity.forEach((issue, i) => {
        console.log(chalk.yellow(`  ${i + 1}. ${issue.file}:${issue.line} - ${issue.issue}`));
        console.log(chalk.white(`     Suggestion: ${issue.suggestion}`));
      });
    }
    
    if (lowSeverity.length > 0) {
      console.log(chalk.blue(`\n${lowSeverity.length} Low Severity Issues:`));
      lowSeverity.forEach((issue, i) => {
        console.log(chalk.blue(`  ${i + 1}. ${issue.file}:${issue.line} - ${issue.issue}`));
        console.log(chalk.white(`     Suggestion: ${issue.suggestion}`));
      });
    }
    
    // Summary
    console.log(chalk.yellow('\nSummary:'));
    console.log(chalk.yellow(`- High severity issues: ${highSeverity.length}`));
    console.log(chalk.yellow(`- Medium severity issues: ${mediumSeverity.length}`));
    console.log(chalk.yellow(`- Low severity issues: ${lowSeverity.length}`));
    console.log(chalk.yellow(`- Total issues: ${allIssues.length}`));
    
    console.log(chalk.blue('\nRecommendation:'));
    console.log(chalk.white('- Use useSafeQuery and useSafeMutation hooks for all API calls'));
    console.log(chalk.white('- Use useSafeForm hook for all form handling'));
    console.log(chalk.white('- Ensure all data is sanitized before rendering or sending to the server'));
    
    // Exit with error code if high severity issues were found
    if (highSeverity.length > 0) {
      process.exit(1);
    }
  }
}

// Run main function
main().catch(error => {
  console.error('Error running secure hooks check:', error);
  process.exit(1);
}); 