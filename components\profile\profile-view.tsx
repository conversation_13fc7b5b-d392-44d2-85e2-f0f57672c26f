"use client"

import { Users, <PERSON><PERSON><PERSON>, Utensils, ShoppingBag } from "lucide-react"
import { useRouter } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface User {
  id: string
  fullName: string
  email: string
  bio: string | null
  role: string
  createdAt: Date
  updatedAt: Date
  _count: {
    trainingPlans: number
    dietPlans: number
    products: number
    clientSubscriptions: number
    athleteSubscriptions: number
  }
}

interface ProfileViewProps {
  user: User
}

export function ProfileView({ user }: ProfileViewProps) {
  const router = useRouter()

  const stats = [
    {
      title: "Training Plans",
      value: user._count.trainingPlans,
      icon: Dumbbell,
      href: "/dashboard/training-plans",
    },
    {
      title: "Diet Plans",
      value: user._count.dietPlans,
      icon: Utensils,
      href: "/dashboard/diet-plans",
    },
    {
      title: "Products",
      value: user._count.products,
      icon: ShoppingB<PERSON>,
      href: "/dashboard/products",
    },
    {
      title: user.role === "athlete" ? "Active Clients" : "Active Subscriptions",
      value: user.role === "athlete" 
        ? user._count.clientSubscriptions 
        : user._count.athleteSubscriptions,
      icon: Users,
      href: user.role === "athlete" ? "/dashboard/clients" : "/dashboard/subscriptions",
    },
  ]

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{user.fullName}</CardTitle>
              <CardDescription>{user.email}</CardDescription>
            </div>
            <Badge variant="outline" className="capitalize">
              {user.role}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {user.bio && (
            <div className="space-y-2">
              <h3 className="font-semibold">About</h3>
              <p className="text-gray-600">{user.bio}</p>
            </div>
          )}
          <div className="mt-4 space-y-2">
            <h3 className="font-semibold">Member Since</h3>
            <p className="text-gray-600">
              {new Date(user.createdAt).toLocaleDateString()}
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat) => (
          <Card 
            key={stat.title} 
            className="cursor-pointer hover:bg-gray-50 transition-colors"
            onClick={() => router.push(stat.href)}
          >
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
                <stat.icon className="h-8 w-8 text-gray-400" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-end">
        <Button onClick={() => router.push("/dashboard/settings")}>
          Edit Profile
        </Button>
      </div>
    </div>
  )
} 