"use client"

import { Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { toast } from "sonner"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal } from "lucide-react"

interface CoachingInquiry {
  id: string
  name: string
  email: string
  phone?: string
  goals: string
  experience: string
  availability: string
  message?: string
  status: "PENDING" | "APPROVED" | "REJECTED"
  createdAt: string
  updatedAt: string
}

export default function CoachingInquiriesPage() {
  const router = useRouter()
  const [inquiries, setInquiries] = useState<CoachingInquiry[]>([])
  const [loading, setLoading] = useState(true)
  const [responding, setResponding] = useState(false)
  const [selectedInquiry, setSelectedInquiry] = useState<CoachingInquiry | null>(null)
  const [responseNote, setResponseNote] = useState("")
  const [detailOpen, setDetailOpen] = useState(false)
  const [responseOpen, setResponseOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("pending")
  const [selectedInquiries, setSelectedInquiries] = useState<string[]>([])
  
  useEffect(() => {
    fetchInquiries()
  }, [])
  
  const fetchInquiries = async () => {
    try {
      setLoading(true)
      
      // Mock inquiries data
      const mockInquiries: CoachingInquiry[] = [
        {
          id: "inq1",
          name: "David Martinez",
          email: "<EMAIL>",
          phone: "+1234567890",
          goals: "I want to build muscle and improve my overall fitness level. I'm looking for a coach to help me with a structured workout plan.",
          experience: "I've been going to the gym for about a year but without a proper plan.",
          availability: "Weekdays after 6pm, and weekends",
          message: "I'm really excited to start working with a professional coach!",
          status: "PENDING",
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: "inq2",
          name: "Rachel Kim",
          email: "<EMAIL>",
          goals: "Training for my first half marathon and need guidance on strength training to complement my running.",
          experience: "Regular runner for 3 years, little experience with strength training.",
          availability: "Mornings before 9am, weekends flexible",
          status: "APPROVED",
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: "inq3",
          name: "Mark Taylor",
          email: "<EMAIL>",
          phone: "+**********",
          goals: "Weight loss and improving general health. I need accountability and motivation.",
          experience: "Complete beginner, haven't exercised regularly in years.",
          availability: "Evenings and weekends",
          message: "I've tried many diets before without success. I hope coaching can help me stick to a plan.",
          status: "REJECTED",
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];
      
      setInquiries(mockInquiries);
    } catch (error) {
      console.error("Error setting up mock inquiries:", error);
      toast.error("Failed to load coaching inquiries");
    } finally {
      setLoading(false);
    }
  }
  
  const handleInquiryResponse = async (action: "approve" | "reject") => {
    if (!selectedInquiry) return
    
    try {
      setResponding(true)
      
      // Update the local state only instead of API call
      setTimeout(() => {
        // Update the local state
        setInquiries(prev => 
          prev.map(inquiry => 
            inquiry.id === selectedInquiry.id
              ? { ...inquiry, status: action === "approve" ? "APPROVED" : "REJECTED" }
              : inquiry
          )
        )
        
        setResponseOpen(false)
        setDetailOpen(false)
        setResponseNote("")
        
        toast.success(
          action === "approve"
            ? "Application approved. The client has been notified."
            : "Application rejected. The client has been notified."
        )
        
        setResponding(false)
      }, 1000); // Simulate a delay
      
    } catch (error) {
      console.error("Error responding to inquiry:", error)
      toast.error("Failed to process the coaching application")
      setResponding(false)
    }
  }
  
  const filteredInquiries = inquiries.filter(inquiry => {
    if (activeTab === "pending") return inquiry.status === "PENDING"
    if (activeTab === "approved") return inquiry.status === "APPROVED"
    if (activeTab === "rejected") return inquiry.status === "REJECTED"
    return true
  })
  
  const openDetailDialog = (inquiry: CoachingInquiry) => {
    setSelectedInquiry(inquiry)
    setDetailOpen(true)
  }
  
  const openResponseDialog = (inquiry: CoachingInquiry) => {
    setSelectedInquiry(inquiry)
    setResponseOpen(true)
  }
  
  const toggleSelectInquiry = (id: string) => {
    if (selectedInquiries.includes(id)) {
      setSelectedInquiries(prev => prev.filter(i => i !== id))
    } else {
      setSelectedInquiries(prev => [...prev, id])
    }
  }
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Coaching Inquiries</h1>
          <p className="text-muted-foreground">Manage your coaching applications</p>
        </div>
      </div>
      
      <Tabs defaultValue="pending" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3 mb-6">
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>
        
        {loading ? (
          <div className="flex justify-center items-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading inquiries...</span>
          </div>
        ) : (
          <TabsContent value={activeTab} className="space-y-4">
            {filteredInquiries.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-10">
                  <p className="text-muted-foreground">No {activeTab} inquiries found</p>
                  {activeTab === "pending" && (
                    <p className="text-sm text-muted-foreground mt-2">
                      When clients apply for coaching, they'll appear here
                    </p>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Table>
                <TableBody>
                  {filteredInquiries.map((inquiry) => (
                    <TableRow key={inquiry.id} onClick={() => openDetailDialog(inquiry)} className="cursor-pointer">
                      <TableCell>
                        <Checkbox checked={selectedInquiries.includes(inquiry.id)} onCheckedChange={() => toggleSelectInquiry(inquiry.id)} />
                      </TableCell>
                      <TableCell className="font-medium">{inquiry.name}</TableCell>
                      <TableCell>{inquiry.email}</TableCell>
                      <TableCell>{inquiry.goals}</TableCell>
                      <TableCell>{new Date(inquiry.createdAt).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Badge variant={inquiry.status === "PENDING" ? "default" : inquiry.status === "APPROVED" ? "secondary" : "destructive"}>
                          {inquiry.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openDetailDialog(inquiry)}>View</DropdownMenuItem>
                            {inquiry.status === "PENDING" && (
                              <DropdownMenuItem onClick={() => openResponseDialog(inquiry)}>Respond</DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </TabsContent>
        )}
      </Tabs>
      
      {/* Detail Dialog */}
      <Dialog open={detailOpen} onOpenChange={setDetailOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Application Details</DialogTitle>
          </DialogHeader>
          
          {selectedInquiry && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold">Name</h3>
                  <p>{selectedInquiry.name}</p>
                </div>
                <div>
                  <h3 className="font-semibold">Email</h3>
                  <p>{selectedInquiry.email}</p>
                </div>
                {selectedInquiry.phone && (
                  <div>
                    <h3 className="font-semibold">Phone</h3>
                    <p>{selectedInquiry.phone}</p>
                  </div>
                )}
                <div>
                  <h3 className="font-semibold">Status</h3>
                  <Badge variant={
                    selectedInquiry.status === "PENDING" 
                      ? "default" 
                      : selectedInquiry.status === "APPROVED" 
                        ? "secondary" 
                        : "destructive"
                  }>
                    {selectedInquiry.status}
                  </Badge>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold">Goals</h3>
                <p className="text-muted-foreground">{selectedInquiry.goals}</p>
              </div>
              
              <div>
                <h3 className="font-semibold">Experience</h3>
                <p className="text-muted-foreground">{selectedInquiry.experience}</p>
              </div>
              
              <div>
                <h3 className="font-semibold">Availability</h3>
                <p className="text-muted-foreground">{selectedInquiry.availability}</p>
              </div>
              
              {selectedInquiry.message && (
                <div>
                  <h3 className="font-semibold">Additional Message</h3>
                  <p className="text-muted-foreground">{selectedInquiry.message}</p>
                </div>
              )}
              
              <div className="text-xs text-muted-foreground">
                Applied on {new Date(selectedInquiry.createdAt).toLocaleDateString()} at {new Date(selectedInquiry.createdAt).toLocaleTimeString()}
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setDetailOpen(false)}>
              Close
            </Button>
            {selectedInquiry?.status === "PENDING" && (
              <Button variant="default" onClick={() => {
                setDetailOpen(false)
                openResponseDialog(selectedInquiry)
              }}>
                Respond
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Response Dialog */}
      <Dialog open={responseOpen} onOpenChange={setResponseOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Respond to Application</DialogTitle>
            <DialogDescription>
              {selectedInquiry && (
                <span>Respond to {selectedInquiry.name}'s coaching application</span>
              )}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Add a personal note (optional)</h3>
              <Textarea
                placeholder="Add a personal message to send with your response..."
                value={responseNote}
                onChange={(e) => setResponseNote(e.target.value)}
                rows={4}
              />
            </div>
          </div>
          
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="destructive"
              onClick={() => handleInquiryResponse("reject")}
              disabled={responding}
            >
              {responding ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
              Decline Application
            </Button>
            <Button
              variant="default"
              onClick={() => handleInquiryResponse("approve")}
              disabled={responding}
            >
              {responding ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
              Approve Application
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Placeholder if no inquiries */}
      {filteredInquiries.length === 0 && !loading && (
        <Card>
          <CardContent className="p-6 text-center text-muted-foreground">
            You don&apos;t have any inquiries matching the current filters.
          </CardContent>
        </Card>
      )}

      {/* Bulk Actions */}
      {selectedInquiries.length > 0 && (
        <div className="fixed bottom-4 right-4 bg-card p-3 rounded-lg shadow-lg border flex items-center space-x-2">
          <p className="text-sm font-medium">{selectedInquiries.length} selected</p>
          <Button variant="outline" size="sm">Mark as Read</Button>
          <Button variant="outline" size="sm">Archive</Button>
          <Button variant="destructive" size="sm">Delete</Button>
        </div>
      )}
    </div>
  )
} 