import { Search } from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ExerciseForm } from "./exercise-form";

interface Exercise {
  id?: string;
  name: string;
  description?: string;
  sets?: number;
  reps?: number;
  duration?: number;
  restTime?: number;
  videoUrl?: string;
  muscleGroup?: string;
  type?: string;
  difficulty?: string;
  equipment?: string;
  calories?: number;
  weight?: number; // Keep for backward compatibility
  rpe?: number; // Rate of Perceived Exertion (1-10)
  rir?: number; // Reps in Reserve (0-10)
  order?: number;
}

interface ExerciseFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  exercise?: Exercise;
  workoutId?: string;
  exercises?: Exercise[];
}

export function ExerciseFormDialog({
  isOpen,
  onClose,
  onSubmit,
  exercise,
  workoutId,
  exercises = []
}: ExerciseFormDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState(exercise ? "create" : workoutId ? "select" : "create");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedExercise, setSelectedExercise] = useState<Exercise | null>(null);

  // Filter exercises for the selection view
  const filteredExercises = exercises.filter(ex => {
    return ex.name.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const handleSubmit = async (data: any) => {
    try {
      setIsSubmitting(true);

      // If we're in select mode and have a selected exercise, use that
      if (activeTab === "select" && selectedExercise) {
        // Make sure numeric values are properly formatted
        const formattedData = {
          ...selectedExercise,
          workoutId,
          sets: data.sets ? Number(data.sets) : selectedExercise.sets ? Number(selectedExercise.sets) : null,
          reps: data.reps ? Number(data.reps) : selectedExercise.reps ? Number(selectedExercise.reps) : null,
          weight: data.weight ? Number(data.weight) : selectedExercise.weight ? Number(selectedExercise.weight) : null
        };

        console.log("Submitting selected exercise with data:", formattedData);
        await onSubmit(formattedData);
      } else {
        // Pass the workout ID if it wasn't included by the form
        if (workoutId && !data.workoutId) {
          data.workoutId = workoutId;
        }

        // Format numeric fields for proper JSON parsing
        const formattedData = {
          ...data,
          sets: data.sets ? Number(data.sets) : null,
          reps: data.reps ? Number(data.reps) : null,
          weight: data.weight ? Number(data.weight) : null,
          duration: data.duration ? Number(data.duration) : null,
          restTime: data.restTime ? Number(data.restTime) : null,
        };

        console.log("Submitting form data:", formattedData);
        await onSubmit(formattedData);
      }
      onClose();
    } catch (error) {
      console.error("Error submitting exercise:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleExerciseSelect = (exercise: Exercise) => {
    setSelectedExercise(exercise);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {exercise?.id ? "Edit Exercise" : workoutId ? "Add Exercise to Workout" : "Create Exercise"}
          </DialogTitle>
          {workoutId && (
            <DialogDescription>
              Select an existing exercise or create a new one
            </DialogDescription>
          )}
        </DialogHeader>

        {workoutId && !exercise ? (
          <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="select">Select Existing</TabsTrigger>
              <TabsTrigger value="create">Create New</TabsTrigger>
            </TabsList>

            <TabsContent value="select" className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search exercises..."
                  className="pl-10 pr-4 py-2 w-full border rounded-md"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-96 overflow-y-auto">
                {filteredExercises.length === 0 ? (
                  <div className="col-span-2 py-8 text-center text-muted-foreground">
                    No exercises found. Try a different search or create a new exercise.
                  </div>
                ) : (
                  filteredExercises.map(ex => (
                    <div
                      key={ex.id}
                      className={`p-3 border rounded-md cursor-pointer transition-colors ${
                        selectedExercise?.id === ex.id
                          ? 'bg-primary/10 border-primary'
                          : 'hover:bg-accent/20'
                      }`}
                      onClick={() => handleExerciseSelect(ex)}
                    >
                      <div className="font-medium">{ex.name}</div>
                      {ex.muscleGroup && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {ex.muscleGroup} • {ex.type || 'No type'}
                        </div>
                      )}
                      <div className="flex gap-2 mt-2 text-xs">
                        {ex.sets && <span className="bg-accent/30 px-2 py-0.5 rounded">{ex.sets} sets</span>}
                        {ex.reps && <span className="bg-accent/30 px-2 py-0.5 rounded">{ex.reps} reps</span>}
                      </div>
                    </div>
                  ))
                )}
              </div>

              {selectedExercise && (
                <div className="mt-4 p-4 border rounded-md bg-accent/5">
                  <h3 className="font-medium">Configure Exercise: {selectedExercise.name}</h3>
                  <div className="grid grid-cols-3 gap-4 mt-3">
                    <div>
                      <label className="text-sm font-medium block mb-1">Sets</label>
                      <input
                        type="number"
                        defaultValue={selectedExercise.sets || ""}
                        className="w-full p-2 border rounded"
                        onChange={(e) => {
                          setSelectedExercise({
                            ...selectedExercise,
                            sets: e.target.value ? parseInt(e.target.value) : undefined
                          });
                        }}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium block mb-1">Reps</label>
                      <input
                        type="number"
                        defaultValue={selectedExercise.reps || ""}
                        className="w-full p-2 border rounded"
                        onChange={(e) => {
                          setSelectedExercise({
                            ...selectedExercise,
                            reps: e.target.value ? parseInt(e.target.value) : undefined
                          });
                        }}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium block mb-1">Weight (kg)</label>
                      <input
                        type="number"
                        defaultValue={selectedExercise.weight || ""}
                        className="w-full p-2 border rounded"
                        onChange={(e) => {
                          setSelectedExercise({
                            ...selectedExercise,
                            weight: e.target.value ? parseFloat(e.target.value) : undefined
                          });
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-between pt-4">
                <Button variant="outline" onClick={onClose}>Cancel</Button>
                <Button
                  disabled={!selectedExercise || isSubmitting}
                  onClick={() => selectedExercise && handleSubmit(selectedExercise)}
                >
                  {isSubmitting ? "Adding..." : "Add to Workout"}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="create">
              <ExerciseForm
                defaultValues={exercise || {}}
                onSubmit={handleSubmit}
                onCancel={onClose}
                isSubmitting={isSubmitting}
                workoutId={workoutId}
              />
            </TabsContent>
          </Tabs>
        ) : (
          <ExerciseForm
            defaultValues={exercise || {}}
            onSubmit={handleSubmit}
            onCancel={onClose}
            isSubmitting={isSubmitting}
            workoutId={workoutId}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}