# Digital Product Workflow Testing Guide

This guide provides step-by-step instructions for testing the digital product workflow in the application.

## Prerequisites

- The application is running locally on http://localhost:3000
- You have access to a web browser

## Test Steps

### 1. Login as a Dev User

1. Visit: http://localhost:3000/api/auth/dev-login?role=client
2. You should be automatically redirected to the dashboard
3. Verify that you see "Development Client" in the navigation bar.

### 2. Browse Products in the Shop

1. Navigate to: http://localhost:3000/dashboard/shop
2. Verify that you can see a list of digital products
3. Each product should have:
   - A title
   - A description
   - A price
   - An "Add to Cart" button

### 3. Add a Product to Cart

1. Click the "Add to Cart" button on any product
2. Verify that you see a toast notification confirming the product was added
3. The button should change to "View Cart"

### 4. View Your Cart

1. Click the "View Cart" button or navigate to: http://localhost:3000/dashboard/cart
2. Verify that you can see the product you added in your cart
3. The cart should show:
   - The product title
   - The product price
   - A "Remove" button
   - A "Checkout" button with the total price

### 5. Checkout

1. Click the "Checkout" button
2. Verify that the checkout process completes successfully
3. You should see a success message
4. After a short delay, you should be redirected to the library page

### 6. View Your Library

1. If not automatically redirected, navigate to: http://localhost:3000/dashboard/library
2. Verify that you can see the product you purchased in your library
3. The library should show:
   - The product title
   - The product description
   - A "Download" button (if applicable)

## Expected Results

If all steps work as expected, the digital product workflow is functioning correctly. Users should be able to:

1. Browse products
2. Add products to cart
3. View their cart
4. Complete the checkout process
5. Access their purchased products in the library

## Troubleshooting

If any step fails, check the browser console for error messages. Common issues include:

- CSRF protection errors in development mode
- Database connection issues
- Missing product fields in the database schema
- Authentication issues with the dev login

## Additional Testing

For a more comprehensive test, try the following variations:

- Add multiple products to the cart
- Apply a promo code (try "FITNESS20" for a 20% discount)
- Remove items from the cart before checkout
- Test with different dev roles (admin, trainer)
