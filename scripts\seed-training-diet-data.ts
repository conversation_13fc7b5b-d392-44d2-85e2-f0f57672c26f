import { db } from "../lib/db"

async function main() {
  try {
    // Find admin user
    const adminUser = await db.user.findFirst({
      where: {
        role: "admin",
      },
    })

    if (!adminUser) {
      console.log("Admin user not found. Please create an admin user first.")
      process.exit(1)
    }

    // Find client user
    const clientUser = await db.user.findFirst({
      where: {
        role: "client",
      },
    })

    if (!clientUser) {
      console.log("Client user not found. Please create a client user first.")
      process.exit(1)
    }

    // Create sample training plans
    const trainingPlans = [
      {
        title: "Beginner Strength Program",
        description: "A 12-week program focused on building foundational strength",
        athleteId: adminUser.id,
        workouts: {
          create: [
            {
              title: "Day 1 - Lower Body",
              description: "Focus on legs and core",
              exercises: {
                create: [
                  { name: "Squats", sets: 3, reps: 12 },
                  { name: "Deadlifts", sets: 3, reps: 10 },
                  { name: "Lu<PERSON>", sets: 3, reps: 12 },
                ],
              },
            },
            {
              title: "Day 2 - Upper Body",
              description: "Chest, back, and arms",
              exercises: {
                create: [
                  { name: "Push-ups", sets: 3, reps: 10 },
                  { name: "Dumbbell Rows", sets: 3, reps: 12 },
                  { name: "Shoulder Press", sets: 3, reps: 10 },
                ],
              },
            },
          ],
        },
      },
      {
        title: "HIIT Fat Loss",
        description: "High-intensity interval training for maximum fat burn",
        athleteId: adminUser.id,
        workouts: {
          create: [
            {
              title: "Circuit 1",
              description: "Full body HIIT",
              exercises: {
                create: [
                  { name: "Burpees", sets: 4, reps: 30 },
                  { name: "Mountain Climbers", sets: 4, reps: 30 },
                  { name: "Jump Squats", sets: 4, reps: 30 },
                ],
              },
            },
          ],
        },
      },
    ]

    // Create sample diet plans
    const dietPlans = [
      {
        title: "Weight Loss Meal Plan",
        description: "1800 calorie diet plan for sustainable weight loss",
        athleteId: adminUser.id,
        meals: {
          create: [
            {
              name: "Breakfast",
              description: "High protein breakfast",
              calories: 400,
              protein: 30,
              carbs: 45,
              fats: 15,
            },
            {
              name: "Lunch",
              description: "Balanced lunch with lean protein",
              calories: 500,
              protein: 35,
              carbs: 50,
              fats: 20,
            },
            {
              name: "Dinner",
              description: "Light dinner with vegetables",
              calories: 400,
              protein: 30,
              carbs: 35,
              fats: 15,
            },
          ],
        },
      },
      {
        title: "Muscle Gain Diet",
        description: "3000 calorie diet plan for muscle growth",
        athleteId: adminUser.id,
        meals: {
          create: [
            {
              name: "Breakfast",
              description: "High calorie breakfast",
              calories: 800,
              protein: 50,
              carbs: 100,
              fats: 25,
            },
            {
              name: "Lunch",
              description: "Protein-rich lunch",
              calories: 900,
              protein: 60,
              carbs: 80,
              fats: 30,
            },
            {
              name: "Dinner",
              description: "Recovery dinner",
              calories: 700,
              protein: 45,
              carbs: 70,
              fats: 25,
            },
          ],
        },
      },
    ]

    // Create sample subscription tiers
    const tiers = [
      {
        name: "Basic",
        price: 29.99,
        description: "Access to basic training plans and diet guides. Includes: Basic training plans, Diet guidelines, Progress tracking",
      },
      {
        name: "Premium",
        price: 49.99,
        description: "Full access to all features plus 1-on-1 coaching. Includes: All training plans, Custom diet plans, Weekly check-ins, 1-on-1 coaching, Priority support",
      },
    ]

    // Clear existing data
    await db.exercise.deleteMany()
    await db.workout.deleteMany()
    await db.trainingPlan.deleteMany()
    await db.meal.deleteMany()
    await db.dietPlan.deleteMany()
    await db.subscriptionTier.deleteMany()

    // Insert new data
    for (const plan of trainingPlans) {
      await db.trainingPlan.create({
        data: plan,
      })
    }

    for (const plan of dietPlans) {
      await db.dietPlan.create({
        data: plan,
      })
    }

    for (const tier of tiers) {
      await db.subscriptionTier.create({
        data: tier,
      })
    }

    // Create a sample subscription
    await db.subscription.create({
      data: {
        athleteId: adminUser.id,
        clientId: clientUser.id,
        tierId: (
          await db.subscriptionTier.findFirst({
            where: { name: "Premium" },
          })
        )?.id!,
        status: "ACTIVE",
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      },
    })

    console.log("Sample training and diet data created successfully!")
  } catch (error) {
    console.error("Error seeding training and diet data:", error)
    process.exit(1)
  } finally {
    await db.$disconnect()
  }
}

main() 